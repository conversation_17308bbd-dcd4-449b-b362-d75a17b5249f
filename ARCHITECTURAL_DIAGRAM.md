# AtomSec Platform - Complete Architectural Diagram

## Overview

AtomSec is a comprehensive security platform built on Azure cloud infrastructure with a microservices architecture. The platform consists of multiple interconnected services that provide security scanning, analysis, and management capabilities for Salesforce and other enterprise systems.

## High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Browser]
        MOBILE[Mobile App]
        API_CLIENT[API Client]
    end
    
    subgraph "Frontend Application"
        REACT[React SPA]
        AUTH_UI[Authentication UI]
        DASHBOARD[Dashboard]
        INTEGRATION_UI[Integration Management]
        REPORTS[Reports & Analytics]
    end
    
    subgraph "API Gateway Layer"
        APIM[Azure API Management]
        CORS[CORS Handler]
        RATE_LIMIT[Rate Limiting]
        AUTH_GATEWAY[Auth Gateway]
    end
    
    subgraph "Microservices Layer"
        subgraph "DB Service (atomsec-func-db-r)"
            DB_API[Database API]
            DB_REPO[Repository Layer]
            DB_AUTH[Auth Endpoints]
            DB_TASKS[Task Management]
            DB_POLICIES[Policy Management]
        end
        
        subgraph "SFDC Service (atomsec-func-sfdc)"
            SFDC_API[Salesforce API]
            SFDC_INTEGRATION[Integration Management]
            SFDC_SECURITY[Security Scanning]
            SFDC_PMD[PMD Analysis]
            SFDC_TASKS[Task Processor]
            SFDC_BLUEPRINTS[Blueprint Components]
        end
    end
    
    subgraph "External Services"
        SALESFORCE[Salesforce API]
        AZURE_AD[Azure Active Directory]
        MS_GRAPH[Microsoft Graph API]
    end
    
    subgraph "Azure Infrastructure"
        KV[Azure Key Vault]
        SB[Azure Service Bus]
        AI[Application Insights]
        STORAGE[Blob Storage]
        SQL[Azure SQL Database]
        REDIS[Redis Cache]
        APIM_SERVICE[API Management]
    end
    
    subgraph "CI/CD Pipeline"
        AZURE_DEVOPS[Azure DevOps]
        PIPELINE_DB[DB Service Pipeline]
        PIPELINE_SFDC[SFDC Service Pipeline]
        PIPELINE_FRONTEND[Frontend Pipeline]
    end
    
    %% Client connections
    WEB --> REACT
    MOBILE --> REACT
    API_CLIENT --> APIM
    
    %% Frontend to Gateway
    REACT --> APIM
    
    %% Gateway to Services
    APIM --> DB_API
    APIM --> SFDC_API
    
    %% Service Communication
    DB_API --> SFDC_API
    SFDC_API --> DB_API
    
    %% External Service Connections
    SFDC_API --> SALESFORCE
    DB_API --> AZURE_AD
    SFDC_API --> MS_GRAPH
    
    %% Azure Service Dependencies
    DB_API --> KV
    SFDC_API --> KV
    DB_API --> SB
    SFDC_API --> SB
    DB_API --> AI
    SFDC_API --> AI
    SFDC_API --> STORAGE
    DB_API --> SQL
    SFDC_API --> REDIS
    
    %% CI/CD Connections
    AZURE_DEVOPS --> PIPELINE_DB
    AZURE_DEVOPS --> PIPELINE_SFDC
    AZURE_DEVOPS --> PIPELINE_FRONTEND
    PIPELINE_DB --> DB_API
    PIPELINE_SFDC --> SFDC_API
    PIPELINE_FRONTEND --> REACT
```

## Detailed Component Architecture

### 1. Frontend Application (atomsec-app-frontend)

```mermaid
graph TB
    subgraph "React Application Structure"
        subgraph "Core Components"
            APP[App.js]
            AUTH_CONTEXT[AuthContext.js]
            ROUTER[React Router]
        end
        
        subgraph "UI Components"
            HEADER[Header.jsx]
            SIDEBAR[Sidebar.jsx]
            DASHBOARD[Dashboard.jsx]
            ACCOUNTS[Accounts.jsx]
            INTEGRATIONS[Integrations.jsx]
            REPORTS[Reports.jsx]
            SETTINGS[Settings.jsx]
        end
        
        subgraph "Authentication"
            LOGIN[Login.jsx]
            AUTH_CALLBACK[AuthCallback.jsx]
            PLATFORM_AUTH[PlatformAuthHandler.jsx]
        end
        
        subgraph "Security Tools"
            SCAN_WIZARD[ScanWizard.jsx]
            SECURITY_TOOLS[SecurityToolsSelector.jsx]
            HEALTH_CHECK[HealthCheckDetails.jsx]
        end
        
        subgraph "Configuration"
            CONFIG[Configurations.jsx]
            TASK_MGMT[TaskManagement.jsx]
            ACCOUNT_MGMT[AccountManagement.jsx]
        end
    end
    
    subgraph "External Dependencies"
        MSAL[MSAL Browser]
        ANT_DESIGN[Ant Design]
        MATERIAL_UI[Material-UI]
        AG_GRID[AG Grid]
        CHART_JS[Chart.js]
    end
    
    APP --> AUTH_CONTEXT
    APP --> ROUTER
    ROUTER --> HEADER
    ROUTER --> SIDEBAR
    ROUTER --> DASHBOARD
    ROUTER --> ACCOUNTS
    ROUTER --> INTEGRATIONS
    ROUTER --> REPORTS
    ROUTER --> SETTINGS
    ROUTER --> LOGIN
    ROUTER --> AUTH_CALLBACK
    ROUTER --> PLATFORM_AUTH
    ROUTER --> SCAN_WIZARD
    ROUTER --> SECURITY_TOOLS
    ROUTER --> HEALTH_CHECK
    ROUTER --> CONFIG
    ROUTER --> TASK_MGMT
    ROUTER --> ACCOUNT_MGMT
    
    APP --> MSAL
    APP --> ANT_DESIGN
    APP --> MATERIAL_UI
    APP --> AG_GRID
    APP --> CHART_JS
```

### 2. Database Service (atomsec-func-db-r)

```mermaid
graph TB
    subgraph "Database Service Architecture"
        subgraph "Entry Point"
            FUNCTION_APP[function_app.py]
        end
        
        subgraph "API Layer"
            USER_API[user_endpoints.py]
            ACCOUNT_API[account_endpoints.py]
            ORG_API[organization_endpoints.py]
            INTEGRATION_API[integration_endpoints.py]
            SECURITY_API[security_endpoints.py]
            TASK_API[task_endpoints.py]
            AUTH_API[auth_endpoints.py]
            POLICY_API[policy_endpoints.py]
        end
        
        subgraph "Repository Layer"
            BASE_REPO[base_repository.py]
            PMD_REPO[pmd_repository.py]
        end
        
        subgraph "Shared Components"
            AUTH_UTILS[auth_utils.py]
            AZURE_SERVICES[azure_services.py]
            CACHE_SERVICE[cache_service.py]
            CONFIG_MANAGER[configuration_manager.py]
        end
        
        subgraph "Data Storage"
            AZURE_TABLES[Azure Table Storage]
            SQL_DB[SQL Database]
        end
    end
    
    FUNCTION_APP --> USER_API
    FUNCTION_APP --> ACCOUNT_API
    FUNCTION_APP --> ORG_API
    FUNCTION_APP --> INTEGRATION_API
    FUNCTION_APP --> SECURITY_API
    FUNCTION_APP --> TASK_API
    FUNCTION_APP --> AUTH_API
    FUNCTION_APP --> POLICY_API
    
    USER_API --> BASE_REPO
    ACCOUNT_API --> BASE_REPO
    ORG_API --> BASE_REPO
    INTEGRATION_API --> BASE_REPO
    SECURITY_API --> BASE_REPO
    TASK_API --> BASE_REPO
    AUTH_API --> AUTH_UTILS
    POLICY_API --> BASE_REPO
    
    BASE_REPO --> AZURE_TABLES
    BASE_REPO --> SQL_DB
    AUTH_UTILS --> AZURE_SERVICES
    AUTH_UTILS --> CACHE_SERVICE
    AUTH_UTILS --> CONFIG_MANAGER
```

### 3. SFDC Service (atomsec-func-sfdc)

```mermaid
graph TB
    subgraph "SFDC Service Architecture"
        subgraph "Entry Point"
            SFDC_FUNCTION_APP[function_app.py]
        end
        
        subgraph "API Layer"
            SFDC_USER_API[user_endpoints.py]
            SFDC_ACCOUNT_API[account_endpoints.py]
            SFDC_ORG_API[organization_endpoints.py]
            SFDC_INTEGRATION_API[integration_endpoints.py]
            SFDC_SECURITY_API[security_endpoints.py]
            SFDC_TASK_API[task_endpoints.py]
            SFDC_AUTH_API[auth_endpoints.py]
        end
        
        subgraph "Blueprint Components"
            ACCOUNT_MGMT_BP[account_management.py]
            AUTH_BP[auth.py]
            INTEGRATION_BP[integration.py]
            SECURITY_BP[security.py]
            TASK_BP[task.py]
        end
        
        subgraph "Task Processor"
            TASK_PROCESSOR[task_processor/]
            API_WHITELISTING[api_whitelisting.py]
            DEVICE_ACTIVATION[device_activation.py]
            LOGIN_HOURS[login_hours.py]
            MFA_SETUP[mfa_setup.py]
            PROFILE_PERMISSIONS[profile_permissions.py]
            SECURITY_SCAN[security_scan.py]
            USER_MANAGEMENT[user_management.py]
        end
        
        subgraph "PMD Components"
            PMD_BLOB_HANDLER[pmd_blob_handler.py]
            PMD_RESULTS_PROCESSOR[pmd_results_processor.py]
            PMD_SCANNER[pmd_scanner.py]
        end
        
        subgraph "Shared Services"
            ADVANCED_AUTH[advanced_auth_service.py]
            ADVANCED_CACHING[advanced_caching_service.py]
            CONFIG_SERVICE[configuration_service.py]
            INTEGRATION_SERVICE[integration_service.py]
            SECURITY_SERVICE[security_service.py]
        end
    end
    
    subgraph "External Integrations"
        SALESFORCE_API[Salesforce API]
        AZURE_KEY_VAULT[Azure Key Vault]
        SERVICE_BUS[Azure Service Bus]
        BLOB_STORAGE[Blob Storage]
    end
    
    SFDC_FUNCTION_APP --> SFDC_USER_API
    SFDC_FUNCTION_APP --> SFDC_ACCOUNT_API
    SFDC_FUNCTION_APP --> SFDC_ORG_API
    SFDC_FUNCTION_APP --> SFDC_INTEGRATION_API
    SFDC_FUNCTION_APP --> SFDC_SECURITY_API
    SFDC_FUNCTION_APP --> SFDC_TASK_API
    SFDC_FUNCTION_APP --> SFDC_AUTH_API
    
    SFDC_USER_API --> ACCOUNT_MGMT_BP
    SFDC_ACCOUNT_API --> ACCOUNT_MGMT_BP
    SFDC_INTEGRATION_API --> INTEGRATION_BP
    SFDC_SECURITY_API --> SECURITY_BP
    SFDC_TASK_API --> TASK_BP
    SFDC_AUTH_API --> AUTH_BP
    
    TASK_BP --> TASK_PROCESSOR
    TASK_PROCESSOR --> API_WHITELISTING
    TASK_PROCESSOR --> DEVICE_ACTIVATION
    TASK_PROCESSOR --> LOGIN_HOURS
    TASK_PROCESSOR --> MFA_SETUP
    TASK_PROCESSOR --> PROFILE_PERMISSIONS
    TASK_PROCESSOR --> SECURITY_SCAN
    TASK_PROCESSOR --> USER_MANAGEMENT
    
    SECURITY_BP --> PMD_BLOB_HANDLER
    SECURITY_BP --> PMD_RESULTS_PROCESSOR
    SECURITY_BP --> PMD_SCANNER
    
    INTEGRATION_BP --> ADVANCED_AUTH
    INTEGRATION_BP --> ADVANCED_CACHING
    INTEGRATION_BP --> CONFIG_SERVICE
    INTEGRATION_BP --> INTEGRATION_SERVICE
    INTEGRATION_BP --> SECURITY_SERVICE
    
    ADVANCED_AUTH --> AZURE_KEY_VAULT
    INTEGRATION_SERVICE --> SALESFORCE_API
    PMD_BLOB_HANDLER --> BLOB_STORAGE
    TASK_PROCESSOR --> SERVICE_BUS
```

## Data Flow Architecture

### 1. Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant APIM
    participant DB_Service
    participant SFDC_Service
    participant Azure_AD
    participant Key_Vault
    
    User->>Frontend: Login Request
    Frontend->>APIM: POST /api/db/auth/login
    APIM->>DB_Service: Forward Request
    DB_Service->>Azure_AD: Validate Credentials
    Azure_AD-->>DB_Service: Authentication Result
    DB_Service->>Key_Vault: Get User Permissions
    Key_Vault-->>DB_Service: User Permissions
    DB_Service-->>APIM: JWT Token + User Info
    APIM-->>Frontend: Authentication Response
    Frontend-->>User: Login Success
    
    Note over User,Key_Vault: Subsequent requests include JWT token
    
    User->>Frontend: API Request with JWT
    Frontend->>APIM: Request with Authorization header
    APIM->>DB_Service: Validate JWT Token
    DB_Service->>Azure_AD: Validate Token
    Azure_AD-->>DB_Service: Token Validation Result
    DB_Service->>DB_Service: Process Request
    DB_Service-->>APIM: Response
    APIM-->>Frontend: Response
    Frontend-->>User: Display Result
```

### 2. Security Scan Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant DB_Service
    participant SFDC_Service
    participant Salesforce
    participant Service_Bus
    participant Blob_Storage
    participant Key_Vault
    
    User->>Frontend: Trigger Security Scan
    Frontend->>DB_Service: POST /api/db/integration/scan/{id}
    DB_Service->>DB_Service: Validate Request & Auth
    DB_Service->>SFDC_Service: POST /api/integration/scan/{id}
    
    SFDC_Service->>SFDC_Service: Create Execution Log
    SFDC_Service->>Service_Bus: Enqueue Scan Tasks
    SFDC_Service-->>DB_Service: Scan Initiated Response
    DB_Service-->>Frontend: Task Created Response
    Frontend-->>User: Scan Started Notification
    
    %% Background Processing
    Service_Bus->>SFDC_Service: Process sfdc_authenticate Task
    SFDC_Service->>Key_Vault: Get Salesforce Credentials
    Key_Vault-->>SFDC_Service: Credentials
    SFDC_Service->>Salesforce: Authenticate with Salesforce
    Salesforce-->>SFDC_Service: Authentication Token
    SFDC_Service->>Service_Bus: Update Task Status
    
    Service_Bus->>SFDC_Service: Process health_check Task
    SFDC_Service->>Salesforce: Health Check API Calls
    Salesforce-->>SFDC_Service: Health Data
    SFDC_Service->>Service_Bus: Update Task Status
    
    Service_Bus->>SFDC_Service: Process metadata_extraction Task
    SFDC_Service->>Salesforce: Extract Metadata
    Salesforce-->>SFDC_Service: Metadata Response
    SFDC_Service->>Blob_Storage: Store Metadata
    SFDC_Service->>Service_Bus: Update Task Status
    
    Service_Bus->>SFDC_Service: Process pmd_apex_security Task
    SFDC_Service->>SFDC_Service: Run PMD Analysis
    SFDC_Service->>Blob_Storage: Store PMD Results
    SFDC_Service->>Service_Bus: Complete Task Sequence
    
    %% Status Updates
    SFDC_Service->>DB_Service: Update Integration Status
    DB_Service->>Frontend: WebSocket/Polling Update
    Frontend->>User: Scan Complete Notification
```

## Infrastructure Architecture

### 1. Azure Resource Layout

```mermaid
graph TB
    subgraph "Azure Subscription"
        subgraph "Resource Groups"
            subgraph "atomsec-dev-backend"
                FUNC_SFDC[func-atomsec-sfdc-dev02]
                FUNC_DB[func-atomsec-dbconnect-dev]
                KEY_VAULT[atomsec-keyvault-dev]
                SERVICE_BUS[atomsec-servicebus-dev]
                STORAGE_ACCOUNT[atomsecstorage-dev]
                SQL_SERVER[atomsec-sql-dev]
                APP_INSIGHTS[atomsec-appinsights-dev]
            end
            
            subgraph "atomsec-dev-data"
                DATA_STORAGE[atomsec-data-storage]
                REDIS_CACHE[atomsec-redis-dev]
            end
            
            subgraph "atomsec-dev-frontend"
                STATIC_WEB[atomsec-static-web-dev]
                CDN[Azure CDN]
            end
        end
        
        subgraph "API Management"
            APIM_SERVICE[atomsec-apim-dev]
            APIM_PRODUCTS[API Products]
            APIM_POLICIES[Policies]
        end
        
        subgraph "Monitoring"
            LOG_ANALYTICS[Log Analytics Workspace]
            ALERT_RULES[Alert Rules]
            DASHBOARDS[Azure Dashboards]
        end
    end
    
    FUNC_SFDC --> KEY_VAULT
    FUNC_DB --> KEY_VAULT
    FUNC_SFDC --> SERVICE_BUS
    FUNC_DB --> SERVICE_BUS
    FUNC_SFDC --> STORAGE_ACCOUNT
    FUNC_DB --> SQL_SERVER
    FUNC_SFDC --> APP_INSIGHTS
    FUNC_DB --> APP_INSIGHTS
    
    APIM_SERVICE --> FUNC_SFDC
    APIM_SERVICE --> FUNC_DB
    
    APP_INSIGHTS --> LOG_ANALYTICS
    LOG_ANALYTICS --> ALERT_RULES
    LOG_ANALYTICS --> DASHBOARDS
```

### 2. CI/CD Pipeline Architecture

```mermaid
graph TB
    subgraph "Azure DevOps"
        subgraph "Repositories"
            FRONTEND_REPO[atomsec-app-frontend]
            SFDC_REPO[atomsec-func-sfdc]
            DB_REPO[atomsec-func-db-r]
        end
        
        subgraph "Pipelines"
            FRONTEND_PIPELINE[pipeline-frontend-dev.yml]
            SFDC_PIPELINE[pipeline-func-sfdc-dev.yml]
            DB_PIPELINE[pipeline-func-db-dev.yml]
        end
        
        subgraph "Build Agents"
            UBUNTU_AGENT[Ubuntu Latest]
            PYTHON_312[Python 3.12]
            NODE_JS[Node.js]
        end
    end
    
    subgraph "Deployment Targets"
        subgraph "Development Environment"
            DEV_FUNC_SFDC[func-atomsec-sfdc-dev02]
            DEV_FUNC_DB[func-atomsec-dbconnect-dev]
            DEV_STATIC_WEB[atomsec-static-web-dev]
        end
        
        subgraph "Staging Environment"
            STAGE_FUNC_SFDC[func-atomsec-sfdc-stage]
            STAGE_FUNC_DB[func-atomsec-dbconnect-stage]
            STAGE_STATIC_WEB[atomsec-static-web-stage]
        end
        
        subgraph "Production Environment"
            PROD_FUNC_SFDC[func-atomsec-sfdc-prod]
            PROD_FUNC_DB[func-atomsec-dbconnect-prod]
            PROD_STATIC_WEB[atomsec-static-web-prod]
        end
    end
    
    FRONTEND_REPO --> FRONTEND_PIPELINE
    SFDC_REPO --> SFDC_PIPELINE
    DB_REPO --> DB_PIPELINE
    
    FRONTEND_PIPELINE --> UBUNTU_AGENT
    SFDC_PIPELINE --> UBUNTU_AGENT
    DB_PIPELINE --> UBUNTU_AGENT
    
    UBUNTU_AGENT --> PYTHON_312
    UBUNTU_AGENT --> NODE_JS
    
    FRONTEND_PIPELINE --> DEV_STATIC_WEB
    SFDC_PIPELINE --> DEV_FUNC_SFDC
    DB_PIPELINE --> DEV_FUNC_DB
    
    DEV_FUNC_SFDC --> STAGE_FUNC_SFDC
    DEV_FUNC_DB --> STAGE_FUNC_DB
    DEV_STATIC_WEB --> STAGE_STATIC_WEB
    
    STAGE_FUNC_SFDC --> PROD_FUNC_SFDC
    STAGE_FUNC_DB --> PROD_FUNC_DB
    STAGE_STATIC_WEB --> PROD_STATIC_WEB
```

## Security Architecture

### 1. Authentication & Authorization

```mermaid
graph TB
    subgraph "Authentication Layers"
        subgraph "Client Authentication"
            MSAL_BROWSER[MSAL Browser]
            JWT_TOKENS[JWT Tokens]
            REFRESH_TOKENS[Refresh Tokens]
        end
        
        subgraph "Service Authentication"
            SERVICE_PRINCIPALS[Service Principals]
            MANAGED_IDENTITIES[Managed Identities]
            CERTIFICATES[Certificates]
        end
        
        subgraph "API Authentication"
            API_KEYS[API Keys]
            OAUTH2[OAuth 2.0]
            AZURE_AD[Azure AD]
        end
    end
    
    subgraph "Authorization"
        subgraph "Role-Based Access Control"
            ROLES[User Roles]
            PERMISSIONS[Permissions]
            POLICIES[Policies]
        end
        
        subgraph "Resource Access"
            KEY_VAULT_ACCESS[Key Vault Access]
            STORAGE_ACCESS[Storage Access]
            DATABASE_ACCESS[Database Access]
        end
    end
    
    MSAL_BROWSER --> JWT_TOKENS
    JWT_TOKENS --> REFRESH_TOKENS
    SERVICE_PRINCIPALS --> MANAGED_IDENTITIES
    MANAGED_IDENTITIES --> CERTIFICATES
    API_KEYS --> OAUTH2
    OAUTH2 --> AZURE_AD
    
    AZURE_AD --> ROLES
    ROLES --> PERMISSIONS
    PERMISSIONS --> POLICIES
    POLICIES --> KEY_VAULT_ACCESS
    POLICIES --> STORAGE_ACCESS
    POLICIES --> DATABASE_ACCESS
```

### 2. Data Security

```mermaid
graph TB
    subgraph "Data Protection"
        subgraph "Encryption at Rest"
            BLOB_ENCRYPTION[Blob Storage Encryption]
            SQL_ENCRYPTION[SQL Database Encryption]
            KEY_VAULT_ENCRYPTION[Key Vault Encryption]
        end
        
        subgraph "Encryption in Transit"
            TLS_13[TLS 1.3]
            HTTPS[HTTPS]
            SERVICE_BUS_ENCRYPTION[Service Bus Encryption]
        end
        
        subgraph "Key Management"
            KEY_ROTATION[Key Rotation]
            KEY_VAULT_KEYS[Key Vault Keys]
            MANAGED_KEYS[Azure Managed Keys]
        end
    end
    
    subgraph "Data Classification"
        subgraph "Sensitivity Levels"
            PUBLIC[Public Data]
            INTERNAL[Internal Data]
            CONFIDENTIAL[Confidential Data]
            RESTRICTED[Restricted Data]
        end
        
        subgraph "Data Handling"
            DATA_LOSS_PREVENTION[Data Loss Prevention]
            AUDIT_LOGGING[Audit Logging]
            COMPLIANCE[Compliance Monitoring]
        end
    end
    
    BLOB_ENCRYPTION --> KEY_VAULT_KEYS
    SQL_ENCRYPTION --> KEY_VAULT_KEYS
    KEY_VAULT_ENCRYPTION --> KEY_VAULT_KEYS
    TLS_13 --> HTTPS
    SERVICE_BUS_ENCRYPTION --> KEY_VAULT_KEYS
    KEY_ROTATION --> MANAGED_KEYS
    
    PUBLIC --> DATA_LOSS_PREVENTION
    INTERNAL --> DATA_LOSS_PREVENTION
    CONFIDENTIAL --> AUDIT_LOGGING
    RESTRICTED --> COMPLIANCE
```

## Monitoring & Observability

### 1. Application Monitoring

```mermaid
graph TB
    subgraph "Application Insights"
        subgraph "Telemetry Collection"
            REQUESTS[Request Telemetry]
            DEPENDENCIES[Dependency Telemetry]
            EXCEPTIONS[Exception Telemetry]
            METRICS[Custom Metrics]
        end
        
        subgraph "Performance Monitoring"
            RESPONSE_TIMES[Response Times]
            THROUGHPUT[Throughput]
            ERROR_RATES[Error Rates]
            AVAILABILITY[Availability]
        end
        
        subgraph "User Analytics"
            USER_BEHAVIOR[User Behavior]
            PAGE_VIEWS[Page Views]
            USER_FLOWS[User Flows]
            CONVERSION[Conversion Tracking]
        end
    end
    
    subgraph "Log Analytics"
        subgraph "Log Collection"
            APPLICATION_LOGS[Application Logs]
            SYSTEM_LOGS[System Logs]
            SECURITY_LOGS[Security Logs]
            AUDIT_LOGS[Audit Logs]
        end
        
        subgraph "Query & Analysis"
            KQL_QUERIES[KQL Queries]
            ALERTS[Alert Rules]
            DASHBOARDS[Dashboards]
            WORKBOOKS[Workbooks]
        end
    end
    
    REQUESTS --> RESPONSE_TIMES
    DEPENDENCIES --> THROUGHPUT
    EXCEPTIONS --> ERROR_RATES
    METRICS --> AVAILABILITY
    
    USER_BEHAVIOR --> PAGE_VIEWS
    PAGE_VIEWS --> USER_FLOWS
    USER_FLOWS --> CONVERSION
    
    APPLICATION_LOGS --> KQL_QUERIES
    SYSTEM_LOGS --> KQL_QUERIES
    SECURITY_LOGS --> KQL_QUERIES
    AUDIT_LOGS --> KQL_QUERIES
    
    KQL_QUERIES --> ALERTS
    KQL_QUERIES --> DASHBOARDS
    KQL_QUERIES --> WORKBOOKS
```

## Deployment Architecture

### 1. Environment Strategy

```mermaid
graph TB
    subgraph "Development Environment"
        DEV_FRONTEND[Frontend - Dev]
        DEV_DB_SERVICE[DB Service - Dev]
        DEV_SFDC_SERVICE[SFDC Service - Dev]
        DEV_SHARED[Shared Resources - Dev]
    end
    
    subgraph "Staging Environment"
        STAGE_FRONTEND[Frontend - Stage]
        STAGE_DB_SERVICE[DB Service - Stage]
        STAGE_SFDC_SERVICE[SFDC Service - Stage]
        STAGE_SHARED[Shared Resources - Stage]
    end
    
    subgraph "Production Environment"
        PROD_FRONTEND[Frontend - Prod]
        PROD_DB_SERVICE[DB Service - Prod]
        PROD_SFDC_SERVICE[SFDC Service - Prod]
        PROD_SHARED[Shared Resources - Prod]
    end
    
    subgraph "Deployment Flow"
        CODE_CHANGES[Code Changes]
        DEV_DEPLOY[Deploy to Dev]
        DEV_TEST[Test in Dev]
        STAGE_DEPLOY[Deploy to Stage]
        STAGE_TEST[Test in Stage]
        PROD_DEPLOY[Deploy to Prod]
        PROD_MONITOR[Monitor Prod]
    end
    
    CODE_CHANGES --> DEV_DEPLOY
    DEV_DEPLOY --> DEV_FRONTEND
    DEV_DEPLOY --> DEV_DB_SERVICE
    DEV_DEPLOY --> DEV_SFDC_SERVICE
    DEV_DEPLOY --> DEV_SHARED
    
    DEV_TEST --> STAGE_DEPLOY
    STAGE_DEPLOY --> STAGE_FRONTEND
    STAGE_DEPLOY --> STAGE_DB_SERVICE
    STAGE_DEPLOY --> STAGE_SFDC_SERVICE
    STAGE_DEPLOY --> STAGE_SHARED
    
    STAGE_TEST --> PROD_DEPLOY
    PROD_DEPLOY --> PROD_FRONTEND
    PROD_DEPLOY --> PROD_DB_SERVICE
    PROD_DEPLOY --> PROD_SFDC_SERVICE
    PROD_DEPLOY --> PROD_SHARED
    
    PROD_DEPLOY --> PROD_MONITOR
```

## Technology Stack Summary

### Frontend Technologies
- **React 18.2.0** - Main frontend framework
- **Material-UI 7.0.2** - UI component library
- **Ant Design 5.24.8** - Additional UI components
- **AG Grid 33.3.1** - Data grid component
- **Chart.js 4.4.1** - Charting library
- **React Router 6.20.1** - Client-side routing
- **MSAL Browser 4.11.0** - Azure AD authentication

### Backend Technologies
- **Python 3.12** - Backend runtime
- **Azure Functions** - Serverless compute
- **Flask/FastAPI** - Web framework (blueprints)
- **Azure Service Bus** - Message queuing
- **Azure Key Vault** - Secret management
- **Azure SQL Database** - Primary database
- **Azure Table Storage** - NoSQL storage
- **Redis Cache** - Caching layer

### Infrastructure Technologies
- **Azure API Management** - API gateway
- **Azure Application Insights** - Monitoring
- **Azure DevOps** - CI/CD pipeline
- **Azure Blob Storage** - File storage
- **Azure Active Directory** - Identity provider
- **Azure Load Balancer** - Traffic distribution

### Security Technologies
- **JWT Tokens** - Authentication tokens
- **OAuth 2.0** - Authorization protocol
- **TLS 1.3** - Transport security
- **Azure Key Vault** - Key management
- **Managed Identities** - Service authentication
- **Role-Based Access Control (RBAC)** - Authorization

This comprehensive architectural diagram provides a complete overview of the AtomSec platform's structure, components, data flows, and technology stack. The platform follows modern microservices architecture patterns with robust security, monitoring, and deployment strategies. 