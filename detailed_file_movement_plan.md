# Detailed File Movement Plan

**Created:** $(date)  
**Purpose:** Comprehensive plan for file movements during folder structure maintenance  
**Backup Branches:** `backup/before-folder-restructure`

## Overview

This document details the specific file movements required to organize the folder structure for both SFDC and DB services. The current structure is already well-organized in the `src/` directories, so the focus is on cleanup and test organization.

## SFDC Service (atomsec-func-sfdc) File Movements

### Current State Analysis
- **Good Structure:** `src/`, `docs/`, `config/`, `infrastructure/`, `scripts/`, `examples/`
- **Needs Organization:** Test files at root, utility scripts at root
- **Needs Cleanup:** Deprecated files, cache files, log files (already cleaned)

### Test Files to Move

#### From Root to tests/unit/api/
```bash
# API-related test files
mv test_account_management.py tests/unit/api/
mv test_api.py tests/unit/api/  # If exists
mv test_endpoints.py tests/unit/api/  # If exists
```

#### From Root to tests/unit/shared/
```bash
# Shared component test files
mv test_enhanced_error_handling.py tests/unit/shared/
mv test_queue_message_processor.py tests/unit/shared/
mv test_queue_processing.py tests/unit/shared/  # If exists
```

#### From Root to tests/integration/
```bash
# Integration test files
mv test_integration.py tests/integration/  # If exists
```

#### From Root to tests/unit/
```bash
# General unit test files
mv test_local_startup.py tests/unit/
```

### Utility Scripts to Move

#### From Root to scripts/
```bash
# Utility scripts currently at root
mv check_credentials.py scripts/
mv task_management.py scripts/
mv service_bus_processor.py scripts/
```

### Directory Structure After Movement
```
atomsec-func-sfdc/
├── src/                              # ✅ Already well-organized
├── tests/
│   ├── unit/
│   │   ├── api/
│   │   │   ├── test_account_management.py    # MOVED
│   │   │   ├── test_api.py                   # MOVED (if exists)
│   │   │   └── test_endpoints.py             # MOVED (if exists)
│   │   ├── shared/
│   │   │   ├── test_enhanced_error_handling.py # MOVED
│   │   │   ├── test_queue_message_processor.py # MOVED
│   │   │   └── test_queue_processing.py        # MOVED (if exists)
│   │   └── test_local_startup.py             # MOVED
│   └── integration/
│       └── test_integration.py               # MOVED (if exists)
├── scripts/
│   ├── check_credentials.py                 # MOVED
│   ├── task_management.py                   # MOVED
│   └── service_bus_processor.py             # MOVED
├── docs/                             # ✅ Already comprehensive
├── config/                           # ✅ Already well-organized
├── infrastructure/                   # ✅ Already well-organized
├── examples/                         # ✅ Already exists
└── [root files remain unchanged]
```

## DB Service (atomsec-func-db-r) File Movements

### Current State Analysis
- **Good Structure:** `src/`, `scripts/`, `tests/` (basic)
- **Needs Organization:** Test files at root, documentation at root, example files at root
- **Needs Cleanup:** Log files (already cleaned)

### Test Files to Move

#### From Root to tests/unit/shared/
```bash
# Enhanced component test files
mv test_enhanced_database_security.py tests/unit/shared/
mv test_enhanced_queue_manager.py tests/unit/shared/
mv test_enhanced_task_coordination_service.py tests/unit/shared/
mv test_enhanced_task_status_service.py tests/unit/shared/
mv test_execution_context_manager.py tests/unit/shared/
```

#### From Root to tests/integration/
```bash
# Integration test runner
mv run_integration_tests.py tests/integration/
```

### Documentation Files to Move

#### From Root to docs/
```bash
# Create docs directory if it doesn't exist
mkdir -p docs/

# Move documentation files
mv ENHANCED_DATABASE_SECURITY_IMPLEMENTATION.md docs/
mv QUEUE_BASED_PROCESSING_DEPLOYMENT_GUIDE.md docs/
mv API_CONSOLIDATION_SUMMARY.md docs/  # If exists
mv CONSOLIDATED_API_ENDPOINTS.md docs/  # If exists
mv MIGRATION_GUIDE.md docs/  # If exists
mv SFDC_PROXY_ARCHITECTURE.md docs/  # If exists
```

### Example Files to Move

#### From Root to examples/
```bash
# Create examples directory if it doesn't exist
mkdir -p examples/

# Move example files (if any exist at root)
mv example_*.py examples/  # If any exist
```

### Directory Structure After Movement
```
atomsec-func-db-r/
├── src/                              # ✅ Already well-organized
├── tests/
│   ├── unit/
│   │   └── shared/
│   │       ├── test_enhanced_database_security.py      # MOVED
│   │       ├── test_enhanced_queue_manager.py          # MOVED
│   │       ├── test_enhanced_task_coordination_service.py # MOVED
│   │       ├── test_enhanced_task_status_service.py    # MOVED
│   │       └── test_execution_context_manager.py       # MOVED
│   └── integration/
│       └── run_integration_tests.py                    # MOVED
├── docs/                             # NEW
│   ├── ENHANCED_DATABASE_SECURITY_IMPLEMENTATION.md   # MOVED
│   ├── QUEUE_BASED_PROCESSING_DEPLOYMENT_GUIDE.md     # MOVED
│   └── [other documentation files]                    # MOVED
├── examples/                         # NEW (if needed)
│   └── [example files]                                # MOVED
├── scripts/                          # ✅ Already exists
└── [root files remain unchanged]
```

## Import Statement Updates Required

### SFDC Service Import Updates

#### Test Files
```python
# In moved test files, update imports from:
from shared.config import get_config
# To:
from src.shared.config import get_config

# Or use relative imports:
from ...src.shared.config import get_config
```

#### Utility Scripts
```python
# In moved utility scripts, update imports from:
from shared.auth_utils import AuthService
# To:
from src.shared.auth_utils import AuthService
```

### DB Service Import Updates

#### Test Files
```python
# In moved test files, update imports from:
from shared.database_models import DatabaseModel
# To:
from src.shared.database_models import DatabaseModel
```

## Movement Commands Summary

### SFDC Service Commands
```bash
cd atomsec-func-sfdc

# Ensure directories exist
mkdir -p tests/unit/api tests/unit/shared tests/integration

# Move test files
mv test_account_management.py tests/unit/api/ 2>/dev/null || true
mv test_enhanced_error_handling.py tests/unit/shared/ 2>/dev/null || true
mv test_queue_message_processor.py tests/unit/shared/ 2>/dev/null || true
mv test_local_startup.py tests/unit/ 2>/dev/null || true

# Move utility scripts
mv check_credentials.py scripts/ 2>/dev/null || true
mv task_management.py scripts/ 2>/dev/null || true
mv service_bus_processor.py scripts/ 2>/dev/null || true
```

### DB Service Commands
```bash
cd atomsec-func-db-r

# Ensure directories exist
mkdir -p tests/unit/shared tests/integration docs examples

# Move test files
mv test_enhanced_database_security.py tests/unit/shared/ 2>/dev/null || true
mv test_enhanced_queue_manager.py tests/unit/shared/ 2>/dev/null || true
mv test_enhanced_task_coordination_service.py tests/unit/shared/ 2>/dev/null || true
mv test_enhanced_task_status_service.py tests/unit/shared/ 2>/dev/null || true
mv test_execution_context_manager.py tests/unit/shared/ 2>/dev/null || true
mv run_integration_tests.py tests/integration/ 2>/dev/null || true

# Move documentation
mv ENHANCED_DATABASE_SECURITY_IMPLEMENTATION.md docs/ 2>/dev/null || true
mv QUEUE_BASED_PROCESSING_DEPLOYMENT_GUIDE.md docs/ 2>/dev/null || true

# Move examples (if any)
mv example_*.py examples/ 2>/dev/null || true
```

## Validation Commands

### After File Movement
```bash
# SFDC Service
cd atomsec-func-sfdc
find tests/ -name "test_*.py" | head -10  # Verify test files moved
find scripts/ -name "*.py" | grep -E "(check_credentials|task_management|service_bus_processor)"

# DB Service
cd atomsec-func-db-r
find tests/ -name "test_*.py" | head -10  # Verify test files moved
find docs/ -name "*.md" | head -5        # Verify docs moved
```

### Test Discovery Validation
```bash
# Both services
python -m pytest --collect-only tests/
```

## Risk Assessment

### Low Risk Movements
- Documentation files (no imports to update)
- Example files (standalone)
- Utility scripts (minimal dependencies)

### Medium Risk Movements
- Test files (import statements need updates)
- Integration test runner (may have hardcoded paths)

### Mitigation Strategies
1. **Incremental Movement:** Move files in small batches
2. **Test After Each Batch:** Run tests after each movement
3. **Import Validation:** Check imports before and after
4. **Rollback Ready:** Keep backup branches available

## Success Criteria

### File Organization Success
- [ ] All test files in appropriate test directories
- [ ] All utility scripts in scripts directory
- [ ] All documentation in docs directory
- [ ] Clean root directories

### Functional Success
- [ ] All tests discoverable and runnable
- [ ] No broken imports
- [ ] Function apps start successfully
- [ ] API endpoints respond correctly

---

**Note:** Use the `2>/dev/null || true` pattern in movement commands to handle cases where files may not exist, preventing script failures.