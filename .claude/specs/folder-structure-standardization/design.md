# Design Document

## Overview

This design establishes a standardized, maintainable folder structure for both the SFDC service (atomsec-func-sfdc) and DB service (atomsec-func-db-r) based on Python best practices and modern project organization standards. The design addresses current organizational issues including scattered test files, inconsistent naming conventions, temporary file pollution, and unclear separation of concerns.

## Architecture

### Target Structure Standard

The design adopts the **flat layout** pattern (appropriate for Azure Functions) with consistent organization across both services:

```
service_root/
├── README.md
├── requirements.txt
├── host.json
├── local.settings.json.example
├── function_app.py
├── .gitignore
├── src/
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   └── *.py (endpoint modules)
│   ├── shared/
│   │   ├── __init__.py
│   │   └── *.py (utility modules)
│   ├── models/
│   │   ├── __init__.py
│   │   └── *.py (data models)
│   └── services/
│       ├── __init__.py
│       └── *.py (business logic)
├── tests/
│   ├── __init__.py
│   ├── unit/
│   │   ├── __init__.py
│   │   └── test_*.py
│   ├── integration/
│   │   ├── __init__.py
│   │   └── test_*.py
│   └── fixtures/
│       ├── __init__.py
│       └── *.py (test data)
├── config/
│   ├── *.json (environment configs)
│   └── *.yaml (policy configs)
├── docs/
│   ├── README.md
│   ├── api/
│   └── deployment/
├── scripts/
│   ├── deployment/
│   ├── migration/
│   └── utilities/
└── infrastructure/
    ├── bicep/
    └── pipelines/
```

### Service-Specific Adaptations

#### SFDC Service Structure
- Maintains Salesforce-specific components in `src/salesforce/`
- PMD components organized under `src/analysis/pmd/`
- Task processing logic in `src/services/task_processing/`

#### DB Service Structure  
- Database models centralized in `src/models/`
- Repository pattern components in `src/repositories/`
- Queue management in `src/services/queue/`

## Components and Interfaces

### Core Organization Principles

1. **Source Code Separation**: All application code moves to `src/` directory
2. **Test Organization**: Hierarchical test structure with clear separation of test types
3. **Configuration Management**: Centralized configuration in `config/` directory
4. **Documentation Structure**: Organized documentation in `docs/` with clear categorization
5. **Utility Scripts**: Deployment and utility scripts organized by purpose in `scripts/`

### File Naming Conventions

- **Python files**: `snake_case.py`
- **Configuration files**: `kebab-case.json`, `kebab-case.yaml`
- **Documentation files**: `SCREAMING_CASE.md` for important docs, `kebab-case.md` for regular docs
- **Test files**: `test_*.py` prefix pattern
- **Directory names**: `snake_case` for Python packages, `kebab-case` for non-Python folders

### Migration Strategy

#### Phase 1: Create New Structure
- Create standardized directory structure
- Establish .gitignore patterns for temporary files

#### Phase 2: Move Core Application Code
- Relocate API endpoints to `src/api/`
- Move shared utilities to `src/shared/`
- Organize business logic in `src/services/`
- Centralize data models in `src/models/`

#### Phase 3: Reorganize Tests
- Move all test files to `tests/` hierarchy
- Separate unit tests, integration tests, and fixtures
- Update test configuration files (pytest.ini, conftest.py)

#### Phase 4: Clean Configuration and Documentation
- Consolidate configuration files in `config/`
- Organize documentation in `docs/` structure
- Group utility scripts by purpose in `scripts/`

#### Phase 5: Remove Temporary and Legacy Files
- Remove .old, .disabled, .backup files
- Clean up Azurite and cache files
- Remove duplicate and temporary files
- Update .gitignore to prevent future pollution

## Data Models

### Directory Structure Model
```python
@dataclass
class DirectoryStructure:
    root_path: str
    directories: List[Directory]
    files: List[File]
    
@dataclass 
class Directory:
    name: str
    path: str
    purpose: str
    naming_convention: str
    
@dataclass
class File:
    name: str
    path: str
    category: str
    should_keep: bool
    target_location: str
```

### File Categorization
- **Source Code**: .py files containing application logic
- **Tests**: test_*.py files and test fixtures
- **Configuration**: .json, .yaml, .ini files for app configuration
- **Documentation**: .md files for project documentation
- **Infrastructure**: .bicep, .yml pipeline files
- **Temporary**: .log, .json cache files, __pycache__ directories
- **Legacy**: .old, .disabled, .backup files

## Error Handling

### File Conflict Resolution
- Duplicate file detection and consolidation strategy
- Backup creation before destructive operations
- Rollback capability for failed migrations

### Import Path Updates
- Systematic update of import statements to reflect new structure
- Validation of import correctness after restructuring
- Unit test execution to verify functionality

### Dependency Management
- Update requirements.txt organization and comments
- Verify all dependencies are still accessible after restructuring
- Update any hardcoded file paths in configuration

## Testing Strategy

### Test Structure Validation
- Verify all tests can be discovered after reorganization
- Update test configuration (pytest.ini, conftest.py) for new structure
- Ensure test fixtures and mocks are properly organized

### Functionality Testing
- Execute full test suite after each migration phase
- Validate that all imports resolve correctly
- Verify that Azure Functions can still locate and execute endpoints

### Integration Testing
- Test that both services maintain their API contracts
- Verify inter-service communication still functions
- Validate deployment pipeline compatibility with new structure

### Performance Testing
- Ensure restructuring doesn't impact function cold start times
- Validate that import paths don't introduce performance regressions
- Monitor deployment and execution metrics

### Automated Validation
- Script to validate directory structure compliance
- Automated checks for naming convention adherence
- Git hooks to prevent temporary file commits

## Implementation Considerations

### Backward Compatibility
- Gradual migration approach to minimize service disruption
- Maintain symlinks during transition period if needed
- Update deployment scripts to handle new structure

### Service Dependencies
- Both services must maintain identical top-level structure
- Shared utility patterns for common functionality
- Consistent naming and organization across services

### Azure Functions Constraints
- Maintain compatibility with Azure Functions runtime expectations
- Ensure function_app.py remains at root level
- Preserve host.json and other Azure-specific configuration files

### Development Workflow
- Update development documentation for new structure
- Modify IDE configurations and debugging setups
- Update local development scripts and tools