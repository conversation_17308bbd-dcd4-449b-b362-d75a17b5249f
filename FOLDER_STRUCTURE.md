# Folder Structure Documentation

This document describes the standardized folder structure for both the SFDC service (atomsec-func-sfdc) and DB service (atomsec-func-db-r).

## Design Principles

1. **Consistent Organization**: Both services follow similar patterns for easy navigation
2. **Clear Separation**: Different types of code are organized in logical directories
3. **Clean Root**: Minimize files at root level to reduce clutter
4. **Maintainable Structure**: Easy to understand and extend

## SFDC Service Structure (atomsec-func-sfdc)

```
atomsec-func-sfdc/
├── src/                          # Source code (already well-organized)
│   ├── api/                      # API endpoints and handlers
│   ├── shared/                   # Shared utilities and common code
│   ├── blueprints/               # Flask/FastAPI blueprint components
│   ├── task_processor/           # Task processing functionality
│   ├── pmd_components/           # PMD (Salesforce metadata) processing
│   ├── repositories/             # Data access layer
│   ├── services/                 # Business logic services
│   └── models/                   # Data models and schemas
├── tests/                        # Test files organized by type
│   ├── unit/                     # Unit tests mirroring src structure
│   │   ├── api/                  # API unit tests
│   │   ├── shared/               # Shared components unit tests
│   │   ├── blueprints/           # Blueprint unit tests
│   │   ├── task_processor/       # Task processor unit tests
│   │   └── pmd_components/       # PMD components unit tests
│   ├── integration/              # Integration tests
│   ├── performance/              # Performance and load tests
│   ├── security/                 # Security tests
│   └── architecture/             # Architecture compliance tests
├── docs/                         # Documentation (already comprehensive)
│   ├── api/                      # API documentation
│   ├── architecture/             # Architecture decisions and diagrams
│   ├── deployment/               # Deployment guides
│   └── operations/               # Operational procedures
├── config/                       # Configuration files (already well-organized)
├── infrastructure/               # Infrastructure as code (already well-organized)
├── scripts/                      # Utility and deployment scripts
├── examples/                     # Example code and usage samples
├── cache/                        # Temporary cache files (excluded from git)
├── logs/                         # Log files (excluded from git)
├── reports/                      # Generated reports (excluded from git)
├── function_app.py               # Main Azure Function entry point
├── host.json                     # Azure Functions configuration
├── requirements.txt              # Python dependencies
└── README.md                     # Service documentation
```

## DB Service Structure (atomsec-func-db-r)

```
atomsec-func-db-r/
├── src/                          # Source code (already well-organized)
│   ├── api/                      # API endpoints and handlers
│   ├── shared/                   # Shared utilities and common code
│   ├── repositories/             # Data access layer
│   ├── services/                 # Business logic services
│   └── models/                   # Data models and schemas
├── tests/                        # Test files organized by type
│   ├── unit/                     # Unit tests mirroring src structure
│   │   ├── api/                  # API unit tests
│   │   ├── shared/               # Shared components unit tests
│   │   └── repositories/         # Repository unit tests
│   └── integration/              # Integration tests
├── docs/                         # Documentation
├── scripts/                      # Utility and deployment scripts
├── config/                       # Configuration files
├── examples/                     # Example code and usage samples
├── cache/                        # Temporary cache files (excluded from git)
├── logs/                         # Log files (excluded from git)
├── function_app.py               # Main Azure Function entry point
├── host.json                     # Azure Functions configuration
├── requirements.txt              # Python dependencies
└── README.md                     # Service documentation
```

## Directory Purposes

### Source Code Directories
- **`src/api/`**: REST API endpoints, request handlers, and API-specific logic
- **`src/shared/`**: Utilities, helpers, and code shared across multiple modules
- **`src/repositories/`**: Data access layer, database interactions
- **`src/services/`**: Business logic and service layer components
- **`src/models/`**: Data models, schemas, and type definitions
- **`src/blueprints/`** (SFDC only): Flask/FastAPI blueprint organization
- **`src/task_processor/`** (SFDC only): Task queue and processing logic
- **`src/pmd_components/`** (SFDC only): Salesforce metadata processing

### Test Directories
- **`tests/unit/`**: Unit tests organized to mirror source structure
- **`tests/integration/`**: Integration tests for service interactions
- **`tests/performance/`** (SFDC only): Load and performance tests
- **`tests/security/`** (SFDC only): Security and penetration tests
- **`tests/architecture/`** (SFDC only): Architecture compliance tests

### Support Directories
- **`docs/`**: All documentation including API specs, architecture, deployment
- **`config/`**: Configuration files for different environments
- **`infrastructure/`**: Infrastructure as code (Bicep, ARM templates)
- **`scripts/`**: Utility scripts, deployment automation, maintenance tools
- **`examples/`**: Example code, usage samples, and demonstrations

### Temporary Directories (Excluded from Git)
- **`cache/`**: Temporary cache files, Azurite data, build artifacts
- **`logs/`**: Application logs, debug output, runtime logs
- **`reports/`** (SFDC only): Generated reports, analysis results

## File Naming Conventions

### Test Files
- Unit tests: `test_<module_name>.py`
- Integration tests: `test_<feature>_integration.py`
- Mock files: `mock_<service_name>.py`

### Configuration Files
- Environment-specific: `<environment>.json` (e.g., `production.json`)
- Common settings: `common.json`
- Feature flags: `feature_flags.json`

### Documentation Files
- API documentation: Use OpenAPI/Swagger format
- Architecture decisions: `adr/NNN-<decision-title>.md`
- Operational guides: `<topic>-guide.md`

## Best Practices

### Adding New Code
1. **API Endpoints**: Add to `src/api/` with corresponding tests in `tests/unit/api/`
2. **Shared Utilities**: Add to `src/shared/` with tests in `tests/unit/shared/`
3. **Business Logic**: Add to `src/services/` with appropriate unit tests
4. **Data Models**: Add to `src/models/` with validation tests

### Test Organization
1. **Mirror Structure**: Test directory structure should mirror source structure
2. **Isolation**: Unit tests should test individual components in isolation
3. **Integration**: Integration tests should test component interactions
4. **Mocking**: Use mocks for external dependencies in unit tests

### Documentation
1. **API Changes**: Update OpenAPI specifications
2. **Architecture**: Document significant decisions in ADR format
3. **Deployment**: Keep deployment guides current
4. **Examples**: Provide working examples for complex features

## Maintenance

### Regular Cleanup
- Remove old log files from `logs/` directory
- Clear cache files from `cache/` directory
- Archive old reports from `reports/` directory
- Remove deprecated test files and examples

### Structure Validation
- Ensure new files are placed in appropriate directories
- Verify test structure mirrors source structure
- Check that temporary files are properly excluded from git
- Validate that documentation is kept current

This structure provides a solid foundation for maintainable, scalable Azure Function services while ensuring consistency between the SFDC and DB services.