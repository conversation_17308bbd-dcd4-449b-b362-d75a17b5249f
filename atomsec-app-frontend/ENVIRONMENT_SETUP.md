# Environment Setup for AtomSec Frontend

## Environment Variables

Create a `.env` file in the root of the frontend directory with the following variables:

```bash
# API Base URL (DB Service - handles all API requests)
REACT_APP_API_BASE_URL=http://localhost:7072/v1

# Database Service URL
REACT_APP_DB_SERVICE_URL=http://localhost:7072/v1

# Azure AD Configuration (optional, for MSAL integration)
REACT_APP_AZURE_CLIENT_ID=your-azure-client-id
REACT_APP_AZURE_TENANT_ID=your-azure-tenant-id
REACT_APP_AZURE_REDIRECT_URI=http://localhost:3000

# Environment
REACT_APP_ENVIRONMENT=development
```

## Local Development Setup

1. **Start the Database Service** (Port 7072):
   ```bash
   cd ../atomsec-func-db
   func start --port 7072
   ```

2. **Start the Main Backend Service** (Port 7071):
   ```bash
   cd ../atomsec-func-sfdc
   func start --port 7071
   ```

3. **Start the Frontend** (Port 3000):
   ```bash
   cd atomsec-app-frontend
   npm install
   npm start
   ```

## Production Configuration

For production deployment, update the environment variables:

```bash
# Production API URLs
REACT_APP_API_BASE_URL=https://atomsec-func-sfdc.azurewebsites.net/api
REACT_APP_DB_SERVICE_URL=https://atomsec-func-db.azurewebsites.net/api/db

# Production Azure AD Configuration
REACT_APP_AZURE_CLIENT_ID=production-client-id
REACT_APP_AZURE_TENANT_ID=production-tenant-id
REACT_APP_AZURE_REDIRECT_URI=https://your-app-domain.com

# Environment
REACT_APP_ENVIRONMENT=production
```

## Verifying the Setup

1. Open the browser console when running the app
2. You should see logs indicating:
   - `API Base URL: http://localhost:7072`
   - `Database Service URL: http://localhost:7072/api/db`

3. Test database connectivity by:
   - Logging into the application
   - Navigating to the user profile page
   - The profile should load from the database service

## Troubleshooting

### Database Service Not Accessible
- Ensure the database service is running on port 7072
- Check for CORS errors in the browser console
- Verify the `REACT_APP_DB_SERVICE_URL` is set correctly

### Authentication Issues
- Ensure both services are using the same JWT configuration
- Check that tokens are being passed in Authorization headers
- Verify token refresh logic works for both services

### API Calls Failing
- Check the network tab in browser dev tools
- Look for 404 errors indicating missing endpoints
- Verify the API endpoints match between frontend and backend