build.properties
build.xml


*.project
*.sublime-project
*.sublime-workspace
*.log
*.sublime-settings
deploy/
debug/

.idea/illuminatedCloud.xml
.idea/workspace.xml
IlluminatedCloud/ideas/OfflineSymbolTable.zip

#ant file
/build.properties

# IntelliJ
.idea/
.project/
IlluminatedCloud/
resource-bundles/
*.iml

# Eclipse IDE
.metadata/

# NPM/Gulp stuff to ignore
*/node_modules/*
node_modules
out
*/out/*

# Local environment files
.env.local
.env.development
.env.development.local
local.settings.json

# Dependencies
/node_modules
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
*.py[cod]
.Python
.venv/
.AppleDouble
.LSOverride
mm.log

# Icon must end with two \r
Icon


# Thumbnails
._*

# Files that might appear on external disk
.Spotlight-V100
.Trashes

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk
dev
static
*.metadata*
projectFilesBackup/


.azurite
.github
.roo/mcp.json
package-lock.json
package-lock.json
package.json
