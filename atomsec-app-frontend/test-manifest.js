// Test script to verify manifest.json accessibility
const https = require('https');

const testUrl = 'https://app-atomsec-dev01.azurewebsites.net/manifest.json';

console.log('Testing manifest.json accessibility...');

https.get(testUrl, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response body:');
    console.log(data);
  });
}).on('error', (err) => {
  console.error('Error:', err.message);
}); 