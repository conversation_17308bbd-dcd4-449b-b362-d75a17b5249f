import React, { createContext, useContext, useState, useEffect } from 'react';
import { authEndpoints } from '../authConfig';
import { useNavigate } from 'react-router-dom';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    console.log('AuthContext state changed:', { isAuthenticated, user, loading });
  }, [isAuthenticated, user, loading]);

  useEffect(() => {
    // Check for tokens in URL (from Azure AD callback)
    const checkUrlForTokens = async () => {
      const params = new URLSearchParams(window.location.search);
      const accessToken = params.get('access_token');
      const refreshToken = params.get('refresh_token');
      const email = params.get('email');
      const name = params.get('name');
      const code = params.get('code');
      const state = params.get('state');

      console.log('Checking URL for tokens:', { code: !!code, state: !!state, accessToken: !!accessToken, email });

      // If we have an authorization code, exchange it for tokens
      if (code && state) {
        console.log('Authorization code found in URL, processing...');

        // Check if we're in production
        const isProduction = window.location.hostname !== 'localhost';

        if (isProduction) {
          console.log('Production environment: Ignoring authorization code - using platform auth');
          // In production, clean up URL and let platform auth handle everything
          const cleanUrl = window.location.pathname;
          window.history.replaceState({}, document.title, cleanUrl);
          return false; // Don't process the code, let platform auth handle it
        }

        // Development only: handle authorization code
        await handleAuthorizationCode(code, state);
        return true;
      }

      // If we already have tokens in the URL
      if (accessToken && email) {
        console.log('Direct tokens found in URL, setting user state...');
        
        // Clear logout flag since we found valid tokens
        localStorage.removeItem('logoutInProgress');
        
        // Store tokens in localStorage
        localStorage.setItem('accessToken', accessToken);
        if (refreshToken) localStorage.setItem('refreshToken', refreshToken);
        localStorage.setItem('userEmail', email);
        if (name) localStorage.setItem('userName', name);

        // Set user state
        setUser({
          accessToken,
          email,
          name,
        });
        setIsAuthenticated(true);

        // Clean up URL
        const cleanUrl = window.location.pathname;
        window.history.replaceState({}, document.title, cleanUrl);
        return true;
      }
      return false;
    };

    // Handle authorization code from Azure AD
    const handleAuthorizationCode = async (code, state) => {
      try {
        console.log('Received code from Azure AD, exchanging for tokens...');
        console.log('Code length:', code?.length);
        console.log('State:', state);

        // Clean up URL immediately to prevent multiple attempts with the same code
        const cleanUrl = window.location.pathname;
        window.history.replaceState({}, document.title, cleanUrl);

        // Check if we're in production
        const isProduction = window.location.hostname !== 'localhost';

        if (isProduction) {
          console.log('Production environment: Authorization code handling not needed - using platform auth');
          // In production, platform auth handles everything, so just return
          return;
        }

        console.log('Development environment: Exchanging code for tokens...');
        console.log('Callback URL:', authEndpoints.azure.callback);
        
        // Exchange authorization code for tokens using Azure AD
        const clientId = process.env.REACT_APP_AZURE_CLIENT_ID || '123d7a1b-7b24-4924-a73c-1fbcff016b12';
        const clientSecret = process.env.REACT_APP_AZURE_CLIENT_SECRET || '';
        const redirectUri = process.env.REACT_APP_REDIRECT_URI || 'http://localhost:3000';
        
        // For development, we'll use the backend to exchange the code
        // since we need a client secret which shouldn't be in the frontend
        const callbackUrl = `${authEndpoints.azure.callback}?code=${code}&state=${state}`;
        console.log('Calling backend callback URL:', callbackUrl);
        
        const response = await fetch(callbackUrl);

        console.log('Backend response status:', response.status);
        console.log('Backend response headers:', Object.fromEntries(response.headers.entries()));

        // Handle redirect responses (302)
        if (response.type === 'opaqueredirect') {
          console.log('Received redirect response from server, will follow redirect');
          return;
        }

        if (!response.ok) {
          console.error('Failed to exchange code for tokens');
          console.error('Response status:', response.status);
          console.error('Response status text:', response.statusText);
          
          try {
            const errorText = await response.text();
            console.error('Response body:', errorText);
          } catch (e) {
            console.error('Could not read response body:', e);
          }
          
          alert('Authentication failed. Please try again.');
          return;
        }

        const data = await response.json();
        console.log('Backend response data:', data);

        if (data.success && data.data) {
          console.log('✅ Token exchange successful, setting user state...');
          
          // Clear logout flag since we successfully authenticated
          localStorage.removeItem('logoutInProgress');
          
          // Store tokens in localStorage
          localStorage.setItem('accessToken', data.data.access_token);
          localStorage.setItem('refreshToken', data.data.refresh_token);
          localStorage.setItem('userEmail', data.data.email);
          if (data.data.name) localStorage.setItem('userName', data.data.name);

          // Set user state
          const userData = {
            accessToken: data.data.access_token,
            email: data.data.email,
            name: data.data.name,
          };
          
          setUser(userData);
          setIsAuthenticated(true);

          console.log('✅ User state set:', { user: userData, isAuthenticated: true });

          // Clear auth in progress flag
          localStorage.removeItem('authInProgress');

          // Redirect to tools page or saved redirect path
          const redirectPath = localStorage.getItem('loginRedirectPath') || '/tools';
          localStorage.removeItem('loginRedirectPath');
          console.log('Redirecting to:', redirectPath);
          navigate(redirectPath);
        } else {
          console.error('Invalid response format:', data);
          alert('Authentication failed. Invalid response format.');
        }
      } catch (error) {
        console.error('Error handling authorization code:', error);
        // Clear auth in progress flag on error
        localStorage.removeItem('authInProgress');
        // Clear logout flag on error to prevent it from getting stuck
        localStorage.removeItem('logoutInProgress');
        alert('Authentication failed. Please try again.');
      }
    };

    const fetchUser = async () => {
      console.log('=== AuthContext fetchUser started ===');
      console.log('Current URL:', window.location.href);
      console.log('Current timestamp:', new Date().toISOString());

      setLoading(true);

      // Check if we're in production and handle platform auth
      const isProduction = window.location.hostname !== 'localhost';
      console.log('Environment detection:', { isProduction, hostname: window.location.hostname });

      if (isProduction) {
        // In production, check platform authentication first
        console.log('Production environment: Checking platform authentication...');
        try {
          const response = await fetch('/.auth/me');
          console.log('Platform auth response status:', response.status);

          if (response.ok) {
            const authData = await response.json();
            console.log('Platform auth data received:', JSON.stringify(authData, null, 2));

            if (authData && authData.length > 0) {
              const userInfo = authData[0];
              console.log('User info from platform auth:', JSON.stringify(userInfo, null, 2));

              const email = userInfo.user_id || userInfo.user_claims?.find(c => c.typ === 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress')?.val;
              const name = userInfo.user_claims?.find(c => c.typ === 'name')?.val;

              console.log('Extracted user data:', { email, name });

              if (email) {
                const platformToken = userInfo.access_token || 'platform-authenticated';

                console.log('✅ Platform authentication successful for:', email);
                console.log('Setting user as authenticated via platform auth');

                // Store platform auth data
                localStorage.setItem('accessToken', platformToken);
                localStorage.setItem('userEmail', email);
                localStorage.setItem('userName', name || '');

                const userData = {
                  accessToken: platformToken,
                  email,
                  name: name || '',
                  platformAuth: true
                };

                setUser(userData);
                setIsAuthenticated(true);
                setLoading(false);

                console.log('✅ AuthContext state updated:', { isAuthenticated: true, user: userData });
                console.log('=== AuthContext fetchUser completed successfully ===');

                // IMPORTANT: Return here to avoid further token validation
                // In production, platform auth is sufficient
                return;
              } else {
                console.log('❌ No email found in platform auth data');
              }
            } else {
              console.log('❌ Platform auth data is empty or invalid format');
            }
          } else {
            console.log('❌ Platform auth response not OK:', response.status, response.statusText);
          }
        } catch (error) {
          console.error('❌ Platform auth check failed:', error);
        }

        // If platform auth failed in production, user is not authenticated
        console.log('❌ Platform authentication failed - setting user as not authenticated');
        setUser(null);
        setIsAuthenticated(false);
        setLoading(false);
        console.log('=== AuthContext fetchUser completed with failure ===');
        return;
      }

      // DEVELOPMENT ONLY: Check URL for tokens and validate with DB service
      console.log('Development environment: Using DB service authentication');

      // Check if logout is in progress
      const logoutInProgress = localStorage.getItem('logoutInProgress');
      if (logoutInProgress === 'true') {
        console.log('Logout in progress - skipping authentication check');
        setUser(null);
        setIsAuthenticated(false);
        setLoading(false);
        return;
      }

      // Then check URL for tokens (from custom redirect)
      const urlTokensFound = await checkUrlForTokens();
      if (urlTokensFound) {
        // Clear logout flag since we found valid tokens
        localStorage.removeItem('logoutInProgress');
        setLoading(false);
        return;
      }

      // Finally check localStorage for tokens
      const accessToken = localStorage.getItem('accessToken');
      let userEmail = localStorage.getItem('userEmail');
      let userName = localStorage.getItem('userName');

      if (accessToken && userEmail) {
        // Check if we're in development mode
        const isDevelopment = window.location.hostname === 'localhost';

        // Clear any logout flags since we have valid tokens
        localStorage.removeItem('logoutInProgress');

        if (isDevelopment) {
          // In development, trust localStorage and skip token validation to avoid logout on refresh
          console.log('Development mode: Restoring session from localStorage without token validation');

          // In development, try to get real user data from backend
          console.log('Development mode: Fetching real user data from backend');
          try {
            const res = await fetch(authEndpoints.azure.me, {
              headers: {
                'Authorization': `Bearer ${accessToken}`
              }
            });

            if (res.ok) {
              const data = await res.json();
              console.log('Backend response:', data); // Debug log
              if (data.success && data.data) {
                console.log('Got real user data from backend:', data.data);

                // Store real user information
                const realUserEmail = data.data.email || userEmail;
                const realUserId = data.data.user_id || 'dev-fallback';
                const realUserName = data.data.name || userName || 'Dev User';

                localStorage.setItem('userEmail', realUserEmail);
                localStorage.setItem('userId', realUserId);
                localStorage.setItem('userName', realUserName);
                localStorage.setItem('userRoles', JSON.stringify(data.data.roles || ['user']));
                localStorage.setItem('isAdmin', String(data.data.isAdmin || false));

                userEmail = realUserEmail;
                userName = realUserName;

                console.log(`Development mode: Using real user ID ${realUserId} for ${realUserEmail}`);
              } else {
                console.log('Backend returned invalid user data, clearing auth state');
                // Clear auth state if backend call fails
                localStorage.removeItem('accessToken');
                localStorage.removeItem('refreshToken');
                localStorage.removeItem('userEmail');
                localStorage.removeItem('userName');
                setUser(null);
                setIsAuthenticated(false);
                setLoading(false);
                return;
              }
            }
          } catch (error) {
            console.log('Error fetching user data from backend, clearing auth state:', error);
            // Clear auth state if backend call fails
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('userEmail');
            localStorage.removeItem('userName');
            setUser(null);
            setIsAuthenticated(false);
            setLoading(false);
            return;
          }

          setUser({
            accessToken,
            email: userEmail,
            name: userName || '',
            roles: JSON.parse(localStorage.getItem('userRoles') || '[]'),
            isAdmin: localStorage.getItem('isAdmin') === 'true'
          });
          setIsAuthenticated(true);
          setLoading(false);
          console.log('✅ Development mode: User authenticated successfully');
          console.log('=== AuthContext fetchUser completed successfully ===');
          return;

        } else {
          // In production, validate token with backend
          try {
            const res = await fetch(authEndpoints.azure.me, {
              headers: {
                'Authorization': `Bearer ${accessToken}`
              }
            });

            if (res.ok) {
              const data = await res.json();
              if (data.success && data.data && data.data.email) {
                console.log('SECURITY: Token validation successful for user:', data.data.email);
                setUser({
                  accessToken: data.data.accessToken || accessToken,
                  email: data.data.email || userEmail,
                  name: data.data.name || userName || '',
                  roles: data.data.roles || [],
                  isAdmin: data.data.isAdmin || false
                });
                setIsAuthenticated(true);
                setLoading(false);
                console.log('✅ Production mode: User authenticated successfully');
                console.log('=== AuthContext fetchUser completed successfully ===');
                return;

              } else {
                // Token invalid or missing user data, clear auth state
                console.warn('Token validation failed - invalid response data:', data);
                localStorage.removeItem('accessToken');
                localStorage.removeItem('refreshToken');
                localStorage.removeItem('userEmail');
                localStorage.removeItem('userName');
                setUser(null);
                setIsAuthenticated(false);
              }
            } else {
              // If token validation fails, clear auth state
              console.warn('Token validation failed with status:', res.status);
              localStorage.removeItem('accessToken');
              localStorage.removeItem('refreshToken');
              localStorage.removeItem('userEmail');
              localStorage.removeItem('userName');
              setUser(null);
              setIsAuthenticated(false);
            }
          } catch (e) {
            console.error('Error validating token:', e);
            // Clear authentication state on any validation error
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('userEmail');
            localStorage.removeItem('userName');
            setUser(null);
            setIsAuthenticated(false);
          }
        }
      } else if (userEmail && !accessToken) {
        // We have user info but no access token - this is an invalid state
        console.log('Found user info but no access token, clearing localStorage');
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('userEmail');
        localStorage.removeItem('userName');
        setUser(null);
        setIsAuthenticated(false);
      } else {
        console.log('No valid tokens found in localStorage');
        setUser(null);
        setIsAuthenticated(false);
      }

      setLoading(false);
      console.log('=== AuthContext fetchUser completed ===');
    };

    fetchUser();
  }, []);



  const login = (redirectUri = '/') => {
    console.log('Login called with redirectUri:', redirectUri);

    // Set auth in progress flag
    localStorage.setItem('authInProgress', 'true');

    // Save the redirect path for after login
    localStorage.setItem('loginRedirectPath', redirectUri);

    // Always use backend login endpoint for both dev and prod
    window.location.href = authEndpoints.azure.login;
  };

  const logout = () => {
    console.log('Logout called - clearing authentication state');
    
    // Set a flag to prevent automatic re-authentication
    localStorage.setItem('logoutInProgress', 'true');
    
    // Clear all authentication-related local storage
    const authKeys = [
      'accessToken',
      'refreshToken', 
      'userEmail',
      'userName',
      'authInProgress',
      'loginRedirectPath',
      'userId',
      'userRoles',
      'isAdmin'
    ];
    
    authKeys.forEach(key => {
      localStorage.removeItem(key);
    });

    // Update state
    setUser(null);
    setIsAuthenticated(false);

    // Clear the logout flag after a short delay
    setTimeout(() => {
      localStorage.removeItem('logoutInProgress');
    }, 2000); // Increased delay to ensure logout completes

    console.log('Logout completed - redirecting to home');
    
    // Redirect to home
    window.location.href = '/';
  };

  const getAccessToken = async () => {
    // First check if we have a token in the user state
    if (user?.accessToken) {
      return user.accessToken;
    }

    // Then check localStorage
    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      return accessToken;
    }

    throw new Error('No access token available');
  };

  const contextValue = {
    isAuthenticated,
    setIsAuthenticated,
    user,
    setUser,
    login,
    logout,
    getAccessToken,
    loading
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
