# Frontend API Migration to Database Service

This document describes the changes made to the frontend API layer to use the new dedicated database service for all database operations.

## Overview

The frontend has been updated to use a new microservice architecture where all database operations are handled by a dedicated database service (`atomsec-func-db`) running on port 7072.

## Changes Made

### 1. Configuration Updates (`config.js`)

- Added `DB_SERVICE_URL` configuration that defaults to `http://localhost:7072/api/db`
- Updated account management endpoints to use the database service
- Updated user profile endpoints to use the database service
- Added new `dbService` endpoints section for direct database service access

### 2. API Client Updates (`api.js`)

#### New Database Service Axios Instance

A separate axios instance (`dbApi`) has been created specifically for database service calls:

```javascript
const dbApi = axios.create({
  baseURL: API_CONFIG.dbServiceURL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'
  },
  timeout: 30000
});
```

#### Updated Functions

The following functions now use the database service:

1. **User Profile Functions**
   - `fetchUserProfile()` - Now fetches user by email from database service
   - `updateUserProfile()` - First fetches user ID, then updates via database service

2. **User Management Functions**
   - `fetchUsers()` - Uses database service with account_id filter
   - `createUser()` - Creates user via database service
   - `updateUser()` - Updates user via database service

3. **Account Management Functions** (Placeholders - need backend implementation)
   - `fetchAccounts()` - Will use organizations endpoint
   - `createAccount()` - Will create organization
   - `updateAccount()` - Will update organization

4. **Role Management Functions** (Temporary implementation)
   - `fetchRoles()` - Returns hardcoded roles until database service implements this
   - `assignUserRole()` - Returns simulated response until implemented

## Environment Configuration

To use a different database service URL in production, set the environment variable:

```bash
REACT_APP_DB_SERVICE_URL=https://your-db-service.azurewebsites.net/api/db
```

## Migration Status

### ✅ Completed
- User profile fetch and update
- User CRUD operations
- Configuration setup
- Axios instance setup with interceptors

### ⏳ Pending Backend Implementation
- Organization/Account CRUD operations
- Role management endpoints
- User role assignment
- Integration with existing authentication flow

### 🔄 Future Improvements
- Implement caching for frequently accessed data
- Add retry logic for failed requests
- Implement batch operations for better performance
- Add request/response transformation layer

## Testing

To test the database service integration:

1. Start the database service:
   ```bash
   cd atomsec-func-db
   func start --port 7072
   ```

2. Start the main backend service:
   ```bash
   cd atomsec-func-sfdc
   func start --port 7071
   ```

3. Start the frontend:
   ```bash
   cd atomsec-app-frontend
   npm start
   ```

4. Test user profile operations:
   - Login to the application
   - Navigate to user profile
   - Update profile information
   - Verify changes are persisted

## Troubleshooting

### CORS Issues
If you encounter CORS errors, ensure both backend services have proper CORS configuration:
- Main service: Check `host.json` and CORS middleware
- Database service: Check `host.json` CORS settings

### Authentication Issues
The database service uses the same JWT tokens as the main service. Ensure:
- Tokens are properly passed in Authorization headers
- Token refresh logic works for both services
- Both services use the same JWT secret/configuration

### Network Errors
- Verify database service is running on port 7072
- Check firewall/network settings
- Ensure environment variables are set correctly 