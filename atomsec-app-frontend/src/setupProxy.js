const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // All API requests now go through the DB microservice (port 7072)
  // The DB service will proxy SFDC-related requests to the SFDC service internally
  // This centralizes all frontend communication through a single service

  // Proxy all /v1 requests to the DB microservice (port 7072)
  // This includes authentication, integrations, database operations, and SFDC proxy endpoints
  app.use(
    '/v1',
    createProxyMiddleware({
      target: 'http://localhost:7072',
      changeOrigin: true,
      logLevel: 'debug'
    })
  );

  // Legacy /api proxy for any remaining endpoints (should be minimal)
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:7072',
      changeOrigin: true,
      logLevel: 'debug'
    })
  );
};
