/**
 * Application configuration
 *
 * This file contains configuration settings for the application.
 * Supports both local development (direct function app calls) and production (Azure APIM).
 */

// Configuration for AtomSec Frontend Application
// Supports both local development and Azure APIM based on environment

// Determine if we're running in production or development
// For production builds served locally, we should still use local backend
const isProduction = window.location.hostname !== 'localhost';

const API_CONFIG = {
  // Base URL - Supports both local and APIM
  // For local development: use direct function app calls
  // For production: use Azure APIM
  baseURL: isProduction 
    ? (process.env.REACT_APP_APIM_BASE_URL || 'https://apim-atomsec-dev.azure-api.net')
    : (process.env.REACT_APP_DB_SERVICE_URL || 'http://localhost:7072'),

  // API versioning (only used for APIM)
  apiVersion: process.env.REACT_APP_API_VERSION || 'v1',

  // APIM subscription key for authentication (only used in production)
  apimSubscriptionKey: process.env.REACT_APP_APIM_SUBSCRIPTION_KEY || '',

  // All endpoints now go through appropriate service based on environment
  endpoints: {
    // Authentication
    auth: {
      login: '/auth/login',
      azureLogin: '/auth/azure/login',
      azureCallback: '/auth/azure/callback',
      azureMe: '/auth/azure/me',
      verifyLogin: '/users/login/verify'
    },

    // User and account management
    users: '/users',
    userProfile: '/user/profile',
    accounts: '/accounts',

    // Integration management
    integrations: '/integrations',
    integrationTestConnection: '/integration/test-connection',
    integrationConnect: '/integration/connect',
    integrationScan: '/integration/scan',

    // Security and scanning
    security: {
      healthChecks: '/security/health-checks',
      policiesResult: '/security/policies-result',
      profileAssignmentCounts: '/security/profile-assignment-counts'
    },

    // Task management
    tasks: '/tasks',

    // System endpoints
    info: '/info',
    health: '/health'
  },

  // Authentication configuration
  auth: {
    clientId: process.env.REACT_APP_AZURE_CLIENT_ID,
    authority: process.env.REACT_APP_AUTHORITY || `https://login.microsoftonline.com/${process.env.REACT_APP_AZURE_TENANT_ID}`,
    redirectUri: process.env.REACT_APP_REDIRECT_URI,
    scopes: ['User.Read', 'api://your-api-id/access_as_user']
  },

  // Feature flags
  features: {
    enableServiceBus: process.env.REACT_APP_ENABLE_SERVICE_BUS === 'true',
    enableKeyVault: process.env.REACT_APP_ENABLE_KEY_VAULT === 'true',
    enableRealTimeUpdates: process.env.REACT_APP_ENABLE_REAL_TIME === 'true',
    enableAdvancedSecurity: process.env.REACT_APP_ENABLE_ADVANCED_SECURITY === 'true'
  },

  // Environment detection
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test',

  // Timeout configurations
  timeouts: {
    request: 60000, // 60 seconds (increased for integration operations)
    upload: 300000, // 5 minutes
    download: 300000, // 5 minutes
    websocket: 30000 // 30 seconds
  },

  // Retry configurations
  retry: {
    maxAttempts: 3,
    delayMs: 1000,
    backoffMultiplier: 2
  },

  // Pagination defaults
  pagination: {
    defaultPageSize: 20,
    maxPageSize: 100,
    pageSizeOptions: [10, 20, 50, 100]
  },

  // Cache configurations
  cache: {
    userProfile: 300000, // 5 minutes
    integrations: 600000, // 10 minutes
    securityData: 300000, // 5 minutes
    profiles: 900000 // 15 minutes
  },

  // Logging configuration
  logging: {
    level: process.env.REACT_APP_LOG_LEVEL || 'info',
    enableConsole: process.env.REACT_APP_ENABLE_CONSOLE_LOGS === 'true',
    enableRemote: process.env.REACT_APP_ENABLE_REMOTE_LOGS === 'true',
    remoteEndpoint: process.env.REACT_APP_LOG_ENDPOINT || '/api/logs'
  },

  // Error handling
  errorHandling: {
    showUserFriendlyErrors: true,
    logErrorsToConsole: true,
    reportErrorsToService: process.env.REACT_APP_REPORT_ERRORS === 'true',
    maxErrorRetries: 3
  },

  // Security settings
  security: {
    enableCSP: process.env.REACT_APP_ENABLE_CSP === 'true',
    enableHSTS: process.env.REACT_APP_ENABLE_HSTS === 'true',
    sessionTimeout: 3600000, // 1 hour
    refreshTokenInterval: 300000 // 5 minutes
  },

  // Performance monitoring
  performance: {
    enableMetrics: process.env.REACT_APP_ENABLE_METRICS === 'true',
    metricsEndpoint: process.env.REACT_APP_METRICS_ENDPOINT || '/api/metrics',
    enableProfiling: process.env.REACT_APP_ENABLE_PROFILING === 'true'
  },

  // Service Bus configuration (for real-time updates)
  serviceBus: {
    connectionString: process.env.REACT_APP_SERVICE_BUS_CONNECTION_STRING,
    topicName: process.env.REACT_APP_SERVICE_BUS_TOPIC || 'atomsec-events',
    subscriptionName: process.env.REACT_APP_SERVICE_BUS_SUBSCRIPTION || 'frontend-subscription'
  },

  // WebSocket configuration
  websocket: {
    enabled: process.env.REACT_APP_ENABLE_WEBSOCKET === 'true',
    url: process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:7071/ws',
    reconnectInterval: 5000,
    maxReconnectAttempts: 10
  },

  // File upload configuration
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['.csv', '.xlsx', '.json', '.xml'],
    chunkSize: 1024 * 1024, // 1MB chunks
    concurrentUploads: 3
  },

  // Export configuration
  export: {
    maxRecords: 10000,
    formats: ['csv', 'xlsx', 'json'],
    defaultFormat: 'csv'
  },

  // Notification settings
  notifications: {
    enablePush: process.env.REACT_APP_ENABLE_PUSH_NOTIFICATIONS === 'true',
    enableEmail: process.env.REACT_APP_ENABLE_EMAIL_NOTIFICATIONS === 'true',
    enableInApp: true,
    defaultDuration: 5000 // 5 seconds
  },

  // Theme and UI configuration
  ui: {
    theme: process.env.REACT_APP_THEME || 'light',
    language: process.env.REACT_APP_LANGUAGE || 'en',
    dateFormat: process.env.REACT_APP_DATE_FORMAT || 'MM/DD/YYYY',
    timeFormat: process.env.REACT_APP_TIME_FORMAT || 'HH:mm:ss',
    timezone: process.env.REACT_APP_TIMEZONE || 'UTC'
  },

  // Development tools
  devTools: {
    enableReduxDevTools: process.env.REACT_APP_ENABLE_REDUX_DEVTOOLS === 'true',
    enableReactDevTools: process.env.REACT_APP_ENABLE_REACT_DEVTOOLS === 'true',
    enableApiLogging: process.env.REACT_APP_ENABLE_API_LOGGING === 'true',
    enablePerformanceMonitoring: process.env.REACT_APP_ENABLE_PERFORMANCE_MONITORING === 'true'
  }
};

// Helper functions for configuration
export const getServiceURL = (service, endpoint = '', version = null) => {
  // All requests go through appropriate service based on environment
  const baseURL = API_CONFIG.baseURL;
  const apiVersion = version || API_CONFIG.apiVersion;

  // Build the full URL based on environment
  if (isProduction) {
    // Production: Use APIM with version and db path
    let fullURL = `${baseURL}/db/${apiVersion}`;
    if (endpoint) {
      const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
      fullURL = `${fullURL}/${cleanEndpoint}`;
    }
    return fullURL;
  } else {
    // Development: Use direct function app calls with /api/db/ prefix
    let fullURL = `${baseURL}/api/db`;
    if (endpoint) {
      const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
      fullURL = `${fullURL}/${cleanEndpoint}`;
    }
    return fullURL;
  }
};

export const getEndpoint = (service, endpointName, version = null) => {
  const endpoint = API_CONFIG.endpoints[service]?.[endpointName];
  if (!endpoint) {
    throw new Error(`Unknown endpoint: ${endpointName} for service: ${service}`);
  }

  const baseURL = API_CONFIG.baseURL;

  // Build the full URL based on environment
  if (isProduction) {
    const apiVersion = version || API_CONFIG.apiVersion;
    return `${baseURL}/db/${apiVersion}${endpoint}`;
  } else {
    return `${baseURL}/api/db${endpoint}`;
  }
};

export const isFeatureEnabled = (featureName) => {
  return API_CONFIG.features[featureName] === true;
};

export const getTimeout = (timeoutType) => {
  return API_CONFIG.timeouts[timeoutType] || API_CONFIG.timeouts.request;
};

export const getCacheDuration = (cacheType) => {
  return API_CONFIG.cache[cacheType] || 300000; // Default 5 minutes
};

// Environment-specific configurations
export const getEnvironmentConfig = () => {
  if (API_CONFIG.isDevelopment) {
    return {
      ...API_CONFIG,
      // Development-specific overrides
      logging: {
        ...API_CONFIG.logging,
        level: 'debug',
        enableConsole: true
      },
      devTools: {
        ...API_CONFIG.devTools,
        enableReduxDevTools: true,
        enableReactDevTools: true,
        enableApiLogging: true
      }
    };
  }
  
  if (API_CONFIG.isProduction) {
    return {
      ...API_CONFIG,
      // Production-specific overrides
      logging: {
        ...API_CONFIG.logging,
        level: 'warn',
        enableConsole: false
      },
      security: {
        ...API_CONFIG.security,
        enableCSP: true,
        enableHSTS: true
      }
    };
  }
  
  return API_CONFIG;
};

export default API_CONFIG;

// Named exports for backward compatibility
export { API_CONFIG };
