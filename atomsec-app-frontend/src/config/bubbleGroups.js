// Bubble chart configuration for permission groupings
export const bubbleGroups = {
  'Administrative': {
    color: '#FF6B6B',
    permissions: [
      'Modify All Data',
      'View All Data',
      'Manage Users',
      'Reset Passwords',
      'Manage Roles'
    ]
  },
  'Data Access': {
    color: '#4ECDC4',
    permissions: [
      'Data Export',
      'View Encrypted Data',
      'Edit Readonly Fields'
    ]
  },
  'Security': {
    color: '#45B7D1',
    permissions: [
      'Manage Encryption Keys',
      'Manage Sharing',
      'View Setup'
    ]
  },
  'Standard': {
    color: '#51D59C',
    permissions: []
  },
  'Custom': {
    color: '#FFD54F',
    permissions: []
  }
};

// Function to determine group based on permissions
export const getPermissionGroup = (permissions) => {
  const adminPerms = bubbleGroups['Administrative'].permissions;
  const dataPerms = bubbleGroups['Data Access'].permissions;
  const securityPerms = bubbleGroups['Security'].permissions;

  const hasAdmin = adminPerms.some(perm => permissions.includes(perm));
  const hasData = dataPerms.some(perm => permissions.includes(perm));
  const hasSecurity = securityPerms.some(perm => permissions.includes(perm));

  if (hasAdmin) return 'Administrative';
  if (hasData) return 'Data Access';
  if (hasSecurity) return 'Security';
  
  return 'Standard';
};

// Function to get color for a group
export const getGroupColor = (group) => {
  return bubbleGroups[group]?.color || '#51D59C';
};
