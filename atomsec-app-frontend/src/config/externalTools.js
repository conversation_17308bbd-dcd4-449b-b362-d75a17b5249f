/**
 * Configuration for external security tools
 * These URLs can be overridden using environment variables
 */

const isProduction = window.location.hostname !== 'localhost';

export const externalToolsConfig = {
  'atomseciq': {
    name: 'AtomSecIQ',
    description: 'Unified security intelligence and analytics dashboard',
    development: {
      url: 'http://dashboards-prod-tn01.eastus.cloudapp.azure.com:5601/',
      authMethod: 'token',
      features: ['Security Analytics', 'Dashboards', 'Reporting']
    },
    production: {
      url: 'http://dashboards-prod-tn01.eastus.cloudapp.azure.com:5601/',
      authMethod: 'token',
      features: ['Security Analytics', 'Dashboards', 'Reporting']
    }
  },
  'opencti': {
    name: 'OpenCTI',
    description: 'Open Cyber Threat Intelligence platform',
    development: {
      url: 'http://opencti-prod-tn01.eastus.cloudapp.azure.com/',
      authMethod: 'token',
      features: ['Threat Intelligence', 'Knowledge Base', 'Incident Management']
    },
    production: {
      url: 'http://opencti-prod-tn01.eastus.cloudapp.azure.com/',
      authMethod: 'token',
      features: ['Threat Intelligence', 'Knowledge Base', 'Incident Management']
    }
  },
  'siem-xdr': {
    name: 'SIEM/XDR',
    description: 'Security Information and Event Management / Extended Detection and Response',
    development: {
      url: 'http://xdr-prod-tn01.eastus.cloudapp.azure.com/',
      authMethod: 'token',
      features: ['Event Monitoring', 'Detection & Response', 'Log Management']
    },
    production: {
      url: 'http://xdr-prod-tn01.eastus.cloudapp.azure.com/',
      authMethod: 'token',
      features: ['Event Monitoring', 'Detection & Response', 'Log Management']
    }
  }
};

/**
 * Get configuration for a specific tool
 * @param {string} toolId - Tool identifier
 * @returns {Object} Tool configuration for current environment
 */
export const getToolConfig = (toolId) => {
  const tool = externalToolsConfig[toolId];
  if (!tool) {
    throw new Error(`Unknown tool: ${toolId}`);
  }
  
  return isProduction ? tool.production : tool.development;
};

/**
 * Get all available external tools
 * @returns {Array} Array of tool configurations
 */
export const getAllExternalTools = () => {
  return Object.entries(externalToolsConfig).map(([id, config]) => ({
    id,
    name: config.name,
    description: config.description,
    ...getToolConfig(id)
  }));
};

/**
 * Validate external tool URLs
 * @returns {Object} Validation results
 */
export const validateExternalToolUrls = () => {
  const results = {};
  
  Object.entries(externalToolsConfig).forEach(([toolId, config]) => {
    const envConfig = isProduction ? config.production : config.development;
    
    try {
      new URL(envConfig.url);
      results[toolId] = { valid: true, url: envConfig.url };
    } catch (error) {
      results[toolId] = { valid: false, error: error.message, url: envConfig.url };
    }
  });
  
  return results;
}; 