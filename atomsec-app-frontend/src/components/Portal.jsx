import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

const Portal = ({ children }) => {
  const [portalRoot, setPortalRoot] = useState(null);

  useEffect(() => {
    // Check if portal-root exists, if not create it
    let element = document.getElementById('portal-root');
    const isNewElement = !element;
    
    if (isNewElement) {
      element = document.createElement('div');
      element.id = 'portal-root';
      document.body.appendChild(element);
    }
    
    setPortalRoot(element);
    
    // Cleanup function to remove the portal-root if we created it
    return () => {
      if (isNewElement && element.parentNode) {
        element.parentNode.removeChild(element);
      }
    };
  }, []);

  // Don't render until we have a portal root
  if (!portalRoot) return null;
  
  // Use createPortal to render children into the portal root
  return createPortal(children, portalRoot);
};

export default Portal;
