import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  Security,
  BugReport,
  Shield,
  Warning,
  CheckCircle,
  Error,
  Info
} from '@mui/icons-material';
import './SecurityAssessment.css';

const SecurityAssessment = () => {
  const [targetUrl, setTargetUrl] = useState('');
  const [authorizationToken, setAuthorizationToken] = useState('');
  const [scanType, setScanType] = useState('vulnerability');
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState(null);
  const [scanHistory, setScanHistory] = useState([]);
  const [rateLimit, setRateLimit] = useState(1000); // ms between requests

  // Assessment types with descriptions
  const assessmentTypes = [
    {
      id: 'vulnerability',
      name: 'Vulnerability Assessment',
      description: 'Scan for common web vulnerabilities',
      icon: <BugReport />,
      duration: '5-10 minutes'
    },
    {
      id: 'headers',
      name: 'Security Headers Analysis',
      description: 'Check security header configurations',
      icon: <Shield />,
      duration: '2-3 minutes'
    },
    {
      id: 'ssl',
      name: 'SSL/TLS Configuration',
      description: 'Analyze SSL/TLS security settings',
      icon: <Security />,
      duration: '3-5 minutes'
    },
    {
      id: 'api',
      name: 'API Security Testing',
      description: 'Test API endpoints for security issues',
      icon: <Security />,
      duration: '10-15 minutes'
    }
  ];

  // Validate target URL
  const validateUrl = (url) => {
    try {
      const urlObj = new URL(url);
      // Only allow HTTPS in production
      if (process.env.NODE_ENV === 'production' && urlObj.protocol !== 'https:') {
        return { valid: false, error: 'HTTPS is required for security assessments' };
      }
      return { valid: true };
    } catch (error) {
      return { valid: false, error: 'Invalid URL format' };
    }
  };

  // Validate authorization
  const validateAuthorization = (token) => {
    if (!token || token.trim().length < 10) {
      return { valid: false, error: 'Valid authorization token is required' };
    }
    return { valid: true };
  };

  // Start security assessment
  const startAssessment = async () => {
    // Validate inputs
    const urlValidation = validateUrl(targetUrl);
    if (!urlValidation.valid) {
      toast.error(urlValidation.error);
      return;
    }

    const authValidation = validateAuthorization(authorizationToken);
    if (!authValidation.valid) {
      toast.error(authValidation.error);
      return;
    }

    // Show ethical guidelines
    const confirmed = window.confirm(
      '⚠️ ETHICAL GUIDELINES\n\n' +
      '1. You must have explicit written permission to scan this target\n' +
      '2. Scanning must be within the defined scope\n' +
      '3. Rate limiting will be applied to avoid service disruption\n' +
      '4. All findings must be reported responsibly\n\n' +
      'Do you confirm you have proper authorization?'
    );

    if (!confirmed) {
      toast.info('Assessment cancelled. Please ensure you have proper authorization.');
      return;
    }

    setIsScanning(true);
    setScanResults(null);

    try {
      // Start the assessment
      const assessmentId = await initiateAssessment({
        targetUrl,
        authorizationToken,
        scanType,
        rateLimit
      });

      // Monitor progress
      await monitorAssessment(assessmentId);

    } catch (error) {
      console.error('Assessment error:', error);
      toast.error('Assessment failed: ' + error.message);
    } finally {
      setIsScanning(false);
    }
  };

  // Initiate assessment with backend
  const initiateAssessment = async (params) => {
    // This would call your backend API
    const response = await fetch('/api/security-assessment/initiate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
      },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      throw new Error('Failed to initiate assessment');
    }

    const data = await response.json();
    return data.assessmentId;
  };

  // Monitor assessment progress
  const monitorAssessment = async (assessmentId) => {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;

    while (attempts < maxAttempts) {
      const response = await fetch(`/api/security-assessment/${assessmentId}/status`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to check assessment status');
      }

      const data = await response.json();

      if (data.status === 'completed') {
        setScanResults(data.results);
        setScanHistory(prev => [data.results, ...prev.slice(0, 9)]);
        toast.success('Security assessment completed successfully!');
        return;
      } else if (data.status === 'failed') {
        throw new Error(data.error || 'Assessment failed');
      }

      // Wait before next check
      await new Promise(resolve => setTimeout(resolve, 5000));
      attempts++;
    }

    throw new Error('Assessment timed out');
  };

  // Get severity color
  const getSeverityColor = (severity) => {
    switch (severity.toLowerCase()) {
      case 'critical': return '#d32f2f';
      case 'high': return '#f57c00';
      case 'medium': return '#fbc02d';
      case 'low': return '#388e3c';
      default: return '#757575';
    }
  };

  return (
    <div className="security-assessment">
      <div className="assessment-header">
        <h1>Security Assessment</h1>
        <p>Perform authorized security assessments on web applications</p>
      </div>

      {/* Ethical Guidelines */}
      <div className="ethical-guidelines">
        <div className="guidelines-header">
          <Warning className="warning-icon" />
          <h3>Ethical Guidelines</h3>
        </div>
        <ul>
          <li>✅ Only scan targets you have explicit written permission to test</li>
          <li>✅ Respect rate limits and avoid service disruption</li>
          <li>✅ Report findings responsibly through proper channels</li>
          <li>✅ Follow responsible disclosure procedures</li>
        </ul>
      </div>

      {/* Assessment Configuration */}
      <div className="assessment-config">
        <h2>Assessment Configuration</h2>
        
        <div className="config-section">
          <label htmlFor="targetUrl">Target URL *</label>
          <input
            id="targetUrl"
            type="url"
            value={targetUrl}
            onChange={(e) => setTargetUrl(e.target.value)}
            placeholder="https://example.salesforce.com"
            required
          />
        </div>

        <div className="config-section">
          <label htmlFor="authToken">Authorization Token *</label>
          <input
            id="authToken"
            type="password"
            value={authorizationToken}
            onChange={(e) => setAuthorizationToken(e.target.value)}
            placeholder="Enter your authorization token"
            required
          />
          <small>Required to prove you have permission to scan this target</small>
        </div>

        <div className="config-section">
          <label htmlFor="scanType">Assessment Type</label>
          <select
            id="scanType"
            value={scanType}
            onChange={(e) => setScanType(e.target.value)}
          >
            {assessmentTypes.map(type => (
              <option key={type.id} value={type.id}>
                {type.name} ({type.duration})
              </option>
            ))}
          </select>
        </div>

        <div className="config-section">
          <label htmlFor="rateLimit">Rate Limit (ms between requests)</label>
          <input
            id="rateLimit"
            type="number"
            value={rateLimit}
            onChange={(e) => setRateLimit(parseInt(e.target.value))}
            min="500"
            max="10000"
            step="100"
          />
          <small>Higher values reduce server load but increase scan time</small>
        </div>

        <button
          className="start-assessment-btn"
          onClick={startAssessment}
          disabled={isScanning || !targetUrl || !authorizationToken}
        >
          {isScanning ? 'Scanning...' : 'Start Assessment'}
        </button>
      </div>

      {/* Scan Results */}
      {scanResults && (
        <div className="scan-results">
          <h2>Assessment Results</h2>
          
          <div className="results-summary">
            <div className="summary-item">
              <span className="label">Target:</span>
              <span className="value">{scanResults.targetUrl}</span>
            </div>
            <div className="summary-item">
              <span className="label">Scan Type:</span>
              <span className="value">{scanResults.scanType}</span>
            </div>
            <div className="summary-item">
              <span className="label">Duration:</span>
              <span className="value">{scanResults.duration}</span>
            </div>
            <div className="summary-item">
              <span className="label">Issues Found:</span>
              <span className="value">{scanResults.issues?.length || 0}</span>
            </div>
          </div>

          {scanResults.issues && scanResults.issues.length > 0 ? (
            <div className="issues-list">
              {scanResults.issues.map((issue, index) => (
                <div key={index} className="issue-item">
                  <div className="issue-header">
                    <span
                      className="severity-badge"
                      style={{ backgroundColor: getSeverityColor(issue.severity) }}
                    >
                      {issue.severity}
                    </span>
                    <h4>{issue.title}</h4>
                  </div>
                  <p className="issue-description">{issue.description}</p>
                  {issue.recommendation && (
                    <div className="issue-recommendation">
                      <strong>Recommendation:</strong> {issue.recommendation}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="no-issues">
              <CheckCircle className="success-icon" />
              <p>No security issues found in this assessment.</p>
            </div>
          )}
        </div>
      )}

      {/* Scan History */}
      {scanHistory.length > 0 && (
        <div className="scan-history">
          <h2>Recent Assessments</h2>
          <div className="history-list">
            {scanHistory.map((result, index) => (
              <div key={index} className="history-item">
                <div className="history-header">
                  <span className="target-url">{result.targetUrl}</span>
                  <span className="scan-date">{new Date(result.timestamp).toLocaleDateString()}</span>
                </div>
                <div className="history-details">
                  <span className="scan-type">{result.scanType}</span>
                  <span className="issues-count">{result.issues?.length || 0} issues</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SecurityAssessment; 