import React, { useState, useEffect } from 'react';
import { fetchScanAccounts } from '../api';
import './ScanResults.css';

const ScanResults = ({ scanDetails }) => {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        // Fetch accounts with health check scores, limited to 2
        const response = await fetchScanAccounts(scanDetails.scanName, 2);

        setAccounts(response.accounts || []);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch accounts');
        setLoading(false);
      }
    };

    fetchAccounts();
  }, [scanDetails]);

  if (loading) return <div>Loading accounts...</div>;
  if (error) return <div>{error}</div>;

  return (
    <div className="scan-results">
      <h2>Scan Results: {scanDetails.scanName}</h2>
      <div className="accounts-list">
        {accounts.length === 0 ? (
          <p>No accounts found.</p>
        ) : (
          accounts.map((account, index) => (
            <div key={index} className="account-card">
              <h3>{account.name}</h3>
              <div className="health-score">
                <span>Health Check Score:</span>
                <div
                  className={`score-indicator ${
                    account.healthScore >= 80 ? 'high' :
                    account.healthScore >= 50 ? 'medium' : 'low'
                  }`}
                >
                  {account.healthScore}%
                </div>
              </div>
              <div className="account-details">
                <p><strong>Owner:</strong> {account.owner}</p>
                <p><strong>Environment:</strong> {account.environment}</p>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default ScanResults;