.data-status-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-align: center;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.data-status-container.pending {
  background-color: #e8f4fd;
  border-left: 5px solid #0066cc;
}

.data-status-container.timeout {
  background-color: #fff8e6;
  border-left: 5px solid #ffc107;
}

.data-status-container.error {
  background-color: #fff5f5;
  border-left: 5px solid #dc3545;
}

.data-status-container.empty {
  background-color: #f8f9fa;
  border-left: 5px solid #6c757d;
}

.timeout-note {
  color: #856404;
  margin-bottom: 15px;
  font-style: italic;
}

.data-status-content {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  max-width: 600px;
}

.data-status-icon {
  margin-right: 20px;
  font-size: 36px;
  flex-shrink: 0;
}

.error-icon, .empty-icon {
  font-size: 36px;
}

.data-status-icon .loading-spinner {
  width: 36px;
  height: 36px;
  border: 4px solid rgba(0, 102, 204, 0.2);
  border-radius: 50%;
  border-top-color: #0066cc;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.data-status-message {
  text-align: left;
  flex-grow: 1;
}

.status-title {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  color: #333;
}

.data-status-message p {
  margin-bottom: 15px;
  font-size: 16px;
  color: #555;
  line-height: 1.5;
}

.refresh-button {
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.refresh-button:hover {
  background-color: #0055aa;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.refresh-button:active {
  transform: translateY(0);
  box-shadow: none;
}

.refresh-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.refresh-button.primary {
  background-color: #28a745;
  font-size: 16px;
  padding: 10px 20px;
}

.refresh-button.primary:hover {
  background-color: #218838;
}
