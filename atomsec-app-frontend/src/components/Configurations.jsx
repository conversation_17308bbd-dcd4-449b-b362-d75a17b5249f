import React, { useState, useEffect } from 'react';
import { FiFileText } from 'react-icons/fi';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { CalendarViewDay, Person } from '@mui/icons-material';
import './Configurations.css';

const Configurations = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('general');
  const [generalSettings, setGeneralSettings] = useState({
    organizationName: 'Atom Security',
    timezone: 'UTC-5 (Eastern Time)',
    language: 'English',
    dateFormat: 'MM/DD/YYYY'
  });

  const [saved, setSaved] = useState(false);
  const [insights, setInsights] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeFilter, setActiveFilter] = useState('all');

  // Check for tab in URL query params
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tab = searchParams.get('tab');
    if (tab && ['general', 'insights', 'advanced'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [location.search]);

  const handleGeneralChange = (e) => {
    const { name, value } = e.target;
    setGeneralSettings({
      ...generalSettings,
      [name]: value
    });
    setSaved(false);
  };

  const handleSave = () => {
    // Simulate saving to API
    setTimeout(() => {
      setSaved(true);
    }, 500);
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    // Update URL query params
    const searchParams = new URLSearchParams(location.search);
    searchParams.set('tab', tab);
    navigate({ search: searchParams.toString() });

    // Fetch data if switching to insights tab
    if (tab === 'insights' && insights.length === 0) {
      // We'll fetch insights data when needed
    }
  };



  // Fetch mock insights data
  useEffect(() => {
    if (activeTab === 'insights') {
      // Simulate fetching insights data
      const fetchInsights = async () => {
        try {
          setLoading(true);
          // Mock data - replace with actual API call when available
          const mockInsights = [
            {
              id: 1,
              title: 'Security Risk Detected',
              description: 'High-risk permission settings found in Salesforce profiles',
              severity: 'high',
              date: '2023-06-15',
              source: 'Salesforce',
              actionRequired: true
            },
            {
              id: 2,
              title: 'Compliance Alert',
              description: 'Your organization is not meeting GDPR requirements for data retention',
              severity: 'medium',
              date: '2023-06-14',
              source: 'Compliance Scanner',
              actionRequired: true
            },
            {
              id: 3,
              title: 'Performance Optimization',
              description: 'Database queries could be optimized to improve response times',
              severity: 'low',
              date: '2023-06-13',
              source: 'Performance Monitor',
              actionRequired: false
            },
            {
              id: 4,
              title: 'New Feature Available',
              description: 'Custom report builder is now available for your subscription tier',
              severity: 'info',
              date: '2023-06-12',
              source: 'System',
              actionRequired: false
            },
            {
              id: 5,
              title: 'Critical Security Vulnerability',
              description: 'Outdated API endpoints with known security vulnerabilities are still in use',
              severity: 'high',
              date: '2023-06-11',
              source: 'Security Scanner',
              actionRequired: true
            }
          ];

          // Simulate API delay
          setTimeout(() => {
            setInsights(mockInsights);
            setLoading(false);
          }, 800);
        } catch (err) {
          setError('Failed to load insights');
          setLoading(false);
        }
      };

      fetchInsights();
    }
  }, [activeTab]);

  const renderGeneralTab = () => (
    <div className="config-section">
      <h3 className="section-title">General Settings</h3>
      <p className="section-description">Configure basic application settings and preferences.</p>
      <div className="form-group">
        <div className="input-wrapper">
          <label className="input-label" htmlFor="organizationName">Organization Name</label>
          <input
            type="text"
            id="organizationName"
            name="organizationName"
            value={generalSettings.organizationName}
            onChange={handleGeneralChange}
            placeholder="Enter your organization name"
          />
        </div>
        <p className="setting-description">This name will appear in reports and notifications</p>
      </div>

      <div className="form-group">
        <div className="select-wrapper">
          <label className="select-label" htmlFor="timezone">Timezone</label>
          <select
            id="timezone"
            name="timezone"
            value={generalSettings.timezone}
            onChange={handleGeneralChange}
          >
            <optgroup label="North America">
              <option value="UTC-5 (Eastern Time)">UTC-5 (Eastern Time - New York)</option>
              <option value="UTC-6 (Central Time)">UTC-6 (Central Time - Chicago)</option>
              <option value="UTC-7 (Mountain Time)">UTC-7 (Mountain Time - Denver)</option>
              <option value="UTC-8 (Pacific Time)">UTC-8 (Pacific Time - Los Angeles)</option>
              <option value="UTC-10 (Hawaii Time)">UTC-10 (Hawaii Time - Honolulu)</option>
            </optgroup>
            <optgroup label="Europe">
              <option value="UTC+0 (GMT)">UTC+0 (GMT - London)</option>
              <option value="UTC+1 (Central European Time)">UTC+1 (Central European Time - Paris)</option>
              <option value="UTC+2 (Eastern European Time)">UTC+2 (Eastern European Time - Athens)</option>
            </optgroup>
            <optgroup label="Asia">
              <option value="UTC+5:30 (India Standard Time)">UTC+5:30 (India Standard Time - Mumbai)</option>
              <option value="UTC+8 (China Standard Time)">UTC+8 (China Standard Time - Beijing)</option>
              <option value="UTC+9 (Japan Standard Time)">UTC+9 (Japan Standard Time - Tokyo)</option>
            </optgroup>
            <optgroup label="Australia & Pacific">
              <option value="UTC+10 (Australian Eastern Time)">UTC+10 (Australian Eastern Time - Sydney)</option>
              <option value="UTC+12 (New Zealand Time)">UTC+12 (New Zealand Time - Auckland)</option>
            </optgroup>
            <optgroup label="Other">
              <option value="UTC+0 (Universal Time Coordinated)">UTC+0 (Universal Time Coordinated)</option>
            </optgroup>
          </select>
        </div>
        <p className="setting-description">Select your local timezone for accurate time display</p>
      </div>

      <div className="form-group">
        <div className="select-wrapper">
          <label className="select-label" htmlFor="language">Language</label>
          <select
            id="language"
            name="language"
            value={generalSettings.language}
            onChange={handleGeneralChange}
          >
            <option value="English">English</option>
            <option value="Spanish">Spanish</option>
            <option value="French">French</option>
            <option value="German">German</option>
            <option value="Chinese">Chinese</option>
            <option value="Japanese">Japanese</option>
            <option value="Hindi">Hindi</option>
          </select>
        </div>
        <p className="setting-description">Select your preferred language for the interface</p>
      </div>

      <div className="form-group">
        <div className="select-wrapper">
          <label className="select-label" htmlFor="dateFormat">Date Format</label>
          <select
            id="dateFormat"
            name="dateFormat"
            value={generalSettings.dateFormat}
            onChange={handleGeneralChange}
          >
            <option value="MM/DD/YYYY">MM/DD/YYYY (e.g., 12/31/2023)</option>
            <option value="DD/MM/YYYY">DD/MM/YYYY (e.g., 31/12/2023)</option>
            <option value="YYYY-MM-DD">YYYY-MM-DD (e.g., 2023-12-31)</option>
            <option value="MMM DD, YYYY">MMM DD, YYYY (e.g., Dec 31, 2023)</option>
            <option value="DD MMM YYYY">DD MMM YYYY (e.g., 31 Dec 2023)</option>
          </select>
        </div>
        <p className="setting-description">Choose how dates will be displayed throughout the application</p>
      </div>
    </div>
  );

  // Render the insights tab content
  const renderInsightsTab = () => {
    const filteredInsights = activeFilter === 'all'
      ? insights
      : insights.filter(insight => insight.severity === activeFilter);

    return (
      <div className="config-section insights-section">
        <h3 className="section-title">Insights</h3>
        <p className="section-description">Security and performance insights for your organization</p>

        <div className="insights-filters">
          <button
            className={`filter-btn ${activeFilter === 'all' ? 'active' : ''}`}
            onClick={() => setActiveFilter('all')}
          >
            All Insights
          </button>
          <button
            className={`filter-btn ${activeFilter === 'high' ? 'active' : ''}`}
            onClick={() => setActiveFilter('high')}
          >
            High Priority
          </button>
          <button
            className={`filter-btn ${activeFilter === 'medium' ? 'active' : ''}`}
            onClick={() => setActiveFilter('medium')}
          >
            Medium Priority
          </button>
          <button
            className={`filter-btn ${activeFilter === 'low' ? 'active' : ''}`}
            onClick={() => setActiveFilter('low')}
          >
            Low Priority
          </button>
          <button
            className={`filter-btn ${activeFilter === 'info' ? 'active' : ''}`}
            onClick={() => setActiveFilter('info')}
          >
            Informational
          </button>
        </div>

        <div className="insights-list">
          {loading ? (
            <div className="loading-spinner">Loading insights...</div>
          ) : error ? (
            <div className="error-message">{error}</div>
          ) : filteredInsights.length === 0 ? (
            <div className="no-insights">No insights found for the selected filter.</div>
          ) : (
            filteredInsights.map(insight => (
              <div key={insight.id} className={`insight-card severity-${insight.severity}`}>
                <div className="insight-header">
                  <h3 className="insight-title">{insight.title}</h3>
                  <span className={`insight-severity ${insight.severity}`}>
                    {insight.severity.charAt(0).toUpperCase() + insight.severity.slice(1)}
                  </span>
                </div>
                <p className="insight-description">{insight.description}</p>
                <div className="insight-meta">
                  <span className="insight-source">{insight.source}</span>
                  <span className="insight-date">{insight.date}</span>
                </div>
                {insight.actionRequired && (
                  <button className="insight-action-btn">Take Action</button>
                )}
              </div>
            ))
          )}
        </div>


      </div>
    );
  };

  // Render the advanced tab content with links to Task Management and Account Management
  const renderAdvancedTab = () => {
    // Get the active org ID for task management link
    const getActiveOrgId = () => {
      const path = window.location.pathname;
      if (path.startsWith('/org/')) {
        return path.split('/')[2];
      }
      return null;
    };

    const activeOrgId = getActiveOrgId();
    const taskManagementPath = activeOrgId ? `/tasks/${activeOrgId}` : '/tasks';

    return (
      <div className="config-section advanced-section">
        <h3 className="section-title">Advanced Settings</h3>
        <p className="section-description">Access advanced system management features</p>

        <div className="advanced-links">
          <div className="advanced-link-card">
            <div className="advanced-link-icon">
              <CalendarViewDay />
            </div>
            <div className="advanced-link-content">
              <h3 className="advanced-link-title">Task Management</h3>
              <p className="advanced-link-description">
                Monitor and manage background tasks and processes
              </p>
              <Link to={taskManagementPath} className="advanced-link-button">
                Open Task Management
              </Link>
            </div>
          </div>

          <div className="advanced-link-card">
            <div className="advanced-link-icon">
              <Person />
            </div>
            <div className="advanced-link-content">
              <h3 className="advanced-link-title">Account Management</h3>
              <p className="advanced-link-description">
                Manage user accounts, roles, and permissions
              </p>
              <Link to="/account-management" className="advanced-link-button">
                Open Account Management
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="configurations-container">
      <div className="page-header">
        <h1 className="page-title">Configurations</h1>
        <p className="page-description">Manage your application settings and preferences</p>
      </div>

      <div className="config-tabs">
        <button
          className={`tab-btn ${activeTab === 'general' ? 'active' : ''}`}
          onClick={() => handleTabChange('general')}
        >
          General
        </button>
        <button
          className={`tab-btn ${activeTab === 'insights' ? 'active' : ''}`}
          onClick={() => handleTabChange('insights')}
        >
          Insights
        </button>
        <button
          className={`tab-btn ${activeTab === 'advanced' ? 'active' : ''}`}
          onClick={() => handleTabChange('advanced')}
        >
          Advanced
        </button>
      </div>

      <div className="config-content">
        {activeTab === 'general' ? (
          <>
            {renderGeneralTab()}
            <div className="config-actions">
              <button className="save-btn" onClick={handleSave}>
                Save Changes
              </button>
              {saved && <span className="save-success">Settings saved successfully!</span>}
            </div>
          </>
        ) : activeTab === 'insights' ? (
          renderInsightsTab()
        ) : (
          renderAdvancedTab()
        )}
      </div>
    </div>
  );
};

export default Configurations;
