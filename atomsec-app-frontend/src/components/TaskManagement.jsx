import React, { useState, useEffect, useCallback } from 'react';
import { Container, Row, Col, Card, Table, <PERSON><PERSON>, Badge, Spinner, Form, Modal } from 'react-bootstrap';
import { toast } from 'react-toastify';
import { formatDistanceToNow } from 'date-fns';
import { API_CONFIG } from '../config';
import axios from 'axios';
import './TaskManagement.css';

// Create a custom axios instance for task management
const api = axios.create({
  baseURL: API_CONFIG.baseURL, // Use the correct baseURL from config
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Task status badge colors
const statusColors = {
  pending: 'warning',
  running: 'primary',
  completed: 'success',
  failed: 'danger',
  retry: 'info',
  cancelled: 'secondary'
};

// Task type display names
const taskTypeNames = {
  overview: 'Overview',
  health_check: 'Health Check',
  profiles: 'Profiles & Permissions',
  guest_user_risks: 'Guest User Risks',
  data_export: 'Data Export',
  report_generation: 'Report Generation',
  scheduled_scan: 'Scheduled Scan',
  notification: 'Notification'
};

// Task priority display names and colors
const taskPriorities = {
  high: { name: 'High', color: 'danger' },
  medium: { name: 'Medium', color: 'warning' },
  low: { name: 'Low', color: 'info' }
};

// Task type to recommended priority mapping
const taskTypePriorityMap = {
  // High priority tasks - critical for security monitoring
  health_check: 'high',
  guest_user_risks: 'high',
  notification: 'high',

  // Medium priority tasks - important but not as time-critical
  profiles: 'medium',
  pmd_issues: 'medium',
  overview: 'medium',
  scheduled_scan: 'medium',

  // Low priority tasks - useful but can be processed when resources are available
  data_export: 'low',
  report_generation: 'low'
};

const TaskManagement = ({ orgId }) => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [includeCompleted, setIncludeCompleted] = useState(false);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  // Get the default priority based on the initial task type
  const defaultTaskType = 'scheduled_scan';
  const defaultPriority = taskTypePriorityMap[defaultTaskType] || 'medium';

  const [scheduleForm, setScheduleForm] = useState({
    taskType: defaultTaskType,
    priority: defaultPriority,
    isRecurring: false,
    scheduleType: 'daily',
    scheduledTime: '',
    params: {}
  });

  // Fetch tasks from API - memoized to avoid infinite loops with useEffect
  const fetchTasks = useCallback(async (showLoadingIndicator = true) => {
    if (showLoadingIndicator) {
      setLoading(true);
    }

    try {
      // Get token from localStorage
      const accessToken = localStorage.getItem('accessToken');

      // Use the standardized API prefix
      const response = await axios.get(`${API_CONFIG.baseURL}/api/tasks/list`, {
        params: {
          org_id: orgId,
          include_completed: includeCompleted
        },
        headers: {
          'Content-Type': 'application/json',
          'Authorization': accessToken ? `Bearer ${accessToken}` : ''
        }
      });

      if (response.data?.data?.tasks) {
        setTasks(response.data.data.tasks);
      } else {
        setTasks([]);
      }

      setError(null);
    } catch (err) {
      console.error('Error fetching tasks:', err);
      setError('Failed to load tasks. Please try again.');
      toast.error('Failed to load tasks');
    } finally {
      if (showLoadingIndicator) {
        setLoading(false);
      }
    }
  }, [orgId, includeCompleted]);

  // Fetch tasks on component mount and when includeCompleted changes
  useEffect(() => {
    if (orgId) {
      fetchTasks();
    }
  }, [orgId, includeCompleted, fetchTasks]);

  // Set up polling for task updates
  useEffect(() => {
    if (orgId) {
      const interval = setInterval(() => {
        fetchTasks(false); // Don't show loading indicator for polling
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [orgId, includeCompleted, fetchTasks]);

  // Cancel a task
  const handleCancelTask = async (taskId) => {
    try {
      // Get token from localStorage
      const accessToken = localStorage.getItem('accessToken');

      // Use the standardized API prefix
      const response = await axios.post(`${API_CONFIG.baseURL}/api/tasks/cancel`, {
        task_id: taskId
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': accessToken ? `Bearer ${accessToken}` : ''
        }
      });

      if (response.data?.success) {
        toast.success('Task cancelled successfully');
        fetchTasks();
      } else {
        toast.error('Failed to cancel task');
      }
    } catch (err) {
      console.error('Error cancelling task:', err);
      toast.error('Failed to cancel task');
    }
  };

  // Schedule a task
  const handleScheduleTask = async () => {
    try {
      const payload = {
        task_type: scheduleForm.taskType,
        org_id: orgId,
        priority: scheduleForm.priority,
        is_recurring: scheduleForm.isRecurring,
        schedule_type: scheduleForm.scheduleType,
        params: scheduleForm.params
      };

      // Add scheduled time if not recurring
      if (!scheduleForm.isRecurring && scheduleForm.scheduledTime) {
        payload.scheduled_time = new Date(scheduleForm.scheduledTime).toISOString();
      }

      // Get token from localStorage
      const accessToken = localStorage.getItem('accessToken');

      // Use the standardized API prefix
      const response = await axios.post(`${API_CONFIG.baseURL}/api/tasks/schedule`, payload, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': accessToken ? `Bearer ${accessToken}` : ''
        }
      });

      if (response.data?.success) {
        toast.success('Task scheduled successfully');
        setShowScheduleModal(false);
        fetchTasks();
      } else {
        toast.error('Failed to schedule task');
      }
    } catch (err) {
      console.error('Error scheduling task:', err);
      toast.error('Failed to schedule task');
    }
  };

  // Handle form input changes
  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newFormValues = {
      ...scheduleForm,
      [name]: type === 'checkbox' ? checked : value
    };

    // If task type changed, update the recommended priority
    if (name === 'taskType') {
      const recommendedPriority = taskTypePriorityMap[value] || 'medium';
      newFormValues.priority = recommendedPriority;
    }

    setScheduleForm(newFormValues);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      return `${date.toLocaleDateString()} ${date.toLocaleTimeString()} (${formatDistanceToNow(date, { addSuffix: true })})`;
    } catch (err) {
      return dateString;
    }
  };

  // Render task status badge
  const renderStatusBadge = (status) => {
    const color = statusColors[status] || 'secondary';
    return <Badge bg={color}>{status}</Badge>;
  };

  // Render task priority badge
  const renderPriorityBadge = (priority) => {
    const { name, color } = taskPriorities[priority] || { name: priority, color: 'secondary' };
    return <Badge bg={color}>{name}</Badge>;
  };

  // Render task progress bar
  const renderProgressBar = (progress) => {
    const progressValue = progress || 0;
    let variant = 'primary';

    if (progressValue >= 100) {
      variant = 'success';
    } else if (progressValue >= 70) {
      variant = 'info';
    } else if (progressValue >= 30) {
      variant = 'warning';
    }

    return (
      <div className="progress">
        <div
          className={`progress-bar bg-${variant}`}
          role="progressbar"
          style={{ width: `${progressValue}%` }}
          aria-valuenow={progressValue}
          aria-valuemin="0"
          aria-valuemax="100"
        >
          {progressValue}%
        </div>
      </div>
    );
  };

  return (
    <Container fluid className="task-management-container">
      <Row className="mb-4">
        <Col>
          <Card>
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h2 className="mb-0">Task Management</h2>
              <div className="d-flex align-items-center">
                <Form.Check
                  type="switch"
                  id="include-completed-switch"
                  label="Include Completed Tasks"
                  checked={includeCompleted}
                  onChange={(e) => setIncludeCompleted(e.target.checked)}
                  className="me-3"
                />
                <Button
                  variant="primary"
                  onClick={() => setShowScheduleModal(true)}
                >
                  Schedule Task
                </Button>
              </div>
            </Card.Header>
            <Card.Body>
              {loading ? (
                <div className="text-center p-5">
                  <Spinner animation="border" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </Spinner>
                  <p className="mt-3">Loading tasks...</p>
                </div>
              ) : error ? (
                <div className="alert alert-danger">
                  {error}
                  <Button
                    variant="outline-danger"
                    size="sm"
                    className="ms-3"
                    onClick={() => fetchTasks()}
                  >
                    Retry
                  </Button>
                </div>
              ) : tasks.length === 0 ? (
                <div className="text-center p-5">
                  <p>No tasks found. Schedule a task to get started.</p>
                </div>
              ) : (
                <div className="table-responsive">
                  <Table striped hover>
                    <thead>
                      <tr>
                        <th>Task ID</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Priority</th>
                        <th>Progress</th>
                        <th>Created</th>
                        <th>Completed</th>
                        <th>Message</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {tasks.map((task) => (
                        <tr key={task.task_id}>
                          <td>
                            <span className="task-id">{task.task_id}</span>
                          </td>
                          <td>{taskTypeNames[task.task_type] || task.task_type}</td>
                          <td>{renderStatusBadge(task.status)}</td>
                          <td>{renderPriorityBadge(task.priority)}</td>
                          <td>{renderProgressBar(task.progress)}</td>
                          <td>{formatDate(task.created_at)}</td>
                          <td>{formatDate(task.completed_at)}</td>
                          <td>
                            <div className="task-message">{task.message}</div>
                          </td>
                          <td>
                            {task.status === 'pending' || task.status === 'running' ? (
                              <Button
                                variant="outline-danger"
                                size="sm"
                                onClick={() => handleCancelTask(task.task_id)}
                              >
                                Cancel
                              </Button>
                            ) : (
                              <Button
                                variant="outline-secondary"
                                size="sm"
                                disabled
                              >
                                {task.status === 'completed' ? 'Completed' : 'Cancelled'}
                              </Button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Schedule Task Modal */}
      <Modal show={showScheduleModal} onHide={() => setShowScheduleModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Schedule Task</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Task Type</Form.Label>
              <Form.Select
                name="taskType"
                value={scheduleForm.taskType}
                onChange={handleFormChange}
              >
                <option value="scheduled_scan">Full Scan</option>
                <option value="overview">Overview</option>
                <option value="health_check">Health Check</option>
                <option value="profiles">Profiles & Permissions</option>
                <option value="guest_user_risks">Guest User Risks</option>
                <option value="data_export">Data Export</option>
                <option value="report_generation">Report Generation</option>
              </Form.Select>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Priority</Form.Label>
              <Form.Select
                name="priority"
                value={scheduleForm.priority}
                onChange={handleFormChange}
              >
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </Form.Select>
              <Form.Text className="text-muted">
                Recommended priority for {taskTypeNames[scheduleForm.taskType] || scheduleForm.taskType}: {' '}
                <span className={`text-${taskPriorities[scheduleForm.priority]?.color || 'secondary'}`}>
                  {taskPriorities[scheduleForm.priority]?.name || scheduleForm.priority}
                </span>
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                label="Recurring Task"
                name="isRecurring"
                checked={scheduleForm.isRecurring}
                onChange={handleFormChange}
              />
            </Form.Group>

            {scheduleForm.isRecurring ? (
              <Form.Group className="mb-3">
                <Form.Label>Schedule Type</Form.Label>
                <Form.Select
                  name="scheduleType"
                  value={scheduleForm.scheduleType}
                  onChange={handleFormChange}
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </Form.Select>
              </Form.Group>
            ) : (
              <Form.Group className="mb-3">
                <Form.Label>Scheduled Time</Form.Label>
                <Form.Control
                  type="datetime-local"
                  name="scheduledTime"
                  value={scheduleForm.scheduledTime}
                  onChange={handleFormChange}
                />
              </Form.Group>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowScheduleModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleScheduleTask}>
            Schedule Task
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default TaskManagement;
