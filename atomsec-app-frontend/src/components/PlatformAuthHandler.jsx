import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

/**
 * Component to handle platform authentication redirects
 * This component is used when users are redirected back from Azure AD platform authentication
 */
const PlatformAuthHandler = () => {
  const navigate = useNavigate();
  const { setUser, setIsAuthenticated } = useAuth();

  useEffect(() => {
    const handlePlatformAuth = async () => {
      try {
        console.log('Handling platform authentication redirect...');
        
        // Check platform authentication status
        const response = await fetch('/.auth/me');
        
        if (response.ok) {
          const authData = await response.json();
          console.log('Platform auth data:', authData);
          
          if (authData && authData.length > 0) {
            const userInfo = authData[0];
            console.log('Platform authenticated user:', userInfo);
            
            // Extract user information from platform auth
            const email = userInfo.user_id || userInfo.user_claims?.find(c => c.typ === 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress')?.val;
            const name = userInfo.user_claims?.find(c => c.typ === 'name')?.val || userInfo.user_claims?.find(c => c.typ === 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name')?.val;
            
            if (email) {
              // Create a platform-based access token
              const platformToken = userInfo.access_token || 'platform-authenticated';
              
              // Store user information
              localStorage.setItem('accessToken', platformToken);
              localStorage.setItem('userEmail', email);
              localStorage.setItem('userName', name || '');
              
              // Set user state
              setUser({
                accessToken: platformToken,
                email,
                name: name || '',
                platformAuth: true
              });
              setIsAuthenticated(true);
              
              console.log('Successfully authenticated via platform auth');
              
              // Get redirect path or default to home
              const redirectPath = localStorage.getItem('loginRedirectPath') || '/';
              localStorage.removeItem('loginRedirectPath');
              
              // Navigate to the intended destination
              navigate(redirectPath);
              return;
            }
          }
        }
        
        // If platform auth failed, redirect to login
        console.error('Platform authentication failed');
        navigate('/login?error=platform_auth_failed');
        
      } catch (error) {
        console.error('Error handling platform auth:', error);
        navigate('/login?error=platform_auth_error');
      }
    };

    handlePlatformAuth();
  }, [navigate, setUser, setIsAuthenticated]);

  return (
    <div className="loading-indicator">
      <h2>Completing authentication...</h2>
      <p>Please wait while we complete your login.</p>
    </div>
  );
};

export default PlatformAuthHandler;
