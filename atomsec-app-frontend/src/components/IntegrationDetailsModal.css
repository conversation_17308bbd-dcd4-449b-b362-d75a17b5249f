.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Higher than anything else */
  transition: background-color 0.3s ease;
  opacity: 0;
  visibility: hidden;
}

.modal-overlay.visible {
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: scale(0.95);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.modal-content.visible {
  transform: scale(1);
  opacity: 1;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #E5E7EB;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 20px;
  color: #6B7280;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.close-button:hover {
  color: #111827;
}

.modal-body {
  padding: 24px;
}

.integration-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-row {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
}

@media (min-width: 640px) {
  .detail-row {
    flex-direction: row;
    gap: 16px;
  }
}

.detail-label {
  font-weight: 500;
  color: #6B7280;
  min-width: 120px;
  flex-shrink: 0;
}

.detail-value {
  color: #111827;
  word-break: break-word;
}

.detail-value.description {
  white-space: pre-line;
}

.status-indicator {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.status-indicator.active {
  background-color: #E6F7F1;
  color: #51D59C;
}

.status-indicator.inactive {
  background-color: #FEE2E2;
  color: #EF4444;
}
