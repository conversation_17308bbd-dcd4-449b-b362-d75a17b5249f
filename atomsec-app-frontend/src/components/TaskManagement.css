.task-management-container {
  padding: 20px;
}

.task-id {
  font-family: monospace;
  font-size: 0.9rem;
  color: #666;
  display: block;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-message {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.progress {
  height: 10px;
  border-radius: 5px;
  background-color: #f0f0f0;
}

.progress-bar {
  font-size: 8px;
  line-height: 10px;
  text-align: center;
}

/* Task status badges */
.badge {
  font-size: 0.75rem;
  padding: 0.35em 0.65em;
  font-weight: 600;
}

/* Task table */
.table-responsive {
  max-height: 600px;
  overflow-y: auto;
}

.table th {
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 1;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
}

/* Schedule task modal */
.modal-body .form-group {
  margin-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .task-management-container {
    padding: 10px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start !important;
  }
  
  .card-header .d-flex {
    margin-top: 10px;
    width: 100%;
    justify-content: space-between;
  }
  
  .task-message {
    max-width: 150px;
  }
}
