.health-check-details {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: var(--font-family-sans);
}

.page-header {
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--color-gray-200);
  padding-bottom: 1rem;
}

.page-header h2 {
  font-size: 1.8rem;
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
}

.page-header h3 {
  font-size: 1.4rem;
  color: var(--color-primary);
  font-weight: 500;
}

/* Tabs */
.tabs-container {
  margin-bottom: 2rem;
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--color-gray-200);
  overflow-x: auto;
  scrollbar-width: thin;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 1rem;
  font-weight: 500;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tab-button:hover {
  color: var(--color-primary);
}

.tab-button.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

/* Tab Content */
.tab-content {
  background-color: var(--color-bg-main);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 2rem;
}

/* Overview Tab */
.org-summary {
  margin-bottom: 2rem;
}

.org-summary h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: var(--color-text-primary);
}

.summary-card {
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.summary-item {
  display: flex;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-gray-200);
}

.summary-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.summary-label {
  font-weight: 500;
  color: var(--color-text-secondary);
  width: 180px;
  flex-shrink: 0;
}

.summary-value {
  color: var(--color-text-primary);
  font-weight: 500;
}

.health-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-sm);
  font-weight: 600;
  color: white;
}

.health-badge.high {
  background-color: var(--color-success);
}

.health-badge.medium {
  background-color: var(--color-warning);
}

.health-badge.low {
  background-color: var(--color-danger);
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-sm);
  font-weight: 600;
}

.status-badge.active {
  background-color: var(--color-success-light);
  color: var(--color-success);
}

.status-badge.inactive {
  background-color: var(--color-gray-200);
  color: var(--color-gray-600);
}

/* Health Check Tab */
.health-check-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
}

.health-score-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.health-score-label {
  font-weight: 600;
  font-size: 1.2rem;
  color: var(--color-text-primary);
}

.health-score-value {
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  font-weight: 700;
  font-size: 1.5rem;
  color: white;
  min-width: 80px;
  text-align: center;
}

.health-score-grade {
  font-weight: 600;
  font-size: 1.2rem;
}

.rescan-button {
  padding: 0.75rem 1.5rem;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rescan-button:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
}

.rescan-button:disabled {
  background-color: var(--color-gray-400);
  cursor: not-allowed;
  transform: none;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.chart-card {
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.chart-card h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--color-text-primary);
  text-align: center;
}

.pie-chart-container,
.bar-chart-container {
  height: 300px;
  position: relative;
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
}

.filter-group,
.search-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label,
.search-group label {
  font-weight: 500;
  color: var(--color-text-secondary);
}

.filter-group select,
.search-group input {
  padding: 0.5rem;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-sm);
  min-width: 200px;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h4 {
  font-size: 1.2rem;
  font-weight: 600;
}

.section-filters {
  display: flex;
  gap: 1rem;
}

.filter-dropdown {
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background-color: white;
}

.search-container {
  position: relative;
}

.search-input {
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  width: 250px;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  opacity: 0.5;
}

/* Policies Result Section */
.policies-result-section {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.policies-result-table-wrapper {
  overflow-x: auto;
  margin-top: 1rem;
}

.policies-result-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.policies-result-table th {
  background-color: #f8f9fa;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #dee2e6;
}

.policies-result-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #dee2e6;
}

.severity-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.high-severity {
  background-color: #f8d7da;
  color: #dc3545;
}

.medium-severity {
  background-color: #fff3cd;
  color: #856404;
}

.low-severity {
  background-color: #d1e7dd;
  color: #0f5132;
}

/* Additional styles for the new layout */
.health-check-figma-ui {
  background: #F8FAF9;
  padding: 32px;
  border-radius: 16px;
  font-family: 'Lato', Arial, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
}

.filter-bar {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: #fff;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
}

.filter-label {
  font-weight: 500;
  color: #333;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 150px;
}

.filter-reset-btn {
  padding: 8px 16px;
  background: #51D59C;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-reset-btn:hover {
  background: #45c492;
}

/* Security Risks Section */
.security-risks-section {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.risks-table-wrapper {
  overflow-x: auto;
  margin-top: 1rem;
}

.risks-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.risks-table th {
  background-color: #f8f9fa;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #dee2e6;
}

.risks-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #dee2e6;
}

.risks-table tr.high-risk {
  background-color: rgba(220, 53, 69, 0.05);
}

.risks-table tr.medium-risk {
  background-color: rgba(255, 193, 7, 0.05);
}

.risks-table tr.meets-standard {
  background-color: rgba(40, 167, 69, 0.05);
}

.risk-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
}

.risk-badge.high-risk {
  background-color: #f8d7da;
  color: #dc3545;
}

.risk-badge.medium-risk {
  background-color: #fff3cd;
  color: #856404;
}

.risk-badge.meets-standard {
  background-color: #d1e7dd;
  color: #0f5132;
}

.info-button {
  background: none;
  border: none;
  cursor: pointer;
  opacity: 0.5;
  transition: opacity 0.2s;
}

.info-button:hover {
  opacity: 1;
}

/* Loading, Error, and Empty States */
.loading-indicator,
.pending-indicator,
.error-indicator,
.empty-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon,
.empty-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
}

.retry-button:hover {
  background-color: #e9ecef;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-gray-300);
  color: var(--color-text-primary);
}

.btn-secondary:hover {
  background-color: var(--color-gray-400);
}

.btn-warning {
  background-color: var(--color-warning);
  color: white;
  margin-left: 0.5rem;
}

.btn-warning:hover {
  background-color: #e0a800;
}

/* Messages */
.loading-message,
.error-message,
.no-data-message,
.coming-soon {
  padding: 2rem;
  text-align: center;
  font-size: 1.1rem;
  color: var(--color-text-secondary);
}

.error-message {
  color: var(--color-danger);
}

.coming-soon {
  font-style: italic;
  color: var(--color-text-secondary);
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: 3rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .health-check-details {
    padding: 1rem;
  }

  .charts-container {
    grid-template-columns: 1fr;
  }

  .filters-container {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

/* Coming Soon */
.coming-soon {
  font-style: italic;
  color: var(--color-text-secondary);
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  padding: 5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  text-align: center;
}

.coming-soon h3 {
  font-size: 1.5rem;
  color: var(--color-primary);
  margin-bottom: 1rem;
}

/* Authentication Error */
.auth-error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.auth-error-message {
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: var(--border-radius-md);
  padding: 2rem;
  text-align: center;
  max-width: 500px;
}

.auth-error-message h3 {
  color: var(--color-danger);
  margin-bottom: 1rem;
}

.auth-error-message p {
  margin-bottom: 1.5rem;
  color: var(--color-text-secondary);
}

.auth-error-message .btn {
  margin-top: 1rem;
}
