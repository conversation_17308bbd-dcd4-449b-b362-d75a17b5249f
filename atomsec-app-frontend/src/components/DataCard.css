.data-card {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.data-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px 6px 24px rgba(0, 0, 0, 0.12);
}

.data-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.data-card-title-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.data-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: rgba(21, 91, 85, 0.1);
}

.data-card-icon.issues {
  background-color: rgba(81, 213, 156, 0.1);
}

.data-card-icon.critical {
  background-color: rgba(237, 108, 2, 0.1);
}

.data-card-icon.orgs {
  background-color: rgba(21, 91, 85, 0.1);
}

.data-card-icon.inactive {
  background-color: rgba(211, 47, 47, 0.1);
}

.data-card-title {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 18px;
  line-height: 1.6;
  color: #000814;
  margin: 0;
}

.data-card-navigate {
  display: flex;
  align-items: center;
}

.data-card-navigate-btn {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.data-card-navigate-btn img {
  width: 24px;
  height: 24px;
}

.data-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-card-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.data-card-refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  padding: 8px 0;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.6em;
  color: #000814;
}

.data-card-refresh-btn:hover {
  color: var(--color-primary);
}

.data-card-refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.data-card-value-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.data-card-value {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 32px;
  line-height: 1.6;
  color: #000814;
  margin: 0;
}

.data-card-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.6em;
}

.data-card-trend.up {
  background-color: #ED6C02;
  color: #ED6C02;
}

.data-card-trend.down {
  background-color: #2E7D32;
  color: #2E7D32;
}

.data-card-trend img {
  width: 16px;
  height: 16px;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
