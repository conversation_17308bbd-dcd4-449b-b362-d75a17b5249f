.integrations-table-container {
  width: 100%;
  overflow-x: auto;
}

.integrations-table {
  width: 100%;
  border-collapse: collapse;
  font-family: '<PERSON>to', sans-serif;
}

.integrations-table th {
  background-color: #F1FCF7;
  padding: 14px 8px 14px 16px;
  font-family: '<PERSON>to', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #393E3C;
  text-align: left;
  border-top: 1px solid #B8D8CB;
  border-bottom: 1px solid #B8D8CB;
}

.integrations-table th:first-child {
  width: 100px;
}

.integrations-table th:nth-child(2) {
  width: 320px;
}

.integrations-table th:nth-child(4) {
  width: 180px;
}

.integrations-table tbody tr {
  border-bottom: 1px solid #B8D8CB;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.integrations-table tbody tr:hover {
  background-color: rgba(184, 216, 203, 0.1);
}

.integrations-table tbody tr.selected-row {
  background-color: rgba(81, 213, 156, 0.1);
  border-left: 4px solid #51D59C;
}

.integrations-table td {
  padding: 14px 8px 14px 16px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #666666;
}

.integrations-table td.org-name {
  font-size: 14px;
  font-weight: 400;
  color: #020A07;
}

.table-score-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.score-ring {
  position: relative;
  width: 32px;
  height: 32px;
}

.score-ring-base {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid #DDDBDA;
  border-radius: 50%;
  box-sizing: border-box;
}

.score-ring-progress {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid #4BCA81;
  border-radius: 50%;
  box-sizing: border-box;
  clip: rect(0, 32px, 32px, 16px);
}

.score-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  font-size: 13px;
  color: #3E3E3C;
}

.status-badge {
  display: inline-block;
  padding: 4px 16px;
  border-radius: 4px;
  background-color: #ED6C02;
  font-weight: 500;
  font-size: 12px;
  color: #ED6C02;
  text-align: center;
}

.actions-cell {
  display: flex;
  align-items: center;
  gap: 16px;
}

.rescan-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: none;
  border: none;
  padding: 0;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #155B55;
  cursor: pointer;
  white-space: nowrap;
  text-decoration: none;
}

.rescan-button:hover {
  text-decoration: underline;
}

.icon {
  width: 16px;
  height: 16px;
  color: #155B55;
}

.more-actions {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.view-details-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: none;
  border: none;
  padding: 0;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #155B55;
  cursor: pointer;
  white-space: nowrap;
  text-decoration: none;
}

.view-details-button:hover {
  text-decoration: underline;
}
