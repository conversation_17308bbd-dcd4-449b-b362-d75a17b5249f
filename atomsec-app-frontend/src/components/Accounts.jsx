import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { FiRefreshCw, FiTrash2, <PERSON>Grid, <PERSON>List, FiSearch, FiFilter, FiX, FiPlus, FiChevronRight, FiSettings } from 'react-icons/fi';
import { toast } from 'react-toastify';
import { formatDistanceToNow } from 'date-fns';
import { API_CONFIG } from '../config';
import axios from 'axios';
import './Accounts.css';

// Create a custom axios instance for accounts
const api = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Helper function to get integration ID from account object
const getIntegrationId = (account) => {
  return account.id || account.Id || account.integrationId || account.IntegrationId || account._id || '';
};

// Helper function to get integration name from account object
const getIntegrationName = (account) => {
  return account.name || account.Name || account.orgName || account.OrgName || 'Unknown';
};

// Helper function to get integration URL from account object
const getIntegrationUrl = (account) => {
  return account.tenantUrl || account.TenantUrl || account.instanceUrl || account.InstanceUrl || '';
};

const Accounts = ({ userEmail }) => {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [integrationFilter, setIntegrationFilter] = useState('');
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [disconnecting, setDisconnecting] = useState(false);
  const [rescanning, setRescanning] = useState(false);

  // Fetch accounts from API
  const fetchUserOrgs = async () => {
    try {
      setLoading(true);
      const accessToken = localStorage.getItem('accessToken');

      const response = await api.get('/api/integrations', {
        headers: {
          'Authorization': accessToken ? `Bearer ${accessToken}` : ''
        }
      });

      if (response.data?.data?.integrations) {
        setAccounts(response.data.data.integrations);
      } else if (Array.isArray(response.data)) {
        setAccounts(response.data);
      } else {
        setAccounts([]);
      }

      setError(null);
    } catch (err) {
      console.error('Error fetching accounts:', err);
      setError('Failed to load accounts. Please try again.');
      toast.error('Failed to load accounts');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserOrgs();
  }, []);

  // Parse URL parameters for integration filter
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const integration = searchParams.get('integration');
    if (integration) {
      setIntegrationFilter(integration);
    }
  }, []);

  // Add disconnect functionality
  const handleDisconnectOrg = async (integrationId) => {
    try {
      setDisconnecting(true);
      const accessToken = localStorage.getItem('accessToken');

      const response = await api.delete(`/api/integrations/${integrationId}`, {
        headers: {
          'Authorization': accessToken ? `Bearer ${accessToken}` : ''
        }
      });

      if (response.data?.success) {
        toast.success('Integration disconnected successfully');
        fetchUserOrgs(); // Refresh the list
        setShowDetailsModal(false);
      } else {
        toast.error('Failed to disconnect integration');
      }
    } catch (err) {
      console.error('Error disconnecting integration:', err);
      toast.error('Failed to disconnect integration');
    } finally {
      setDisconnecting(false);
    }
  };

  // Add rescan functionality
  const handleRescanOrg = async (integrationId, accessToken) => {
    try {
      setRescanning(true);
      const token = accessToken || localStorage.getItem('accessToken');

      const response = await api.post(`/api/integrations/${integrationId}/rescan`, {}, {
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      });

      if (response.data?.success) {
        toast.success('Integration rescan initiated successfully');
        fetchUserOrgs(); // Refresh the list
      } else {
        toast.error('Failed to initiate rescan');
      }
    } catch (err) {
      console.error('Error rescanning integration:', err);
      toast.error('Failed to initiate rescan');
    } finally {
      setRescanning(false);
    }
  };

  const filteredAccounts = accounts.filter(account => {
    // Check if account.name exists before trying to use toLowerCase
    if (!account.name) {
      console.warn('Account missing name property:', account);
      return false;
    }

    // Filter by search term
    const matchesSearch = account.name.toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by integration if set
    const matchesIntegration = integrationFilter ?
      account.integration === integrationFilter || account.source === integrationFilter :
      true;

    return matchesSearch && matchesIntegration;
  });

  console.log('Filtered accounts:', filteredAccounts);

  const handleAccountSelect = (account) => {
    setSelectedAccount(account);
  };

  const handleCloseDetails = () => {
    setSelectedAccount(null);
  };

  const toggleViewMode = () => {
    setViewMode(viewMode === 'grid' ? 'list' : 'grid');
  };

  // Function to determine health score color class
  const getHealthScoreClass = (score) => {
    if (score >= 80) return 'high';
    if (score >= 60) return 'medium';
    return 'low';
  };

  if (loading) return <div className="loading-spinner">Loading accounts...</div>;
  if (error) return <div className="error-alert">Error: {error}</div>;

  return (
    <div className="accounts-container">
      {/* Page Header */}
      <div className="page-header">
        <div>
          <h1>Accounts</h1>
          <p className="page-description">Manage your connected Salesforce organizations</p>
          {integrationFilter && (
            <div className="filter-badge">
              Filtered by: {integrationFilter}
              <button
                className="clear-filter-btn"
                onClick={() => {
                  setIntegrationFilter('');
                  window.history.pushState({}, '', '/accounts');
                }}
              >
                ×
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Action Bar */}
      <div className="action-bar">
        <div className="search-filter-container">
          <div className="search-container">
            <FiSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search accounts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          <div className="view-toggle">
            <button
              className={`view-toggle-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={toggleViewMode}
            >
              {viewMode === 'grid' ? 'Grid' : 'List'}
            </button>
          </div>
        </div>
        <button className="btn btn-primary add-account-btn">
          <FiPlus className="btn-icon" /> Add Account
        </button>
      </div>

      {/* Account Details Modal */}
      {selectedAccount && (
        <div className="account-details-modal">
          <div className="modal-overlay" onClick={handleCloseDetails}></div>
          <div className="modal-content">
            <div className="modal-header">
              <h2>{selectedAccount.name}</h2>
              <button className="close-btn" onClick={handleCloseDetails}>×</button>
            </div>
            <div className="modal-body">
              <div className="account-summary">
                <div className="health-score-circle">
                  <div className={`circle ${getHealthScoreClass(selectedAccount.healthScore)}`}>
                    <span className="score">{selectedAccount.healthScore}%</span>
                  </div>
                  <span className="score-label">Health Score</span>
                </div>
                <div className="account-info">
                  <div className="info-item">
                    <span className="info-label">Instance URL</span>
                    <span className="info-value">{selectedAccount.instanceUrl}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">Environment</span>
                    <span className="info-value">{selectedAccount.environment}</span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">Last Scan</span>
                    <span className="info-value">
                      {new Date(selectedAccount.lastScan).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="info-item">
                    <span className="info-label">Status</span>
                    <span className={`status-badge ${(selectedAccount.status || 'active').toLowerCase()}`}>
                      {selectedAccount.status === 'inactive' ? 'Inactive' : 'Active'}
                    </span>
                  </div>
                </div>
              </div>
              <div className="account-tabs">
                <button className="tab-btn active">Overview</button>
                <button className="tab-btn">Security</button>
                <button className="tab-btn">Users</button>
                <button className="tab-btn">Settings</button>
              </div>
              <div className="tab-content">
                <h3>Security Overview</h3>
                <div className="security-metrics">
                  <div className="metric-card">
                    <h4>Password Policy</h4>
                    <div className={`metric-score ${getHealthScoreClass(85)}`}>85%</div>
                  </div>
                  <div className="metric-card">
                    <h4>Session Settings</h4>
                    <div className={`metric-score ${getHealthScoreClass(70)}`}>70%</div>
                  </div>
                  <div className="metric-card">
                    <h4>Network Access</h4>
                    <div className={`metric-score ${getHealthScoreClass(90)}`}>90%</div>
                  </div>
                  <div className="metric-card">
                    <h4>User Permissions</h4>
                    <div className={`metric-score ${getHealthScoreClass(65)}`}>65%</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="btn btn-primary"
                onClick={() => handleRescanOrg(selectedAccount.integrationId)}
              >
                <FiRefreshCw className="btn-icon" /> Rescan Org
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => handleDisconnectOrg(selectedAccount.integrationId)}
              >
                <FiTrash2 className="btn-icon" /> Disconnect Org
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Accounts Display */}
      {viewMode === 'grid' ? (
        <div className="accounts-grid">
          {filteredAccounts.length === 0 ? (
            <div className="no-accounts">No accounts found matching your search.</div>
          ) : (
            filteredAccounts.map(account => (
              <div
                key={account.integrationId}
                className="account-card"
                onClick={() => handleAccountSelect(account)}
              >
                <div className="account-card-header">
                  <div className="account-name-container">
                    <h3 className="account-name">{account.name}</h3>
                    <span className={`status-indicator ${account.status ? account.status.toLowerCase() : 'active'}`}></span>
                  </div>
                  <div className="account-type">{account.environment}</div>
                </div>
                <div className="account-card-body">
                  <div className="health-score-container">
                    <div className="health-score-circle">
                      <div className={`circle ${getHealthScoreClass(account.healthScore)}`}>
                        <span className="score">{account.healthScore}%</span>
                      </div>
                    </div>
                    <div className="health-score-info">
                      <div className="health-score-label">Health Score</div>
                      <div className="last-scan">Last scan: {new Date(account.lastScan).toLocaleDateString()}</div>
                    </div>
                  </div>
                </div>
                <div className="account-card-footer">
                  <button
                    className="btn btn-icon-only"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent card selection
                      handleRescanOrg(account.integrationId);
                    }}
                    title="Rescan"
                  >
                    <FiRefreshCw />
                  </button>
                  <button
                    className="btn btn-icon-only"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent card selection
                      handleDisconnectOrg(account.integrationId);
                    }}
                    title="Disconnect"
                  >
                    <FiTrash2 />
                  </button>
                  <button
                    className="btn btn-icon-only"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent card selection
                      handleAccountSelect(account);
                    }}
                    title="View Details"
                  >
                    <FiChevronRight />
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      ) : (
        <div className="accounts-list">
          {filteredAccounts.length === 0 ? (
            <div className="no-accounts">No accounts found matching your search.</div>
          ) : (
            <table className="accounts-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Environment</th>
                  <th>Health Score</th>
                  <th>Last Scan</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredAccounts.map(account => (
                  <tr key={account.integrationId} onClick={() => handleAccountSelect(account)}>
                    <td className="account-name-cell">
                      <div className="account-name-with-indicator">
                        <span className={`status-indicator ${account.status ? account.status.toLowerCase() : 'active'}`}></span>
                        <span>{account.name}</span>
                      </div>
                    </td>
                    <td>{account.environment}</td>
                    <td>
                      <span className={`health-badge ${getHealthScoreClass(account.healthScore)}`}>
                        {account.healthScore}%
                      </span>
                    </td>
                    <td>{new Date(account.lastScan).toLocaleDateString()}</td>
                    <td>
                      <span className={`status-badge ${(account.status || 'active').toLowerCase()}`}>
                        {account.status === 'inactive' ? 'Inactive' : 'Active'}
                      </span>
                    </td>
                    <td className="actions-cell">
                      <button
                        className="btn btn-icon-only"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent row selection
                          handleRescanOrg(account.integrationId);
                        }}
                        title="Rescan"
                      >
                        <FiRefreshCw />
                      </button>
                      <button
                        className="btn btn-icon-only"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent row selection
                          handleDisconnectOrg(account.integrationId);
                        }}
                        title="Disconnect"
                      >
                        <FiTrash2 />
                      </button>
                      <button
                        className="btn btn-icon-only"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent row selection
                          handleAccountSelect(account);
                        }}
                        title="Settings"
                      >
                        <FiSettings />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      )}
    </div>
  );
};

export default Accounts;