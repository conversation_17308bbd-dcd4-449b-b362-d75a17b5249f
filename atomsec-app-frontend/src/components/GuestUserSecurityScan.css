.guest-user-security-scan {
  padding: 24px;
  font-family: 'Inter', sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  background: #F8F9FA;
  min-height: 100vh;
}

/* <PERSON><PERSON> */
.scan-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #51D59C 0%, #45B7D1 100%);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  box-shadow: 0 4px 12px rgba(81, 213, 156, 0.2);
}

.header-content h2 {
  font-family: 'Poppins', sans-serif;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
}

.header-content p {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  margin: 0;
  opacity: 0.95;
  line-height: 1.5;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.security-icon {
  font-size: 32px !important;
  color: white !important;
}

/* Ethical Guidelines */
.ethical-guidelines {
  background: #FFF3E0;
  border: 1px solid #FFB74D;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(255, 183, 77, 0.1);
}

.guidelines-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.warning-icon {
  color: #F57C00;
  font-size: 24px !important;
}

.guidelines-header h3 {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #E65100;
  margin: 0;
}

.ethical-guidelines ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ethical-guidelines li {
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #BF360C;
  margin-bottom: 12px;
  padding-left: 24px;
  position: relative;
  line-height: 1.5;
}

.ethical-guidelines li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #4CAF50;
  font-weight: bold;
  font-size: 16px;
}

/* Scan Configuration */
.scan-config {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 32px;
  margin-bottom: 24px;
  border: 1px solid #E9ECEF;
}

.url-input-section label {
  display: block;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #020A07;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.url-input-container {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  align-items: flex-start;
}

.url-input {
  flex: 1;
  padding: 16px 20px;
  border: 2px solid #E0E0E0;
  border-radius: 8px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: #020A07;
  background: #FFFFFF;
  transition: all 0.3s ease;
  min-height: 52px;
}

.url-input:focus {
  outline: none;
  border-color: #51D59C;
  box-shadow: 0 0 0 4px rgba(81, 213, 156, 0.1);
}

.url-input:disabled {
  background: #F5F5F5;
  color: #757575;
  cursor: not-allowed;
}

.scan-button {
  background: linear-gradient(135deg, #51D59C 0%, #45B7D1 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 8px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 140px;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(81, 213, 156, 0.3);
  white-space: nowrap;
}

.scan-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(81, 213, 156, 0.4);
}

.scan-button:disabled {
  background: #DEE2E6;
  color: #6C757D;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.scan-button.scanning {
  background: linear-gradient(135deg, #F57C00 0%, #FF9800 100%);
}

.button-icon {
  font-size: 18px !important;
}

.url-input-section small {
  display: block;
  font-family: 'Lato', sans-serif;
  font-size: 12px;
  color: #6C757D;
  margin-top: 8px;
  line-height: 1.4;
}

/* Scan Progress */
.scan-progress {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 32px;
  margin-bottom: 24px;
  border: 1px solid #E9ECEF;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.progress-header h3 {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #020A07;
  margin: 0;
}

.progress-percentage {
  font-family: 'Inter', sans-serif;
  font-size: 18px;
  font-weight: 700;
  color: #51D59C;
  background: rgba(81, 213, 156, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: #E0E0E0;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 16px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #51D59C 0%, #45B7D1 100%);
  border-radius: 6px;
  transition: width 0.3s ease;
}

.current-check {
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #393E3C;
  margin: 0;
  text-align: center;
  font-weight: 500;
}

/* Scan Results */
.scan-results {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 32px;
  margin-bottom: 24px;
  border: 1px solid #E9ECEF;
}

.results-header {
  margin-bottom: 32px;
}

.results-header h3 {
  font-family: 'Poppins', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #020A07;
  margin: 0 0 20px 0;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  padding: 24px;
  background: #F8F9FA;
  border-radius: 12px;
  border: 1px solid #E9ECEF;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0; /* Allow text truncation */
}

.summary-item .label {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: #6C757D;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-item .value {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #020A07;
  word-break: break-all; /* Prevent text overflow */
  line-height: 1.4;
}

/* Issues List */
.issues-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.issue-item {
  border: 1px solid #E9ECEF;
  border-radius: 12px;
  padding: 24px;
  background: #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.issue-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-color: #51D59C;
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.severity-badge {
  padding: 6px 16px;
  border-radius: 20px;
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 700;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.issue-header h4 {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #020A07;
  margin: 0;
  flex: 1;
}

.issue-description {
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #393E3C;
  line-height: 1.6;
  margin-bottom: 16px;
}

.issue-details {
  margin-bottom: 16px;
  background: #F8F9FA;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #51D59C;
}

.issue-details strong {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #020A07;
  display: block;
  margin-bottom: 8px;
}

.issue-details ul {
  margin: 0;
  padding-left: 20px;
}

.issue-details li {
  font-family: 'Lato', sans-serif;
  font-size: 13px;
  color: #393E3C;
  margin-bottom: 6px;
  line-height: 1.5;
}

.issue-recommendation {
  background: #E8F5E8;
  border: 1px solid #C8E6C9;
  border-radius: 8px;
  padding: 16px;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #2E7D32;
  line-height: 1.5;
}

.issue-recommendation strong {
  font-weight: 600;
  color: #1B5E20;
}

/* No Issues */
.no-issues {
  text-align: center;
  padding: 48px;
  background: #F8F9FA;
  border-radius: 12px;
  border: 2px dashed #C8E6C9;
}

.success-icon {
  color: #4CAF50;
  font-size: 64px !important;
  margin-bottom: 20px;
}

.no-issues p {
  font-family: 'Lato', sans-serif;
  font-size: 18px;
  color: #393E3C;
  margin: 0 0 12px 0;
  font-weight: 500;
}

.no-issues small {
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #6C757D;
  line-height: 1.5;
}

/* Scan Tips */
.scan-tips {
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 32px;
  border: 1px solid #E9ECEF;
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.info-icon {
  color: #51D59C;
  font-size: 28px !important;
}

.tips-header h3 {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #020A07;
  margin: 0;
}

.tips-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.tip-item {
  background: #F8F9FA;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #E9ECEF;
}

.tip-item strong {
  display: block;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #020A07;
  margin-bottom: 12px;
}

.tip-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tip-item li {
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #393E3C;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
  line-height: 1.5;
}

.tip-item li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #51D59C;
  font-weight: bold;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .guest-user-security-scan {
    padding: 16px;
  }

  .scan-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 24px;
  }

  .header-content h2 {
    font-size: 24px;
  }

  .url-input-container {
    flex-direction: column;
    gap: 12px;
  }

  .scan-button {
    width: 100%;
    min-width: auto;
  }

  .results-summary {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .tips-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .issue-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .severity-badge {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .scan-header {
    padding: 20px;
  }

  .header-content h2 {
    font-size: 20px;
  }

  .scan-config,
  .scan-progress,
  .scan-results,
  .scan-tips {
    padding: 20px;
  }

  .results-summary {
    padding: 16px;
  }

  .issue-item {
    padding: 16px;
  }
} 