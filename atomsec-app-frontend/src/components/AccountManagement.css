.account-management {
  padding: 20px;
}

.account-management h2 {
  margin-bottom: 20px;
  color: #333;
}

.account-management-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.accounts-section,
.users-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  margin: 0;
  color: #333;
}

.add-button {
  padding: 8px 16px;
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.add-button:hover {
  background-color: #0055aa;
}

.add-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.accounts-list,
.users-list {
  margin-top: 15px;
}

.accounts-list ul,
.users-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.accounts-list li,
.users-list li {
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: white;
  cursor: pointer;
  transition: background-color 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.accounts-list li:hover,
.users-list li:hover {
  background-color: #f0f7ff;
}

.accounts-list li.selected,
.users-list li.selected {
  background-color: #e6f2ff;
  border-left: 3px solid #0066cc;
}

.account-name {
  font-weight: 600;
  color: #333;
}

.account-status {
  float: right;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  background-color: #e6f2ff;
  color: #0066cc;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #333;
}

.user-email {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.user-roles {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.role-badge {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  background-color: #e6f2ff;
  color: #0066cc;
}

.loading-state,
.error-state,
.empty-state {
  padding: 20px;
  text-align: center;
  color: #666;
}

.error-state {
  color: #dc3545;
}

.retry-button {
  padding: 6px 12px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
  transition: background-color 0.3s ease;
}

.retry-button:hover {
  background-color: #c82333;
}

.user-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.edit-button,
.assign-role-button {
  padding: 8px 16px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.edit-button:hover,
.assign-role-button:hover {
  background-color: #5a6268;
}

.form-container {
  margin-top: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-container h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.submit-button {
  padding: 8px 16px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submit-button:hover {
  background-color: #218838;
}

.submit-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.cancel-button {
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancel-button:hover {
  background-color: #c82333;
}

@media (max-width: 768px) {
  .account-management-container {
    grid-template-columns: 1fr;
  }
}
