import React, { useState } from 'react';
import './IntegrationTabs.css';
import DataStatusMessage from './DataStatusMessage';

/**
 * PMD Issues Tab Component
 *
 * This component displays the PMD scan results for a Salesforce org,
 * showing both development and security issues found in the code.
 *
 * @param {Object} props - Component props
 * @param {Object} props.data - PMD issues data
 * @param {string} props.status - Data status (loading, error, available, pending, empty)
 * @param {Function} props.onRefresh - Function to refresh the data
 * @param {boolean} props.isRefreshing - Whether the data is currently being refreshed
 */
const PMDIssuesTab = ({ data, status, onRefresh, isRefreshing }) => {
  const [activeIssueType, setActiveIssueType] = useState('security'); // 'security' or 'development'
  const [priorityFilter, setPriorityFilter] = useState('all'); // 'all', 'high', 'medium', 'low'
  const [searchTerm, setSearchTerm] = useState('');

  // Handle loading state
  if (status === 'loading') {
    return (
      <div className="loading-state">
        <div className="spinner"></div>
        <p>Loading PMD issues data...</p>
      </div>
    );
  }

  // Handle error state
  if (status === 'error') {
    return (
      <div className="error-state">
        <div className="error-icon">⚠️</div>
        <p>{data?.message || 'Failed to load PMD issues data'}</p>
        <p>Use the Rescan button at the top to try again.</p>
      </div>
    );
  }

  // Handle pending state
  if (status === 'pending') {
    return (
      <DataStatusMessage
        status="pending"
        message="PMD issues data is being fetched in the background. This may take a few moments."
        onRefresh={() => onRefresh()}
      />
    );
  }

  // Handle empty state
  if (status === 'empty') {
    return (
      <div className="empty-state">
        <div className="empty-icon">🔍</div>
        <h3>No PMD Issues Data Available</h3>
        <p>No data was found in the database. Click the <strong>Rescan</strong> button at the top of the page to scan your Salesforce org for code issues.</p>
      </div>
    );
  }

  // If we have data, render the content
  if (data && data.dataStatus === 'available') {
    const { issues: allIssues, lastUpdated: scanTimestamp } = data;

    if (!allIssues) {
      // Handle case where issues array might be missing even if dataStatus is available
      return (
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <p>PMD issues data is available but malformed (missing 'issues' array).</p>
        </div>
      );
    }

    // Assuming each issue object has a 'category' field (e.g., 'Security', 'Code Style', 'Performance')
    // Or a 'type' field. For now, let's assume 'category'. Adjust if different.
    const securityIssues = allIssues.filter(issue => issue.category === 'Security'); // Example category
    const developmentIssues = allIssues.filter(issue => issue.category !== 'Security'); // Example: all others are dev

    // Calculate summary
    const summary = {
      totalIssues: allIssues.length,
      securityIssues: securityIssues.length,
      developmentIssues: developmentIssues.length,
      highPriorityIssues: allIssues.filter(issue => issue.priority === 1).length, // Assuming priority 1 is high
      filesWithIssues: [...new Set(allIssues.map(issue => issue.file))].length
    };

    // Get the issues based on the active tab
    const currentDisplayIssues = activeIssueType === 'security' ? securityIssues : developmentIssues;

    // Filter issues by priority
    const filteredIssues = currentDisplayIssues.filter(issue => {
      // Convert priority number to string level
      const priorityLevel = issue.priority === 1 ? 'high' : issue.priority === 2 ? 'medium' : 'low';

      // Apply priority filter
      if (priorityFilter !== 'all' && priorityFilter !== priorityLevel) {
        return false;
      }

      // Apply search filter
      if (searchTerm && !issue.file.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !issue.rule.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !issue.description.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      return true;
    });

    // Format the last updated timestamp
    const lastUpdated = new Date(scanTimestamp).toLocaleString();

    return (
      <div className="pmd-issues-content">
        <div className="pmd-issues-header">
          <h3>PMD Code Issues</h3>
        </div>

        <div className="pmd-issues-summary">
          <div className="stat-card">
            <h4>Total Issues</h4>
            <div className="stat-value">{summary.totalIssues}</div>
          </div>
          <div className="stat-card">
            <h4>Security Issues</h4>
            <div className="stat-value">{summary.securityIssues}</div>
          </div>
          <div className="stat-card">
            <h4>Development Issues</h4>
            <div className="stat-value">{summary.developmentIssues}</div>
          </div>
          <div className="stat-card">
            <h4>High Priority</h4>
            <div className="stat-value">{summary.highPriorityIssues}</div>
          </div>
          <div className="stat-card">
            <h4>Files Affected</h4>
            <div className="stat-value">{summary.filesWithIssues}</div>
          </div>
        </div>

        <div className="pmd-issues-tabs">
          <button
            className={`tab-button ${activeIssueType === 'security' ? 'active' : ''}`}
            onClick={() => setActiveIssueType('security')}
          >
            Security Issues ({securityIssues.length})
          </button>
          <button
            className={`tab-button ${activeIssueType === 'development' ? 'active' : ''}`}
            onClick={() => setActiveIssueType('development')}
          >
            Development Issues ({developmentIssues.length})
          </button>
        </div>

        <div className="pmd-issues-filters">
          <div className="search-filter">
            <input
              type="text"
              placeholder="Search issues..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          <div className="priority-filter">
            <label>Priority: </label>
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="priority-select"
            >
              <option value="all">All Priorities</option>
              <option value="high">High Priority</option>
              <option value="medium">Medium Priority</option>
              <option value="low">Low Priority</option>
            </select>
          </div>
        </div>

        <div className="pmd-issues-list">
          {filteredIssues.length > 0 ? (
            <table className="issues-table">
              <thead>
                <tr>
                  <th>File</th>
                  <th>Line</th>
                  <th>Rule</th>
                  <th>Priority</th>
                  <th>Description</th>
                </tr>
              </thead>
              <tbody>
                {filteredIssues.map((issue) => {
                  // Determine priority class
                  const priorityClass = issue.priority === 1
                    ? 'high-priority'
                    : issue.priority === 2
                      ? 'medium-priority'
                      : 'low-priority';

                  // Determine priority label
                  const priorityLabel = issue.priority === 1
                    ? 'High'
                    : issue.priority === 2
                      ? 'Medium'
                      : 'Low';

                  return (
                    <tr key={issue.id} className={priorityClass}>
                      <td>{issue.file}</td>
                      <td>{issue.line}</td>
                      <td>{issue.rule}</td>
                      <td>
                        <span className={`priority-badge ${priorityClass}`}>
                          {priorityLabel}
                        </span>
                      </td>
                      <td>{issue.description}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          ) : (
            <div className="no-issues-message">
              <p>No {activeIssueType} issues found matching the current filters.</p>
            </div>
          )}
        </div>

        <div className="last-updated">
          Last updated: {lastUpdated}
        </div>
      </div>
    );
  }

  // Fallback for any other state
  return (
    <div className="no-data-state">
      <p>No PMD issues data available. Status: {status}</p>
      <p>No data was found in the database. Click the <strong>Rescan</strong> button at the top of the page to scan your Salesforce org for code issues.</p>
    </div>
  );
};

export default PMDIssuesTab;
