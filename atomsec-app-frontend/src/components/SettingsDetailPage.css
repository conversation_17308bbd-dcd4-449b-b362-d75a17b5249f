.settings-detail-main-content {
  padding: 32px;
  background: #F8F9FA;
  min-height: 100vh;
}

.settings-detail-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0px 4px 20px 0px rgba(0,0,0,0.10);
  padding: 32px 32px 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.settings-detail-title {
  font-weight: 700;
  font-size: 24px;
  color: #181C1A;
  margin-bottom: 24px;
  text-align: left;
}

.settings-detail-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #fff;
  font-size: 15px;
  overflow: hidden;
  margin-top: 8px;
  border: 1px solid rgba(0,0,0,0.08);
  border-radius: 8px;
}
.settings-detail-table th, .settings-detail-table td {
  padding: 12px 8px 12px 16px;
  border-bottom: 1px solid rgba(0,0,0,0.08);
  border-right: 1px solid rgba(0,0,0,0.08);
  text-align: left;
}

.settings-detail-table th:last-child, .settings-detail-table td:last-child {
  border-right: none;
}
.settings-detail-table th {
  background: rgba(0,0,0,0.04);
  color: #393E3C;
  font-weight: 600;
  font-size: 15px;
  border-top: 1px solid rgba(0,0,0,0.08);
  border-left: 1px solid rgba(0,0,0,0.08);
}

.settings-detail-table th:first-child {
  border-left: none;
}

.settings-detail-table td {
  border-left: 1px solid rgba(0,0,0,0.08);
}

.settings-detail-table td:first-child {
  border-left: none;
}
.settings-detail-table tr:last-child td {
  border-bottom: none;
}
.settings-detail-empty {
  text-align: center;
  color: #888;
  padding: 32px 0;
}
.settings-detail-loading {
  color: #888;
  padding: 32px 0;
  text-align: center;
}
.settings-detail-error {
  color: #d32f2f;
  padding: 32px 0;
  text-align: center;
} 

/* Breadcrumb Navigation Styles */
.breadcrumbs-bar {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
}

.breadcrumb-link {
  background: #f8f8f8;
  border: 1px solid #e0e0e0;
  color: #393E3C;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  text-decoration: none;
}

.breadcrumb-link:hover {
  background-color: #f0f0f0;
  color: #393E3C;
} 