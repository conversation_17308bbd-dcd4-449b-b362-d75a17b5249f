import React, { useEffect, useRef } from 'react';
import { FiChevronRight } from 'react-icons/fi';
import './MenuDropdown.css';

const MenuDropdown = ({
  isOpen,
  onClose,
  onViewDetails,
  onEditIntegration,
  onScanNow,
  onDeleteIntegration,
  isActive,
  isScanning,
  isDeleting
}) => {
  const dropdownRef = useRef(null);

  useEffect(() => {
    if (!isOpen) return;
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="menu-dropdown" ref={dropdownRef}>
      <button className="menu-item" onClick={onViewDetails}>
        <span>View Details</span>
        <FiChevronRight className="menu-icon" />
      </button>
      <button className="menu-item" onClick={onEditIntegration}>
        <span>Edit Integration</span>
        <FiChevronRight className="menu-icon" />
      </button>
      {isActive && (
        <button
          className="menu-item"
          onClick={onScanNow}
          disabled={isScanning}
        >
          <span>{isScanning ? 'Scanning...' : 'Scan Now'}</span>
          <FiChevronRight className="menu-icon" />
        </button>
      )}

      <button
        className="menu-item danger"
        onClick={onDeleteIntegration}
        disabled={isDeleting}
      >
        <span>{isDeleting ? 'Deleting...' : 'Delete Integration'}</span>
        <FiChevronRight className="menu-icon" />
      </button>
    </div>
  );
};

export default MenuDropdown;
