import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { toast } from 'react-toastify';
import './Dashboard.css';

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalScans: 0,
    completedScans: 0,
    pendingScans: 0,
    averageScore: 0
  });

  const [userOrgs, setUserOrgs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quickViewOrg, setQuickViewOrg] = useState(null);
  const [showQuickView, setShowQuickView] = useState(false);

  const fetchUserOrgs = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch orgs from the server
      const response = await axios.get('/api/user-orgs', {
        withCredentials: true
      });

      console.log('Fetched orgs:', response.data);

      const orgs = response.data.orgs || [];
      setUserOrgs(orgs);

      // Calculate stats based on orgs
      if (orgs.length > 0) {
        const completedScans = orgs.length;
        const totalScores = orgs.reduce((sum, org) => sum + parseInt(org.healthScore || 0), 0);
        const avgScore = completedScans > 0 ? Math.round(totalScores / completedScans) : 0;

        setStats({
          totalScans: completedScans,
          completedScans: completedScans,
          pendingScans: 0,
          averageScore: avgScore
        });
      }

      setLoading(false);
    } catch (err) {
      console.error('Error fetching user orgs:', err);
      setError('Failed to load connected Salesforce orgs');
      setLoading(false);

      // Fallback to mock data if API fails
      setStats({
        totalScans: 0,
        completedScans: 0,
        pendingScans: 0,
        averageScore: 0
      });
    }
  };

  const handleQuickView = (org) => {
    setQuickViewOrg(org);
    setShowQuickView(true);
  };

  const handleCloseQuickView = () => {
    setShowQuickView(false);
    setQuickViewOrg(null);
  };

  const handleRescan = async (org) => {
    try {
      setLoading(true);

      // Get the access token from localStorage if available
      const token = localStorage.getItem('sf_access_token');

      const response = await axios.post('/api/rescan-org', {
        instanceUrl: org.instanceUrl,
        accessToken: token
      }, {
        withCredentials: true
      });

      toast.success(`Org rescanned successfully. New health score: ${response.data.healthScore}%`);
      fetchUserOrgs(); // Refresh the list
    } catch (err) {
      console.error('Error rescanning org:', err);
      toast.error('Failed to rescan org: ' + (err.response?.data?.error || err.message));
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserOrgs();
  }, []);

  if (loading) return <div className="loading-spinner">Loading dashboard data...</div>;
  if (error) return <div className="error-alert">{error}</div>;

  return (
    <div className="dashboard">
      <div className="page-header">
        <h1 className="page-title">Dashboard</h1>
        <p className="page-description">Overview of your Salesforce orgs and security metrics</p>
      </div>

      <div className="stats-container">
        <div className="stat-card">
          <div className="stat-icon total-scans">📊</div>
          <div className="stat-content">
            <h3 className="stat-value">{stats.totalScans}</h3>
            <p className="stat-label">Connected Orgs</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon completed-scans">✅</div>
          <div className="stat-content">
            <h3 className="stat-value">{stats.completedScans}</h3>
            <p className="stat-label">Scanned Orgs</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon pending-scans">⏳</div>
          <div className="stat-content">
            <h3 className="stat-value">{stats.pendingScans}</h3>
            <p className="stat-label">Pending Scans</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon average-score">🏆</div>
          <div className="stat-content">
            <h3 className="stat-value">{stats.averageScore}%</h3>
            <p className="stat-label">Avg. Health Score</p>
          </div>
        </div>
      </div>

      <div className="card recent-scans">
        <div className="card-header">
          <h2 className="card-title">Connected Salesforce Orgs</h2>
          <Link to="/scan" className="btn btn-primary">Connect New Org</Link>
        </div>

        {userOrgs.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">🔍</div>
            <h3>No Salesforce Orgs Connected</h3>
            <p>Connect your first Salesforce org to see its health score and security metrics.</p>
            <Link to="/scan" className="btn btn-primary">Connect Org Now</Link>
          </div>
        ) : (
          <>
            <div className="scans-table-container">
              <table className="scans-table">
                <thead>
                  <tr>
                    <th>Org Name</th>
                    <th>Environment</th>
                    <th>Last Scan</th>
                    <th>Health Score</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {userOrgs.map((org, index) => {
                    // Format the date
                    const scanDate = new Date(org.lastScan);
                    const formattedDate = scanDate.toLocaleDateString();

                    return (
                      <tr key={index}>
                        <td>{org.name}</td>
                        <td>{org.environment}</td>
                        <td>{formattedDate}</td>
                        <td>
                          <div className="score-pill">
                            <span
                              className={
                                parseInt(org.healthScore) >= 80 ? 'high' :
                                parseInt(org.healthScore) >= 60 ? 'medium' : 'low'
                              }
                            >
                              {org.healthScore}%
                            </span>
                          </div>
                        </td>
                        <td>
                          <div className="action-buttons">
                            <button
                              className="action-button view"
                              onClick={(e) => {
                                e.preventDefault();
                                handleQuickView(org);
                              }}
                            >
                              Quick View
                            </button>
                            <button
                              className="action-button scan"
                              onClick={(e) => {
                                e.preventDefault();
                                handleRescan(org);
                              }}
                            >
                              Rescan
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            <div className="card-footer">
              <Link to="/accounts" className="view-all-link">
                View All Orgs →
              </Link>
            </div>
          </>
        )}
      </div>

      {userOrgs.length > 0 && (
        <div className="card health-summary">
          <div className="card-header">
            <h2 className="card-title">Health Score Summary</h2>
          </div>
          <div className="health-summary-content">
            <div className="health-chart">
              {/* Simple visual representation of health scores */}
              <div className="chart-bars">
                {userOrgs.map((org, index) => (
                  <div key={index} className="chart-bar-container">
                    <div
                      className={`chart-bar ${parseInt(org.healthScore) >= 80 ? 'high' : parseInt(org.healthScore) >= 60 ? 'medium' : 'low'}`}
                      style={{ height: `${Math.max(20, parseInt(org.healthScore))}px` }}
                      data-score={`${org.healthScore}%`}
                    ></div>
                    <div className="chart-label">{org.name.substring(0, 10)}{org.name.length > 10 ? '...' : ''}</div>
                  </div>
                ))}
              </div>
            </div>
            <div className="health-summary-legend">
              <div className="legend-item">
                <span className="legend-color high"></span>
                <span className="legend-text">Good (80-100%)</span>
              </div>
              <div className="legend-item">
                <span className="legend-color medium"></span>
                <span className="legend-text">Moderate (60-79%)</span>
              </div>
              <div className="legend-item">
                <span className="legend-color low"></span>
                <span className="legend-text">Poor (0-59%)</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick View Popup */}
      {showQuickView && quickViewOrg && (
        <div className="quick-view-overlay" onClick={handleCloseQuickView}>
          <div className="quick-view-popup" onClick={(e) => e.stopPropagation()}>
            <div className="quick-view-header">
              <h3>{quickViewOrg.name}</h3>
              <button className="close-btn" onClick={handleCloseQuickView}>×</button>
            </div>
            <div className="quick-view-content">
              <div className="detail-item">
                <span className="detail-label">Instance URL:</span>
                <span className="detail-value">{quickViewOrg.instanceUrl}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Environment:</span>
                <span className="detail-value">{quickViewOrg.environment}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Health Score:</span>
                <span className={`health-badge ${parseInt(quickViewOrg.healthScore) >= 80 ? 'high' : parseInt(quickViewOrg.healthScore) >= 60 ? 'medium' : 'low'}`}>
                  {quickViewOrg.healthScore}%
                </span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Last Scan:</span>
                <span className="detail-value">{new Date(quickViewOrg.lastScan).toLocaleDateString()}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Status:</span>
                <span className={`status-badge ${quickViewOrg.status || 'active'}`}>
                  {quickViewOrg.status === 'inactive' ? 'Inactive' : 'Active'}
                </span>
              </div>
            </div>
            <div className="quick-view-footer">
              <button
                className="btn btn-primary"
                onClick={() => {
                  handleCloseQuickView();
                  window.location.href = '/accounts';
                }}
              >
                View All Details
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
