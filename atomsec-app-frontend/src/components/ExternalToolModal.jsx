import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import {
  Close,
  Launch,
  Security,
  ArrowBack
} from '@mui/icons-material';
import './ExternalToolModal.css';

const ExternalToolModal = ({ 
  isOpen, 
  toolName, 
  toolUrl, 
  onClose, 
  onNavigateToDashboard 
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (isOpen && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isOpen, countdown]);

  const handleClose = () => {
    onClose();
    setCountdown(5); // Reset countdown
  };

  const handleNavigateToDashboard = () => {
    onNavigateToDashboard();
    navigate('/dashboard');
    handleClose();
  };

  const handleOpenInNewTab = () => {
    window.open(toolUrl, '_blank', 'noopener,noreferrer');
    handleClose();
  };

  if (!isOpen) return null;

  return (
    <div className="external-tool-modal-overlay">
      <div className="external-tool-modal">
        <div className="modal-header">
          <div className="modal-title">
            <Launch className="modal-icon" />
            <h2>External Tool Opened</h2>
          </div>
          <button 
            className="close-button"
            onClick={handleClose}
            aria-label="Close modal"
          >
            <Close />
          </button>
        </div>

        <div className="modal-content">
          <div className="tool-info">
            <h3>{toolName}</h3>
            <p>
              {toolName} has been opened in a new tab. You can continue working in that tab 
              or return to the AtomSec platform.
            </p>
          </div>

          <div className="user-info">
            <p>
              <strong>Logged in as:</strong> {user?.name || user?.email}
            </p>
          </div>

          <div className="action-buttons">
            <button 
              className="btn btn-primary"
              onClick={handleOpenInNewTab}
            >
              <Launch />
              Open {toolName} Again
            </button>
            
            <button 
              className="btn btn-secondary"
              onClick={handleNavigateToDashboard}
            >
              <Security />
              Go to Attack Surface Dashboard
            </button>
          </div>

          <div className="auto-close-info">
            <p>
              This window will automatically close in <strong>{countdown}</strong> seconds, 
              or you can close it manually.
            </p>
          </div>
        </div>

        <div className="modal-footer">
          <button 
            className="btn btn-outline"
            onClick={handleClose}
          >
            <ArrowBack />
            Close This Window
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExternalToolModal; 