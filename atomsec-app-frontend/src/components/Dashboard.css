.dashboard {
  animation: fadeIn var(--transition-normal);
  padding: 32px;
  font-family: 'Inter', sans-serif;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 24px;
  color: #020A07;
  margin: 0 0 8px 0;
}

.page-description {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  color: #393E3C;
  margin: 0;
}

/* Stats Cards */
.stats-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 24px;
}

.data-card {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  padding: 24px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.data-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.1);
}

.data-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.data-card-title {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 18px;
  color: #020A07;
  margin: 0;
}

.navigate-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.data-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.value-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.data-card-value {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 32px;
  color: #020A07;
  margin: 0;
}

.trend-badge {
  display: flex;
  align-items: center;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 12px;
  gap: 4px;
}

.trend-badge.up {
  background-color: #ED6C02;
  color: #ED6C02;
}

.trend-badge.down {
  background-color: #2E7D32;
  color: #2E7D32;
}

.trend-badge .icon {
  width: 16px;
  height: 16px;
}

.refresh-button {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #020A07;
  cursor: pointer;
  padding: 8px 0;
}

.refresh-button:hover {
  opacity: 0.8;
}

.refresh-button .icon {
  width: 16px;
  height: 16px;
}

/* Dashboard Content */
.dashboard-content {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 19px;
  margin-bottom: 24px;
}

.dashboard-left-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboard-right-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Severity Chart Card */
.severity-chart-card {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.severity-chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px 6px 24px rgba(0, 0, 0, 0.12);
}

.severity-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.severity-chart-title {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 18px;
  color: #020A07;
  margin: 0;
}

.severity-chart-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.severity-chart-menu-btn {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.severity-chart-tabs {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px;
  border: 1px solid #B8D8CB;
  border-radius: 64px;
  margin-top: 8px;
}

.severity-chart-tab {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.6em;
  padding: 4px 16px;
  border-radius: 999px;
  border: none;
  background-color: rgba(0, 0, 0, 0.08);
  color: #020A07;
  cursor: pointer;
  transition: all 0.2s ease;
}

.severity-chart-tab.active {
  background-color: #000000;
  color: #FFFFFF;
}

.severity-chart-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 16px;
}

.severity-chart-visualization {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.severity-chart-pie {
  width: 200px;
  height: 200px;
  position: relative;
}

/* Pie chart container */
.pie-chart-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Pie segments container */
.pie-segments {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  overflow: hidden;
}

/* Individual pie segments */
.pie-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  transform-origin: 50% 50%;
}

.pie-segment.high {
  background-color: #ED6C02;
}

.pie-segment.medium {
  background-color: #FFB400;
}

.pie-segment.low {
  background-color: #51D59C;
}

.pie-segment.empty {
  background-color: #E0E0E0;
  border-radius: 50%;
}

/* Center text */
.pie-center-text {
  position: relative;
  z-index: 2;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 24px;
  line-height: 1.2;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
}

.severity-chart-values {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-top: 16px;
}

.severity-chart-value {
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #323232;
}

.severity-chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.severity-legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.severity-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 100px;
}

.severity-legend-color.high {
  background-color: #ED6C02;
}

.severity-legend-color.medium {
  background-color: #FFB400;
}

.severity-legend-color.low {
  background-color: #51D59C;
}

.severity-legend-label {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.6em;
  letter-spacing: 8%;
  text-transform: uppercase;
  color: #020A07;
}

/* Integrations Card */
.integrations-card {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Table Header */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
}

.table-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 20px;
  color: #020A07;
  margin: 0;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  width: 280px;
  height: 42px;
  position: relative;
}

.search-input {
  border: none;
  outline: none;
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  width: 100%;
  color: #020A07;
  background: transparent;
}

.search-input::placeholder {
  color: rgba(0, 0, 0, 0.26);
}

.search-input:focus::placeholder {
  color: transparent;
}

.search-icon {
  color: #51D59C;
  width: 16px;
  height: 16px;
}

.add-integration-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 24px;
  background-color: #51D59C;
  border-radius: 4px;
  border: none;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #020A07;
  cursor: pointer;
}

.add-integration-btn:hover {
  opacity: 0.9;
}

/* Integrations Table */
.integrations-table {
  width: 100%;
  border-collapse: collapse;
}

.integrations-table th {
  background-color: #F1FCF7;
  padding: 14px 8px 14px 16px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #393E3C;
  text-align: left;
  border-top: 1px solid #B8D8CB;
  border-bottom: 1px solid #B8D8CB;
}

.integrations-table th:first-child {
  width: 100px;
}

.integrations-table th:nth-child(2) {
  width: 320px;
}

.integrations-table th:nth-child(4) {
  width: 180px;
}

.integrations-table td {
  padding: 14px 8px 14px 16px;
  border-bottom: 1px solid #B8D8CB;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #666666;
}

.integrations-table td.org-name {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #020A07;
}

.table-score-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.score-ring {
  position: relative;
  width: 32px;
  height: 32px;
}

.score-ring-base {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid #DDDBDA;
  border-radius: 50%;
  box-sizing: border-box;
}

.score-ring-progress {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid #4BCA81;
  border-radius: 50%;
  box-sizing: border-box;
  clip: rect(0, 32px, 32px, 16px);
}

.score-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  font-size: 13px;
  color: #3E3E3C;
}

.status-badge {
  display: inline-block;
  padding: 4px 16px;
  border-radius: 4px;
  background-color: rgba(237, 108, 2, 0.1);
  font-weight: 500;
  font-size: 12px;
  color: #ED6C02;
  text-align: center;
}

.status-badge.active {
  background-color: rgba(81, 213, 156, 0.1);
  color: #51D59C;
}

.status-badge.inactive {
  background-color: rgba(194, 57, 52, 0.1);
  color: #C23934;
}

.rescan-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: none;
  border: none;
  padding: 0;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #155B55;
  cursor: pointer;
  white-space: nowrap;
  text-decoration: none;
}

.rescan-button:hover {
  text-decoration: underline;
}

.actions-cell {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  width: 100%;
}

.more-actions {
  cursor: pointer;
  width: 16px;
  height: 16px;
}

.view-details-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 0;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #155B55;
  cursor: pointer;
  white-space: nowrap;
  text-decoration: none;
}

.view-details-button:hover {
  text-decoration: underline;
}

/* PMD Issues Card */
.pmd-issues-card {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Selected Organization Info */
.selected-org-info {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.selected-org-info h3 {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #020A07;
  margin: 0 0 12px 0;
}

.selected-org-info p {
  margin: 0 0 8px 0;
  color: #393E3C;
}

.selected-org-info .org-url {
  font-family: monospace;
  font-size: 14px;
  color: #155B55;
  word-break: break-all;
}

/* PMD Issues Summary */
.pmd-issues-summary {
  padding: 24px;
}

.pmd-stats-row {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 16px;
}

.pmd-stat-card {
  flex: 1;
  background-color: #F8F9FA;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.pmd-stat-card h4 {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #393E3C;
  margin: 0 0 8px 0;
}

.pmd-stat-value {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 24px;
  color: #020A07;
}

.pmd-stat-value.high {
  color: #ED6C02;
}

.pmd-stat-value.medium {
  color: #FFB400;
}

.pmd-stat-value.low {
  color: #51D59C;
}

.pmd-issues-footer {
  margin-top: 24px;
  text-align: center;
}

.view-all-link {
  color: #155B55;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.view-all-link:hover {
  text-decoration: underline;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #155B55;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Empty State */
.empty-state {
  padding: var(--spacing-xl, 32px);
  text-align: center;
  color: #393E3C;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #B8D8CB;
}

.empty-state h3 {
  margin-bottom: 16px;
  color: #020A07;
  font-weight: 600;
}

.empty-state p {
  margin-bottom: 24px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.btn {
  display: inline-block;
  padding: 8px 24px;
  border-radius: 4px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 16px;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-primary {
  background-color: #51D59C;
  color: #020A07;
  border: none;
}

.btn-primary:hover {
  background-color: #41c58b;
}

.btn-secondary {
  background-color: transparent;
  color: #155B55;
  border: 1px solid #155B55;
}

.btn-secondary:hover {
  background-color: rgba(21, 91, 85, 0.05);
}

/* Card Footer */
.card-footer {
  padding: 16px;
  text-align: center;
  border-top: 1px solid #E0E0E0;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .dashboard-left-column {
    order: 2;
  }

  .dashboard-right-column {
    order: 1;
  }
}

@media (max-width: 992px) {
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .pmd-stats-row {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .add-integration-btn {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .stats-container {
    grid-template-columns: 1fr;
  }
}

/* --- Figma-inspired Connected Orgs Section --- */
.integrations-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  gap: 24px;
}

.integrations-header-title {
  display: flex;
  align-items: center;
}

.integrations-header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.integrations-table-wrapper {
  padding: 0 24px 24px 24px;
}
/* --- End Figma-inspired Connected Orgs Section --- */

/* --- All Scans Section --- */
.all-scans-section {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.all-scans-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  gap: 24px;
}

.all-scans-title h3 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 20px;
  color: #020A07;
  margin: 0;
}

.all-scans-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.all-scans-table-container {
  padding: 0 24px 24px 24px;
}

@media (max-width: 768px) {
  .all-scans-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .all-scans-actions {
    width: 100%;
    flex-direction: column;
    align-items: stretch;
  }

  .search-input-container {
    width: 100%;
  }
}
