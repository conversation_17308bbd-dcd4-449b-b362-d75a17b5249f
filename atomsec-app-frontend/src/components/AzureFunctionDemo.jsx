import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { callProtectedApi, getUserProfile } from '../utils/azureFunctionApi';

const AzureFunctionDemo = () => {
  const { isAuthenticated, user } = useAuth();
  const [apiData, setApiData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Function to call a protected Azure Function API
  const callApi = async () => {
    if (!isAuthenticated) {
      setError('You must be logged in to call the API');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Call your Azure Function API
      // Using the standardized API prefix structure
      const data = await callProtectedApi('/api/test');
      setApiData(data);
    } catch (error) {
      console.error('Error calling Azure Function API:', error);
      setError('Failed to call the API. See console for details.');
    } finally {
      setLoading(false);
    }
  };

  // Function to get user profile from Azure Function
  const getProfile = async () => {
    if (!isAuthenticated) {
      setError('You must be logged in to get your profile');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Call the user profile API
      const profile = await getUserProfile();
      setApiData(profile);
    } catch (error) {
      console.error('Error getting user profile:', error);
      setError('Failed to get user profile. See console for details.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="azure-function-demo">
      <h2>Azure Function API Demo</h2>

      {isAuthenticated ? (
        <div>
          <p>You are logged in as: {user?.name || user?.username}</p>

          <div className="button-group" style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
            <button
              onClick={callApi}
              disabled={loading}
              className="btn btn-primary"
            >
              {loading ? 'Loading...' : 'Call Azure Function API'}
            </button>

            <button
              onClick={getProfile}
              disabled={loading}
              className="btn btn-secondary"
            >
              {loading ? 'Loading...' : 'Get User Profile'}
            </button>
          </div>

          {error && (
            <div className="alert alert-danger">
              {error}
            </div>
          )}

          {apiData && (
            <div className="api-response">
              <h3>API Response:</h3>
              <pre style={{
                backgroundColor: '#f8f9fa',
                padding: '15px',
                borderRadius: '5px',
                maxHeight: '300px',
                overflow: 'auto'
              }}>
                {JSON.stringify(apiData, null, 2)}
              </pre>
            </div>
          )}
        </div>
      ) : (
        <div className="alert alert-warning">
          Please log in to use the Azure Function API.
        </div>
      )}
    </div>
  );
};

export default AzureFunctionDemo;
