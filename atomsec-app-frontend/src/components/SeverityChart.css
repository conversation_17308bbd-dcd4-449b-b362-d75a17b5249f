.severity-chart-card {
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.severity-chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px 6px 24px rgba(0, 0, 0, 0.12);
}

.severity-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.severity-chart-title {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 18px;
  line-height: 1.6;
  color: #020A07;
  margin: 0;
}

.severity-chart-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.severity-chart-menu-btn,
.severity-chart-refresh-btn {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.severity-chart-refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Rotation animation for refresh icon */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotating {
  animation: rotate 1.5s linear infinite;
}

.severity-chart-tabs {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px;
  border: 1px solid #B8D8CB;
  border-radius: 64px;
  margin-top: 8px;
}

.severity-chart-tab {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.6em;
  padding: 4px 16px;
  border-radius: 999px;
  border: none;
  background-color: rgba(0, 0, 0, 0.08);
  color: #020A07;
  cursor: pointer;
  transition: all 0.2s ease;
}

.severity-chart-tab.active {
  background-color: #000000;
  color: #FFFFFF;
}

.severity-chart-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 16px;
}

.severity-chart-visualization {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.severity-chart-pie {
  width: 200px;
  height: 200px;
  position: relative;
}

/* Pie chart container */
.pie-chart-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Custom Pie Chart */
.pie-chart-custom {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-chart-circle {
  width: 180px;
  height: 180px;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
}

/* SVG Pie Chart */
.pie-chart-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
  border-radius: 50%;
  overflow: visible;
}

/* SVG Pie Segments */
.pie-segment-svg {
  stroke-width: 0;
}

.pie-segment-svg.high {
  fill: #ED6C02;
}

.pie-segment-svg.medium {
  fill: #FFB400;
}

.pie-segment-svg.low {
  fill: #51D59C;
}

/* Center circle */
.pie-center-circle {
  position: absolute;
  width: 80px;
  height: 80px;
  background-color: white;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

/* Segment labels */
.pie-segment-label {
  position: absolute;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #FFFFFF;
  z-index: 3;
}

/* Empty pie chart */
.pie-segment.empty {
  width: 100%;
  height: 100%;
  background-color: #E0E0E0;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Loading state */
.pie-chart-loading {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.pie-chart-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #51D59C;
  animation: rotate 1s linear infinite;
  margin-bottom: 10px;
}

.pie-chart-loading-text {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #393E3C;
}

/* Center text */
.pie-center-text {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 24px;
  line-height: 1.2;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
}

/* Removed severity-chart-values styles as they are no longer needed */

.severity-chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.severity-legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.severity-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 100px;
}

.severity-legend-color.high {
  background-color: #ED6C02;
}

.severity-legend-color.medium {
  background-color: #FFB400;
}

.severity-legend-color.low {
  background-color: #51D59C;
}

.severity-legend-label {
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.6em;
  letter-spacing: 8%;
  text-transform: uppercase;
  color: #020A07;
}
