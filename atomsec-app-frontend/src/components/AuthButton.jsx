import React from 'react';
import { API_CONFIG } from '../config';
import { API_BASE_URL, authEndpoints } from '../authConfig';

const AuthButton = ({ environment }) => {
  const handleAuth = async () => {
    try {
      // Initiate auth flow with selected environment
      const url = `${authEndpoints.login}?environment=${environment}`;

      // Redirect to the auth URL
      window.location.href = url;
    } catch (error) {
      console.error('Auth initiation error:', error);
      alert('Failed to initiate authentication');
    }
  };

  return (
    <button
      onClick={handleAuth}
      className="btn btn-primary"
    >
      Authorize & Scan
    </button>
  );
};

export default AuthButton; 