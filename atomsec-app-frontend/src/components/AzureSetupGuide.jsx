import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

const AzureSetupGuide = () => {
  const [setupInstructions, setSetupInstructions] = useState('');

  useEffect(() => {
    // Fetch the AZURE_AD_SETUP.md file
    fetch('/AZURE_AD_SETUP.md')
      .then(response => response.text())
      .then(text => {
        setSetupInstructions(text);
      })
      .catch(error => {
        console.error('Error fetching setup instructions:', error);
        setSetupInstructions('Error loading setup instructions. Please check the AZURE_AD_SETUP.md file in the project root.');
      });
  }, []);

  return (
    <div className="azure-setup-guide" style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Azure AD Application Setup Guide</h1>

      <div className="alert alert-warning" style={{
        padding: '15px',
        backgroundColor: '#fcf8e3',
        border: '1px solid #faebcc',
        borderRadius: '4px',
        color: '#8a6d3b',
        marginBottom: '20px'
      }}>
        <strong>Configuration Required:</strong> Your Azure AD application needs to be configured as a Single-Page Application (SPA) for authentication to work properly.
      </div>

      <div className="setup-instructions" style={{
        whiteSpace: 'pre-wrap',
        backgroundColor: '#f8f9fa',
        padding: '15px',
        border: '1px solid #e9ecef',
        borderRadius: '4px',
        fontFamily: 'monospace',
        lineHeight: '1.5'
      }}>
        {setupInstructions}
      </div>

      <div className="actions" style={{ marginTop: '20px' }}>
        <Link to="/auth-test" style={{
          display: 'inline-block',
          padding: '10px 20px',
          backgroundColor: '#5cb85c',
          color: 'white',
          textDecoration: 'none',
          borderRadius: '4px',
          marginRight: '10px'
        }}>
          Test Authentication
        </Link>

        <button onClick={() => window.location.reload()} style={{
          padding: '10px 20px',
          backgroundColor: '#f0ad4e',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}>
          Reload Application
        </button>
      </div>
    </div>
  );
};

export default AzureSetupGuide;
