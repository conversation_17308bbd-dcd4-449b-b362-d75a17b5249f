.security-assessment {
  padding: 32px;
  font-family: 'Inter', sans-serif;
  max-width: 1200px;
  margin: 0 auto;
}

.assessment-header {
  text-align: center;
  margin-bottom: 32px;
}

.assessment-header h1 {
  font-family: 'Poppins', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #020A07;
  margin-bottom: 8px;
}

.assessment-header p {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  color: #393E3C;
  margin: 0;
}

/* Ethical Guidelines */
.ethical-guidelines {
  background: #FFF3E0;
  border: 1px solid #FFB74D;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 32px;
}

.guidelines-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.warning-icon {
  color: #F57C00;
  font-size: 24px !important;
}

.guidelines-header h3 {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #E65100;
  margin: 0;
}

.ethical-guidelines ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ethical-guidelines li {
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #BF360C;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.ethical-guidelines li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #4CAF50;
  font-weight: bold;
}

/* Assessment Configuration */
.assessment-config {
  background: #FFFFFF;
  border-radius: 8px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  padding: 32px;
  margin-bottom: 32px;
}

.assessment-config h2 {
  font-family: 'Poppins', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #020A07;
  margin-bottom: 24px;
}

.config-section {
  margin-bottom: 24px;
}

.config-section label {
  display: block;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #020A07;
  margin-bottom: 8px;
}

.config-section input,
.config-section select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #DEE2E6;
  border-radius: 4px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: #020A07;
  background: #FFFFFF;
  transition: border-color 0.2s ease;
}

.config-section input:focus,
.config-section select:focus {
  outline: none;
  border-color: #51D59C;
  box-shadow: 0 0 0 3px rgba(81, 213, 156, 0.1);
}

.config-section small {
  display: block;
  font-family: 'Lato', sans-serif;
  font-size: 12px;
  color: #6C757D;
  margin-top: 4px;
}

.start-assessment-btn {
  background: #51D59C;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.start-assessment-btn:hover:not(:disabled) {
  background: #45B7D1;
  transform: translateY(-1px);
}

.start-assessment-btn:disabled {
  background: #DEE2E6;
  color: #6C757D;
  cursor: not-allowed;
  transform: none;
}

/* Scan Results */
.scan-results {
  background: #FFFFFF;
  border-radius: 8px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  padding: 32px;
  margin-bottom: 32px;
}

.scan-results h2 {
  font-family: 'Poppins', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #020A07;
  margin-bottom: 24px;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: #F8F9FA;
  border-radius: 8px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.summary-item .label {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: #6C757D;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-item .value {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #020A07;
}

/* Issues List */
.issues-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.issue-item {
  border: 1px solid #E9ECEF;
  border-radius: 8px;
  padding: 20px;
  background: #FFFFFF;
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.severity-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.issue-header h4 {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #020A07;
  margin: 0;
}

.issue-description {
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #393E3C;
  line-height: 1.6;
  margin-bottom: 12px;
}

.issue-recommendation {
  background: #E8F5E8;
  border: 1px solid #C8E6C9;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  color: #2E7D32;
}

/* No Issues */
.no-issues {
  text-align: center;
  padding: 40px;
  background: #F8F9FA;
  border-radius: 8px;
}

.success-icon {
  color: #4CAF50;
  font-size: 48px !important;
  margin-bottom: 16px;
}

.no-issues p {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  color: #393E3C;
  margin: 0;
}

/* Scan History */
.scan-history {
  background: #FFFFFF;
  border-radius: 8px;
  box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
  padding: 32px;
}

.scan-history h2 {
  font-family: 'Poppins', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #020A07;
  margin-bottom: 24px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  border: 1px solid #E9ECEF;
  border-radius: 8px;
  padding: 16px;
  background: #F8F9FA;
  transition: all 0.2s ease;
}

.history-item:hover {
  border-color: #51D59C;
  background: #FFFFFF;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.target-url {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #020A07;
}

.scan-date {
  font-family: 'Lato', sans-serif;
  font-size: 12px;
  color: #6C757D;
}

.history-details {
  display: flex;
  gap: 16px;
}

.scan-type,
.issues-count {
  font-family: 'Lato', sans-serif;
  font-size: 12px;
  color: #6C757D;
}

/* Responsive Design */
@media (max-width: 768px) {
  .security-assessment {
    padding: 16px;
  }

  .assessment-header h1 {
    font-size: 24px;
  }

  .assessment-config,
  .scan-results,
  .scan-history {
    padding: 20px;
  }

  .results-summary {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .history-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .history-details {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .assessment-header h1 {
    font-size: 20px;
  }

  .assessment-config h2,
  .scan-results h2,
  .scan-history h2 {
    font-size: 20px;
  }

  .config-section input,
  .config-section select {
    padding: 10px 12px;
  }
} 