import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './Login.css';
import logo from '../assets/logo.svg';
import AuthDebugInfo from './AuthDebugInfo';

const Login = () => {
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const { isAuthenticated, login, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the intended destination from the location state or default to tools
  const from = location.state?.from?.pathname || '/tools';

  // Redirect to tools if already authenticated
  useEffect(() => {
    if (isAuthenticated && !loading) {
      console.log('User is authenticated, redirecting to:', from);
      navigate(from);
    }
  }, [isAuthenticated, loading, navigate, from]);

  // Handle Azure AD login with redirect - using useCallback to prevent unnecessary re-renders
  const handleAzureLoginRedirect = useCallback(async () => {
    setError(null);
    setIsLoading(true);

    try {
      console.log('Starting Azure login process with redirect, path:', from);

      // Save the current path to localStorage
      localStorage.setItem('loginRedirectPath', from);

      // Set auth in progress flag
      localStorage.setItem('authInProgress', 'true');

      // Directly call login with immediate redirect
      login(from);

      // The redirect will happen automatically
      console.log('Login redirect called');

    } catch (error) {
      console.error('Azure login redirect error:', error);
      setError('Failed to sign in with Microsoft. Please try again.');
      setIsLoading(false);
      localStorage.removeItem('authInProgress');
    }
  }, [from, login, setError, setIsLoading]);

  return (
    <div className="login-container">
      {/* Debug info - remove in production */}
      {process.env.NODE_ENV === 'development' && <AuthDebugInfo />}

      <div className="login-left">
        <div className="login-logo">
        <img src={logo} alt="Atom Security Logo" className="logo" />
        </div>
        <div className="login-card">
          <div className="login-header">
            <h1 className="login-title">Welcome Back!</h1>
            <p className="login-subtitle">Sign in to your account to continue</p>
          </div>

          <div className="login-form">
            {error && (
              <div className="error-message">
                <span className="error-icon">⚠️</span>
                {error}
              </div>
            )}

            <button
              type="button"
              className="login-button azure-button"
              onClick={(e) => { e.preventDefault(); handleAzureLoginRedirect(); }}
              disabled={isLoading || loading}
            >
              {isLoading || loading ? 'Signing in...' : 'Sign in with Microsoft'}
            </button>
          </div>
        </div>
      </div>

      <div className="login-right">
        <h2 className="right-title">Secure your <br /><span className="brand-color">Digital World.</span></h2>
        <div className="features-grid">
          <div className="feature-card">
            <span className="feature-plus">+</span>
            <p className="feature-text">Comprehensive Security Planning</p>
          </div>
          <div className="feature-card">
            <span className="feature-plus">+</span>
            <p className="feature-text">Detailed Vulnerability Reports</p>
          </div>
          <div className="feature-card">
            <span className="feature-plus">+</span>
            <p className="feature-text">Guided Remediation Steps</p>
          </div>
          <div className="feature-card">
            <span className="feature-plus">+</span>
            <p className="feature-text">Compliance Monitoring</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;