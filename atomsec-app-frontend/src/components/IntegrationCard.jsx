import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiX } from 'react-icons/fi';
import { toast } from 'react-toastify';
import { deleteIntegration, scanIntegration } from '../api';
import IntegrationDetailsModal from './IntegrationDetailsModal';
import MenuDropdown from './MenuDropdown';
import './Integrations.css';

const IntegrationCard = ({ integration, viewMode, onScan, onClose, isActive = false, onIntegrationDeleted }) => {
  const navigate = useNavigate();
  const [menuOpen, setMenuOpen] = useState(false);
  const [isScanActive, setIsScanActive] = useState(isActive);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isScanning, setIsScanning] = useState(false);

  const [menuPosition, setMenuPosition] = useState(null);
  const menuButtonRef = useRef(null);

  const formatDate = (dateString) => {
    if (!dateString) return 'Not synced yet';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid date';

    // Get day with ordinal suffix
    const day = date.getDate();
    const ordinalSuffix = getOrdinalSuffix(day);

    // Get month name
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = monthNames[date.getMonth()];

    // Format time
    let hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // Convert 0 to 12

    return `${day}${ordinalSuffix} ${month} ${date.getFullYear()}, ${hours}:${minutes} ${ampm}`;
  };

  const getOrdinalSuffix = (day) => {
    if (day > 3 && day < 21) return 'th';
    switch (day % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  };

  const handleScan = () => {
    if (onScan && typeof onScan === 'function') {
      // Check if the integration is active
      const isIntegrationActive = integration.isActive !== undefined ? integration.isActive :
                                 integration.IsActive !== undefined ? integration.IsActive : true;

      // If it's not active, we're fixing the connection
      if (!isIntegrationActive) {
        // Navigate to the scan wizard with the integration ID to fix the connection
        onScan(integration, true); // Pass true as second parameter to indicate fixing connection
      } else {
        // Regular scan
        setIsScanActive(true);
        onScan(integration);
      }
    }
  };

  const handleClose = () => {
    if (onClose && typeof onClose === 'function') {
      setIsScanActive(false);
      onClose();
    }
  };

  const toggleMenu = (e) => {
    e.stopPropagation();
    setMenuOpen(!menuOpen);
  };

  // Handler for View Details menu item
  const handleViewDetails = (e) => {
    e.stopPropagation();
    setMenuOpen(false);

    // Get the integration ID
    const integrationId = integration.id || integration.Id || integration.RowKey;

    if (integrationId) {
      // Navigate to the integration details page using the integration ID
      navigate(`/integration/${integrationId}`);
    } else {
      toast.error('Error: Integration ID not found');
    }
  };

  // Handler for Edit Integration menu item
  const handleEditIntegration = (e) => {
    e.stopPropagation();
    setMenuOpen(false);

    // Navigate to the scan wizard with the integration data for editing
    navigate('/scan', {
      state: {
        integrationToFix: integration,
        isFixingConnection: true
      }
    });
  };

  // Handler for Scan Now menu item
  const handleScanNow = async (e) => {
    e.stopPropagation();
    setMenuOpen(false);

    if (isScanning) return;

    setIsScanning(true);
    const integrationId = integration.id || integration.Id;

    try {
      const loadingToast = toast.loading('Scanning integration... This may take up to 2 minutes.');

      // Call the API to scan the integration
      const response = await scanIntegration(integrationId);

      if (response && response.success) {
        // Check if we have a health score in the response
        if (response.healthScore !== undefined) {
          toast.update(loadingToast, {
            render: `Scan completed successfully! Health Score: ${response.healthScore}%`,
            type: 'success',
            isLoading: false,
            autoClose: 3000
          });

          // Update the integration with the latest scan time and health score
          const updatedIntegration = {
            ...integration,
            lastScan: new Date().toISOString(),
            healthScore: response.healthScore
          };

          // Regular scan flow - use the existing onScan handler
          if (onScan && typeof onScan === 'function') {
            onScan(updatedIntegration);
          }
        } else {
          toast.update(loadingToast, {
            render: 'Scan completed successfully!',
            type: 'success',
            isLoading: false,
            autoClose: 3000
          });

          // Update the integration with just the latest scan time
          const updatedIntegration = {
            ...integration,
            lastScan: new Date().toISOString()
          };

          // Regular scan flow - use the existing onScan handler
          if (onScan && typeof onScan === 'function') {
            onScan(updatedIntegration);
          }
        }
      } else if (response && response.isTimeout) {
        // Handle timeout specifically
        toast.update(loadingToast, {
          render: 'Scan is taking longer than expected. The scan is still running in the background. Please check back in a few minutes.',
          type: 'warning',
          isLoading: false,
          autoClose: 8000
        });

        // Update the integration with the latest scan time even for timeout
        const updatedIntegration = {
          ...integration,
          lastScan: new Date().toISOString()
        };

        if (onScan && typeof onScan === 'function') {
          onScan(updatedIntegration);
        }
      } else {
        const errorMsg = response?.error || response?.message || 'Failed to scan integration';
        toast.update(loadingToast, {
          render: `Scan failed: ${errorMsg}`,
          type: 'error',
          isLoading: false,
          autoClose: 5000
        });
      }
    } catch (error) {
      console.error('Error scanning integration:', error);
      toast.error(`Failed to scan integration: ${error.message || 'Unknown error'}`);
    } finally {
      setIsScanning(false);
    }
  };



  // Handler for Delete Integration menu item
  const handleDeleteIntegration = async (e) => {
    e.stopPropagation();
    setMenuOpen(false);

    if (isDeleting) return;

    // Show confirmation dialog
    if (window.confirm('Are you sure you want to delete this integration? This action cannot be undone.')) {
      setIsDeleting(true);
      const integrationId = integration.id || integration.Id || integration.RowKey;

      try {
        console.log(`Attempting to delete integration with ID: ${integrationId}`);
        const loadingToast = toast.loading('Deleting integration...');

        // Call the API to delete the integration
        const response = await deleteIntegration(integrationId);
        console.log('Delete response:', response);

        // Check for success in the response
        // The backend may return success in different formats
        const isSuccess =
          (response && response.success === true) ||
          (response && response.data && response.data.success === true) ||
          (response && response.statusCode >= 200 && response.statusCode < 300);

        if (isSuccess) {
          toast.update(loadingToast, {
            render: 'Integration deleted successfully!',
            type: 'success',
            isLoading: false,
            autoClose: 3000
          });

          // Notify parent component that integration was deleted
          if (onIntegrationDeleted && typeof onIntegrationDeleted === 'function') {
            onIntegrationDeleted(integrationId);
          }
        } else {
          // Extract error message from various possible response formats
          const errorMsg =
            response?.error ||
            response?.message ||
            (response?.data && (response.data.error || response.data.message)) ||
            'Failed to delete integration';

          toast.update(loadingToast, {
            render: `Deletion failed: ${errorMsg}`,
            type: 'error',
            isLoading: false,
            autoClose: 5000
          });
          console.error('Delete failed with error:', errorMsg);
        }
      } catch (error) {
        console.error('Error deleting integration:', error);
        toast.error(`Failed to delete integration: ${error.message || 'Unknown error'}`);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const getStatusIcon = () => {
    // Check if the integration is active based on the isActive property
    const isIntegrationActive = integration.isActive !== undefined ? integration.isActive :
                               integration.IsActive !== undefined ? integration.IsActive : true;

    return (
      <div className="integration-status-icon">
        <div className={`status-icon-bg ${!isIntegrationActive ? 'inactive' : ''}`}>
          {isIntegrationActive ? (
            <img src="/assets/check-circle-icon.svg" alt="Active" className="status-check" />
          ) : (
            <img src="/assets/cross-circle-icon.svg" alt="Inactive" className="status-check" />
          )}
        </div>
      </div>
    );
  };

  // Update isScanActive when isActive prop changes
  useEffect(() => {
    setIsScanActive(isActive);
  }, [isActive]);

  // Cleanup event listeners when component unmounts
  useEffect(() => {
    return () => {
      // Remove any click event listeners that might be active
      document.removeEventListener('click', () => setMenuOpen(false));
      // Ensure modal is closed when component unmounts
      setShowDetailsModal(false);
    };
  }, []);

  // Check if the integration is active based on the isActive property
  const isIntegrationActive = integration.isActive !== undefined ? integration.isActive :
                             integration.IsActive !== undefined ? integration.IsActive : true;

  return (
    <div className={`integration-card ${viewMode === 'list' ? 'list-view' : ''} ${isScanActive ? 'active-scan' : ''} ${!isIntegrationActive ? 'inactive-integration' : ''} ${menuOpen ? 'menu-open' : ''}`}>
      {!isIntegrationActive && (
        <div className="integration-error-banner">
          <img src="/assets/cross-circle-icon.svg" alt="Error" className="error-icon" />
          <span>Connection Error</span>
        </div>
      )}
      <div className="integration-header">
        <div className="integration-icon-container">
          <div className="integration-icon salesforce-icon">
            <img src="/assets/salesforce-icon.svg" alt="Salesforce" className="salesforce-svg" />
          </div>
          {getStatusIcon()}
        </div>
      </div>

      <div className="integration-content">
        <div className="integration-info">
          <div className="integration-name-container">
            <h3 className="integration-name">{integration.name || integration.Name || 'Sample Project Name'}</h3>
          </div>
          <div className="integration-type">
            <span>{integration.type || integration.Type || 'Salesforce'}</span>
            <img src="/assets/keyboard-right-icon.svg" alt="right arrow" className="keyboard-right-icon" />
          </div>
          <p className="integration-description">
            {integration.description || integration.Description || 'Lorem ipsum dolor sit amet consectetur. Neque lectus proin scelerisque viverra quis at. Ante augue pretium velit et risus ante sed laoreet vel.'}
          </p>
          <p className="integration-last-synced">
            Last Synced on: {formatDate(integration.lastScan || integration.LastScan)}
          </p>
        </div>

        <div className="integration-actions">
          {isScanActive ? (
            <button className="scan-btn active" onClick={handleClose}>
              <FiX className="scan-icon" />
              <span>Close</span>
            </button>
          ) : !isIntegrationActive ? (
            <button className="scan-btn fix-connection" onClick={handleScan}>
              <img src="/assets/sync-icon.svg" alt="Fix" className="scan-icon" />
              <span>Fix Connection</span>
            </button>
          ) : (
            <button className="scan-btn" onClick={handleScan}>
              <img src="/assets/sync-icon.svg" alt="Sync" className="scan-icon" />
              <span>Scan</span>
            </button>
          )}
          <div className="menu-container">
            <button
              className="menu-btn"
              onClick={toggleMenu}
              ref={menuButtonRef}
            >
              <img src="/assets/more-vert-icon.svg" alt="Menu" />
            </button>

            {/* Menu Dropdown Portal */}
            <MenuDropdown
              isOpen={menuOpen}
              onClose={() => setMenuOpen(false)}
              onViewDetails={handleViewDetails}
              onEditIntegration={handleEditIntegration}
              onScanNow={handleScanNow}
              onDeleteIntegration={handleDeleteIntegration}
              isActive={isIntegrationActive}
              isScanning={isScanning}
              isDeleting={isDeleting}
            />

            {/* Details Modal */}
            {showDetailsModal && (
              <IntegrationDetailsModal
                integration={integration}
                onClose={() => setShowDetailsModal(false)}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntegrationCard;
