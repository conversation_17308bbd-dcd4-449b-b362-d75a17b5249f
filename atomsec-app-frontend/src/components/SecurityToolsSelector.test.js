import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from '../context/AuthContext';
import SecurityToolsSelector from './SecurityToolsSelector';

// Mock the external tool navigation utilities
jest.mock('../utils/externalToolNavigation', () => ({
  navigateToExternalTool: jest.fn(),
  validateToolConfig: jest.fn(() => true),
  handleExternalToolError: jest.fn((error, toolName) => `Error accessing ${toolName}`)
}));

// Mock the external tools configuration
jest.mock('../config/externalTools', () => ({
  getAllExternalTools: jest.fn(() => [
    {
      id: 'open-search',
      name: 'Open Search',
      description: 'Advanced search and discovery platform',
      url: 'https://opensearch.atomsec.com',
      features: ['Threat Intelligence', 'Data Discovery']
    },
    {
      id: 'grey-log',
      name: 'Grey Log',
      description: 'Comprehensive logging solution',
      url: 'https://greylog.atomsec.com',
      features: ['Log Management', 'Event Correlation']
    }
  ])
}));

// Mock react-toastify
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

const renderWithProviders = (component) => {
  return render(
    <BrowserRouter>
      <AuthProvider>
        {component}
      </AuthProvider>
    </BrowserRouter>
  );
};

describe('SecurityToolsSelector', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders security tools selection page', () => {
    renderWithProviders(<SecurityToolsSelector />);
    
    expect(screen.getByText('Welcome to AtomSec Security Platform')).toBeInTheDocument();
    expect(screen.getByText('Choose a security tool to get started with your security operations')).toBeInTheDocument();
  });

  test('displays Attack Surface tool card', () => {
    renderWithProviders(<SecurityToolsSelector />);
    
    expect(screen.getByText('Attack Surface')).toBeInTheDocument();
    expect(screen.getByText('Comprehensive security assessment and vulnerability management platform')).toBeInTheDocument();
    expect(screen.getByText('Built-in')).toBeInTheDocument();
  });

  test('displays external tool cards', () => {
    renderWithProviders(<SecurityToolsSelector />);
    
    expect(screen.getByText('Open Search')).toBeInTheDocument();
    expect(screen.getByText('Grey Log')).toBeInTheDocument();
  });

  test('displays tool features', () => {
    renderWithProviders(<SecurityToolsSelector />);
    
    expect(screen.getByText('Vulnerability Scanning')).toBeInTheDocument();
    expect(screen.getByText('Risk Assessment')).toBeInTheDocument();
    expect(screen.getByText('Threat Intelligence')).toBeInTheDocument();
    expect(screen.getByText('Log Management')).toBeInTheDocument();
  });

  test('shows help section', () => {
    renderWithProviders(<SecurityToolsSelector />);
    
    expect(screen.getByText('Need Help?')).toBeInTheDocument();
    expect(screen.getByText(/Contact our support team/)).toBeInTheDocument();
  });
}); 