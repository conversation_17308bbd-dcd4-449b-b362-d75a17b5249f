.settings-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  margin-bottom: 8px;
  color: #020A07;
}

.page-description {
  font-size: 16px;
  color: #666;
}

.settings-content {
  display: flex;
  gap: 30px;
}

.settings-sidebar {
  width: 250px;
  flex-shrink: 0;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  position: sticky;
  top: 80px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
}

.sidebar-btn {
  display: block;
  width: 100%;
  text-align: left;
  padding: 12px 15px;
  background: none;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 8px;
}

.sidebar-btn:hover {
  background-color: #f8f9fa;
}

.sidebar-btn.active {
  background-color: #e6f7f1;
  color: #51D59C;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(81, 213, 156, 0.2);
  border-left: 3px solid #51D59C;
}

.settings-main {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 20px;
  margin-bottom: 20px;
  color: #020A07;
}

.section-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  gap: 20px;
  /* margin-bottom: 20px; */
}

.form-group {
  /* margin-bottom: 20px; */
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
}

.form-group input:focus {
  border-color: #51D59C;
  outline: none;
  box-shadow: 0 0 0 3px rgba(81, 213, 156, 0.2);
}

.settings-actions {
  margin-top: 30px;
  display: flex;
  align-items: center;
}

.save-btn {
  padding: 10px 20px;
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-btn:hover {
  background-color: #3DC488;
}

.save-success {
  margin-left: 15px;
  color: #51D59C;
  font-weight: 500;
}

.password-error {
  color: #FF3B30;
  background-color: #FFEEEE;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.password-success {
  color: #34C759;
  background-color: #E6F9ED;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .settings-content {
    flex-direction: column;
  }

  .settings-sidebar {
    width: 100%;
    margin-bottom: 20px;
    display: flex;
    overflow-x: auto;
    padding-bottom: 10px;
  }

  .sidebar-btn {
    white-space: nowrap;
    margin-right: 10px;
    margin-bottom: 0;
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .page-title {
    font-size: 24px;
  }

  .settings-main {
    padding: 20px;
  }

}
