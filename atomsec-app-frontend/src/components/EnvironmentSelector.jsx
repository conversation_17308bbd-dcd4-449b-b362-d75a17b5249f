import React, { useState } from 'react';

const EnvironmentSelector = ({ onSelect }) => {
  const [selectedEnv, setSelectedEnv] = useState('production');

  const handleChange = (e) => {
    const env = e.target.value;
    setSelectedEnv(env);
    onSelect(env);
  };

  return (
    <div className="environment-selector">
      <select 
        value={selectedEnv} 
        onChange={handleChange}
        className="form-select"
      >
        <option value="production">Production</option>
        <option value="sandbox">Sandbox</option>
      </select>
    </div>
  );
};

export default EnvironmentSelector;