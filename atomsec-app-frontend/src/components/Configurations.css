.configurations-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-title {
  font-size: 28px;
  margin-bottom: 8px;
  color: #020A07;
}

.page-description {
  font-size: 16px;
  color: #666;
}

.config-tabs {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 20px;
}

.tab-btn {
  padding: 10px 20px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  color: #495057;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.tab-btn:hover {
  color: #51D59C;
}

.tab-btn.active {
  color: #51D59C;
  border-bottom-color: #51D59C;
}

.config-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 20px;
  margin-bottom: 10px;
  color: #020A07;
}

.section-description {
  font-size: 14px;
  color: #6C757D;
  margin-bottom: 20px;
  line-height: 1.5;
}

.form-group {
  /* margin-bottom: 20px; */
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.checkbox-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.checkbox-group:hover {
  background-color: #F1FCF7;
}

.checkbox-group label {
  margin-bottom: 0;
  margin-left: 10px;
  font-weight: 500;
  cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.setting-description {
  margin-left: 28px;
  font-size: 14px;
  color: #6C757D;
  margin-top: 4px;
  margin-bottom: 15px;
}

.form-group input[type="text"],
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
  background-color: #fff;
  transition: all 0.2s;
}

.form-group input[type="text"]:focus,
.form-group select:focus {
  border-color: #51D59C;
  outline: none;
  box-shadow: 0 0 0 3px rgba(81, 213, 156, 0.2);
}

.form-group select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23495057' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 30px;
  cursor: pointer;
}

.form-group select:hover {
  border-color: #51D59C;
}

.select-wrapper,
.input-wrapper {
  position: relative;
  width: 100%;
}

.select-label,
.input-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.form-group input[type="text"] {
  transition: all 0.2s;
}

.form-group input[type="text"]:hover {
  border-color: #51D59C;
}

.form-group input[type="text"]::placeholder {
  color: #ADB5BD;
  font-style: italic;
}

.config-actions {
  margin-top: 30px;
  display: flex;
  align-items: center;
}

.save-btn {
  padding: 10px 20px;
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-btn:hover {
  background-color: #3DC488;
}

.save-success {
  margin-left: 15px;
  color: #51D59C;
  font-weight: 500;
}

/* Insights Section Styles */
.insights-section {
  max-width: 1000px;
}

.insights-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eaeaea;
}

.filter-btn {
  padding: 8px 16px;
  border-radius: 20px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #495057;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover {
  background-color: #e9ecef;
}

.filter-btn.active {
  background-color: #51D59C;
  color: #020A07;
  border-color: #51D59C;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(81, 213, 156, 0.3);
  transform: translateY(-2px);
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  position: relative;
  scroll-margin-top: 20px;
  margin-bottom: 30px;
}

.insight-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid transparent;
}

.insight-card.severity-high {
  border-left-color: #FF3B30;
}

.insight-card.severity-medium {
  border-left-color: #FF9500;
}

.insight-card.severity-low {
  border-left-color: #34C759;
}

.insight-card.severity-info {
  border-left-color: #007AFF;
}

.insight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.insight-title {
  font-size: 18px;
  color: #020A07;
  margin: 0;
}

.insight-severity {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
}

.insight-severity.high {
  background-color: #FFEEEE;
  color: #FF3B30;
}

.insight-severity.medium {
  background-color: #FFF5E6;
  color: #FF9500;
}

.insight-severity.low {
  background-color: #E6F9ED;
  color: #34C759;
}

.insight-severity.info {
  background-color: #E6F2FF;
  color: #007AFF;
}

.insight-description {
  font-size: 14px;
  color: #495057;
  margin-bottom: 15px;
  line-height: 1.5;
}

.insight-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 15px;
}

.insight-action-btn {
  padding: 8px 16px;
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.insight-action-btn:hover {
  background-color: #3DC488;
}

.no-insights {
  padding: 30px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 18px;
  color: #666;
}

.error-message {
  background-color: #fff0f0;
  color: #FF3B30;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

/* Security Health Section Styles */
.security-health-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eaeaea;
}

.security-health-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.security-health-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.security-health-card h4 {
  font-size: 18px;
  color: #020A07;
  margin-top: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eaeaea;
}

.security-health-html {
  max-height: 500px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.security-health-html table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

.security-health-html th,
.security-health-html td {
  padding: 8px 12px;
  text-align: left;
  border: 1px solid #dee2e6;
}

.security-health-html th {
  background-color: #e9ecef;
  font-weight: 600;
}

.security-health-html tr:nth-child(even) {
  background-color: #f2f2f2;
}

.security-health-html tr:hover {
  background-color: #e9ecef;
}

/* Advanced Section Styles */
.advanced-section {
  max-width: 1000px;
}

.advanced-links {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.advanced-link-card {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-left: 4px solid #51D59C;
}

.advanced-link-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.advanced-link-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: #F1FCF7;
  border-radius: 8px;
  margin-right: 20px;
  color: #51D59C;
}

.advanced-link-content {
  flex: 1;
}

.advanced-link-title {
  font-size: 18px;
  color: #020A07;
  margin: 0 0 8px 0;
}

.advanced-link-description {
  font-size: 14px;
  color: #6C757D;
  margin-bottom: 15px;
  line-height: 1.5;
}

.advanced-link-button {
  display: inline-block;
  padding: 8px 16px;
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  text-decoration: none;
}

.advanced-link-button:hover {
  background-color: #3DC488;
  text-decoration: none;
  color: #020A07;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .config-tabs {
    overflow-x: auto;
    white-space: nowrap;
  }

  .tab-btn {
    padding: 10px 15px;
    font-size: 14px;
  }

  .page-title {
    font-size: 24px;
  }

  .section-title {
    font-size: 18px;
  }

  .insights-filters {
    overflow-x: auto;
    padding-bottom: 10px;
    flex-wrap: nowrap;
  }

  .insight-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .insight-severity {
    align-self: flex-start;
  }

  .security-health-html {
    max-height: 300px;
  }

  .advanced-link-card {
    flex-direction: column;
  }

  .advanced-link-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
