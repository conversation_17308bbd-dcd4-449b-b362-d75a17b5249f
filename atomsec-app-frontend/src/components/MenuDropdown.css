.menu-dropdown-portal {
  position: fixed;
  background-color: white;
  border: 1px solid #DEE2E6;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 9998; /* Just below modal but above everything else */
  min-width: 180px;
  max-width: 220px;
  animation: fadeIn 0.15s ease-in-out;
  transform-origin: top right;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  text-align: left;
  padding: 8px 16px;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  color: #495057;
}

.menu-icon {
  color: #51D59C;
}

.menu-item:hover {
  background-color: #F1F4F9;
}

.menu-item.danger {
  color: #D32F2F;
}

.menu-item.danger:hover {
  background-color: rgba(211, 47, 47, 0.04);
}

.menu-item:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.menu-item:disabled:hover {
  background-color: transparent;
}
