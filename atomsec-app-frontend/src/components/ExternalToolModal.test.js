import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from '../context/AuthContext';
import ExternalToolModal from './ExternalToolModal';

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const renderWithProviders = (component) => {
  return render(
    <BrowserRouter>
      <AuthProvider>
        {component}
      </AuthProvider>
    </BrowserRouter>
  );
};

describe('ExternalToolModal', () => {
  const defaultProps = {
    isOpen: true,
    toolName: 'Open Search',
    toolUrl: 'https://opensearch.atomsec.com',
    onClose: jest.fn(),
    onNavigateToDashboard: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders modal when isOpen is true', () => {
    renderWithProviders(<ExternalToolModal {...defaultProps} />);
    
    expect(screen.getByText('External Tool Opened')).toBeInTheDocument();
    expect(screen.getByText('Open Search')).toBeInTheDocument();
  });

  test('does not render when isOpen is false', () => {
    renderWithProviders(<ExternalToolModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('External Tool Opened')).not.toBeInTheDocument();
  });

  test('displays tool information', () => {
    renderWithProviders(<ExternalToolModal {...defaultProps} />);
    
    expect(screen.getByText('Open Search')).toBeInTheDocument();
    expect(screen.getByText(/Open Search has been opened in a new tab/)).toBeInTheDocument();
  });

  test('shows action buttons', () => {
    renderWithProviders(<ExternalToolModal {...defaultProps} />);
    
    expect(screen.getByText('Open Open Search Again')).toBeInTheDocument();
    expect(screen.getByText('Go to Attack Surface Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Close This Window')).toBeInTheDocument();
  });

  test('calls onClose when close button is clicked', () => {
    renderWithProviders(<ExternalToolModal {...defaultProps} />);
    
    const closeButton = screen.getByLabelText('Close modal');
    fireEvent.click(closeButton);
    
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  test('calls onNavigateToDashboard when dashboard button is clicked', () => {
    renderWithProviders(<ExternalToolModal {...defaultProps} />);
    
    const dashboardButton = screen.getByText('Go to Attack Surface Dashboard');
    fireEvent.click(dashboardButton);
    
    expect(defaultProps.onNavigateToDashboard).toHaveBeenCalled();
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
  });

  test('opens tool in new tab when open again button is clicked', () => {
    const mockOpen = jest.fn();
    Object.defineProperty(window, 'open', {
      writable: true,
      value: mockOpen,
    });

    renderWithProviders(<ExternalToolModal {...defaultProps} />);
    
    const openAgainButton = screen.getByText('Open Open Search Again');
    fireEvent.click(openAgainButton);
    
    expect(mockOpen).toHaveBeenCalledWith(
      'https://opensearch.atomsec.com',
      '_blank',
      'noopener,noreferrer'
    );
  });

  test('shows countdown timer', async () => {
    renderWithProviders(<ExternalToolModal {...defaultProps} />);
    
    expect(screen.getByText(/This window will automatically close in/)).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  test('displays user information', () => {
    renderWithProviders(<ExternalToolModal {...defaultProps} />);
    
    expect(screen.getByText(/Logged in as:/)).toBeInTheDocument();
  });
}); 