import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

/**
 * Component to display authentication debug information
 * This helps troubleshoot authentication issues
 */
const AuthDebugInfo = () => {
  const { user, isAuthenticated } = useAuth();
  const [platformAuthData, setPlatformAuthData] = useState(null);
  const [platformAuthError, setPlatformAuthError] = useState(null);

  useEffect(() => {
    const checkPlatformAuth = async () => {
      // Only check platform auth in production environment
      const isProduction = window.location.hostname !== 'localhost';

      if (!isProduction) {
        setPlatformAuthError('Platform authentication not available in development environment');
        return;
      }

      try {
        const response = await fetch('/.auth/me');
        if (response.ok) {
          const contentType = response.headers.get('content-type');
          if (!contentType || !contentType.includes('application/json')) {
            setPlatformAuthError('Platform auth endpoint returned non-JSON response');
            return;
          }
          const data = await response.json();
          setPlatformAuthData(data);
        } else {
          setPlatformAuthError(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        setPlatformAuthError(error.message);
      }
    };

    checkPlatformAuth();
  }, []);

  const currentUrl = window.location.href;
  const isProduction = window.location.hostname !== 'localhost';

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#f5f5f5', 
      border: '1px solid #ddd', 
      borderRadius: '4px',
      margin: '20px',
      fontFamily: 'monospace',
      fontSize: '12px'
    }}>
      <h3>Authentication Debug Information</h3>
      
      <div style={{ marginBottom: '15px' }}>
        <strong>Environment:</strong> {isProduction ? 'Production' : 'Development'}
      </div>
      
      <div style={{ marginBottom: '15px' }}>
        <strong>Current URL:</strong> {currentUrl}
      </div>
      
      <div style={{ marginBottom: '15px' }}>
        <strong>React Auth State:</strong>
        <div style={{ marginLeft: '10px' }}>
          <div>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</div>
          <div>User: {user ? JSON.stringify(user, null, 2) : 'None'}</div>
        </div>
      </div>
      
      <div style={{ marginBottom: '15px' }}>
        <strong>LocalStorage:</strong>
        <div style={{ marginLeft: '10px' }}>
          <div>accessToken: {localStorage.getItem('accessToken') ? 'Present' : 'None'}</div>
          <div>userEmail: {localStorage.getItem('userEmail') || 'None'}</div>
          <div>userName: {localStorage.getItem('userName') || 'None'}</div>
        </div>
      </div>
      
      <div style={{ marginBottom: '15px' }}>
        <strong>Platform Authentication (/.auth/me):</strong>
        <div style={{ marginLeft: '10px' }}>
          {platformAuthError ? (
            <div style={{ color: 'red' }}>Error: {platformAuthError}</div>
          ) : platformAuthData ? (
            <pre style={{ backgroundColor: '#fff', padding: '10px', overflow: 'auto' }}>
              {JSON.stringify(platformAuthData, null, 2)}
            </pre>
          ) : (
            <div>Loading...</div>
          )}
        </div>
      </div>
      
      <div style={{ marginBottom: '15px' }}>
        <strong>Quick Actions:</strong>
        <div style={{ marginLeft: '10px' }}>
          {isProduction && (
            <>
              <button
                onClick={() => window.location.href = '/.auth/login/aad'}
                style={{ margin: '5px', padding: '5px 10px' }}
              >
                Platform Login
              </button>
              <button
                onClick={() => window.location.href = '/.auth/logout'}
                style={{ margin: '5px', padding: '5px 10px' }}
              >
                Platform Logout
              </button>
            </>
          )}
          <button
            onClick={() => {
              localStorage.removeItem('accessToken');
              localStorage.removeItem('refreshToken');
              localStorage.removeItem('userEmail');
              localStorage.removeItem('userName');
              window.location.reload();
            }}
            style={{ margin: '5px', padding: '5px 10px', backgroundColor: '#ff6b6b', color: 'white' }}
          >
            Clear LocalStorage
          </button>
          <button
            onClick={() => window.location.reload()}
            style={{ margin: '5px', padding: '5px 10px' }}
          >
            Refresh Page
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuthDebugInfo;
