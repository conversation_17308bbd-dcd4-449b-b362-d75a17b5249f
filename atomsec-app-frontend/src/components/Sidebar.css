.sidebar {
  width: 250px;
  height: calc(100vh - 60px); /* Subtract header height */
  background-color: #FFFFFF;
  color: #393E3C;
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
  z-index: 100;
  overflow-y: auto;
  flex-shrink: 0;
  border-right: 1px solid #DEE2E6;
  position: fixed;
  left: 0;
  top: 60px; /* Match header height */
}

.sidebar-collapsed {
  width: 64px;
  position: fixed;
  left: 0;
  top: 60px;
}

.sidebar-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

.toggle-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #393E3C;
  font-size: 16px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.toggle-button:hover {
  background-color: #F1F4F9;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 16px;
  flex: 1; /* Take up available space but allow footer to be visible */
  min-height: 0; /* Allow flex shrinking */
}

.nav-item {
  padding: 8px 0 8px 32px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-item:hover {
  background-color: #F1F4F9;
}

.nav-item.active {
  background-color: #F1FCF7;
  border-right: 2px solid #51D59C;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  color: #393E3C;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.6;
  transition: color 0.2s ease;
  padding: 8px 0;
}

.nav-link:hover {
  color: #020A07;
}

.nav-link.active {
  color: #020A07;
}

/* Override default NavLink active styles */
.nav-link.active,
.nav-link:active,
.nav-link:focus {
  background-color: transparent;
  outline: none;
}

.nav-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
  transition: color 0.2s ease;
}

.nav-dropdown {
  margin-left: auto;
  color: #51D59C;
  width: 24px;
  height: 24px;
  transition: transform 0.2s ease;
}

.nav-dropdown:hover {
  transform: rotate(180deg);
}

/* Responsive styles */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    min-height: 60px;
    flex-direction: row;
    align-items: center;
    padding: 10px;
    background-color: #FFFFFF;
    border-right: none;
    border-bottom: 1px solid #DEE2E6;
    position: relative;
    top: 0;
    left: 0;
  }

  .nav-text,
  .nav-dropdown {
    display: none;
  }

  .nav-menu {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    padding: 0;
  }

  .nav-item {
    padding: 8px 0;
    display: flex;
    justify-content: center;
    width: auto;
  }

  .nav-icon {
    margin: 0;
  }

  .sidebar-footer {
    display: none;
  }
}

.sidebar-footer {
  margin-top: auto;
  padding: 20px;
  border-top: 1px solid #DEE2E6;
  display: flex;
  flex-direction: column;
  gap: 20px;
  color: #393E3C;
  flex-shrink: 0; /* Prevent footer from shrinking */
}

.help-section {
  color: #393E3C;
}

.help-section h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #020A07;
}

.help-section p {
  margin: 0;
  font-size: 14px;
  color: #6C757D;
}

.collapse-button {
  background: none;
  border: none;
  color: #393E3C;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  transition: all 0.3s ease;
}

.collapse-button:hover {
  background-color: #F1F4F9;
  border-radius: 4px;
}

.collapse-icon {
  font-size: 24px;
  color: #393E3C;
}

.sidebar-collapsed .collapse-button {
  justify-content: center;
}

.sidebar-collapsed .help-section {
  display: none;
}

.sidebar-collapsed .nav-menu {
  padding: 0;
}

.sidebar-collapsed .nav-item {
  padding: 8px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.sidebar-collapsed .nav-link {
  justify-content: center;
  padding: 8px 0;
  width: 100%;
}

