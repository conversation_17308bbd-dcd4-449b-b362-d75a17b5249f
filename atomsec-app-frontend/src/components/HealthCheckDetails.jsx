import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Pie, Bar } from 'react-chartjs-2';
import { API_CONFIG } from '../config';
import {
  fetchOrgDetails,
  fetchHealthScore as apiFetchHealthScore,
  fetchHealthRisks as apiFetchHealthRisks,
  fetchProfiles as apiFetchProfiles,
  fetchPermissionSets as apiFetchPermissionSets,
  fetchPoliciesResultByIntegrationId
} from '../api';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
} from 'chart.js';
import AuthPopup from './AuthPopup';
import ProfilesPermissionSets from './ProfilesPermissionSets';
import DataStatusMessage from './DataStatusMessage';
import './HealthCheckDetails.css';

// Register ChartJS components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

const HealthCheckDetails = () => {
  const { instanceUrl } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [orgData, setOrgData] = useState(null);
  const [healthRisks, setHealthRisks] = useState([]);
  const [healthScore, setHealthScore] = useState(null);
  const [profiles, setProfiles] = useState([]);
  const [permissionSets, setPermissionSets] = useState([]);
  const [isRescanning, setIsRescanning] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showAuthPopup, setShowAuthPopup] = useState(false);
  const [authError, setAuthError] = useState(false);

  // Data status states
  const [orgDataStatus, setOrgDataStatus] = useState('loading');
  const [healthScoreStatus, setHealthScoreStatus] = useState('loading');
  const [healthRisksStatus, setHealthRisksStatus] = useState('loading');
  const [profilesStatus, setProfilesStatus] = useState('loading');
  const [permissionSetsStatus, setPermissionSetsStatus] = useState('loading');
  const [isRefreshing, setIsRefreshing] = useState({
    orgData: false,
    healthScore: false,
    healthRisks: false,
    profiles: false,
    permissionSets: false
  });

  const [policiesResult, setPoliciesResult] = useState([]);
  const [policiesResultStatus, setPoliciesResultStatus] = useState('loading');

  useEffect(() => {
    fetchOrgData();
    if (activeTab === 'healthCheck') {
      fetchHealthRisks();
    } else if (activeTab === 'profilesPermissions') {
      fetchProfiles();
    }
  }, [activeTab]);

  // Fetch health score when component mounts
  useEffect(() => {
    fetchHealthScore();
  }, []);

  useEffect(() => {
    console.log('DEBUG useEffect orgData:', orgData, 'activeTab:', activeTab);
    if (orgData && activeTab === 'healthCheck') {
      fetchPoliciesResult();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orgData, activeTab]);

  useEffect(() => {
    if (orgData) {
      console.log('DEBUG orgData:', orgData);
    }
  }, [orgData]);

  const fetchOrgData = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setOrgDataStatus('loading');
      setAuthError(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, orgData: true }));
      }

      // Include the email in the request if available
      const response = await fetchOrgDetails(instanceUrl, forceRefresh);

      // Check data status
      if (response.dataStatus === 'pending') {
        setOrgDataStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchOrgData(), 5000);
        setLoading(false);
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        setOrgData(response);
        console.log('DEBUG setOrgData', response);
        setOrgDataStatus('available');
        setLoading(false);
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, orgData: false }));
      }
    } catch (err) {
      console.error('Error fetching org data:', err);
      setOrgDataStatus('error');

      // Check if it's an authentication error
      if (err.response && err.response.status === 401) {
        setAuthError(true);
        setError('Authentication required. Please authenticate with Salesforce.');
      } else {
        setError('Failed to fetch organization details');
      }

      setLoading(false);

      if (err.response && err.response.status === 401) {
        toast.error('Authentication required. Please click the Authenticate button below.');
      } else {
        toast.error('Failed to fetch organization details: ' + (err.response?.data?.error || err.message));
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, orgData: false }));
      }
    }
  };

  const fetchHealthScore = async (forceRefresh = false) => {
    try {
      setHealthScoreStatus('loading');
      setAuthError(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthScore: true }));
      }

      const response = await apiFetchHealthScore(instanceUrl, forceRefresh);

      // Check data status
      if (response.dataStatus === 'pending') {
        setHealthScoreStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchHealthScore(), 5000);
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        if (response.score !== undefined && response.score !== null) {
          setHealthScore(response.score);
        } else {
          console.log('Health score is null or undefined, setting to N/A');
          setHealthScore('N/A');
        }
        setHealthScoreStatus('available');
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthScore: false }));
      }
    } catch (err) {
      console.error('Error fetching health score:', err);
      setHealthScore('N/A');
      setHealthScoreStatus('error');

      // Don't show authentication errors anymore
      if (err.response && err.response.status !== 401) {
        toast.error('Failed to fetch health score: ' + (err.response?.data?.error || err.message));
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthScore: false }));
      }
    }
  };

  const fetchHealthRisks = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setHealthRisksStatus('loading');
      setAuthError(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthRisks: true }));
      }

      const response = await apiFetchHealthRisks(instanceUrl, forceRefresh);
      console.log('Health check API response:', response);

      // Check data status
      if (response.dataStatus === 'pending') {
        setHealthRisksStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchHealthRisks(), 5000);
        setLoading(false);
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        if (Array.isArray(response.risks)) {
          setHealthRisks(response.risks);
        } else if (Array.isArray(response.data?.risks)) {
          setHealthRisks(response.data.risks);
        } else if (Array.isArray(response)) {
          setHealthRisks(response);
        } else {
          // If data is not an array, set empty array
          console.error('Health risks data is not an array or not found in response:', response);
          setHealthRisks([]);
        }

        setHealthRisksStatus('available');
        setLoading(false);

        // Also fetch the health score
        await fetchHealthScore();
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthRisks: false }));
      }
    } catch (err) {
      console.error('Error fetching health risks:', err);
      setHealthRisks([]);
      setHealthRisksStatus('error');
      setLoading(false);

      // Don't show authentication errors anymore
      if (err.response && err.response.status !== 401) {
        setError('Failed to fetch health risk data');
        toast.error('Failed to fetch health risk data: ' + (err.response?.data?.error || err.message));
      }

      // Still fetch the health score even if health risks fail
      try {
        await fetchHealthScore();
      } catch (scoreErr) {
        console.error('Error fetching health score after health risks failure:', scoreErr);
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, healthRisks: false }));
      }
    }
  };

  const fetchProfiles = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setProfilesStatus('loading');
      setAuthError(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: true }));
      }

      const response = await apiFetchProfiles(instanceUrl, forceRefresh);

      // Check data status
      if (response.dataStatus === 'pending') {
        setProfilesStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchProfiles(), 5000);
        setLoading(false);
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        if (Array.isArray(response.profiles)) {
          setProfiles(response.profiles);
        } else if (Array.isArray(response)) {
          setProfiles(response);
        } else {
          // If data is not an array, set empty array
          console.log('Profile data is not in expected format, setting to empty array');
          setProfiles([]);
        }

        setProfilesStatus('available');
        setLoading(false);

        // Also fetch permission sets
        await fetchPermissionSets(forceRefresh);
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: false }));
      }
    } catch (err) {
      console.error('Error fetching profiles:', err);
      setProfiles([]);
      setProfilesStatus('error');
      setLoading(false);

      // Check if it's an authentication error
      if (err.response && err.response.status === 401) {
        setAuthError(true);
        setError('Authentication required. Please authenticate with Salesforce.');
      } else {
        setError('Failed to fetch profile data');
      }

      if (err.response && err.response.status === 401) {
        toast.error('Authentication required. Please click the Authenticate button below.');
      } else {
        toast.error('Failed to fetch profile data: ' + (err.response?.data?.error || err.message));
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: false }));
      }
    }
  };

  const fetchPermissionSets = async (forceRefresh = false) => {
    try {
      setPermissionSetsStatus('loading');
      setAuthError(false);

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, permissionSets: true }));
      }

      const response = await apiFetchPermissionSets(instanceUrl, forceRefresh);

      // Check data status
      if (response.dataStatus === 'pending') {
        setPermissionSetsStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchPermissionSets(), 5000);
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        if (Array.isArray(response.permissionSets)) {
          setPermissionSets(response.permissionSets);
        } else if (Array.isArray(response)) {
          setPermissionSets(response);
        } else {
          // If data is not an array, set empty array
          console.log('Permission set data is not in expected format, setting to empty array');
          setPermissionSets([]);
        }

        setPermissionSetsStatus('available');
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, permissionSets: false }));
      }
    } catch (err) {
      console.error('Error fetching permission sets:', err);
      setPermissionSets([]);
      setPermissionSetsStatus('error');

      // Check if it's an authentication error
      if (err.response && err.response.status === 401) {
        setAuthError(true);
        setError('Authentication required. Please authenticate with Salesforce.');
      } else {
        setError('Failed to fetch permission set data');
      }

      if (err.response && err.response.status === 401) {
        toast.error('Authentication required. Please click the Authenticate button below.');
      } else {
        toast.error('Failed to fetch permission set data: ' + (err.response?.data?.error || err.message));
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, permissionSets: false }));
      }
    }
  };

  const fetchPoliciesResult = async () => {
    try {
      setPoliciesResultStatus('loading');
      let integrationId = orgData?.id || orgData?.RowKey;
      if (!integrationId) {
        setPoliciesResultStatus('error');
        setPoliciesResult([]);
        return;
      }

      // 1. Fetch latest completed health_check task for this integration
              const taskStatusResp = await fetch(`/api/db/task-status?integration_id=${integrationId}&status=completed&task_type=health_check&limit=1`);
      const taskStatusData = await taskStatusResp.json();
      const latestHealthCheckTask = taskStatusData?.data?.[0];

      if (!latestHealthCheckTask || !latestHealthCheckTask.ExecutionLogId) {
        setPoliciesResultStatus('empty');
        setPoliciesResult([]);
        return;
      }

      // 2. Fetch ONLY health check policies result for the latest ExecutionLogId
      console.log('HealthCheckDetails - Calling fetchPoliciesResultByIntegrationId with:', {
        integrationId,
        executionLogId: latestHealthCheckTask.ExecutionLogId,
        type: 'HealthCheck'
      });
      const result = await fetchPoliciesResultByIntegrationId(integrationId, latestHealthCheckTask.ExecutionLogId, 'HealthCheck');

      if (Array.isArray(result)) {
        // Filter to only show HealthCheck records
        const healthCheckRecords = result.filter(record => record.Type === 'HealthCheck');
        setPoliciesResult(healthCheckRecords);
        setPoliciesResultStatus('available');
      } else if (result && result.data) {
        // Filter to only show HealthCheck records
        const healthCheckRecords = result.data.filter(record => record.Type === 'HealthCheck');
        setPoliciesResult(healthCheckRecords);
        setPoliciesResultStatus('available');
      } else if (result && result.dataStatus === 'pending') {
        setPoliciesResultStatus('pending');
        setTimeout(fetchPoliciesResult, 5000);
      } else {
        setPoliciesResult([]);
        setPoliciesResultStatus('empty');
      }
    } catch (err) {
      console.error('Error fetching health check policies result:', err);
      setPoliciesResultStatus('error');
      setPoliciesResult([]);
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const handleFilterChange = (e) => {
    setFilterType(e.target.value);
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleAuthenticate = () => {
    setShowAuthPopup(true);
  };

  const handleAuthSuccess = () => {
    // Refresh the data after successful authentication
    fetchOrgData();
    if (activeTab === 'healthCheck') {
      fetchHealthRisks();
    }
  };

  const handleRescan = async () => {
    try {
      setIsRescanning(true);
      toast.info('Rescanning Salesforce org...');

      // Fetch fresh data with force refresh
      if (activeTab === 'healthCheck') {
        await fetchHealthRisks(true);
      } else if (activeTab === 'profilesPermissions') {
        await fetchProfiles(true);
      } else {
        // Always fetch the latest org data
        await fetchOrgData(true);
      }

      toast.success('Rescan completed successfully!');
      setIsRescanning(false);
    } catch (error) {
      console.error('Rescan error:', error);
      toast.error('Failed to rescan: ' + (error.response?.data?.error || error.message));
      setIsRescanning(false);
    }
  };

  const filteredPoliciesResult = policiesResult.filter(record => {
    const severity = record.Severity || record.severity || '';
    const setting = record.Setting || record.setting || '';
    const settingGroup = record.SettingGroup || record.settingGroup || '';
    
    const matchesFilter = filterType === 'all' || severity.toUpperCase() === filterType;
    const matchesSearch = searchTerm === '' ||
      setting.toLowerCase().includes(searchTerm.toLowerCase()) ||
      settingGroup.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  // Get health score grade
  const getHealthScoreGrade = (score) => {
    if (!score) return { grade: 'N/A', color: '#6c757d' };

    const numScore = parseInt(score, 10);

    if (numScore >= 90) return { grade: 'Excellent', color: '#28a745' };
    if (numScore >= 80) return { grade: 'Very Good', color: '#5cb85c' };
    if (numScore >= 70) return { grade: 'Good', color: '#5bc0de' };
    if (numScore >= 55) return { grade: 'Poor', color: '#f0ad4e' };
    return { grade: 'Very Poor', color: '#d9534f' };
  };

  // Prepare data for pie chart
  const prepareRiskDistributionData = () => {
    if (!policiesResult || policiesResult.length === 0) {
      // Return default data if no health check data
      return {
        labels: ['No Data'],
        datasets: [
          {
            data: [1],
            backgroundColor: ['#6c757d'],
            borderWidth: 1
          }
        ]
      };
    }

    const riskCounts = policiesResult.reduce((acc, record) => {
      const severity = record.Severity || record.severity || 'UNKNOWN';
      acc[severity] = (acc[severity] || 0) + 1;
      return acc;
    }, {});

    return {
      labels: Object.keys(riskCounts),
      datasets: [
        {
          data: Object.values(riskCounts),
          backgroundColor: [
            '#dc3545', // HIGH - Red
            '#ffc107', // MEDIUM - Yellow
            '#28a745', // LOW - Green
            '#6c757d'  // UNKNOWN - Gray
          ],
          borderWidth: 1
        }
      ]
    };
  };

  // Prepare data for bar chart
  const prepareSettingGroupData = () => {
    if (!policiesResult || policiesResult.length === 0) {
      // Return default data if no health check data
      return {
        labels: ['No Data'],
        datasets: [
          {
            label: 'No Data',
            data: [0],
            backgroundColor: '#6c757d',
          }
        ]
      };
    }

    // Group by SettingGroup (if available) or use a default group
    const groupCounts = policiesResult.reduce((acc, record) => {
      const settingGroup = record.SettingGroup || record.settingGroup || 'Unknown Group';
      const severity = record.Severity || record.severity || 'UNKNOWN';
      
      if (!acc[settingGroup]) {
        acc[settingGroup] = {
          HIGH: 0,
          MEDIUM: 0,
          LOW: 0,
          UNKNOWN: 0
        };
      }
      acc[settingGroup][severity.toUpperCase()] = (acc[settingGroup][severity.toUpperCase()] || 0) + 1;
      return acc;
    }, {});

    return {
      labels: Object.keys(groupCounts),
      datasets: [
        {
          label: 'High Risk',
          data: Object.values(groupCounts).map(group => group.HIGH || 0),
          backgroundColor: '#dc3545',
        },
        {
          label: 'Medium Risk',
          data: Object.values(groupCounts).map(group => group.MEDIUM || 0),
          backgroundColor: '#ffc107',
        },
        {
          label: 'Low Risk',
          data: Object.values(groupCounts).map(group => group.LOW || 0),
          backgroundColor: '#28a745',
        }
      ]
    };
  };

  const renderOverviewTab = () => {
    return (
      <div className="coming-soon">
        <h3>Overview - analysis coming soon</h3>
      </div>
    );
  };

  const renderHealthCheckTab = () => {
    // Loading state
    if (healthRisksStatus === 'loading') {
      return (
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading health check data...</p>
        </div>
      );
    }

    // Error state
    if (healthRisksStatus === 'error') {
      return (
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <p>{error || 'Failed to load health check data'}</p>
          <button className="refresh-button" onClick={() => fetchHealthRisks(true)}>
            Try Again
          </button>
        </div>
      );
    }

    // Pending state
    if (healthRisksStatus === 'pending') {
      return (
        <DataStatusMessage
          status="pending"
          message="Health check data is being fetched in the background. This may take a few moments."
          onRefresh={() => fetchHealthRisks()}
        />
      );
    }

    // Empty state
    if (!healthRisks || healthRisks.length === 0) {
      return (
        <div className="empty-state">
          <div className="empty-icon">🔍</div>
          <h3>No Health Check Data Available</h3>
          <p>No data was found in the database. Click the <strong>Rescan</strong> button at the top of the page to fetch security health check data from Salesforce.</p>
          <button className="refresh-button" onClick={() => fetchHealthRisks(true)}>
            Rescan
          </button>
        </div>
      );
    }

    // Available state: show health score, last updated, charts, and tables
    const healthScoreGrade = getHealthScoreGrade(healthScore);
    const lastUpdated = orgData && orgData.lastScan ? new Date(orgData.lastScan).toLocaleString() : 'Unknown';

    // Prepare chart data
    const pieData = prepareRiskDistributionData();
    const barData = prepareSettingGroupData();

    // Chart options
    const pieOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              return `${context.label}: ${context.parsed}`;
            }
          }
        }
      }
    };

    const barOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              return `${context.dataset.label}: ${context.parsed.y}`;
            }
          }
        }
      },
      scales: {
        x: {
          stacked: true,
          grid: {
            display: false
          }
        },
        y: {
          stacked: true,
          beginAtZero: true,
          grid: {
            color: '#f0f0f0'
          }
        }
      }
    };

    return (
      <div className="health-check-figma-ui" style={{ background: '#F8FAF9', padding: '32px', borderRadius: '16px', fontFamily: 'Lato, Arial, sans-serif', maxWidth: 1200, margin: '0 auto' }}>
        {/* Health Score Section */}
        <div className="health-score-section" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '32px', padding: '24px', background: '#fff', border: '1px solid #E0E0E0', borderRadius: '8px' }}>
          <div className="health-score">
            <h4 style={{ margin: '0 0 8px 0', fontSize: '18px', color: '#333' }}>Health Score</h4>
            <div className="score-pill" style={{ 
              backgroundColor: healthScoreGrade.color, 
              padding: '8px 16px', 
              borderRadius: '20px', 
              color: 'white', 
              fontWeight: 'bold',
              fontSize: '16px'
            }}>
              {healthScore} ({healthScoreGrade.grade})
            </div>
          </div>
          <div className="last-updated" style={{ fontSize: '14px', color: '#666' }}>
            Last updated: {lastUpdated}
          </div>
        </div>

        {/* Filter Bar */}
        <div className="filter-bar" style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '24px', padding: '16px', background: '#fff', border: '1px solid #E0E0E0', borderRadius: '8px' }}>
          <span className="filter-label" style={{ fontWeight: '500', color: '#333' }}>Filter By:</span>
          <select
            className="filter-select"
            value={filterType}
            onChange={handleFilterChange}
            style={{ padding: '8px 12px', border: '1px solid #ddd', borderRadius: '4px', minWidth: '150px' }}
          >
            <option value="all">All Severity Levels</option>
            <option value="HIGH">High Risk</option>
            <option value="MEDIUM">Medium Risk</option>
            <option value="LOW">Low Risk</option>
          </select>
          <input
            type="text"
            placeholder="Search settings..."
            value={searchTerm}
            onChange={handleSearchChange}
            style={{ padding: '8px 12px', border: '1px solid #ddd', borderRadius: '4px', minWidth: '200px' }}
          />
          <button 
            className="filter-reset-btn" 
            onClick={() => { setFilterType('all'); setSearchTerm(''); }}
            style={{ 
              padding: '8px 16px', 
              background: '#51D59C', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px', 
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M12 5V2L7 7l5 5V8c3.31 0 6 2.69 6 6 0 1.3-.42 2.5-1.13 3.47l1.46 1.46C19.07 17.07 20 15.13 20 13c0-4.42-3.58-8-8-8z" fill="white"/>
            </svg>
            Reset
          </button>
        </div>

        {/* Charts Row: Pie left, Bar right */}
        <div style={{ display: 'flex', gap: 32, alignItems: 'flex-start', marginBottom: 32 }}>
          {/* Pie Chart Section */}
          <div style={{ width: 340, background: '#fff', border: '1px solid #E0E0E0', borderRadius: 8, padding: 24, minHeight: 460, display: 'flex', flexDirection: 'column', justifyContent: 'flex-start' }}>
            <div style={{ fontFamily: 'Poppins, Arial, sans-serif', fontWeight: 700, fontSize: 16, color: '#000', marginBottom: 16, textAlign: 'center' }}>Distribution of Risk Types</div>
            <div style={{ width: 260, height: 260, margin: '0 auto' }}>
              <Pie data={pieData} options={pieOptions} />
            </div>
            {/* Legend */}
            <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: 24, marginTop: 24, maxHeight: 80, overflowY: 'auto' }}>
              {pieData.labels.map((label, idx) => (
                <div key={label} style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <span style={{ width: 16, height: 16, borderRadius: 8, background: pieData.datasets[0].backgroundColor[idx], display: 'inline-block' }}></span>
                  <span style={{ fontSize: 14, color: '#393E3C' }}>{label.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}</span>
                </div>
              ))}
            </div>
          </div>
          {/* Bar Chart Section */}
          <div style={{ flex: 1, minWidth: 400, background: '#fff', border: '1px solid #E0E0E0', borderRadius: 8, padding: 24, minHeight: 460, display: 'flex', flexDirection: 'column', justifyContent: 'flex-start' }}>
            <div style={{ fontFamily: 'Poppins, Arial, sans-serif', fontWeight: 700, fontSize: 16, color: '#000', marginBottom: 16, textAlign: 'center' }}>Misaligned Settings</div>
            <div style={{ width: '100%', height: 260 }}>
              <Bar data={barData} options={barOptions} />
            </div>
            {/* Legend */}
            <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', gap: 24, marginTop: 16, maxHeight: 80, overflowY: 'auto' }}>
              {barData.datasets.map((dataset, idx) => (
                <div key={dataset.label} style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <span style={{ width: 16, height: 16, borderRadius: 8, background: dataset.backgroundColor, display: 'inline-block' }}></span>
                  <span style={{ fontSize: 14, color: '#393E3C' }}>{dataset.label}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* Security Risk Details Section */}
        <div style={{ background: '#fff', border: '1px solid #E0E0E0', borderRadius: 4, padding: '24px', marginTop: 24 }}>
          <div style={{ fontFamily: 'Poppins, Arial, sans-serif', fontWeight: 700, fontSize: 20, color: '#020A07', marginBottom: 8 }}>Security Risk Details</div>
          <div style={{ fontSize: 16, color: '#393E3C', marginBottom: 24 }}>Measures how closely your org's security settings align with Salesforce best practices.</div>
          
          {policiesResultStatus === 'loading' && (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div className="spinner"></div>
              <p>Loading security risk details...</p>
            </div>
          )}
          
          {policiesResultStatus === 'pending' && (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div className="spinner"></div>
              <p>Security risk details are being processed. Please wait...</p>
            </div>
          )}
          
          {policiesResultStatus === 'error' && (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div className="error-icon">⚠️</div>
              <p>Failed to load security risk details.</p>
              <button className="retry-button" onClick={fetchPoliciesResult}>Retry</button>
            </div>
          )}
          
          {policiesResultStatus === 'empty' && (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <div className="empty-icon">🔍</div>
              <p>No security risk details available.</p>
            </div>
          )}
          
          {policiesResultStatus === 'available' && filteredPoliciesResult.length > 0 && (
            <div style={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse', background: '#fff' }}>
                <thead>
                  <tr style={{ background: '#F1FCF7', borderBottom: '1px solid #B8D8CB' }}>
                    <th style={{ fontWeight: 500, fontSize: 14, color: '#393E3C', padding: '14px 8px 14px 16px', textAlign: 'left' }}>Risk</th>
                    <th style={{ fontWeight: 500, fontSize: 14, color: '#1D2433', padding: '14px 8px 14px 16px', textAlign: 'left' }}>Setting</th>
                    <th style={{ fontWeight: 500, fontSize: 14, color: '#1D2433', padding: '14px 8px 14px 16px', textAlign: 'left' }}>Org Value</th>
                    <th style={{ fontWeight: 500, fontSize: 14, color: '#393E3C', padding: '14px 8px 14px 16px', textAlign: 'left' }}>Standard Value</th>
                    <th style={{ fontWeight: 500, fontSize: 14, color: '#393E3C', padding: '14px 8px 14px 16px', textAlign: 'left' }}>OWASP Category</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPoliciesResult.map((row, idx) => {
                    const severity = row.Severity || row.severity || '';
                    const severityClass = severity.toLowerCase().includes('high') ? 'high-severity' :
                                         severity.toLowerCase().includes('medium') ? 'medium-severity' :
                                         'low-severity';
                    
                    return (
                      <tr key={idx} style={{ borderBottom: '1px solid #B8D8CB' }}>
                        <td style={{ fontSize: 14, color: '#1D2433', padding: '14px 8px 14px 16px' }}>
                          <span className={`severity-badge ${severityClass}`}>
                            {severity}
                          </span>
                        </td>
                        <td style={{ fontSize: 14, color: '#393E3C', padding: '14px 8px 14px 16px' }}>{row.Setting || row.setting}</td>
                        <td style={{ fontSize: 14, color: '#393E3C', padding: '14px 8px 14px 16px' }}>{row.OrgValue || row.orgValue}</td>
                        <td style={{ fontSize: 14, color: '#1D2433', padding: '14px 8px 14px 16px' }}>{row.StandardValue || row.standardValue}</td>
                        <td style={{ fontSize: 14, color: '#393E3C', padding: '14px 8px 14px 16px' }}>{row.OWASPCategory || row.owaspCategory}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    );
  };

  console.log('DEBUG render HealthCheckDetails', { orgData, activeTab });

  return (
    <div className="health-check-details">
      {/* Authentication Popup */}
      <AuthPopup
        isOpen={showAuthPopup}
        env={orgData?.environment || 'production'}
        instanceUrl={instanceUrl}
        onClose={() => setShowAuthPopup(false)}
        onSuccess={handleAuthSuccess}
      />

      <div className="page-header">
        <h2>Salesforce Org Details</h2>
        {orgData && <h3>{orgData.name}</h3>}
      </div>

      <div className="tabs-container">
        <div className="tabs">
          <button
            className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => handleTabChange('overview')}
          >
            Overview
          </button>
          <button
            className={`tab-button ${activeTab === 'healthCheck' ? 'active' : ''}`}
            onClick={() => handleTabChange('healthCheck')}
          >
            Health Check
          </button>
          <button
            className={`tab-button ${activeTab === 'profilesPermissions' ? 'active' : ''}`}
            onClick={() => handleTabChange('profilesPermissions')}
          >
            Profiles and Permission Sets
          </button>
          <button
            className={`tab-button ${activeTab === 'guestUserProfile' ? 'active' : ''}`}
            onClick={() => handleTabChange('guestUserProfile')}
          >
            Guest User Profile Risks
          </button>

        </div>
      </div>

      <div className="tab-content">
        {authError ? (
          <div className="auth-error-container">
            <div className="auth-error-message">
              <h3>Authentication Required</h3>
              <p>You need to authenticate with Salesforce to view this data.</p>
              <button className="btn btn-primary" onClick={handleAuthenticate}>
                Authenticate with Salesforce
              </button>
            </div>
          </div>
        ) : (
          <>
            {activeTab === 'overview' && renderOverviewTab()}
            {activeTab === 'healthCheck' && renderHealthCheckTab()}
            {activeTab === 'profilesPermissions' && <div className="coming-soon"><h3>Profiles and Permission Sets - analysis coming soon</h3></div>}
            {activeTab === 'guestUserProfile' && <div className="coming-soon"><h3>Guest User Profile Risks - analysis coming soon</h3></div>}
          </>
        )}
      </div>
    </div>
  );
};

export default HealthCheckDetails;
