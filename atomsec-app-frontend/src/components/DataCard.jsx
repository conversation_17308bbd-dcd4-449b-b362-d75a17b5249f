import React from 'react';
import './DataCard.css';

/**
 * DataCard component for displaying statistics with optional trend indicators
 * Updated to match the Figma design
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Card title
 * @param {string|number} props.value - Main value to display
 * @param {number} [props.trend] - Optional trend percentage (positive or negative)
 * @param {boolean} [props.trendUp] - Whether trend is upward (true) or downward (false)
 * @param {Function} [props.onRefresh] - Optional refresh callback function
 * @param {boolean} [props.isRefreshing] - Whether the card is currently refreshing
 * @param {string} [props.icon] - Optional icon name to display (issues, critical, orgs, inactive)
 * @param {Function} [props.onNavigate] - Optional navigation callback function
 */
const DataCard = ({ title, value, trend, trendUp, onRefresh, isRefreshing, icon, onNavigate }) => {
  // Function to get the appropriate icon based on the icon name
  const getIconElement = (iconName) => {
    switch (iconName) {
      case 'issues':
        return (
          <div className="data-card-icon issues">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="#51D59C"/>
              <path d="M11 7H13V13H11V7ZM11 15H13V17H11V15Z" fill="#51D59C"/>
            </svg>
          </div>
        );
      case 'critical':
        return (
          <div className="data-card-icon critical">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="#ED6C02"/>
              <path d="M11 7H13V13H11V7ZM11 15H13V17H11V15Z" fill="#ED6C02"/>
            </svg>
          </div>
        );
      case 'orgs':
        return (
          <div className="data-card-icon orgs">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 7V3H2V21H22V7H12ZM6 19H4V17H6V19ZM6 15H4V13H6V15ZM6 11H4V9H6V11ZM6 7H4V5H6V7ZM10 19H8V17H10V19ZM10 15H8V13H10V15ZM10 11H8V9H10V11ZM10 7H8V5H10V7ZM20 19H12V17H14V15H12V13H14V11H12V9H20V19ZM18 11H16V13H18V11ZM18 15H16V17H18V15Z" fill="#155B55"/>
            </svg>
          </div>
        );
      case 'inactive':
        return (
          <div className="data-card-icon inactive">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.47 2 2 6.47 2 12C2 17.53 6.47 22 12 22C17.53 22 22 17.53 22 12C22 6.47 17.53 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM15.59 7L12 10.59L8.41 7L7 8.41L10.59 12L7 15.59L8.41 17L12 13.41L15.59 17L17 15.59L13.41 12L17 8.41L15.59 7Z" fill="#D32F2F"/>
            </svg>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="data-card">
      <div className="data-card-header">
        <div className="data-card-title-container">
          {icon && getIconElement(icon)}
          <h3 className="data-card-title">{title}</h3>
        </div>
        {onNavigate && (
          <div className="data-card-navigate">
            <button
              className="data-card-navigate-btn"
              onClick={onNavigate}
              aria-label="Navigate to details"
            >
              <img
                src="/assets/dashboard/navigate_next.svg"
                alt="Navigate"
              />
            </button>
          </div>
        )}
      </div>
      <div className="data-card-content">
        <div className="value-container">
          <h2 className="data-card-value">{value}</h2>
          {trend !== undefined && (
            <div className={`trend-badge ${trendUp ? 'up' : 'down'}`}>
              <img
                src={trendUp
                  ? "/assets/dashboard/arrow_upward.svg"
                  : "/assets/dashboard/arrow_downward.svg"}
                alt={trendUp ? 'Trending up' : 'Trending down'}
                className="icon"
              />
              <span>{Math.abs(trend)}%</span>
            </div>
          )}
        </div>
        {onRefresh && (
          <div className="data-card-actions">
            <button
              className="refresh-button"
              onClick={onRefresh}
              disabled={isRefreshing}
            >
              <img
                src="/assets/dashboard/refresh.svg"
                alt="Refresh"
                className={`icon ${isRefreshing ? 'rotating' : ''}`}
              />
              <span>Refresh</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DataCard;
