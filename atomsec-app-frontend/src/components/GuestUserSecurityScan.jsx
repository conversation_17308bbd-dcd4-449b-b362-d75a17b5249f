import React, { useState } from 'react';
import { toast } from 'react-toastify';
import {
  Security,
  BugReport,
  Shield,
  Warning,
  CheckCircle,
  Error,
  Info,
  PlayArrow,
  Stop
} from '@mui/icons-material';
import './GuestUserSecurityScan.css';

const GuestUserSecurityScan = () => {
  const [targetUrl, setTargetUrl] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState(null);
  const [scanProgress, setScanProgress] = useState(0);
  const [currentCheck, setCurrentCheck] = useState('');

  // Validate target URL
  const validateUrl = (url) => {
    try {
      const urlObj = new URL(url);
      if (!urlObj.protocol || !urlObj.hostname) {
        return { valid: false, error: 'Please enter a valid URL with protocol (http:// or https://)' };
      }
      return { valid: true };
    } catch (error) {
      return { valid: false, error: 'Please enter a valid URL format' };
    }
  };

  // Start security scan
  const startScan = async () => {
    // Validate URL
    const urlValidation = validateUrl(targetUrl);
    if (!urlValidation.valid) {
      toast.error(urlValidation.error);
      return;
    }

    // Show ethical guidelines
    const confirmed = window.confirm(
      '⚠️ ETHICAL GUIDELINES\n\n' +
      '1. You must have permission to scan this website\n' +
      '2. This scan will be performed responsibly with rate limiting\n' +
      '3. Only public-facing security checks will be performed\n' +
      '4. No intrusive or harmful tests will be conducted\n\n' +
      'Do you confirm you have permission to scan this website?'
    );

    if (!confirmed) {
      toast.info('Scan cancelled. Please ensure you have proper permission.');
      return;
    }

    setIsScanning(true);
    setScanResults(null);
    setScanProgress(0);
    setCurrentCheck('Initializing scan...');

    try {
      // Simulate scan progress
      const scanSteps = [
        'Checking website accessibility...',
        'Analyzing security headers...',
        'Testing SSL/TLS configuration...',
        'Scanning for common vulnerabilities...',
        'Checking for exposed endpoints...',
        'Analyzing response patterns...',
        'Generating security report...'
      ];

      for (let i = 0; i < scanSteps.length; i++) {
        setCurrentCheck(scanSteps[i]);
        setScanProgress(((i + 1) / scanSteps.length) * 100);
        
        // Simulate scan time
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      }

      // Generate mock results
      const results = await performSecurityChecks(targetUrl);
      setScanResults(results);
      toast.success('Security scan completed successfully!');

    } catch (error) {
      console.error('Scan error:', error);
      toast.error('Scan failed: ' + error.message);
    } finally {
      setIsScanning(false);
      setScanProgress(0);
      setCurrentCheck('');
    }
  };

  // Stop scan
  const stopScan = () => {
    setIsScanning(false);
    setScanProgress(0);
    setCurrentCheck('');
    toast.info('Scan stopped by user');
  };

  // Perform security checks
  const performSecurityChecks = async (url) => {
    // This would call your backend API
    // For now, we'll simulate the results
    
    const mockResults = {
      targetUrl: url,
      scanTimestamp: new Date().toISOString(),
      duration: '45 seconds',
      issues: [
        {
          title: 'Missing Security Headers',
          description: 'Several important security headers are not configured.',
          severity: 'medium',
          details: [
            'X-Frame-Options header is missing (clickjacking protection)',
            'X-Content-Type-Options header is missing (MIME type sniffing protection)',
            'Strict-Transport-Security header is missing (HTTPS enforcement)'
          ],
          recommendation: 'Configure security headers to improve protection against common attacks.'
        },
        {
          title: 'Weak SSL Configuration',
          description: 'SSL/TLS configuration has some security weaknesses.',
          severity: 'low',
          details: [
            'TLS 1.0 and 1.1 are enabled (should be disabled)',
            'Weak cipher suites are supported',
            'Certificate has long validity period'
          ],
          recommendation: 'Disable older TLS versions and weak cipher suites.'
        },
        {
          title: 'Information Disclosure',
          description: 'Some sensitive information is exposed in responses.',
          severity: 'medium',
          details: [
            'Server version information is exposed in headers',
            'Error messages reveal internal structure',
            'Directory listing is enabled'
          ],
          recommendation: 'Configure server to hide version information and sanitize error messages.'
        }
      ],
      summary: {
        total_issues: 3,
        critical: 0,
        high: 0,
        medium: 2,
        low: 1
      }
    };

    return mockResults;
  };

  // Get severity color
  const getSeverityColor = (severity) => {
    switch (severity.toLowerCase()) {
      case 'critical': return '#d32f2f';
      case 'high': return '#f57c00';
      case 'medium': return '#fbc02d';
      case 'low': return '#388e3c';
      default: return '#757575';
    }
  };

  return (
    <div className="guest-user-security-scan">
      <div className="scan-header">
        <div className="header-content">
          <h2>Guest User Live Security Scan</h2>
          <p>Perform real-time security assessment on public websites</p>
        </div>
        <div className="header-icon">
          <Security className="security-icon" />
        </div>
      </div>

      {/* Ethical Guidelines */}
      <div className="ethical-guidelines">
        <div className="guidelines-header">
          <Warning className="warning-icon" />
          <h3>Important Guidelines</h3>
        </div>
        <ul>
          <li>✅ Only scan websites you own or have explicit permission to test</li>
          <li>✅ This tool performs non-intrusive, public security checks only</li>
          <li>✅ Rate limiting is applied to prevent service disruption</li>
          <li>✅ All findings are for educational and security improvement purposes</li>
        </ul>
      </div>

      {/* Scan Configuration */}
      <div className="scan-config">
        <div className="url-input-section">
          <label htmlFor="targetUrl">Website URL to Scan *</label>
          <div className="url-input-container">
            <input
              id="targetUrl"
              type="url"
              value={targetUrl}
              onChange={(e) => setTargetUrl(e.target.value)}
              placeholder="https://example.com"
              disabled={isScanning}
              className="url-input"
            />
            <button
              className={`scan-button ${isScanning ? 'scanning' : ''}`}
              onClick={isScanning ? stopScan : startScan}
              disabled={!targetUrl.trim()}
            >
              {isScanning ? (
                <>
                  <Stop className="button-icon" />
                  Stop Scan
                </>
              ) : (
                <>
                  <PlayArrow className="button-icon" />
                  Start Scan
                </>
              )}
            </button>
          </div>
          <small>Enter the full URL including protocol (http:// or https://)</small>
        </div>
      </div>

      {/* Scan Progress */}
      {isScanning && (
        <div className="scan-progress">
          <div className="progress-header">
            <h3>Scan in Progress</h3>
            <span className="progress-percentage">{Math.round(scanProgress)}%</span>
          </div>
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${scanProgress}%` }}
            ></div>
          </div>
          <p className="current-check">{currentCheck}</p>
        </div>
      )}

      {/* Scan Results */}
      {scanResults && (
        <div className="scan-results">
          <div className="results-header">
            <h3>Security Scan Results</h3>
            <div className="results-summary">
              <div className="summary-item">
                <span className="label">Target:</span>
                <span className="value">{scanResults.targetUrl}</span>
              </div>
              <div className="summary-item">
                <span className="label">Scan Time:</span>
                <span className="value">{scanResults.duration}</span>
              </div>
              <div className="summary-item">
                <span className="label">Issues Found:</span>
                <span className="value">{scanResults.summary.total_issues}</span>
              </div>
            </div>
          </div>

          {scanResults.issues && scanResults.issues.length > 0 ? (
            <div className="issues-list">
              {scanResults.issues.map((issue, index) => (
                <div key={index} className="issue-item">
                  <div className="issue-header">
                    <span
                      className="severity-badge"
                      style={{ backgroundColor: getSeverityColor(issue.severity) }}
                    >
                      {issue.severity}
                    </span>
                    <h4>{issue.title}</h4>
                  </div>
                  <p className="issue-description">{issue.description}</p>
                  
                  {issue.details && issue.details.length > 0 && (
                    <div className="issue-details">
                      <strong>Details:</strong>
                      <ul>
                        {issue.details.map((detail, detailIndex) => (
                          <li key={detailIndex}>{detail}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {issue.recommendation && (
                    <div className="issue-recommendation">
                      <strong>Recommendation:</strong> {issue.recommendation}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="no-issues">
              <CheckCircle className="success-icon" />
              <p>No security issues found in this scan.</p>
              <small>The website appears to have good security practices implemented.</small>
            </div>
          )}
        </div>
      )}

      {/* Scan Tips */}
      <div className="scan-tips">
        <div className="tips-header">
          <Info className="info-icon" />
          <h3>Security Scan Tips</h3>
        </div>
        <div className="tips-content">
          <div className="tip-item">
            <strong>What this scan checks:</strong>
            <ul>
              <li>Security header configurations</li>
              <li>SSL/TLS certificate and configuration</li>
              <li>Common vulnerability patterns</li>
              <li>Information disclosure risks</li>
              <li>Server configuration security</li>
            </ul>
          </div>
          <div className="tip-item">
            <strong>Best practices:</strong>
            <ul>
              <li>Run scans during low-traffic periods</li>
              <li>Review and act on findings promptly</li>
              <li>Keep security configurations updated</li>
              <li>Monitor for new vulnerabilities regularly</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GuestUserSecurityScan; 