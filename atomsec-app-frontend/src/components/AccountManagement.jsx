import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import './AccountManagement.css';
import {
  fetchAccounts,
  fetchUsers,
  fetchRoles,
  createAccount,
  updateAccount,
  createUser,
  updateUser,
  assignUserRole
} from '../api';

const AccountManagement = () => {
  // State for accounts
  const [accounts, setAccounts] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [newAccount, setNewAccount] = useState({ name: '' });
  const [isAddingAccount, setIsAddingAccount] = useState(false);
  const [isEditingAccount, setIsEditingAccount] = useState(false);
  
  // State for users
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [newUser, setNewUser] = useState({ name: '', email: '', phone: '', accountId: '' });
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [isEditingUser, setIsEditingUser] = useState(false);
  
  // State for roles
  const [roles, setRoles] = useState([]);
  const [selectedRole, setSelectedRole] = useState(null);
  const [isAssigningRole, setIsAssigningRole] = useState(false);
  
  // Loading and error states
  const [loading, setLoading] = useState({
    accounts: false,
    users: false,
    roles: false
  });
  const [error, setError] = useState({
    accounts: null,
    users: null,
    roles: null
  });
  
  // Fetch accounts, users, and roles on component mount
  useEffect(() => {
    fetchAccountsData();
    fetchRolesData();
  }, []);
  
  // Fetch users when selected account changes
  useEffect(() => {
    if (selectedAccount) {
      fetchUsersData(selectedAccount.ID);
    } else {
      setUsers([]);
    }
  }, [selectedAccount]);
  
  // Fetch accounts data
  const fetchAccountsData = async () => {
    try {
      setLoading(prev => ({ ...prev, accounts: true }));
      setError(prev => ({ ...prev, accounts: null }));
      
      const data = await fetchAccounts();
      setAccounts(data);
      
      setLoading(prev => ({ ...prev, accounts: false }));
    } catch (error) {
      console.error('Error fetching accounts:', error);
      setError(prev => ({ ...prev, accounts: 'Failed to fetch accounts. Please try again.' }));
      setLoading(prev => ({ ...prev, accounts: false }));
    }
  };
  
  // Fetch users data
  const fetchUsersData = async (accountId) => {
    try {
      setLoading(prev => ({ ...prev, users: true }));
      setError(prev => ({ ...prev, users: null }));
      
      const data = await fetchUsers(accountId);
      setUsers(data);
      
      setLoading(prev => ({ ...prev, users: false }));
    } catch (error) {
      console.error('Error fetching users:', error);
      setError(prev => ({ ...prev, users: 'Failed to fetch users. Please try again.' }));
      setLoading(prev => ({ ...prev, users: false }));
    }
  };
  
  // Fetch roles data
  const fetchRolesData = async () => {
    try {
      setLoading(prev => ({ ...prev, roles: true }));
      setError(prev => ({ ...prev, roles: null }));
      
      const data = await fetchRoles();
      setRoles(data);
      
      setLoading(prev => ({ ...prev, roles: false }));
    } catch (error) {
      console.error('Error fetching roles:', error);
      setError(prev => ({ ...prev, roles: 'Failed to fetch roles. Please try again.' }));
      setLoading(prev => ({ ...prev, roles: false }));
    }
  };
  
  // Handle account selection
  const handleAccountSelect = (account) => {
    setSelectedAccount(account);
    setSelectedUser(null);
  };
  
  // Handle user selection
  const handleUserSelect = (user) => {
    setSelectedUser(user);
  };
  
  // Handle new account form change
  const handleNewAccountChange = (e) => {
    setNewAccount({
      ...newAccount,
      [e.target.name]: e.target.value
    });
  };
  
  // Handle new user form change
  const handleNewUserChange = (e) => {
    setNewUser({
      ...newUser,
      [e.target.name]: e.target.value
    });
  };
  
  // Handle add account
  const handleAddAccount = async (e) => {
    e.preventDefault();
    
    try {
      const result = await createAccount(newAccount);
      
      if (result) {
        toast.success('Account created successfully');
        setNewAccount({ name: '' });
        setIsAddingAccount(false);
        fetchAccountsData();
      }
    } catch (error) {
      console.error('Error creating account:', error);
      toast.error('Failed to create account');
    }
  };
  
  // Handle edit account
  const handleEditAccount = async (e) => {
    e.preventDefault();
    
    try {
      const result = await updateAccount(selectedAccount.ID, newAccount);
      
      if (result) {
        toast.success('Account updated successfully');
        setNewAccount({ name: '' });
        setIsEditingAccount(false);
        fetchAccountsData();
      }
    } catch (error) {
      console.error('Error updating account:', error);
      toast.error('Failed to update account');
    }
  };
  
  // Handle add user
  const handleAddUser = async (e) => {
    e.preventDefault();
    
    try {
      const userData = {
        ...newUser,
        accountId: selectedAccount.ID
      };
      
      const result = await createUser(userData);
      
      if (result) {
        toast.success('User created successfully');
        setNewUser({ name: '', email: '', phone: '', accountId: '' });
        setIsAddingUser(false);
        fetchUsersData(selectedAccount.ID);
      }
    } catch (error) {
      console.error('Error creating user:', error);
      toast.error('Failed to create user');
    }
  };
  
  // Handle edit user
  const handleEditUser = async (e) => {
    e.preventDefault();
    
    try {
      const userData = {
        ...newUser,
        accountId: selectedAccount.ID
      };
      
      const result = await updateUser(selectedUser.UserId, userData);
      
      if (result) {
        toast.success('User updated successfully');
        setNewUser({ name: '', email: '', phone: '', accountId: '' });
        setIsEditingUser(false);
        fetchUsersData(selectedAccount.ID);
      }
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    }
  };
  
  // Handle assign role
  const handleAssignRole = async (e) => {
    e.preventDefault();
    
    try {
      const result = await assignUserRole(selectedUser.UserId, selectedRole.RoleId);
      
      if (result) {
        toast.success('Role assigned successfully');
        setSelectedRole(null);
        setIsAssigningRole(false);
        fetchUsersData(selectedAccount.ID);
      }
    } catch (error) {
      console.error('Error assigning role:', error);
      toast.error('Failed to assign role');
    }
  };
  
  // Start adding account
  const startAddingAccount = () => {
    setNewAccount({ name: '' });
    setIsAddingAccount(true);
    setIsEditingAccount(false);
  };
  
  // Start editing account
  const startEditingAccount = () => {
    setNewAccount({
      name: selectedAccount.Name
    });
    setIsEditingAccount(true);
    setIsAddingAccount(false);
  };
  
  // Start adding user
  const startAddingUser = () => {
    setNewUser({ name: '', email: '', phone: '', accountId: '' });
    setIsAddingUser(true);
    setIsEditingUser(false);
  };
  
  // Start editing user
  const startEditingUser = () => {
    setNewUser({
      name: selectedUser.Name,
      email: selectedUser.Email,
      phone: selectedUser.Phone || ''
    });
    setIsEditingUser(true);
    setIsAddingUser(false);
  };
  
  // Start assigning role
  const startAssigningRole = () => {
    setSelectedRole(null);
    setIsAssigningRole(true);
  };
  
  // Cancel form
  const cancelForm = (formType) => {
    switch (formType) {
      case 'account':
        setIsAddingAccount(false);
        setIsEditingAccount(false);
        setNewAccount({ name: '' });
        break;
      case 'user':
        setIsAddingUser(false);
        setIsEditingUser(false);
        setNewUser({ name: '', email: '', phone: '', accountId: '' });
        break;
      case 'role':
        setIsAssigningRole(false);
        setSelectedRole(null);
        break;
      default:
        break;
    }
  };
  
  return (
    <div className="account-management">
      <h2>Account Management</h2>
      
      <div className="account-management-container">
        {/* Accounts Section */}
        <div className="accounts-section">
          <div className="section-header">
            <h3>Accounts</h3>
            <button className="add-button" onClick={startAddingAccount}>Add Account</button>
          </div>
          
          {loading.accounts ? (
            <div className="loading-state">Loading accounts...</div>
          ) : error.accounts ? (
            <div className="error-state">
              <p>{error.accounts}</p>
              <button className="retry-button" onClick={fetchAccountsData}>Retry</button>
            </div>
          ) : (
            <div className="accounts-list">
              {accounts.length === 0 ? (
                <div className="empty-state">No accounts found</div>
              ) : (
                <ul>
                  {accounts.map(account => (
                    <li 
                      key={account.ID} 
                      className={selectedAccount && selectedAccount.ID === account.ID ? 'selected' : ''}
                      onClick={() => handleAccountSelect(account)}
                    >
                      <span className="account-name">{account.Name}</span>
                      <span className="account-status">{account.IsActive ? 'Active' : 'Inactive'}</span>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}
          
          {/* Add/Edit Account Form */}
          {(isAddingAccount || isEditingAccount) && (
            <div className="form-container">
              <h4>{isAddingAccount ? 'Add Account' : 'Edit Account'}</h4>
              <form onSubmit={isAddingAccount ? handleAddAccount : handleEditAccount}>
                <div className="form-group">
                  <label htmlFor="name">Account Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={newAccount.name}
                    onChange={handleNewAccountChange}
                    required
                  />
                </div>
                
                <div className="form-actions">
                  <button type="submit" className="submit-button">
                    {isAddingAccount ? 'Add' : 'Update'}
                  </button>
                  <button 
                    type="button" 
                    className="cancel-button"
                    onClick={() => cancelForm('account')}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}
        </div>
        
        {/* Users Section */}
        <div className="users-section">
          <div className="section-header">
            <h3>Users</h3>
            {selectedAccount && (
              <button 
                className="add-button" 
                onClick={startAddingUser}
                disabled={!selectedAccount}
              >
                Add User
              </button>
            )}
          </div>
          
          {!selectedAccount ? (
            <div className="empty-state">Select an account to view users</div>
          ) : loading.users ? (
            <div className="loading-state">Loading users...</div>
          ) : error.users ? (
            <div className="error-state">
              <p>{error.users}</p>
              <button className="retry-button" onClick={() => fetchUsersData(selectedAccount.ID)}>Retry</button>
            </div>
          ) : (
            <div className="users-list">
              {users.length === 0 ? (
                <div className="empty-state">No users found</div>
              ) : (
                <ul>
                  {users.map(user => (
                    <li 
                      key={user.UserId} 
                      className={selectedUser && selectedUser.UserId === user.UserId ? 'selected' : ''}
                      onClick={() => handleUserSelect(user)}
                    >
                      <div className="user-info">
                        <span className="user-name">{user.Name}</span>
                        <span className="user-email">{user.Email}</span>
                      </div>
                      <div className="user-roles">
                        {user.Roles && user.Roles.map(role => (
                          <span key={role.RoleId} className="role-badge">{role.Rolename}</span>
                        ))}
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}
          
          {/* User Actions */}
          {selectedUser && (
            <div className="user-actions">
              <button className="edit-button" onClick={startEditingUser}>Edit User</button>
              <button className="assign-role-button" onClick={startAssigningRole}>Assign Role</button>
            </div>
          )}
          
          {/* Add/Edit User Form */}
          {(isAddingUser || isEditingUser) && (
            <div className="form-container">
              <h4>{isAddingUser ? 'Add User' : 'Edit User'}</h4>
              <form onSubmit={isAddingUser ? handleAddUser : handleEditUser}>
                <div className="form-group">
                  <label htmlFor="name">Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={newUser.name}
                    onChange={handleNewUserChange}
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="email">Email</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={newUser.email}
                    onChange={handleNewUserChange}
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="phone">Phone (optional)</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={newUser.phone}
                    onChange={handleNewUserChange}
                  />
                </div>
                
                <div className="form-actions">
                  <button type="submit" className="submit-button">
                    {isAddingUser ? 'Add' : 'Update'}
                  </button>
                  <button 
                    type="button" 
                    className="cancel-button"
                    onClick={() => cancelForm('user')}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}
          
          {/* Assign Role Form */}
          {isAssigningRole && (
            <div className="form-container">
              <h4>Assign Role to {selectedUser.Name}</h4>
              <form onSubmit={handleAssignRole}>
                <div className="form-group">
                  <label htmlFor="role">Role</label>
                  <select
                    id="role"
                    name="role"
                    value={selectedRole ? selectedRole.RoleId : ''}
                    onChange={(e) => {
                      const roleId = e.target.value;
                      const role = roles.find(r => r.RoleId.toString() === roleId);
                      setSelectedRole(role);
                    }}
                    required
                  >
                    <option value="">Select a role</option>
                    {roles.map(role => (
                      <option key={role.RoleId} value={role.RoleId}>
                        {role.Rolename}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="form-actions">
                  <button 
                    type="submit" 
                    className="submit-button"
                    disabled={!selectedRole}
                  >
                    Assign
                  </button>
                  <button 
                    type="button" 
                    className="cancel-button"
                    onClick={() => cancelForm('role')}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AccountManagement;
