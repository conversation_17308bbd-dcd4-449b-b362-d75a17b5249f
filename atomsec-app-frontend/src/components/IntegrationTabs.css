.integration-tabs {
  width: 100%;
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  font-size: 24px;
  margin-bottom: 5px;
}

.page-header h3 {
  font-size: 18px;
  color: #666;
  margin-top: 0;
}

.integration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  flex-grow: 1;
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  position: relative;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #0066cc;
}

.tab-button.active {
  color: #0066cc;
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #0066cc;
}

.tab-content {
  padding: 20px 0;
}

.loading-state, .error-state, .no-data-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  min-height: 300px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #0066cc;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon, .empty-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.empty-state h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.empty-state p {
  margin-bottom: 20px;
  color: #666;
  max-width: 500px;
}

.refresh-button {
  padding: 8px 16px;
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.refresh-button:hover {
  background-color: #0055aa;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.refresh-button:active {
  transform: translateY(0);
  box-shadow: none;
}

.refresh-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.refresh-button.primary {
  background-color: #28a745;
  font-size: 16px;
  padding: 10px 20px;
}

.refresh-button.primary:hover {
  background-color: #218838;
}

.rescan-button {
  padding: 10px 25px;
  background-color: #155B55;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  margin-left: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.rescan-button:hover {
  background-color: #17635c;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.rescan-button:active {
  transform: translateY(0);
  box-shadow: none;
}

.rescan-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Overview Tab Styles */
.overview-content, .health-check-content, .profiles-content, .guest-user-risks-content, .pmd-issues-content {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.overview-header, .health-check-header, .profiles-header, .guest-user-risks-header, .pmd-issues-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.overview-header h3, .health-check-header h3, .profiles-header h3, .guest-user-risks-header h3, .pmd-issues-header h3 {
  margin: 0;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-card h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #555;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.score-pill {
  display: inline-block;
  padding: 5px 15px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 18px;
  color: white;
}

.score-pill.high {
  background-color: #28a745;
}

.score-pill.medium {
  background-color: #ffc107;
  color: #333;
}

.score-pill.low {
  background-color: #dc3545;
}

.risk-summary {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.risk-summary h4 {
  margin-top: 0;
  margin-bottom: 15px;
}

.risk-bars {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.risk-bar {
  display: flex;
  align-items: center;
  gap: 10px;
}

.risk-label {
  width: 60px;
  font-weight: 500;
}

.bar-container {
  flex-grow: 1;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.bar {
  height: 100%;
  border-radius: 10px;
}

.bar.high {
  background-color: #dc3545;
}

.bar.medium {
  background-color: #ffc107;
}

.bar.low {
  background-color: #28a745;
}

.risk-count {
  width: 30px;
  text-align: right;
  font-weight: 500;
}

.last-updated {
  font-size: 12px;
  color: #777;
  text-align: right;
  margin-top: 20px;
}

/* Health Check Tab Styles */
.health-score {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  text-align: center;
}

.health-score h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.risks-table {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.risks-table h4 {
  margin-top: 0;
  margin-bottom: 15px;
}

.risks-table table {
  width: 100%;
  border-collapse: collapse;
}

.risks-table th, .risks-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.risks-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.risks-table tr.high-risk {
  background-color: rgba(220, 53, 69, 0.1);
}

.risks-table tr.medium-risk {
  background-color: rgba(255, 193, 7, 0.1);
}

.risks-table tr.low-risk {
  background-color: rgba(40, 167, 69, 0.1);
}

.risks-table tr.meets-standard {
  background-color: rgba(40, 167, 69, 0.2);
}

.no-risks-message {
  padding: 20px;
  text-align: center;
  background-color: rgba(40, 167, 69, 0.1);
  border-radius: 8px;
  margin-top: 10px;
}

.no-risks-message p {
  color: #28a745;
  font-weight: 500;
  margin: 0;
}

/* Health Score Section */
.health-score-section {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.health-score {
  flex: 0 0 auto;
  text-align: center;
  margin-right: 30px;
}

.risk-summary {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: 15px;
}

.risk-count {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  min-width: 180px;
  margin: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.risk-count .count {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.risk-count .label {
  font-size: 14px;
  color: #555;
  text-align: center;
  line-height: 1.3;
}

.risk-count.high {
  background-color: rgba(220, 53, 69, 0.1);
}

.risk-count.high .count {
  color: #dc3545;
}

.risk-count.medium {
  background-color: rgba(255, 193, 7, 0.1);
}

.risk-count.medium .count {
  color: #ffc107;
}

.risk-count.low {
  background-color: rgba(40, 167, 69, 0.1);
}

.risk-count.low .count {
  color: #28a745;
}

.risk-count.meets {
  background-color: rgba(23, 162, 184, 0.1);
}

.risk-count.meets .count {
  color: #17a2b8;
}

/* Empty State Styles */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  text-align: center;
  min-height: 300px;
}

.empty-state .empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 20px;
}

.empty-state p {
  margin-bottom: 25px;
  color: #666;
  max-width: 500px;
  line-height: 1.5;
}

.empty-state .refresh-button.primary {
  background-color: #28a745;
  color: white;
  padding: 10px 20px;
  font-size: 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.empty-state .refresh-button.primary:hover {
  background-color: #218838;
}

/* Loading State Styles */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  min-height: 300px;
  text-align: center;
}

.loading-state .spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 102, 204, 0.2);
  border-radius: 50%;
  border-top-color: #0066cc;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-state p {
  color: #666;
  font-size: 16px;
}

/* Error State Styles */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background-color: #fff5f5;
  border-radius: 8px;
  text-align: center;
  min-height: 300px;
}

.error-state .error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.error-state p {
  margin-bottom: 25px;
  color: #dc3545;
  max-width: 500px;
  line-height: 1.5;
}

.error-state .refresh-button {
  background-color: #dc3545;
  color: white;
  padding: 10px 20px;
  font-size: 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.error-state .refresh-button:hover {
  background-color: #c82333;
}

/* Profiles Tab Styles */
.profiles-section, .permission-sets-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.profiles-section h4, .permission-sets-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
}

.profiles-list, .permission-sets-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.profile-card, .permission-set-card {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.profile-card h5, .permission-set-card h5 {
  margin-top: 0;
  margin-bottom: 5px;
  color: #333;
}

.profile-card p, .permission-set-card p {
  margin-top: 0;
  margin-bottom: 10px;
  color: #666;
  font-size: 14px;
}

.permissions-summary {
  margin-top: 10px;
}

.permissions-summary h6 {
  margin-top: 0;
  margin-bottom: 5px;
  color: #555;
}

.permissions-summary ul {
  margin: 0;
  padding-left: 20px;
  font-size: 14px;
}

.permissions-summary li {
  margin-bottom: 8px;
  padding: 5px;
  border-radius: 4px;
  position: relative;
}

/* Risk level styling */
.risk-level-high-risk {
  background-color: rgba(220, 53, 69, 0.05);
  border-left: 3px solid #dc3545;
}

.risk-level-medium-risk {
  background-color: rgba(255, 193, 7, 0.05);
  border-left: 3px solid #ffc107;
}

.risk-level-low-risk {
  background-color: rgba(40, 167, 69, 0.05);
  border-left: 3px solid #28a745;
}

.risk-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 8px;
}

.risk-level-high-risk .risk-badge {
  background-color: #dc3545;
  color: white;
}

.risk-level-medium-risk .risk-badge {
  background-color: #ffc107;
  color: #333;
}

.risk-level-low-risk .risk-badge {
  background-color: #28a745;
  color: white;
}

/* Profile and permission set card styling */
.profile-card, .permission-set-card {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.profile-header h5 {
  margin: 0;
}

/* System permissions styling */
.system-permissions {
  margin-top: 10px;
}

.permission-group {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 6px;
}

.permission-group h6 {
  margin-top: 0;
  margin-bottom: 8px;
  font-weight: 600;
}

.permission-group ul {
  margin: 0;
  padding-left: 20px;
}

.permission-group li {
  margin-bottom: 4px;
}

/* Risk explanation and filter styles */
.risk-explanation {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border-left: 4px solid #0066cc;
}

.risk-explanation h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #0066cc;
}

.risk-explanation p {
  margin-bottom: 10px;
}

.risk-explanation ul {
  margin: 0;
  padding-left: 20px;
}

.risk-explanation li {
  margin-bottom: 5px;
}

.risk-filter {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
}

.risk-filter label {
  margin-right: 10px;
  font-weight: 500;
}

.risk-filter select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
  min-width: 200px;
}

/* Risk summary styles */
.risk-counts {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: 15px;
  margin-bottom: 20px;
}

/* Guest User Profile Risks Tab Styles */
/* Already covered by the risks-table styles */

/* Security Data Styles */
.button-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.tab-content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.security-data-content {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-top: 20px;
}

.security-data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.security-data-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #020A07;
}

/* PMD Issues Tab Styles */
.pmd-issues-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.pmd-issues-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.pmd-issues-tabs .tab-button {
  padding: 10px 20px;
  background-color: transparent;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  font-weight: 500;
  color: #666;
}

.pmd-issues-tabs .tab-button.active {
  border-bottom: 3px solid var(--color-primary, #0066cc);
  color: var(--color-primary, #0066cc);
}

.pmd-issues-filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  align-items: center;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 250px;
}

.priority-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-left: 10px;
}

.issues-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.issues-table th {
  background-color: #f2f2f2;
  padding: 12px;
  text-align: left;
  font-weight: bold;
  border-bottom: 2px solid #ddd;
}

.issues-table td {
  padding: 12px;
  border-bottom: 1px solid #ddd;
}

.issues-table tr:hover {
  background-color: #f9f9f9;
}

.issues-table .high-priority {
  background-color: rgba(255, 99, 71, 0.1);
}

.issues-table .medium-priority {
  background-color: rgba(255, 165, 0, 0.1);
}

.issues-table .low-priority {
  background-color: rgba(255, 255, 0, 0.1);
}

.priority-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.priority-badge.high-priority {
  background-color: #ffcccc;
  color: #cc0000;
}

.priority-badge.medium-priority {
  background-color: #fff2cc;
  color: #cc7a00;
}

.priority-badge.low-priority {
  background-color: #e6ffcc;
  color: #5c8a00;
}

.no-issues-message {
  text-align: center;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 20px;
}

/* Filter Bar Styles */
.filter-bar {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
  flex-wrap: nowrap;
  background: #F8FAF9;
  padding: 16px 24px;
  border-radius: 8px;
  box-sizing: border-box;
  overflow-x: auto;
  padding-left: 4px;
}

.filter-label {
  font-size: 16px;
  font-weight: 500;
  color: #393E3C;
  margin-right: 16px;
  min-width: 70px;
  white-space: nowrap;
}

.filter-select {
  flex: 0 0 260px;
  max-width: 320px;
  min-width: 180px;
  font-size: 16px;
  font-weight: 500;
  border: 1px solid #E0E0E0;
  border-radius: 999px;
  background: #fff;
  color: #393E3C;
  padding: 12px 28px 12px 20px;
  outline: none;
  height: 44px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  white-space: nowrap;
  margin-right: 0;
}

.filter-reset-btn {
  flex: 0 0 120px;
  display: flex;
  align-items: center;
  border: 1px solid #51D59C;
  background: #fff;
  color: #020A07;
  border-radius: 8px;
  padding: 0 28px;
  font-weight: 500;
  font-size: 18px;
  cursor: pointer;
  height: 44px;
  margin-left: 16px;
  min-width: 110px;
  transition: border 0.2s;
  white-space: nowrap;
}

@media (max-width: 900px) {
  .filter-bar {
    flex-wrap: wrap;
    gap: 16px;
    padding: 12px 8px;
  }
  .filter-select {
    min-width: 140px;
    max-width: 100%;
    font-size: 15px;
    padding: 10px 18px 10px 14px;
    height: 40px;
  }
  .filter-reset-btn {
    height: 40px;
    font-size: 16px;
    padding: 0 18px;
    margin-left: 0;
  }
}

@media (max-width: 600px) {
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  .filter-label {
    margin-bottom: 2px;
    margin-right: 0;
  }
  .filter-select {
    width: 100%;
    min-width: 0;
    max-width: 100%;
    margin-right: 0;
  }
  .filter-reset-btn {
    width: 100%;
    min-width: 0;
    margin-left: 0;
  }
}
