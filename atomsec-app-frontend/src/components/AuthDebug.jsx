import React from 'react';
import { Link } from 'react-router-dom';
import { API_CONFIG } from '../config';

const AuthDebug = () => {
  const handleGetTokenInfo = () => {
    const accessToken = localStorage.getItem('accessToken');
    const refreshToken = localStorage.getItem('refreshToken');
    const email = localStorage.getItem('userEmail');
    const name = localStorage.getItem('userName');

    console.log('Token info:', {
      accessToken: accessToken ? `${accessToken.substring(0, 20)}...` : null,
      refreshToken: refreshToken ? `${refreshToken.substring(0, 20)}...` : null,
      email,
      name
    });

    alert(`Token info logged to console. Email: ${email || 'Not found'}`);
  };

  const handleClearCache = () => {
    if (window.confirm('Are you sure you want to clear the auth cache? This will log you out.')) {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('userEmail');
      localStorage.removeItem('userName');
      window.location.reload();
    }
  };

  const handleCheckBackendStatus = async () => {
    try {
      const accessToken = localStorage.getItem('accessToken');
      if (!accessToken) {
        alert('No access token found. Please log in first.');
        return;
      }

      const response = await fetch(`${API_CONFIG.baseURL}${API_CONFIG.endpoints.auth.azure.me}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      const data = await response.json();
      console.log('Backend status:', data);
      alert(`Backend status: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.error('Backend check error:', error);
      alert(`Backend check error: ${error.message}`);
    }
  };

  // Get authentication status
  const isLoggedIn = !!localStorage.getItem('accessToken');
  const userEmail = localStorage.getItem('userEmail');
  const userName = localStorage.getItem('userName');

  return (
    <div className="auth-debug" style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '5px', margin: '20px' }}>
      <h2>Authentication Debug</h2>

      <div style={{ marginBottom: '20px' }}>
        <h3>Current State</h3>
        <p><strong>Logged In:</strong> {isLoggedIn ? 'Yes' : 'No'}</p>
        <p><strong>Email:</strong> {userEmail || 'Not available'}</p>
        <p><strong>Name:</strong> {userName || 'Not available'}</p>
      </div>

      <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginBottom: '20px' }}>
        <button
          onClick={handleGetTokenInfo}
          style={{ padding: '8px 16px', background: '#0078d4', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
        >
          Get Token Info
        </button>

        <button
          onClick={handleCheckBackendStatus}
          style={{ padding: '8px 16px', background: '#0078d4', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
        >
          Check Backend Status
        </button>

        <button
          onClick={handleClearCache}
          style={{ padding: '8px 16px', background: '#d42a0d', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
        >
          Clear Cache
        </button>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', border: '1px solid #e9ecef', borderRadius: '4px' }}>
        <h3>Troubleshooting</h3>
        <p>If you're experiencing authentication issues, check the following:</p>
        <ul>
          <li>Make sure your Azure AD application is configured as a Web application (not SPA)</li>
          <li>Verify that the redirect URI is set correctly in the Azure portal (http://localhost:7072/api/db/auth/azure/callback)</li>
          <li>Ensure you've added a client secret to your Azure AD application</li>
          <li>Ensure you've granted admin consent for the required permissions</li>
          <li>Check that the client secret is properly configured in your backend</li>
        </ul>
        <Link
          to="/azure-setup"
          style={{ display: 'inline-block', marginTop: '10px', padding: '8px 16px', background: '#5cb85c', color: 'white', textDecoration: 'none', borderRadius: '4px' }}
        >
          View Azure AD Setup Guide
        </Link>
      </div>
    </div>
  );
};

export default AuthDebug;
