import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Pie, Bar } from 'react-chartjs-2';
import { API_CONFIG } from '../config';
import {
  fetchIntegrationProfilesById
} from '../api';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
} from 'chart.js';
import { toast } from 'react-toastify';
import {
  Container, Row, Col, Table, Card, Spinner, Alert, Button,
  Form
} from 'react-bootstrap';
import DataStatusMessage from './DataStatusMessage';
import './IntegrationTabs.css';

// Register ChartJS components
ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title);

const ProfilesPermissionSets = ({ integrationId }) => {
  // Use integrationId prop if provided, otherwise fall back to useParams (for backward compatibility)
  const params = useParams();
  const finalIntegrationId = integrationId || params.instanceUrl;
  
  const [profiles, setProfiles] = useState([]);
  const [permissionSets, setPermissionSets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [authError, setAuthError] = useState(false);
  const [activeTab, setActiveTab] = useState('profiles'); // 'profiles' or 'permissionSets'
  const [filterColumn, setFilterColumn] = useState('Name');
  const [filterValue, setFilterValue] = useState('');
  const [filteredData, setFilteredData] = useState([]);

  // Data status states
  const [profilesStatus, setProfilesStatus] = useState('loading');
  const [permissionSetsStatus, setPermissionSetsStatus] = useState('loading');
  const [isRefreshing, setIsRefreshing] = useState({
    profiles: false,
    permissionSets: false
  });

  // Add a CSS class for cursor pointer
  useEffect(() => {
    // Create a style element
    const style = document.createElement('style');
    style.innerHTML = `
      .cursor-pointer {
        cursor: pointer;
      }
    `;
    // Append the style to the head
    document.head.appendChild(style);

    // Clean up when component unmounts
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Custom styles for the component
  const styles = {
    chartContainer: {
      height: '300px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      maxWidth: '100%'
    },
    chartsRow: {
      display: 'flex',
      flexWrap: 'wrap'
    },
    tableContainer: {
      overflowX: 'auto',
      width: '100%',
      maxWidth: '100vw',
      marginLeft: '-15px',
      marginRight: '-15px',
      paddingLeft: '15px',
      paddingRight: '15px'
    },
    tableWrapper: {
      minWidth: '1200px' // Ensure table has minimum width for all columns
    },
    filterSection: {
      backgroundColor: '#f8f9fa',
      padding: '15px',
      borderRadius: '5px',
      marginBottom: '20px'
    },
    filterLabel: {
      fontWeight: 'bold',
      color: '#495057',
      marginRight: '10px',
      marginBottom: '0',
      display: 'inline-block',
      verticalAlign: 'middle'
    },
    filterCount: {
      fontStyle: 'italic',
      color: '#6c757d',
      marginTop: '10px'
    },
    filterControl: {
      display: 'inline-block',
      width: 'auto',
      marginRight: '15px',
      verticalAlign: 'middle'
    },
    filterRow: {
      display: 'flex',
      alignItems: 'center',
      flexWrap: 'wrap'
    },
    tabNav: {
      borderBottom: '1px solid #dee2e6',
      marginBottom: '20px'
    },
    tabItem: {
      margin: '0 5px'
    },
    activeTab: {
      fontWeight: 'bold',
      backgroundColor: '#f8f9fa',
      borderColor: '#dee2e6 #dee2e6 #fff',
      borderRadius: '5px 5px 0 0',
      padding: '10px 20px'
    },
    inactiveTab: {
      backgroundColor: '#fff',
      borderColor: 'transparent',
      padding: '10px 20px',
      color: '#6c757d'
    },
    yesCell: {
      backgroundColor: '#d4edda',  // Light green color
      fontWeight: 'bold',
      color: '#155724'
    },
    noCell: {
      color: '#6c757d'
    },
    tableHeader: {
      backgroundColor: '#f8f9fa',
      color: '#495057',
      fontWeight: 'bold',
      textAlign: 'center'
    },
    profileNameCell: {
      fontWeight: 'bold',
      backgroundColor: '#e9ecef'
    },
    card: {
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      borderRadius: '8px'
    },
    cardTitle: {
      borderBottom: '1px solid #dee2e6',
      paddingBottom: '10px',
      marginBottom: '15px',
      fontWeight: 'bold',
      color: '#495057'
    }
  };

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [finalIntegrationId]);

  // Update filtered data when profiles, permissionSets, activeTab, filterColumn, or filterValue changes
  useEffect(() => {
    applyFilters();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [profiles, permissionSets, activeTab, filterColumn, filterValue]);

  // Function to apply filters
  const applyFilters = () => {
    const currentData = activeTab === 'profiles' ? profiles : permissionSets;

    if (!filterValue.trim()) {
      setFilteredData(currentData);
      return;
    }

    const filtered = currentData.filter(item => {
      if (filterColumn === 'Name') {
        return item.Name.toLowerCase().includes(filterValue.toLowerCase());
      } else {
        // For boolean columns (permissions)
        const boolValue = filterValue.toLowerCase() === 'yes' || filterValue.toLowerCase() === 'true';
        return item[filterColumn] === boolValue;
      }
    });

    setFilteredData(filtered);
  };

  const fetchData = async () => {
    if (!finalIntegrationId) {
      setError('No integration ID provided');
      setLoading(false);
      return;
    }

    setLoading(true);
    setAuthError(false);

    try {
      await Promise.all([
        fetchProfileData(),
        fetchPermissionSetData()
      ]);
    } catch (err) {
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchProfileData = async (forceRefresh = false) => {
    try {
      setProfilesStatus('loading');

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: true }));
      }

      const response = await fetchIntegrationProfilesById(finalIntegrationId, forceRefresh);

      // Check data status
      if (response.dataStatus === 'pending') {
        setProfilesStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchProfileData(), 5000);
        return [];
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        if (Array.isArray(response.profiles)) {
          setProfiles(response.profiles);
          setProfilesStatus('available');
          return response.profiles;
        } else if (Array.isArray(response)) {
          setProfiles(response);
          setProfilesStatus('available');
          return response;
        } else {
          console.log('Profile data is not in expected format, setting to empty array');
          setProfiles([]);
          setProfilesStatus('available');
          return [];
        }
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: false }));
      }
    } catch (err) {
      console.error('Error fetching profile data:', err);
      setProfiles([]);
      setProfilesStatus('error');

      // Check if it's an authentication error
      if (err.response && err.response.status === 401) {
        setAuthError(true);
        setError('Authentication required. Please authenticate with Salesforce.');
      } else {
        setError('Failed to fetch profile data');
      }

      if (err.response && err.response.status === 401) {
        toast.error('Authentication required. Please click the Authenticate button below.');
      } else {
        toast.error('Failed to fetch profile data: ' + (err.response?.data?.error || err.message));
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, profiles: false }));
      }

      throw err;
    }
  };

  const fetchPermissionSetData = async (forceRefresh = false) => {
    try {
      setPermissionSetsStatus('loading');

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, permissionSets: true }));
      }

      const response = await fetchIntegrationProfilesById(finalIntegrationId, forceRefresh);

      // Check data status
      if (response.dataStatus === 'pending') {
        setPermissionSetsStatus('pending');
        // Schedule a refresh after a delay
        setTimeout(() => fetchPermissionSetData(), 5000);
        return [];
      } else if (response.dataStatus === 'available' || !response.dataStatus) {
        // Handle both new and old response formats
        if (Array.isArray(response.permissionSets)) {
          setPermissionSets(response.permissionSets);
          setPermissionSetsStatus('available');
          return response.permissionSets;
        } else if (Array.isArray(response)) {
          setPermissionSets(response);
          setPermissionSetsStatus('available');
          return response;
        } else {
          console.log('Permission set data is not in expected format, setting to empty array');
          setPermissionSets([]);
          setPermissionSetsStatus('available');
          return [];
        }
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, permissionSets: false }));
      }
    } catch (err) {
      console.error('Error fetching permission set data:', err);
      setPermissionSets([]);
      setPermissionSetsStatus('error');

      // Check if it's an authentication error
      if (err.response && err.response.status === 401) {
        setAuthError(true);
        setError('Authentication required. Please authenticate with Salesforce.');
      } else {
        setError('Failed to fetch permission set data');
      }

      if (err.response && err.response.status === 401) {
        toast.error('Authentication required. Please click the Authenticate button below.');
      } else {
        toast.error('Failed to fetch permission set data: ' + (err.response?.data?.error || err.message));
      }

      if (forceRefresh) {
        setIsRefreshing(prev => ({ ...prev, permissionSets: false }));
      }

      throw err;
    }
  };

  // Prepare data for pie chart
  const preparePieChartData = () => {
    // Count profiles/permission sets with different permission combinations
    const permissionCounts = {
      'Modify All Data': 0,
      'View All Data': 0,
      'Manage Users': 0,
      'Reset Passwords': 0,
      'Data Export': 0
    };

    const data = activeTab === 'profiles' ? profiles : permissionSets;

    data.forEach(item => {
      if (item.PermissionsModifyAllData) permissionCounts['Modify All Data']++;
      if (item.PermissionsViewAllData) permissionCounts['View All Data']++;
      if (item.PermissionsManageUsers) permissionCounts['Manage Users']++;
      if (item.PermissionsResetPasswords) permissionCounts['Reset Passwords']++;
      if (item.PermissionsDataExport) permissionCounts['Data Export']++;
    });

    return {
      labels: Object.keys(permissionCounts),
      datasets: [
        {
          label: activeTab === 'profiles' ? 'Profiles with Permission' : 'Permission Sets with Permission',
          data: Object.values(permissionCounts),
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)'
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare data for bar chart
  const prepareBarChartData = () => {
    // Count profiles/permission sets with different permission combinations
    const permissionCounts = {
      'Manage Sharing': 0,
      'Manage Roles': 0,
      'Edit Readonly Fields': 0,
      'Manage Encryption Keys': 0,
      'View Encrypted Data': 0,
      'View Setup': 0
    };

    const data = activeTab === 'profiles' ? profiles : permissionSets;

    data.forEach(item => {
      if (item.PermissionsManageSharing) permissionCounts['Manage Sharing']++;
      if (item.PermissionsManageRoles) permissionCounts['Manage Roles']++;
      if (item.PermissionsEditReadonlyFields) permissionCounts['Edit Readonly Fields']++;
      if (item.PermissionsManageEncryptionKeys) permissionCounts['Manage Encryption Keys']++;
      if (item.PermissionsViewEncryptedData) permissionCounts['View Encrypted Data']++;
      if (item.PermissionsViewSetup) permissionCounts['View Setup']++;
    });

    return {
      labels: Object.keys(permissionCounts),
      datasets: [
        {
          label: activeTab === 'profiles' ? 'Profiles with Permission' : 'Permission Sets with Permission',
          data: Object.values(permissionCounts),
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1,
        },
      ],
    };
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: activeTab === 'profiles' ? 'Profile Permissions Distribution' : 'Permission Set Distribution',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      }
    }
  };

  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: activeTab === 'profiles' ? 'Profile Permissions Distribution' : 'Permission Set Distribution',
      },
    }
  };

  // Show loading spinner if both data sources are loading
  if (loading && profilesStatus !== 'pending' && permissionSetsStatus !== 'pending') {
    return (
      <Container className="mt-4">
        <div className="text-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="mt-2">Loading profile data...</p>
        </div>
      </Container>
    );
  }

  // Show authentication error if present
  if (error && authError) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">
          {error}
        </Alert>
        <div className="text-center mt-3">
          <p>You need to authenticate with Salesforce to view this data.</p>
          <Button
            variant="primary"
            onClick={() => window.location.href = `${API_CONFIG.baseURL}${API_CONFIG.endpoints.auth.login}?instanceUrl=${encodeURIComponent(finalIntegrationId)}`}
          >
            Authenticate
          </Button>
        </div>
      </Container>
    );
  }

  // Show pending message if data is being fetched
  const currentStatus = activeTab === 'profiles' ? profilesStatus : permissionSetsStatus;
  const isCurrentTabRefreshing = activeTab === 'profiles' ? isRefreshing.profiles : isRefreshing.permissionSets;

  if (currentStatus === 'pending') {
    return (
      <Container className="mt-4">
        <h2 className="mb-4">Profiles and Permission Sets</h2>
        <DataStatusMessage
          status="pending"
          message={`The ${activeTab === 'profiles' ? 'profiles' : 'permission sets'} data will be available shortly. Please wait or click refresh to check if data is available.`}
          onRefresh={() => activeTab === 'profiles' ? fetchProfileData(true) : fetchPermissionSetData(true)}
          isRefreshing={isCurrentTabRefreshing}
        />
      </Container>
    );
  }

  // Show error message if there's an error but not an auth error
  if (error && !authError) {
    return (
      <Container className="mt-4">
        <h2 className="mb-4">Profiles and Permission Sets</h2>
        <Alert variant="danger">
          {error}
        </Alert>
        <Button
          variant="primary"
          onClick={() => activeTab === 'profiles' ? fetchProfileData(true) : fetchPermissionSetData(true)}
          disabled={isCurrentTabRefreshing}
        >
          {isCurrentTabRefreshing ? 'Refreshing...' : 'Retry'}
        </Button>
      </Container>
    );
  }

  // Show empty state if no data is available for the current tab
  const currentData = activeTab === 'profiles' ? profiles : permissionSets;

  if (currentStatus === 'available' && (!currentData || currentData.length === 0)) {
    return (
      <Container className="mt-4">
        <h2 className="mb-4">Profiles and Permission Sets</h2>

        <div className="mb-4" style={styles.tabNav}>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex">
              <div
                className={`cursor-pointer ${activeTab === 'profiles' ? 'active' : ''}`}
                style={activeTab === 'profiles' ? styles.activeTab : styles.inactiveTab}
                onClick={() => setActiveTab('profiles')}
              >
                <strong>Profiles</strong>
              </div>
              <div
                className={`cursor-pointer ${activeTab === 'permissionSets' ? 'active' : ''}`}
                style={activeTab === 'permissionSets' ? styles.activeTab : styles.inactiveTab}
                onClick={() => setActiveTab('permissionSets')}
              >
                <strong>Permission Sets</strong>
              </div>
            </div>
          </div>
        </div>

        <div className="empty-state">
          <div className="empty-icon">
            {activeTab === 'profiles' ? '👤' : '🔐'}
          </div>
          <h3>
            No {activeTab === 'profiles' ? 'Profiles' : 'Permission Sets'} Data Available
          </h3>
          <p>
            No {activeTab === 'profiles' ? 'profiles' : 'permission sets'} data was found in the database.
            Click the <strong>Rescan</strong> button at the top of the page to fetch {activeTab === 'profiles' ? 'profiles' : 'permission sets'} data from Salesforce.
          </p>
          <Button
            variant="primary"
            onClick={() => activeTab === 'profiles' ? fetchProfileData(true) : fetchPermissionSetData(true)}
            disabled={isCurrentTabRefreshing}
            className="refresh-button"
          >
            {isCurrentTabRefreshing ? 'Rescanning...' : 'Rescan'}
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid className="mt-4 px-0">
      <h2 className="mb-4">Profiles and Permission Sets</h2>

      <div className="mb-4" style={styles.tabNav}>
        <div className="d-flex justify-content-between align-items-center">
          <div className="d-flex">
            <div
              className={`cursor-pointer ${activeTab === 'profiles' ? 'active' : ''}`}
              style={activeTab === 'profiles' ? styles.activeTab : styles.inactiveTab}
              onClick={() => setActiveTab('profiles')}
            >
              <strong>Profiles</strong>
            </div>
            <div
              className={`cursor-pointer ${activeTab === 'permissionSets' ? 'active' : ''}`}
              style={activeTab === 'permissionSets' ? styles.activeTab : styles.inactiveTab}
              onClick={() => setActiveTab('permissionSets')}
            >
              <strong>Permission Sets</strong>
            </div>
          </div>
          <Button
            variant="outline-primary"
            size="sm"
            onClick={() => activeTab === 'profiles' ? fetchProfileData(true) : fetchPermissionSetData(true)}
            disabled={isCurrentTabRefreshing}
          >
            {isCurrentTabRefreshing ? 'Refreshing...' : 'Refresh Data'}
          </Button>
        </div>
      </div>

      <Row className="mb-4" style={styles.chartsRow}>
        <Col md={5}>
          <Card style={styles.card}>
            <Card.Body>
              <Card.Title style={styles.cardTitle}>
                Key Permissions - {activeTab === 'profiles' ? 'Profiles' : 'Permission Sets'}
              </Card.Title>
              <div style={styles.chartContainer}>
                {(activeTab === 'profiles' ? profiles : permissionSets).length > 0 ? (
                  <Pie data={preparePieChartData()} options={pieOptions} />
                ) : (
                  <p>No data available</p>
                )}
              </div>
            </Card.Body>
          </Card>
        </Col>
        <Col md={2}></Col>
        <Col md={5}>
          <Card style={styles.card}>
            <Card.Body>
              <Card.Title style={styles.cardTitle}>
                Additional Permissions - {activeTab === 'profiles' ? 'Profiles' : 'Permission Sets'}
              </Card.Title>
              <div style={styles.chartContainer}>
                {(activeTab === 'profiles' ? profiles : permissionSets).length > 0 ? (
                  <Bar data={prepareBarChartData()} options={barOptions} />
                ) : (
                  <p>No data available</p>
                )}
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Card className="mb-4" style={styles.card}>
        <Card.Body>
          <Card.Title style={styles.cardTitle}>
            {activeTab === 'profiles' ? 'Profiles' : 'Permission Sets'} and Permissions
          </Card.Title>

          <div style={styles.filterSection}>
            <div style={styles.filterRow}>
              <Form.Label style={styles.filterLabel}>Filter by:</Form.Label>
              <Form.Select
                value={filterColumn}
                onChange={(e) => setFilterColumn(e.target.value)}
                style={styles.filterControl}
                className="me-2"
              >
                <option value="Name">Name</option>
                <option value="PermissionsModifyAllData">Modify All Data</option>
                <option value="PermissionsViewAllData">View All Data</option>
                <option value="PermissionsManageUsers">Manage Users</option>
                <option value="PermissionsResetPasswords">Reset Passwords</option>
                <option value="PermissionsDataExport">Data Export</option>
                <option value="PermissionsManageSharing">Manage Sharing</option>
                <option value="PermissionsManageRoles">Manage Roles</option>
                <option value="PermissionsEditReadonlyFields">Edit Readonly Fields</option>
                <option value="PermissionsManageEncryptionKeys">Manage Encryption Keys</option>
                <option value="PermissionsViewEncryptedData">View Encrypted Data</option>
                <option value="PermissionsViewSetup">View Setup</option>
              </Form.Select>

              <Form.Label style={styles.filterLabel}>Value:</Form.Label>
              {filterColumn === 'Name' ? (
                <Form.Control
                  type="text"
                  placeholder="Enter name to filter"
                  value={filterValue}
                  onChange={(e) => setFilterValue(e.target.value)}
                  style={styles.filterControl}
                  className="me-2"
                />
              ) : (
                <Form.Select
                  value={filterValue}
                  onChange={(e) => setFilterValue(e.target.value)}
                  style={styles.filterControl}
                  className="me-2"
                >
                  <option value="">Select a value</option>
                  <option value="Yes">Yes</option>
                  <option value="No">No</option>
                </Form.Select>
              )}

              <Button
                variant="outline-secondary"
                onClick={() => {
                  setFilterValue('');
                  setFilterColumn('Name');
                }}
                size="sm"
                className="me-2"
              >
                Clear Filter
              </Button>
            </div>

            <div style={styles.filterCount}>
              Showing {filteredData.length} of {activeTab === 'profiles' ? profiles.length : permissionSets.length} {activeTab === 'profiles' ? 'profiles' : 'permission sets'}
              {filterValue && ` (filtered by ${filterColumn === 'Name' ? 'name' : filterColumn.replace('Permissions', '')} = "${filterValue}")`}
            </div>
          </div>
          <div style={styles.tableContainer}>
            <Table bordered hover className="shadow-sm" style={styles.tableWrapper}>
              <thead>
                <tr>
                  {[
                    'Name', 'Modify All Data', 'View All Data', 'Manage Users', 'Reset Passwords',
                    'Data Export', 'Manage Sharing', 'Manage Roles', 'Edit Readonly Fields',
                    'Manage Encryption Keys', 'View Encrypted Data', 'View Setup'
                  ].map(header => (
                    <th key={header} style={styles.tableHeader}>{header}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredData.map((item) => (
                  <tr key={item.Id}>
                    <td style={styles.profileNameCell}>{item.Name}</td>
                    <td style={item.PermissionsModifyAllData ? styles.yesCell : styles.noCell}>
                      {item.PermissionsModifyAllData ? 'Yes' : 'No'}
                    </td>
                    <td style={item.PermissionsViewAllData ? styles.yesCell : styles.noCell}>
                      {item.PermissionsViewAllData ? 'Yes' : 'No'}
                    </td>
                    <td style={item.PermissionsManageUsers ? styles.yesCell : styles.noCell}>
                      {item.PermissionsManageUsers ? 'Yes' : 'No'}
                    </td>
                    <td style={item.PermissionsResetPasswords ? styles.yesCell : styles.noCell}>
                      {item.PermissionsResetPasswords ? 'Yes' : 'No'}
                    </td>
                    <td style={item.PermissionsDataExport ? styles.yesCell : styles.noCell}>
                      {item.PermissionsDataExport ? 'Yes' : 'No'}
                    </td>
                    <td style={item.PermissionsManageSharing ? styles.yesCell : styles.noCell}>
                      {item.PermissionsManageSharing ? 'Yes' : 'No'}
                    </td>
                    <td style={item.PermissionsManageRoles ? styles.yesCell : styles.noCell}>
                      {item.PermissionsManageRoles ? 'Yes' : 'No'}
                    </td>
                    <td style={item.PermissionsEditReadonlyFields ? styles.yesCell : styles.noCell}>
                      {item.PermissionsEditReadonlyFields ? 'Yes' : 'No'}
                    </td>
                    <td style={item.PermissionsManageEncryptionKeys ? styles.yesCell : styles.noCell}>
                      {item.PermissionsManageEncryptionKeys ? 'Yes' : 'No'}
                    </td>
                    <td style={item.PermissionsViewEncryptedData ? styles.yesCell : styles.noCell}>
                      {item.PermissionsViewEncryptedData ? 'Yes' : 'No'}
                    </td>
                    <td style={item.PermissionsViewSetup ? styles.yesCell : styles.noCell}>
                      {item.PermissionsViewSetup ? 'Yes' : 'No'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>
        </Card.Body>
      </Card>

      <div className="text-center mb-4">
        <Button variant="primary" onClick={fetchData}>
          Refresh Data
        </Button>
      </div>
    </Container>
  );
};

export default ProfilesPermissionSets;
