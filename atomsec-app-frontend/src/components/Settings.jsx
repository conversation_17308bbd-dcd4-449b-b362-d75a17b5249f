import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import './Settings.css';
import './Configurations.css';
import { fetchUserProfile, updateUserProfile, changePassword } from '../api';
import { useAuth } from '../context/AuthContext';

const Settings = () => {
  const { setUser, user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    jobTitle: '',
    phone: '',
    company: ''
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    securityAlerts: true,
    weeklyReports: true,
    systemUpdates: false
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: true,
    sessionTimeout: '30 minutes',
    passwordExpiry: '90 days',
    ipRestrictions: false
  });

  const [loading, setLoading] = useState(false);
  const [saved, setSaved] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState('');

  // Fetch user profile data when component mounts
  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        setLoading(true);

        console.log('Current user email in localStorage:', localStorage.getItem('userEmail'));

        const userData = await fetchUserProfile();
        console.log('Loaded user data:', userData);
        if (userData) {
          // Update profile form data
          setProfileData({
            firstName: userData.firstName || '',
            lastName: userData.lastName || '',
            email: userData.email || '',
            jobTitle: userData.jobTitle || '',
            phone: userData.contact || '',
            company: userData.company || ''
          });
        }
      } catch (error) {
        console.error('Failed to load user profile:', error);
        toast.error('Failed to load user profile');
      } finally {
        setLoading(false);
      }
    };

    loadUserProfile();
  }, []);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setSaved(false);
    setPasswordError('');
    setPasswordSuccess('');
  };

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileData({
      ...profileData,
      [name]: value
    });
    setSaved(false);
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData({
      ...passwordData,
      [name]: value
    });
    setPasswordError('');
    setPasswordSuccess('');
  };

  const handleProfileSave = async () => {
    try {
      setLoading(true);
      setSaved(false);

      // Map the frontend form data to the API format
      const apiProfileData = {
        firstName: profileData.firstName,
        lastName: profileData.lastName,
        email: profileData.email,
        jobTitle: profileData.jobTitle,
        contact: profileData.phone,
        company: profileData.company
      };

      console.log('Saving profile data:', apiProfileData);
      const result = await updateUserProfile(apiProfileData);

      if (result) {
        // Update the user name in localStorage and AuthContext if it changed
        const fullName = `${profileData.firstName} ${profileData.lastName}`.trim();
        const currentUserName = localStorage.getItem('userName');

        if (fullName && fullName !== currentUserName) {
          localStorage.setItem('userName', fullName);

          // Update the AuthContext with the new user name
          if (user) {
            setUser({
              ...user,
              name: fullName
            });
          }

          console.log('Updated user name for session:', fullName);
        }

        setSaved(true);
        toast.success('Profile updated successfully');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordSave = async () => {
    try {
      // Clear previous messages
      setPasswordError('');
      setPasswordSuccess('');

      // Validate passwords
      if (passwordData.newPassword !== passwordData.confirmPassword) {
        setPasswordError('New passwords do not match');
        return;
      }

      if (passwordData.newPassword.length < 8) {
        setPasswordError('Password must be at least 8 characters long');
        return;
      }

      setLoading(true);

      // Call API to change password
      const result = await changePassword(
        passwordData.currentPassword,
        passwordData.newPassword
      );

      if (result.success) {
        setPasswordSuccess('Password updated successfully');
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        setPasswordError(result.error || 'Failed to update password');
      }
    } catch (error) {
      console.error('Error changing password:', error);
      setPasswordError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationChange = (e) => {
    const { name, checked } = e.target;
    setNotificationSettings({
      ...notificationSettings,
      [name]: checked
    });
    setSaved(false);
  };

  const handleSecurityChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSecuritySettings({
      ...securitySettings,
      [name]: type === 'checkbox' ? checked : value
    });
    setSaved(false);
  };

  const handleSettingsSave = () => {
    // Simulate saving to API
    setTimeout(() => {
      setSaved(true);
    }, 500);
  };

  const renderProfileTab = () => (
    <div className="settings-section">
      <h3 className="section-title">Profile Information</h3>
      <div className="form-row">
        <div className="form-group">
          <label htmlFor="firstName">First Name</label>
          <input
            type="text"
            id="firstName"
            name="firstName"
            value={profileData.firstName}
            onChange={handleProfileChange}
          />
        </div>

        <div className="form-group">
          <label htmlFor="lastName">Last Name</label>
          <input
            type="text"
            id="lastName"
            name="lastName"
            value={profileData.lastName}
            onChange={handleProfileChange}
          />
        </div>
      </div>

      <div className="form-group">
        <label htmlFor="email">Email Address</label>
        <input
          type="email"
          id="email"
          name="email"
          value={profileData.email}
          onChange={handleProfileChange}
        />
      </div>

      <div className="form-group">
        <label htmlFor="jobTitle">Job Title</label>
        <input
          type="text"
          id="jobTitle"
          name="jobTitle"
          value={profileData.jobTitle}
          onChange={handleProfileChange}
        />
      </div>

      <div className="form-row">
        <div className="form-group">
          <label htmlFor="phone">Phone Number</label>
          <input
            type="text"
            id="phone"
            name="phone"
            value={profileData.phone}
            onChange={handleProfileChange}
          />
        </div>

        <div className="form-group">
          <label htmlFor="company">Company</label>
          <input
            type="text"
            id="company"
            name="company"
            value={profileData.company}
            onChange={handleProfileChange}
          />
        </div>
      </div>

      <div className="settings-actions">
        <button
          className="save-btn"
          onClick={handleProfileSave}
          disabled={loading}
        >
          {loading ? 'Saving...' : 'Save Changes'}
        </button>
        {saved && <span className="save-success">Profile updated successfully!</span>}
      </div>
    </div>
  );

  const renderPasswordTab = () => (
    <div className="settings-section">
      <h3 className="section-title">Change Password</h3>
      <div className="form-group">
        <label htmlFor="currentPassword">Current Password</label>
        <input
          type="password"
          id="currentPassword"
          name="currentPassword"
          value={passwordData.currentPassword}
          onChange={handlePasswordChange}
        />
      </div>

      <div className="form-group">
        <label htmlFor="newPassword">New Password</label>
        <input
          type="password"
          id="newPassword"
          name="newPassword"
          value={passwordData.newPassword}
          onChange={handlePasswordChange}
        />
      </div>

      <div className="form-group">
        <label htmlFor="confirmPassword">Confirm New Password</label>
        <input
          type="password"
          id="confirmPassword"
          name="confirmPassword"
          value={passwordData.confirmPassword}
          onChange={handlePasswordChange}
        />
      </div>

      {passwordError && <div className="password-error">{passwordError}</div>}
      {passwordSuccess && <div className="password-success">{passwordSuccess}</div>}

      <div className="settings-actions">
        <button
          className="save-btn"
          onClick={handlePasswordSave}
          disabled={loading}
        >
          {loading ? 'Updating...' : 'Update Password'}
        </button>
      </div>
    </div>
  );

  const renderSecurityTab = () => (
    <div className="settings-section">
      <h3 className="section-title">Security Settings</h3>
      <p className="section-description">Configure security settings to protect your account and data.</p>

      <div className="form-group">
        <div className="checkbox-group">
          <input
            type="checkbox"
            id="twoFactorAuth"
            name="twoFactorAuth"
            checked={securitySettings.twoFactorAuth}
            onChange={handleSecurityChange}
          />
          <label htmlFor="twoFactorAuth">Two-Factor Authentication</label>
        </div>
        <p className="setting-description">Add an extra layer of security by requiring a verification code in addition to your password</p>
      </div>

      <div className="form-group">
        <div className="select-wrapper">
          <label className="select-label" htmlFor="sessionTimeout">Session Timeout</label>
          <select
            id="sessionTimeout"
            name="sessionTimeout"
            value={securitySettings.sessionTimeout}
            onChange={handleSecurityChange}
          >
            <option value="15 minutes">15 minutes</option>
            <option value="30 minutes">30 minutes</option>
            <option value="1 hour">1 hour</option>
            <option value="2 hours">2 hours</option>
          </select>
        </div>
        <p className="setting-description">Automatically log out after a period of inactivity</p>
      </div>

      <div className="form-group">
        <div className="select-wrapper">
          <label className="select-label" htmlFor="passwordExpiry">Password Expiry</label>
          <select
            id="passwordExpiry"
            name="passwordExpiry"
            value={securitySettings.passwordExpiry}
            onChange={handleSecurityChange}
          >
            <option value="30 days">30 days</option>
            <option value="60 days">60 days</option>
            <option value="90 days">90 days</option>
            <option value="Never">Never</option>
          </select>
        </div>
        <p className="setting-description">Require password changes at regular intervals</p>
      </div>

      <div className="form-group">
        <div className="checkbox-group">
          <input
            type="checkbox"
            id="ipRestrictions"
            name="ipRestrictions"
            checked={securitySettings.ipRestrictions}
            onChange={handleSecurityChange}
          />
          <label htmlFor="ipRestrictions">IP Restrictions</label>
        </div>
        <p className="setting-description">Limit access to your account from specific IP addresses or ranges</p>
      </div>

      <div className="settings-actions">
        <button className="save-btn" onClick={handleSettingsSave}>
          Save Changes
        </button>
        {saved && activeTab === 'security' && <span className="save-success">Security settings saved successfully!</span>}
      </div>
    </div>
  );

  const renderNotificationsTab = () => (
    <div className="settings-section">
      <h3 className="section-title">Notification Settings</h3>
      <p className="section-description">Configure how and when you receive notifications from the system.</p>

      <div className="form-group">
        <div className="checkbox-group">
          <input
            type="checkbox"
            id="emailNotifications"
            name="emailNotifications"
            checked={notificationSettings.emailNotifications}
            onChange={handleNotificationChange}
          />
          <label htmlFor="emailNotifications">Email Notifications</label>
        </div>
        <p className="setting-description">Receive important notifications via email</p>
      </div>

      <div className="form-group">
        <div className="checkbox-group">
          <input
            type="checkbox"
            id="securityAlerts"
            name="securityAlerts"
            checked={notificationSettings.securityAlerts}
            onChange={handleNotificationChange}
          />
          <label htmlFor="securityAlerts">Security Alerts</label>
        </div>
        <p className="setting-description">Get notified immediately about security issues and vulnerabilities</p>
      </div>

      <div className="form-group">
        <div className="checkbox-group">
          <input
            type="checkbox"
            id="weeklyReports"
            name="weeklyReports"
            checked={notificationSettings.weeklyReports}
            onChange={handleNotificationChange}
          />
          <label htmlFor="weeklyReports">Weekly Reports</label>
        </div>
        <p className="setting-description">Receive a weekly summary of your security posture and changes</p>
      </div>

      <div className="form-group">
        <div className="checkbox-group">
          <input
            type="checkbox"
            id="systemUpdates"
            name="systemUpdates"
            checked={notificationSettings.systemUpdates}
            onChange={handleNotificationChange}
          />
          <label htmlFor="systemUpdates">System Updates</label>
        </div>
        <p className="setting-description">Get notified about platform updates and new features</p>
      </div>

      <div className="settings-actions">
        <button className="save-btn" onClick={handleSettingsSave}>
          Save Changes
        </button>
        {saved && activeTab === 'notifications' && <span className="save-success">Notification settings saved successfully!</span>}
      </div>
    </div>
  );

  return (
    <div className="settings-container">
      <div className="page-header">
        <h1 className="page-title">Account Settings</h1>
        <p className="page-description">Manage your account settings and preferences</p>
      </div>

      <div className="settings-content">
        <div className="settings-sidebar">
          <button
            className={`sidebar-btn ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => handleTabChange('profile')}
          >
            Profile Information
          </button>
          <button
            className={`sidebar-btn ${activeTab === 'password' ? 'active' : ''}`}
            onClick={() => handleTabChange('password')}
          >
            Change Password
          </button>
          <button
            className={`sidebar-btn ${activeTab === 'security' ? 'active' : ''}`}
            onClick={() => handleTabChange('security')}
          >
            Security Settings
          </button>
          <button
            className={`sidebar-btn ${activeTab === 'notifications' ? 'active' : ''}`}
            onClick={() => handleTabChange('notifications')}
          >
            Notification Settings
          </button>
        </div>

        <div className="settings-main">
          {activeTab === 'profile' && renderProfileTab()}
          {activeTab === 'password' && renderPasswordTab()}
          {activeTab === 'security' && renderSecurityTab()}
          {activeTab === 'notifications' && renderNotificationsTab()}
        </div>
      </div>
    </div>
  );
};

export default Settings;
