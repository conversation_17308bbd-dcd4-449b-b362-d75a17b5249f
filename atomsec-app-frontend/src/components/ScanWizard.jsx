import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import './ScanWizard.css';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { integrationAPI } from '../api';

const ScanWizard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [step, setStep] = useState(1);
  const [selectedIntegration, setSelectedIntegration] = useState(null);
  const [formData, setFormData] = useState({
    orgName: '',
    tenantUrl: '',
    clientId: '',
    clientSecret: '',
    description: '',
    environment: 'production',
    useJwt: false,
    username: '',
    privateKey: ''
  });
  const [isFixingConnection, setIsFixingConnection] = useState(false);
  const [errors, setErrors] = useState({});
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [testConnectionResponse, setTestConnectionResponse] = useState(null);
  const [isAuthorizing, setIsAuthorizing] = useState(false);
  const [isSaveEnabled, setIsSaveEnabled] = useState(false); // New: controls Save button
  const [isSaveComplete, setIsSaveComplete] = useState(false); // Disables Save after save
  const [formDirtySinceTest, setFormDirtySinceTest] = useState(false); // Tracks edits after test
  const [connectionStatus, setConnectionStatus] = useState(null); // { type: 'success'|'error'|'info', message: string }
  const [isSaving, setIsSaving] = useState(false); // Tracks when save operation is in progress

  // Use useMemo to prevent the integrations array from being recreated on every render
  const integrations = useMemo(() => [
    {
      id: 'salesforce',
      name: 'Salesforce',
      icon: '/assets/salesforce-icon.svg',
            description: `Salesforce is a leading cloud-based CRM platform that helps businesses manage customer relationships, sales, and service operations. This integration allows AtomSec to scan your Salesforce org for security vulnerabilities, permission issues, and configuration risks.\n\n \n\n Prerequisites:You will need a Salesforce Connected App with OAuth enabled, Client ID (Consumer Key), Client Secret (Consumer Secret), and appropriate API permissions. For production orgs, use your instance URL (e.g., https://mycompany.my.salesforce.com). For sandbox environments, select the Sandbox option.`
    }
  ], []);

  // Define handleClose function with useCallback to prevent unnecessary re-renders
  const handleClose = useCallback(() => {
    // If the user has entered data in the form, show a confirmation dialog
    if (step > 1 ||
        formData.orgName ||
        formData.tenantUrl ||
        formData.clientId ||
        formData.clientSecret ||
        formData.description ||
        formData.environment !== 'production') {
      if (window.confirm('Are you sure you want to close? Any unsaved changes will be lost.')) {
        // Navigate to the integrations page when the close button is clicked
        navigate('/integrations');
      }
    } else {
      // If no data has been entered, just navigate without confirmation
      navigate('/integrations');
    }
  }, [step, formData, navigate]);

  // Initialize form data when the component mounts
  useEffect(() => {
    // Check if we're fixing a connection
    if (location.state?.isFixingConnection && location.state?.integrationToFix) {
      const integration = location.state.integrationToFix;
      setIsFixingConnection(true);

      // Skip to step 3 (connection form)
      setStep(3);

      // Select the integration type
      const integrationType = integration.type || integration.Type || 'Salesforce';
      const matchedIntegration = integrations.find(i => i.id.toLowerCase() === integrationType.toLowerCase());
      if (matchedIntegration) {
        setSelectedIntegration(matchedIntegration);
      } else {
        setSelectedIntegration(integrations[0]); // Default to first integration
      }

      // Pre-fill the form with integration data
      setFormData({
        orgName: integration.name || integration.Name || '',
        tenantUrl: integration.tenantUrl || integration.TenantUrl || '',
        clientId: '', // Security credentials need to be re-entered
        clientSecret: '',
        description: integration.description || integration.Description || '',
        environment: integration.environment || integration.Environment || 'production',
        useJwt: false,
        username: '',
        privateKey: ''
      });

      // Show a toast message
      toast.info('Please update the connection details to fix this integration');
    } else {
      // Reset form data to ensure no autofilled values persist
      setFormData({
        orgName: '',
        tenantUrl: '',
        clientId: '',
        clientSecret: '',
        description: '',
        environment: 'production',
        useJwt: false,
        username: '',
        privateKey: ''
      });
    }
  }, [location.state, integrations]); // Depends on location.state and integrations

  // Add escape key handler
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        handleClose();
      }
    };

    // Add event listener for escape key
    document.addEventListener('keydown', handleEscKey);

    // Clean up event listener on component unmount
    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [handleClose]); // Only depends on handleClose

  const handleNext = () => {
    if (step === 1 && !selectedIntegration) {
      toast.error('Please select an integration');
      return;
    }
    if (step === 3 && !validateStep2()) return;
    setStep(prev => Math.min(prev + 1, 3));
  };

  const handleBack = () => {
    setStep(prev => Math.max(prev - 1, 1));
  };

  const validateStep2 = () => {
    const newErrors = {};
    if (!formData.orgName) newErrors.orgName = 'Org name is required';
    if (!formData.tenantUrl) newErrors.tenantUrl = 'Tenant URL is required';
    if (!formData.clientId) newErrors.clientId = 'Client ID is required';

    // Only validate client secret if not using JWT
    if (!formData.useJwt && !formData.clientSecret) {
      newErrors.clientSecret = 'Client Secret is required';
    }

    // Validate JWT-specific fields if using JWT
    if (formData.useJwt) {
      if (!formData.username) newErrors.username = 'Salesforce username is required for JWT flow';
      if (!formData.privateKey) newErrors.privateKey = 'Private key is required for JWT flow';
    }

    if (!formData.description) newErrors.description = 'Description is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Reset Save state and require re-test if form is edited after a successful test
  const handleChange = (e) => {
    const { name, value, type, checked, dataset } = e.target;
    // If the field has a data-field attribute, use that as the key
    const fieldName = dataset.field || name;
    setFormData(prev => ({ ...prev, [fieldName]: type === 'checkbox' ? checked : value }));
    setFormDirtySinceTest(true); // Mark form as dirty after test
    setIsSaveEnabled(false); // Disable Save until re-tested
    setIsSaveComplete(false); // Allow Save again if user edits
  };

  const handleIntegrationSelect = (integration) => {
    setSelectedIntegration(integration);
  };

  const handleTestConnection = async () => {
    try {
      // Prevent duplicate requests (especially in React Strict Mode)
      if (isAuthorizing) {
        console.log('Test connection already in progress, skipping duplicate request');
        return;
      }

      // Validate required fields based on authentication flow
      if (!formData.tenantUrl || !formData.clientId) {
        toast.error('Please fill in all required fields');
        return;
      }

      // Validate flow-specific fields
      if (formData.useJwt) {
        if (!formData.username || !formData.privateKey) {
          toast.error('Please fill in all JWT flow fields (Username and Private Key)');
          return;
        }
      } else {
        if (!formData.clientSecret) {
          toast.error('Please fill in Client Secret for Client Credentials flow');
          return;
        }
      }

      setIsAuthorizing(true);
      setIsSaveComplete(false);
      setConnectionStatus(null); // Clear previous status

      // Log the request details for debugging
      console.log('Testing connection with:', {
        tenantUrl: formData.tenantUrl,
        clientId: formData.clientId,
        clientSecret: formData.useJwt ? 'Not used (JWT flow)' : '***REDACTED***',
        useJwt: formData.useJwt,
        username: formData.useJwt ? formData.username : 'Not used',
        privateKey: formData.useJwt ? '***REDACTED***' : 'Not used',
        type: selectedIntegration?.id || 'Salesforce',
        environment: formData.environment
      });

      // Get integration ID if we're fixing a connection
      let integrationId = null;
      if (isFixingConnection && location.state?.integrationToFix) {
        integrationId = location.state.integrationToFix.id || location.state.integrationToFix.Id;
      }

      // Test connection using the API - only pass the minimum required parameters
      // This ensures we're only testing the connection without saving any data
      const response = await integrationAPI.testConnection({
        tenantUrl: formData.tenantUrl,
        clientId: formData.clientId,
        clientSecret: formData.useJwt ? null : formData.clientSecret,
        type: selectedIntegration?.id || 'Salesforce',
        environment: formData.environment,
        useJwt: formData.useJwt,
        username: formData.useJwt ? formData.username : null,
        privateKey: formData.useJwt ? formData.privateKey : null,
        integrationId,
      });

      console.log('Connection test response:', response);

      // Store the response for later use
      setTestConnectionResponse(response);

      if (response && response.success) {
        setConnectionStatus({ type: 'success', message: 'Connection test successful! Click Save to complete the integration setup.' });
        toast.success('Connection test successful! Click Save to complete the integration setup.');
        setIsAuthorized(true);
        setIsSaveEnabled(true);
        setFormDirtySinceTest(false);
      } else {
        const errorMsg = response?.error || 'Failed to connect to Salesforce. Please check your credentials and try again.';
        setConnectionStatus({ type: 'error', message: `Connection test failed: ${errorMsg}` });
        toast.error(`Connection test failed: ${errorMsg}`);
        toast.info('You can still save this integration, but it will be marked as inactive until connection succeeds.');
        setIsAuthorized(true);
        setIsSaveEnabled(false);
        setFormDirtySinceTest(false);
      }
    } catch (error) {
      console.error('Connection error:', error);

      // Log detailed error information for debugging
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        console.error('Error response headers:', error.response.headers);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('Error request:', error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', error.message);
      }

      let errorMessage = 'Failed to establish connection';

      if (error.response?.data?.error) {
        errorMessage += `: ${error.response.data.error}`;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      } else {
        errorMessage += '. Please check your network connection and try again.';
      }

      setConnectionStatus({ type: 'error', message: errorMessage });
      toast.error(errorMessage);
      setIsAuthorized(false);
      setIsSaveEnabled(false);
      setFormDirtySinceTest(false);
    } finally {
      setIsAuthorizing(false);
    }
  };

  // Validate form before saving
  const validateBeforeSave = () => {
    const newErrors = {};

    if (!formData.orgName.trim()) {
      newErrors.orgName = 'Organization name is required';
    }

    if (!formData.tenantUrl.trim()) {
      newErrors.tenantUrl = 'Tenant URL is required';
    }

    if (!formData.clientId.trim()) {
      newErrors.clientId = 'Client ID is required';
    }

    // Only validate client secret if not using JWT
    if (!formData.useJwt && !formData.clientSecret.trim()) {
      newErrors.clientSecret = 'Client Secret is required';
    }

    // Validate JWT-specific fields if using JWT
    if (formData.useJwt) {
      if (!formData.username.trim()) {
        newErrors.username = 'Salesforce username is required for JWT flow';
      }

      if (!formData.privateKey.trim()) {
        newErrors.privateKey = 'Private key is required for JWT flow';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleStartScan = async () => {
    // Prevent multiple submissions
    if (isSaving || isSaveComplete) {
      return;
    }

    // Validate form before saving
    if (!validateBeforeSave()) {
      toast.error('Please fix the errors before saving');
      return;
    }

    setIsSaving(true);
    let loadingToast;
    try {
      loadingToast = toast.loading(isFixingConnection ? 'Fixing connection...' : 'Saving integration...');

      // Determine isActive based on last test connection
      const isActive = testConnectionResponse && testConnectionResponse.success ? true : false;

      // Log the request details for debugging
      console.log('Connecting integration with:', {
        orgName: formData.orgName,
        tenantUrl: formData.tenantUrl,
        clientId: formData.clientId,
        clientSecret: formData.useJwt ? 'Not used (JWT flow)' : '***REDACTED***',
        description: formData.description,
        type: selectedIntegration?.id || 'Salesforce',
        environment: formData.environment,
        useJwt: formData.useJwt,
        username: formData.useJwt ? formData.username : 'Not used',
        privateKey: formData.useJwt ? '***REDACTED***' : 'Not used',
        isActive
      });

      // If we're fixing a connection or have a test connection response with an integration ID, use that
      let response;
      let integrationId = null;

      if (isFixingConnection && location.state?.integrationToFix) {
        integrationId = location.state.integrationToFix.id || location.state.integrationToFix.Id;
      } else if (testConnectionResponse?.data?.integrationId) {
        integrationId = testConnectionResponse.data.integrationId;
      }

      // Pass isActive to connectIntegration
      if (integrationId) {
        response = await integrationAPI.connectIntegration({
          orgName: formData.orgName,
          tenantUrl: formData.tenantUrl,
          clientId: formData.clientId,
          clientSecret: formData.useJwt ? null : formData.clientSecret,
          description: formData.description,
          type: selectedIntegration?.id || 'Salesforce',
          environment: formData.environment,
          integrationId,
          isActive,
          useJwt: formData.useJwt,
          username: formData.useJwt ? formData.username : null,
          privateKey: formData.useJwt ? formData.privateKey : null,
        });
      } else {
        response = await integrationAPI.connectIntegration({
          orgName: formData.orgName,
          tenantUrl: formData.tenantUrl,
          clientId: formData.clientId,
          clientSecret: formData.useJwt ? null : formData.clientSecret,
          description: formData.description,
          type: selectedIntegration?.id || 'Salesforce',
          environment: formData.environment,
          isActive,
          useJwt: formData.useJwt,
          username: formData.useJwt ? formData.username : null,
          privateKey: formData.useJwt ? formData.privateKey : null,
        });
      }

      console.log('Connection response:', response);

      if (response && response.success) {
        toast.update(loadingToast, {
          render: isFixingConnection ? 'Connection fixed successfully!' : 'Integration saved successfully!',
          type: 'success',
          isLoading: false,
          autoClose: 3000
        });
        setIsSaveComplete(true); // Disable Save after save

        // If this is a new integration, or if we are fixing the connection,
        // trigger a scan after saving.
        if (response.data && response.data.integrationId) {
          integrationAPI.scanIntegration(response.data.integrationId);
        }

        // Optionally, navigate after a short delay
        setTimeout(() => navigate('/integrations'), 1500);
      } else {
        const errorMsg = response?.error ||
                         'Failed to connect integration. Please check your credentials and try again.';
        toast.update(loadingToast, {
          render: isFixingConnection ? `Failed to fix connection: ${errorMsg}` : `Failed to connect integration: ${errorMsg}`,
          type: 'error',
          isLoading: false,
          autoClose: 5000
        });
      }
    } catch (error) {
      console.error('Error connecting integration:', error);

      // Log detailed error information for debugging
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
      } else if (error.request) {
        console.error('Error request:', error.request);
      } else {
        console.error('Error message:', error.message);
      }

      let errorMessage = 'Failed to connect integration';

      if (error.response?.data?.error) {
        errorMessage += `: ${error.response.data.error}`;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      } else {
        errorMessage += '. Please check your network connection and try again.';
      }

      if (loadingToast) {
        toast.update(loadingToast, {
          render: errorMessage,
          type: 'error',
          isLoading: false,
          autoClose: 5000
        });
      } else {
        toast.error(errorMessage);
      }
    } finally {
      // Reset saving state but keep saveComplete state if successful
      if (!isSaveComplete) {
        setIsSaving(false);
      }
    }
  };



  return (
    <div className="scan-wizard-container">
      <div className="wizard-header">
        <div className="header-content">
          <h2>{isFixingConnection ? 'Fix Integration Connection' : 'New Integration'}</h2>
          <button className="close-button" onClick={handleClose} type="button">
            <span className="close-icon">×</span>
          </button>
        </div>
      </div>

      <div className="wizard-content">
        {step === 1 && (
          <div className="integration-options">
            {integrations.map(integration => (
              <div
                key={integration.id}
                className={`integration-card ${selectedIntegration?.id === integration.id ? 'selected' : ''}`}
                onClick={() => handleIntegrationSelect(integration)}
              >
                <div className="integration-card-content">
                  <div className="integration-icon">
                    <img src={integration.icon} alt={integration.name} />
                  </div>
                  <div className="integration-name">{integration.name}</div>
                  <div className="arrow-icon">
                    <img src="/assets/keyboard-right-icon-figma.svg" alt="Right arrow" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {step === 2 && selectedIntegration && (
          <div className="integration-description-step">
            <div className="integration-header">
              <div className="integration-icon">
                <img src={selectedIntegration.icon} alt={selectedIntegration.name} />
              </div>
              <h3>About {selectedIntegration.name}</h3>
            </div>
            <div className="description-content">
              <p>{selectedIntegration.description}</p>
            </div>
          </div>
        )}

        {step === 3 && selectedIntegration && (
          <form className="integration-form" autoComplete="off" key={`form-step-${step}`} onSubmit={(e) => e.preventDefault()}>
            <div className="integration-header">
              <div className="integration-icon">
                <img src={selectedIntegration.icon} alt={selectedIntegration.name} />
              </div>
              <h3>{isFixingConnection ? 'Fix Connection with' : 'Connect with'} {selectedIntegration.name}</h3>
            </div>

            <div className="form-group">
              <label>Org Name *</label>
              <input
                type="text"
                name="orgName"
                value={formData.orgName}
                onChange={handleChange}
                className={errors.orgName ? 'error' : ''}
                placeholder="Enter org name"
                autoComplete="off"
              />
              {errors.orgName && <span className="error-message">{errors.orgName}</span>}
            </div>

            <div className="form-group">
              <label>Tenant URL *</label>
              <input
                type="text"
                name="tenantUrl"
                value={formData.tenantUrl}
                onChange={handleChange}
                className={errors.tenantUrl ? 'error' : ''}
                placeholder="Enter tenant URL"
                autoComplete="off"
              />
              {errors.tenantUrl && <span className="error-message">{errors.tenantUrl}</span>}
            </div>

            <div className="form-group">
              <label>Client ID *</label>
              <input
                type="text"
                name="integration_client_id_field"
                data-field="clientId"
                value={formData.clientId}
                onChange={handleChange}
                className={errors.clientId ? 'error' : ''}
                placeholder="Enter client ID"
                autoComplete="off"
              />
              {errors.clientId && <span className="error-message">{errors.clientId}</span>}
            </div>

            <div className="form-group">
              <label>Client Secret *</label>
              <input
                type="password"
                name="integration_client_secret_field"
                data-field="clientSecret"
                value={formData.clientSecret}
                onChange={handleChange}
                className={errors.clientSecret ? 'error' : ''}
                placeholder="Enter client secret"
                autoComplete="new-password"
              />
              {errors.clientSecret && <span className="error-message">{errors.clientSecret}</span>}
              <span className="help-text">The Consumer Secret from your Salesforce Connected App</span>
            </div>

            <div className="form-group">
              <label>Description *</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                className={errors.description ? 'error' : ''}
                placeholder="Enter description"
                rows="4"
                autoComplete="off"
              />
              {errors.description && <span className="error-message">{errors.description}</span>}
            </div>

            <div className="form-group">
              <label>Environment *</label>
              <div className="environment-toggle">
                <label className="toggle-option">
                  <input
                    type="radio"
                    name="environment"
                    value="production"
                    checked={formData.environment === 'production'}
                    onChange={handleChange}
                  />
                  <span className="toggle-label">Production</span>
                </label>
                <label className="toggle-option">
                  <input
                    type="radio"
                    name="environment"
                    value="sandbox"
                    checked={formData.environment === 'sandbox'}
                    onChange={handleChange}
                  />
                  <span className="toggle-label">Sandbox</span>
                </label>
              </div>
            </div>

            <div className="form-group">
              <label className="toggle-option" htmlFor="useJwt-checkbox">
                <input
                  type="checkbox"
                  id="useJwt-checkbox"
                  name="useJwt"
                  checked={formData.useJwt}
                  onChange={handleChange}
                />
                <span className="toggle-label">Use JWT Bearer Flow (Recommended for Metadata API access)</span>
              </label>
              <span className="help-text">JWT Bearer flow provides better access for Metadata API operations</span>
            </div>

            {formData.useJwt && (
              <>
                <div className="form-group">
                  <label>Salesforce Username *</label>
                  <input
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleChange}
                    className={errors.username ? 'error' : ''}
                    placeholder="Enter Salesforce username"
                    autoComplete="off"
                  />
                  {errors.username && <span className="error-message">{errors.username}</span>}
                  <span className="help-text">The username of the Salesforce user to impersonate</span>
                </div>

                <div className="form-group">
                  <label>Private Key *</label>
                  <textarea
                    name="privateKey"
                    value={formData.privateKey}
                    onChange={handleChange}
                    className={errors.privateKey ? 'error' : ''}
                    placeholder="Paste your private key here (including BEGIN/END RSA PRIVATE KEY lines)"
                    rows="4"
                    autoComplete="off"
                  />
                  {errors.privateKey && <span className="error-message">{errors.privateKey}</span>}
                  <span className="help-text">The private key used to sign the JWT token</span>
                </div>
              </>
            )}

            <button
              className="test-connection-button"
              onClick={handleTestConnection}
              disabled={isAuthorizing}
              type="button"
              title="Test the connection without saving any data"
            >
              {isAuthorizing ? 'Testing Connection...' : 'Test Connection'}
            </button>
            {connectionStatus && (
              <div
                className={`connection-status-message ${connectionStatus.type}`}
                style={{ marginTop: 8 }}
              >
                {connectionStatus.message}
              </div>
            )}
          </form>
        )}


      </div>

      <div className="wizard-footer">
        <div className="footer-content">
          {step > 1 ? (
            <button className="back-button" onClick={handleBack} type="button">
              Previous
            </button>
          ) : (
            // Add an empty div to push the "Next" button to the right on step 1
            <div />
          )}

          {/* Conditional rendering for Next/Save button */}
          {step === 1 || step === 2 ? (
            <button
              className="next-button"
              onClick={handleNext}
              disabled={(step === 1 && !selectedIntegration) || (step === 2 && false) /* placeholder for potential future step 2 disable logic */}
              type="button"
            >
              Next
            </button>
          ) : step === 3 ? (
            <button
              className={`save-button ${isSaving ? 'saving' : ''} ${isSaveComplete ? 'completed' : ''}`}
              onClick={handleStartScan}
              disabled={isSaving || isSaveComplete}
              type="button"
            >
              {isFixingConnection
                ? (isSaving ? 'Fixing...' : isSaveComplete ? 'Fixed' : 'Fix Connection')
                : (isSaving ? 'Saving...' : isSaveComplete ? 'Saved' : 'Save')}
            </button>
          ) : null}
        </div>
      </div>

      {/* Optionally, show a message if Save is disabled after test */}
      {step === 3 && isAuthorized && !isSaveComplete && false && (
        <div className="info-message">Please test the connection before saving.</div>
      )}
    </div>
  );
};

export default ScanWizard;
