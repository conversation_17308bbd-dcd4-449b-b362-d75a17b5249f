import React from 'react';
import { AgGridReact } from 'ag-grid-react';
import { ModuleRegistry, AllCommunityModule } from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';

ModuleRegistry.registerModules([AllCommunityModule]);

const PermissionSetAssignmentsTable = ({ permissionSetAssignments }) => {
  console.log('PermissionSetAssignmentsTable permissionSetAssignments:', permissionSetAssignments); // Debug log
  if (!permissionSetAssignments || permissionSetAssignments.length === 0) {
    return <div>No permission set assignments available.</div>;
  }
  const columnDefs = [
    { headerName: 'Profile Name', field: 'ProfileName', filter: true },
    { headerName: 'Permission Set Name', field: 'PermissionSetName', filter: true },
    { headerName: 'Assignment Count', field: 'AssignmentCount', filter: 'agNumberColumnFilter' },
  ];
  return (
    <div className="ag-theme-quartz" style={{ height: 400, width: '100%' }}>
      <AgGridReact
        rowData={permissionSetAssignments}
        columnDefs={columnDefs}
        domLayout="normal"
        animateRows={true}
      />
    </div>
  );
};

export default PermissionSetAssignmentsTable;
