.app-header {
  background-color: #000000;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 34px;
}

.app-logo {
  height: 40px;
}

.search-container {
  display: flex;
  align-items: center;
  background-color: #000000;
  border-radius: 8px;
  padding: 6px 12px;
  width: 300px;
}

.search-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.search-input {
  background: transparent;
  border: none;
  color: #FFFFFF;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  width: 100%;
}

.search-input::placeholder {
  color: #FFFFFF;
  opacity: 0.7;
}

.search-input:focus {
  outline: none;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 34px;
}

.header-icons {
  display: flex;
  gap: 24px;
}

.icon-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.user-profile {
  position: relative;
}

.user-profile-info {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.user-profile-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #EAFAF3;
  border: 1px solid #0EA8DE;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  backdrop-filter: blur(22px);
}

.avatar-initials {
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #020A07;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
}

.dropdown-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #020A07;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  min-width: 200px;
  margin-top: 8px;
  overflow: hidden;
}

.user-dropdown ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-dropdown li {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-dropdown li:last-child {
  border-bottom: none;
}

.user-dropdown a,
.user-dropdown button {
  display: block;
  padding: 12px 16px;
  color: #FFFFFF;
  text-decoration: none;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-dropdown a:hover,
.user-dropdown button:hover {
  background-color: rgba(255, 255, 255, 0.1);
} 