import React, { useEffect, useState } from 'react';
import './Footer.css';

const Footer = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  useEffect(() => {
    // Check if sidebar is collapsed initially
    const appContainer = document.querySelector('.app-container');
    if (appContainer && appContainer.classList.contains('sidebar-is-collapsed')) {
      setIsSidebarCollapsed(true);
    }

    // Set up a mutation observer to watch for class changes on app-container
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          const hasCollapsedClass = appContainer.classList.contains('sidebar-is-collapsed');
          setIsSidebarCollapsed(hasCollapsedClass);
        }
      });
    });

    if (appContainer) {
      observer.observe(appContainer, { attributes: true });
    }

    return () => {
      if (appContainer) {
        observer.disconnect();
      }
    };
  }, []);

  return (
    <footer className={`internal-footer ${isSidebarCollapsed ? 'sidebar-is-collapsed' : ''}`}>
      <div className="footer-content">
        <div className="footer-left">
          <span className="footer-logo">Atomsec</span>
          <div className="footer-divider"></div>
          <span className="footer-copyright">Copyright 2025</span>
        </div>
        <div className="footer-right">
          <span className="footer-links">Accessibility, data and privacy policies</span>
        </div>
      </div>
    </footer>
  );
};

export default Footer;