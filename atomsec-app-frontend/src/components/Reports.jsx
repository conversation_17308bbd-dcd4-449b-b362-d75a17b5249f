import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './Dashboard.css'; // Reuse dashboard styles

const Reports = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchReports = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // In a real implementation, this would fetch reports from the API
        // For now, we'll just simulate a delay and use mock data
        setTimeout(() => {
          const mockReports = [
            {
              id: 1,
              name: 'Monthly Security Summary',
              date: '2023-06-01',
              type: 'Security',
              status: 'completed'
            },
            {
              id: 2,
              name: 'Quarterly Compliance Report',
              date: '2023-04-15',
              type: 'Compliance',
              status: 'completed'
            },
            {
              id: 3,
              name: 'Annual Security Review',
              date: '2022-12-31',
              type: 'Security',
              status: 'completed'
            }
          ];
          
          setReports(mockReports);
          setLoading(false);
        }, 1000);
        
      } catch (err) {
        console.error('Error fetching reports:', err);
        setError('Failed to load reports');
        setLoading(false);
      }
    };

    fetchReports();
  }, []);

  if (loading) return <div className="loading-spinner">Loading reports...</div>;
  if (error) return <div className="error-alert">{error}</div>;

  return (
    <div className="dashboard">
      <div className="page-header">
        <h1 className="page-title">Reports</h1>
        <p className="page-description">View and download security reports</p>
      </div>
      
      {reports.length === 0 ? (
        <div className="empty-state">
          <div className="empty-icon">📊</div>
          <h3>No Reports Available</h3>
          <p>Run scans on your Salesforce orgs to generate reports.</p>
        </div>
      ) : (
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">Available Reports</h2>
          </div>
          
          <div className="scans-table-container">
            <table className="scans-table">
              <thead>
                <tr>
                  <th>Report Name</th>
                  <th>Date</th>
                  <th>Type</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {reports.map(report => (
                  <tr key={report.id}>
                    <td>{report.name}</td>
                    <td>{report.date}</td>
                    <td>{report.type}</td>
                    <td>
                      <span className={`status-badge ${report.status}`}>
                        {report.status === 'completed' ? 'Completed' : 'Pending'}
                      </span>
                    </td>
                    <td>
                      <div className="action-buttons">
                        <button className="action-button view">
                          View
                        </button>
                        <button className="action-button download">
                          Download
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default Reports;
