import React, { useState, useEffect } from 'react';
import { <PERSON>dal, Button, Form, Alert, Spinner } from 'react-bootstrap';
import './AuthPopup.css';
import axios from 'axios';
import { toast } from 'react-toastify';
import { API_CONFIG } from '../config';
import { API_BASE_URL, authEndpoints } from '../authConfig';

const AuthPopup = ({ isOpen, onClose, integrationId, orgName, onSuccess, env = 'production' }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [authUrl, setAuthUrl] = useState('');

  // Determine environment based on integrationId or orgName
  const determineEnvironment = () => {
    if (orgName && orgName.toLowerCase().includes('sandbox')) {
      return 'sandbox';
    } else if (orgName && orgName.toLowerCase().includes('test')) {
      return 'test';
    } else if (orgName && orgName.toLowerCase().includes('developer')) {
      return 'developer';
    }
    return env;
  };

  const environment = determineEnvironment();

  // Generate authentication URL
  const generateAuthUrl = () => {
    const baseUrl = `${API_CONFIG.baseURL}${API_CONFIG.endpoints.auth.login}`;
    const params = new URLSearchParams({
      customDomain: integrationId || '',
      targetInstanceUrl: integrationId || '', // Use integrationId as the target
      environment: environment
    });

    return `${baseUrl}?${params.toString()}`;
  };

  useEffect(() => {
    if (isOpen) {
      setError(null);
      setAuthUrl(generateAuthUrl());
    }
  }, [isOpen, integrationId, environment]);

  const handleAuthenticate = () => {
    setIsLoading(true);
    setError(null);

    // Open authentication URL in a new window
    const authWindow = window.open(authUrl, 'auth', 'width=600,height=700,scrollbars=yes,resizable=yes');

    // Poll for authentication completion
    const pollInterval = setInterval(() => {
      try {
        if (authWindow.closed) {
          clearInterval(pollInterval);
          setIsLoading(false);
          
          // Check if authentication was successful
          const authResult = localStorage.getItem('authResult');
          if (authResult) {
            try {
              const result = JSON.parse(authResult);
              if (result.success) {
                onSuccess && onSuccess(result);
                onClose();
              } else {
                setError(result.error || 'Authentication failed');
              }
            } catch (e) {
              setError('Authentication failed');
            }
            localStorage.removeItem('authResult');
          }
        }
      } catch (e) {
        // Window might be closed
        clearInterval(pollInterval);
        setIsLoading(false);
      }
    }, 1000);

    // Cleanup after 5 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      setIsLoading(false);
    }, 300000);
  };

  const handleClose = () => {
    setError(null);
    setIsLoading(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="auth-popup-overlay" onClick={onClose}>
      <div className="auth-popup-content" onClick={(e) => e.stopPropagation()}>
        <div className="auth-popup-header">
          <h3>Authentication Required</h3>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>
        <div className="auth-popup-body">
          <p>Your session with <strong>{orgName || 'this Salesforce org'}</strong> has expired or is no longer valid.</p>
          <p>You need to re-authenticate to perform this action.</p>
        </div>
        <div className="auth-popup-footer">
          <button className="btn btn-secondary" onClick={onClose}>Cancel</button>
          <button className="btn btn-primary" onClick={handleAuthenticate}>Authenticate Now</button>
        </div>
      </div>
    </div>
  );
};

export default AuthPopup;
