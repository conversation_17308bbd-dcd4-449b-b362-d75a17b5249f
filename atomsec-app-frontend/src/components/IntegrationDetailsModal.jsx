import React, { useEffect, useState } from 'react';
import { FiX } from 'react-icons/fi';
import Portal from './Portal';
import './IntegrationDetailsModal.css';

const IntegrationDetailsModal = ({ integration, onClose }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Add a small delay before showing the modal to prevent flickering
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 50);

    // Prevent scrolling on the body when modal is open
    document.body.style.overflow = 'hidden';

    // Cleanup function
    return () => {
      clearTimeout(timer);
      document.body.style.overflow = '';
    };
  }, []);

  const handleClose = () => {
    // Animate out first, then call onClose
    setIsVisible(false);
    setTimeout(onClose, 300); // Match this with the CSS transition time
  };

  if (!integration) return null;

  const formatDate = (dateString) => {
    if (!dateString) return 'Not available';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid date';

    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Portal>
      <div
        className={`modal-overlay ${isVisible ? 'visible' : ''}`}
        onClick={handleClose}
      >
        <div
          className={`modal-content ${isVisible ? 'visible' : ''}`}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="modal-header">
            <h2>Integration Details</h2>
            <button className="close-button" onClick={handleClose}>
              <FiX />
            </button>
          </div>
          <div className="modal-body">
            <div className="integration-details">
              <div className="detail-row">
                <div className="detail-label">Integration ID:</div>
                <div className="detail-value">{integration.id || integration.Id || integration.integrationId || integration.IntegrationId || 'N/A'}</div>
              </div>
              <div className="detail-row">
                <div className="detail-label">Name:</div>
                <div className="detail-value">{integration.name || integration.Name || integration.orgName || integration.OrgName}</div>
              </div>
              <div className="detail-row">
                <div className="detail-label">Type:</div>
                <div className="detail-value">{integration.type || integration.Type || 'Salesforce'}</div>
              </div>
              <div className="detail-row">
                <div className="detail-label">Instance URL:</div>
                <div className="detail-value">{integration.tenantUrl || integration.TenantUrl || integration.instanceUrl || integration.InstanceUrl || 'N/A'}</div>
              </div>
              <div className="detail-row">
                <div className="detail-label">Environment:</div>
                <div className="detail-value">{integration.environment || integration.Environment || 'Production'}</div>
              </div>
              <div className="detail-row">
                <div className="detail-label">Status:</div>
                <div className="detail-value">
                  <span className={`status-indicator ${(integration.isActive || integration.IsActive) ? 'active' : 'inactive'}`}>
                    {(integration.isActive || integration.IsActive) ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
              <div className="detail-row">
                <div className="detail-label">Last Scan:</div>
                <div className="detail-value">{formatDate(integration.lastScan || integration.LastScan)}</div>
              </div>
              <div className="detail-row">
                <div className="detail-label">Created At:</div>
                <div className="detail-value">{formatDate(integration.createdAt || integration.CreatedAt)}</div>
              </div>
              <div className="detail-row">
                <div className="detail-label">Description:</div>
                <div className="detail-value description">{integration.description || integration.Description || 'No description provided.'}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Portal>
  );
};

export default IntegrationDetailsModal;
