import React from 'react';
import { useAuth } from '../context/AuthContext';
import AuthDebug from './AuthDebug';

const AuthTest = () => {
  const { isAuthenticated, user, login, logout, loading, error } = useAuth();

  return (
    <div className="auth-test">
      <h2>Authentication Test</h2>

      {loading && <p>Loading authentication state...</p>}

      {error && (
        <div className="error-message">
          <p>Error: {error}</p>
        </div>
      )}

      <div className="auth-status">
        <p><strong>Authentication Status:</strong> {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</p>

        {isAuthenticated && user && (
          <div className="user-info">
            <h3>User Information</h3>
            <p><strong>Name:</strong> {user.name || 'Not available'}</p>
            <p><strong>Username:</strong> {user.username || 'Not available'}</p>
            <p><strong>Token Available:</strong> {user.accessToken ? 'Yes' : 'No'}</p>
          </div>
        )}

        <div className="auth-actions">
          {!isAuthenticated ? (
            <button
              onClick={login}
              className="login-button azure-button"
              disabled={loading}
            >
              Sign in with Microsoft
            </button>
          ) : (
            <button
              onClick={logout}
              className="login-button"
              disabled={loading}
            >
              Sign Out
            </button>
          )}
        </div>
      </div>

      {/* Debug component */}
      <AuthDebug />
    </div>
  );
};

export default AuthTest;
