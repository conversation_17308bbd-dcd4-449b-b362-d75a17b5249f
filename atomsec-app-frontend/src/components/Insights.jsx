import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { FiRefreshCw } from 'react-icons/fi';
import { IoChevronDown } from 'react-icons/io5';
import axios from 'axios';
import { API_CONFIG } from '../config';
import DataStatusMessage from './DataStatusMessage';
import './Insights.css';

const Insights = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { integrationId } = useParams(); // Get integrationId from URL params
  const [securityHealthData, setSecurityHealthData] = useState(null);
  const [securityViewData, setSecurityViewData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastRefreshTime, setLastRefreshTime] = useState(new Date().toLocaleString());
  const [securityViewDataStatus, setSecurityViewDataStatus] = useState('loading');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const pollingTimeoutRef = useRef(null);

  // Fetch security health check data
  const fetchSecurityHealthData = async () => {
    setLoading(true);
    setError(null);
    try {
      // Use integrationId if available, otherwise fetch all data
      const url = integrationId
        ? `${API_CONFIG.baseURL}/api/integrations/${integrationId}/health-check`
        : `${API_CONFIG.baseURL}/api/security_health_check`;

      const response = await axios.get(url);
      setSecurityHealthData(response.data);
      setLastRefreshTime(new Date().toLocaleString());
    } catch (err) {
      console.error('Error fetching security health check data:', err);
      setError('Failed to load security health check data');
    } finally {
      setLoading(false);
    }
  };

  // Clear polling timeout when component unmounts
  useEffect(() => {
    return () => {
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
      }
    };
  }, []);

  // Fetch security view data
  const fetchSecurityViewData = async (forceRefresh = false) => {
    // Only attempt to fetch if we have an integrationId
    if (!integrationId) {
      console.log('No integration ID provided, skipping security view data fetch');
      return;
    }

    if (forceRefresh) {
      setIsRefreshing(true);
    }

    setSecurityViewDataStatus('loading');
    setLoading(true);

    try {
      // Use the new integration-based API endpoint
      const refreshParam = forceRefresh ? '?forceRefresh=true' : '';
      const url = `${API_CONFIG.baseURL}/api/integrations/${integrationId}/security-view${refreshParam}`;
      const response = await axios.get(url);

      // Check data status
      if (response.data.dataStatus === 'pending') {
        setSecurityViewDataStatus('pending');

        // Clear any existing polling timeout
        if (pollingTimeoutRef.current) {
          clearTimeout(pollingTimeoutRef.current);
        }

        // Schedule a refresh after a delay (5 seconds)
        pollingTimeoutRef.current = setTimeout(() => {
          console.log('Polling for security view data...');
          fetchSecurityViewData();
        }, 5000);
      } else if (response.data.dataStatus === 'available' || !response.data.dataStatus) {
        setSecurityViewData(response.data);
        setSecurityViewDataStatus('available');
        setLastRefreshTime(new Date().toLocaleString());

        // Clear any existing polling timeout
        if (pollingTimeoutRef.current) {
          clearTimeout(pollingTimeoutRef.current);
          pollingTimeoutRef.current = null;
        }
      } else if (response.data.dataStatus === 'error') {
        console.error('Error in security view data:', response.data.message);
        setSecurityViewDataStatus('error');
        setError(response.data.message || 'Failed to fetch security view data');
      } else if (response.data.dataStatus === 'empty') {
        console.log('No security view data available');
        setSecurityViewDataStatus('empty');
      }
    } catch (err) {
      console.error('Error fetching security view data:', err);
      setSecurityViewDataStatus('error');
      setError('Failed to fetch security view data. Please try again.');
    } finally {
      setLoading(false);
      if (forceRefresh) {
        setIsRefreshing(false);
      }
    }
  };

  useEffect(() => {
    fetchSecurityHealthData();
    fetchSecurityViewData();
  }, [integrationId]); // Re-fetch when integrationId changes

  // Show loading spinner only when initially loading
  if (loading && securityViewDataStatus !== 'pending') {
    return <div className="loading-spinner">Loading security health check data...</div>;
  }

  // Show pending status message when data is being fetched in the background
  if (securityViewDataStatus === 'pending') {
    return (
      <div className="insights-container">
        <div className="main-content-card">
          <DataStatusMessage
            status="pending"
            message="Security data is being fetched from Salesforce. This may take a few moments."
            onRefresh={() => fetchSecurityViewData(true)}
            isRefreshing={isRefreshing}
          />
        </div>
      </div>
    );
  }

  // Show a more user-friendly error message
  if (error || securityViewDataStatus === 'error') {
    return (
      <div className="insights-container">
        <div className="main-content-card">
          <div className="error-state">
            <h2>Unable to load insights data</h2>
            <p>{error}</p>
            <p>Please select a Salesforce organization from the dashboard to view insights.</p>
            <div className="error-actions">
              <button className="btn btn-secondary" onClick={() => fetchSecurityViewData(true)}>
                Try Again
              </button>
              <button className="btn btn-primary" onClick={() => navigate('/')}>
                Return to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show empty state message when no data is available
  if (securityViewDataStatus === 'empty') {
    return (
      <div className="insights-container">
        <div className="main-content-card">
          <div className="empty-state">
            <h2>No security data available</h2>
            <p>No security data is available for this Salesforce organization.</p>
            <div className="empty-actions">
              <button className="btn btn-primary" onClick={() => fetchSecurityViewData(true)}>
                Fetch Security Data
              </button>
              <button className="btn btn-secondary" onClick={() => navigate('/')}>
                Return to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="insights-container">
      <div className="main-content-card">
        <div className="page-header">
          <div className="header-content">
            <h1>Welcome!</h1>
            <p>Last refreshed on: {lastRefreshTime}</p>
          </div>
        </div>

        <div className="filter-controls">
          <span className="filter-label">View dashboard by:</span>
          <div className="filter-select">
            <span>Setting Group</span>
            <IoChevronDown />
          </div>
          <div className="filter-select">
            <span>Risk Type</span>
            <IoChevronDown />
          </div>
          <button
            className="reset-btn"
            onClick={() => {
              fetchSecurityHealthData();
              fetchSecurityViewData(true);
            }}
            disabled={isRefreshing}
          >
            <FiRefreshCw className={isRefreshing ? 'spinning' : ''} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>

        <div className="charts-container">
          <div className="chart-card">
            <h3 className="chart-title">Distribution of Risk Types</h3>
            {/* Add pie chart component here */}
            <div className="chart-legend">
              <div className="legend-item">
                <span className="dot high-risk"></span>
                <span>High Risk (30)</span>
              </div>
              <div className="legend-item">
                <span className="dot medium-risk"></span>
                <span>Medium Risk (80)</span>
              </div>
              <div className="legend-item">
                <span className="dot meets-standard"></span>
                <span>Meets Standard (32)</span>
              </div>
            </div>
          </div>

          <div className="chart-card">
            <h3 className="chart-title">Misaligned settings</h3>
            {/* Add bar chart component here */}
          </div>
        </div>

        <div className="details-section">
          <div className="details-header">
            <h2>Details</h2>
            <p>Measures how closely your org's security settings align with Salesforce best practices.</p>
          </div>

          <div className="table-container">
            <div className="table-header">
              <div className="table-header-cell risk-type-cell">
                <span>Risk Type</span>
                <IoChevronDown />
              </div>
              <div className="table-header-cell setting-cell">
                <span>Setting</span>
                <IoChevronDown />
              </div>
              <div className="table-header-cell setting-group-cell">
                <span>Setting Group</span>
                <IoChevronDown />
              </div>
              <div className="table-header-cell org-value-cell">
                <span>Org value</span>
                <IoChevronDown />
              </div>
              <div className="table-header-cell standard-value-cell">
                <span>Standard value</span>
                <IoChevronDown />
              </div>
            </div>

            {/* Example table row */}
            <div className="table-row">
              <div className="table-cell risk-type-cell">HIGH_RISK</div>
              <div className="table-cell setting-cell">
                Enable click jack protection for customer Visualforce pages with standard headers
              </div>
              <div className="table-cell setting-group-cell">CertificateAndKeyManagement</div>
              <div className="table-cell org-value-cell">1</div>
              <div className="table-cell standard-value-cell">0</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Insights;
