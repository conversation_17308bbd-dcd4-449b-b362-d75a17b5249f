import React, { useState, useEffect } from 'react';
import './SeverityChart.css';

// Import SVG assets
import moreVertSvg from '../assets/dashboard/more_vert.svg';
import refreshSvg from '../assets/dashboard/refresh.svg';

/**
 * SeverityChart component for displaying issue severity distribution
 * Updated to match the Figma design
 *
 * @param {Object} props - Component props
 * @param {Object} props.data - Chart data with high, medium, and low counts
 * @param {Function} [props.onRefresh] - Optional refresh callback function
 * @param {boolean} [props.isRefreshing] - Whether the chart is currently refreshing
 */
const SeverityChart = ({ data, onRefresh, isRefreshing }) => {
  const [activeTab, setActiveTab] = useState('7days'); // '7days', '30days', 'allTime'
  const [chartData, setChartData] = useState({ high: 0, medium: 0, low: 0 });

  // Update chart data when tab changes or data changes
  useEffect(() => {
    // Recalculate time periods data when data changes
    const updatedTimePeriodsData = {
      '7days': {
        high: data?.high || 0,
        medium: data?.medium || 0,
        low: data?.low || 0
      },
      '30days': {
        high: Math.round((data?.high || 0) * 1.5),
        medium: Math.round((data?.medium || 0) * 1.8),
        low: Math.round((data?.low || 0) * 1.3)
      },
      'allTime': {
        high: Math.round((data?.high || 0) * 2.2),
        medium: Math.round((data?.medium || 0) * 2.5),
        low: Math.round((data?.low || 0) * 2.0)
      }
    };

    // Update chart data based on active tab
    setChartData(updatedTimePeriodsData[activeTab]);
  }, [activeTab, data]);

  // Calculate total for percentages
  const total = (chartData?.high || 0) + (chartData?.medium || 0) + (chartData?.low || 0);

  // Handle refresh button click
  const handleRefresh = () => {
    if (onRefresh && !isRefreshing) {
      onRefresh();
    }
  };

  return (
    <div className="severity-chart-card">
      <div className="severity-chart-header">
        <h3 className="severity-chart-title">Issue Severity Distribution</h3>
        <div className="severity-chart-actions">
          <button
            className="severity-chart-refresh-btn"
            aria-label="Refresh data"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <img
              src={refreshSvg}
              alt="Refresh"
              className={isRefreshing ? 'rotating' : ''}
            />
          </button>
          <button
            className="severity-chart-menu-btn"
            aria-label="More options"
          >
            <img src={moreVertSvg} alt="More options" />
          </button>
        </div>
      </div>

      <div className="severity-chart-tabs">
        <button
          className={`severity-chart-tab ${activeTab === '7days' ? 'active' : ''}`}
          onClick={() => setActiveTab('7days')}
        >
          Last 7 Days
        </button>
        <button
          className={`severity-chart-tab ${activeTab === '30days' ? 'active' : ''}`}
          onClick={() => setActiveTab('30days')}
        >
          Last 30 Days
        </button>
        <button
          className={`severity-chart-tab ${activeTab === 'allTime' ? 'active' : ''}`}
          onClick={() => setActiveTab('allTime')}
        >
          All Time
        </button>
      </div>

      <div className="severity-chart-content">
        <div className="severity-chart-visualization">
          <div className="severity-chart-pie">
            {/* Figma-based pie chart */}
            <div className="pie-chart-container">
              {isRefreshing ? (
                <div className="pie-chart-loading">
                  <div className="pie-chart-spinner"></div>
                  <div className="pie-chart-loading-text">Loading...</div>
                </div>
              ) : total > 0 ? (
                <div className="pie-chart-custom">
                  {/* Create a custom pie chart using CSS */}
                  <div className="pie-chart-circle">
                    {/* Dynamic pie chart based on actual data values */}
                    {(() => {
                      // Calculate percentages
                      const highPercent = total > 0 ? (chartData.high / total) * 100 : 0;
                      const mediumPercent = total > 0 ? (chartData.medium / total) * 100 : 0;
                      const lowPercent = total > 0 ? (chartData.low / total) * 100 : 0;

                      // Calculate angles for the SVG arcs (in degrees)
                      const highAngle = (highPercent / 100) * 360;
                      const mediumAngle = (mediumPercent / 100) * 360;
                      const lowAngle = (lowPercent / 100) * 360;

                      // Calculate start and end angles for each segment
                      const highStart = 0;
                      const highEnd = highAngle;

                      const mediumStart = highEnd;
                      const mediumEnd = mediumStart + mediumAngle;

                      const lowStart = mediumEnd;
                      const lowEnd = 360; // Complete the circle

                      // Helper function to create SVG arc path
                      const createArc = (startAngle, endAngle, radius) => {
                        // Convert angles from degrees to radians
                        const startRad = (startAngle - 90) * (Math.PI / 180);
                        const endRad = (endAngle - 90) * (Math.PI / 180);

                        // Calculate start and end points
                        const startX = 100 + radius * Math.cos(startRad);
                        const startY = 100 + radius * Math.sin(startRad);
                        const endX = 100 + radius * Math.cos(endRad);
                        const endY = 100 + radius * Math.sin(endRad);

                        // Determine if the arc should be drawn the long way around
                        const largeArcFlag = endAngle - startAngle <= 180 ? 0 : 1;

                        // Create the SVG path
                        return `M 100 100 L ${startX} ${startY} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${endX} ${endY} Z`;
                      };

                      return (
                        <svg viewBox="0 0 200 200" className="pie-chart-svg">
                          {/* High segment */}
                          {highPercent > 0 && (
                            <path
                              d={createArc(highStart, highEnd, 100)}
                              className="pie-segment-svg high"
                            />
                          )}

                          {/* Medium segment */}
                          {mediumPercent > 0 && (
                            <path
                              d={createArc(mediumStart, mediumEnd, 100)}
                              className="pie-segment-svg medium"
                            />
                          )}

                          {/* Low segment */}
                          {lowPercent > 0 && (
                            <path
                              d={createArc(lowStart, lowEnd, 100)}
                              className="pie-segment-svg low"
                            />
                          )}
                        </svg>
                      );
                    })()}

                    {/* Dynamic value labels positioned based on segment angles */}
                    {(() => {
                      // Calculate percentages
                      const highPercent = total > 0 ? (chartData.high / total) * 100 : 0;
                      const mediumPercent = total > 0 ? (chartData.medium / total) * 100 : 0;
                      const lowPercent = total > 0 ? (chartData.low / total) * 100 : 0;

                      // Calculate angles for positioning labels
                      const highAngle = (highPercent / 100) * 360;
                      const mediumAngle = (mediumPercent / 100) * 360;

                      // Calculate midpoint angles for each segment (in radians)
                      const highMidAngle = ((0 + highAngle) / 2 - 90) * (Math.PI / 180);
                      const mediumMidAngle = ((highAngle + (highAngle + mediumAngle)) / 2 - 90) * (Math.PI / 180);
                      const lowMidAngle = ((highAngle + mediumAngle + 360) / 2 - 90) * (Math.PI / 180);

                      // Calculate label positions (distance from center = 70px)
                      const labelDistance = 70;
                      const highLabelX = 100 + labelDistance * Math.cos(highMidAngle);
                      const highLabelY = 100 + labelDistance * Math.sin(highMidAngle);
                      const mediumLabelX = 100 + labelDistance * Math.cos(mediumMidAngle);
                      const mediumLabelY = 100 + labelDistance * Math.sin(mediumMidAngle);
                      const lowLabelX = 100 + labelDistance * Math.cos(lowMidAngle);
                      const lowLabelY = 100 + labelDistance * Math.sin(lowMidAngle);

                      return (
                        <>
                          {chartData?.high > 0 && (
                            <div className="pie-segment-label" style={{
                              position: 'absolute',
                              left: `${(highLabelX / 200) * 100}%`,
                              top: `${(highLabelY / 200) * 100}%`,
                              transform: 'translate(-50%, -50%)',
                              color: '#FFFFFF'
                            }}>{chartData?.high}</div>
                          )}

                          {chartData?.medium > 0 && (
                            <div className="pie-segment-label" style={{
                              position: 'absolute',
                              left: `${(mediumLabelX / 200) * 100}%`,
                              top: `${(mediumLabelY / 200) * 100}%`,
                              transform: 'translate(-50%, -50%)',
                              color: '#FFFFFF'
                            }}>{chartData?.medium}</div>
                          )}

                          {chartData?.low > 0 && (
                            <div className="pie-segment-label" style={{
                              position: 'absolute',
                              left: `${(lowLabelX / 200) * 100}%`,
                              top: `${(lowLabelY / 200) * 100}%`,
                              transform: 'translate(-50%, -50%)',
                              color: '#FFFFFF'
                            }}>{chartData?.low}</div>
                          )}
                        </>
                      );
                    })()}

                    {/* Center circle and text */}
                    <div className="pie-center-circle">
                      <div className="pie-center-text">{total}</div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="pie-segment empty">
                  <div className="pie-center-text">No data</div>
                </div>
              )}
            </div>
          </div>

          {/* Removed the severity-chart-values div as values are now shown on the pie chart */}
        </div>

        <div className="severity-chart-legend">
          <div className="severity-legend-item">
            <div className="severity-legend-color high"></div>
            <div className="severity-legend-label">High</div>
          </div>
          <div className="severity-legend-item">
            <div className="severity-legend-color medium"></div>
            <div className="severity-legend-label">Medium</div>
          </div>
          <div className="severity-legend-item">
            <div className="severity-legend-color low"></div>
            <div className="severity-legend-label">Low</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SeverityChart;
