/* Code Quality Dashboard - SonarQube Style with Atom Security Branding */

.code-quality-dashboard {
  background: #f8f9fa;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Section */
.dashboard-header {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.scan-info {
  display: flex;
  gap: 20px;
  margin-top: 8px;
  font-size: 14px;
  color: #6c757d;
}

.scan-info span {
  display: flex;
  align-items: center;
  gap: 6px;
}

.icon-link::before {
  content: "🔗";
  font-size: 12px;
}

.icon-check::before {
  content: "✓";
  color: #51D59C;
  font-weight: bold;
}

.export-button {
  background: #51D59C;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.export-button:hover {
  background: #45c492;
}

.icon-download::before {
  content: "⬇";
  font-size: 12px;
}

/* Filter Section - FIXED with more specific rules to override IntegrationTabs.css */
.code-quality-dashboard .filter-section {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.code-quality-dashboard .filters {
  display: flex;
  gap: 20px;
  align-items: flex-end;
}

.code-quality-dashboard .filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 140px;
}

.code-quality-dashboard .filter-group label {
  font-size: 11px !important;
  font-weight: 600 !important;
  color: #495057 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  margin-bottom: 2px !important;
}

/* Override IntegrationTabs.css filter-select styles */
.code-quality-dashboard .filter-section .filter-select {
  flex: none !important;
  max-width: 200px !important;
  min-width: 140px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 4px !important;
  background: white !important;
  color: #495057 !important;
  padding: 8px 32px 8px 12px !important;
  outline: none !important;
  height: 36px !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  white-space: normal !important;
  margin-right: 0 !important;
  box-sizing: border-box !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 8px center !important;
  background-size: 16px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: auto !important;
}

.code-quality-dashboard .filter-section .filter-select:focus {
  outline: none !important;
  border-color: #51D59C !important;
  box-shadow: 0 0 0 2px rgba(81, 213, 156, 0.1) !important;
}

.code-quality-dashboard .filter-section .search-input {
  flex: none !important;
  max-width: 300px !important;
  min-width: 200px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 4px !important;
  background: white !important;
  color: #495057 !important;
  padding: 8px 12px !important;
  outline: none !important;
  height: 36px !important;
  box-sizing: border-box !important;
  background-image: none !important;
  white-space: normal !important;
  margin-right: 0 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: auto !important;
}

.code-quality-dashboard .filter-section .search-input:focus {
  outline: none !important;
  border-color: #51D59C !important;
  box-shadow: 0 0 0 2px rgba(81, 213, 156, 0.1) !important;
}

.code-quality-dashboard .filter-section .reset-filters-button {
  background: #6c757d !important;
  color: white !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: 4px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
  height: 36px !important;
  box-sizing: border-box !important;
  flex: none !important;
  min-width: auto !important;
  margin-left: 0 !important;
  white-space: nowrap !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.code-quality-dashboard .filter-section .reset-filters-button:hover {
  background: #5a6268 !important;
}

/* Additional specificity to override any remaining conflicts */
.code-quality-dashboard .filter-section .filters .filter-group .filter-select,
.code-quality-dashboard .filter-section .filters .filter-group .search-input {
  border-radius: 4px !important;
  border: 1px solid #dee2e6 !important;
  background: white !important;
  color: #495057 !important;
  font-size: 14px !important;
  height: 36px !important;
  padding: 8px 12px !important;
  box-sizing: border-box !important;
}

.code-quality-dashboard .filter-section .filters .filter-group .filter-select {
  padding-right: 32px !important;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right 8px center !important;
  background-size: 16px !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
}

/* Main Dashboard Content */
.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  padding: 24px;
}

.content-left,
.content-right {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Chart Containers */
.chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  overflow: hidden; /* Ensure content doesn't overflow */
  min-height: 200px; /* Ensure consistent height */
  display: flex;
  flex-direction: column;
}

.chart-container h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.chart-container canvas {
  max-height: 300px;
}

/* API Versions Chart */
.api-versions-chart {
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.api-bar {
  display: flex;
  height: 50px;
  border-radius: 6px;
  overflow: hidden;
  margin-top: 10px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.api-segment {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 11px;
  font-weight: 600;
  position: relative;
  padding: 0 4px;
  text-align: center;
  line-height: 1.2;
}

.api-segment.obsolete {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.api-segment.deprecated {
  background: linear-gradient(135deg, #ffc107, #ffa000);
}

.api-segment.current {
  background: linear-gradient(135deg, #51D59C, #45c492);
}

.api-segment .api-label {
  font-size: 9px;
  line-height: 1.1;
  text-align: center;
  margin-bottom: 2px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.api-segment .count {
  font-size: 16px;
  font-weight: 700;
  margin-top: 2px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.api-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #2c3e50;
  font-weight: 500;
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.legend-color.obsolete {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.legend-color.deprecated {
  background: linear-gradient(135deg, #ffc107, #ffa000);
}

.legend-color.current {
  background: linear-gradient(135deg, #51D59C, #45c492);
}

/* Rules List */
.rules-list {
  height: 575px; /* Fixed height to match API Versions component *5
  overflow-y: auto;
  padding-right: 8px;
  margin-right: -8px; /* Compensate for padding */
  border-radius: 4px;
}

.rules-list::-webkit-scrollbar {
  width: 6px;
}

.rules-list::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 3px;
}

.rules-list::-webkit-scrollbar-thumb {
  background: #51D59C;
  border-radius: 3px;
}

.rules-list::-webkit-scrollbar-thumb:hover {
  background: #45c492;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 0;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s;
  margin-right: 8px; /* Add margin to prevent overlap with scrollbar */
}

.rule-item:hover {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding-left: 8px;
  padding-right: 8px;
  margin-left: -8px;
  margin-right: 0px; /* Reset margin on hover */
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-name {
  flex: 1;
  font-size: 12px;
  color: #2c3e50;
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 500;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px; /* Limit width to prevent overflow */
}

.rule-bar {
  width: 120px; /* Reduced width to fit better */
  height: 8px;
  background: #f1f3f4;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.rule-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.rule-count {
  font-size: 12px;
  font-weight: 600;
  color: #2c3e50;
  min-width: 25px; /* Reduced min-width */
  text-align: right;
  flex-shrink: 0;
}

/* Metrics Section */
.metrics-section {
  padding: 0 24px 24px 24px;
}

.metrics-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.metrics-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.metrics-table {
  width: 100%;
  border-collapse: collapse;
}

.metrics-table th {
  background: #f8f9fa;
  padding: 12px 16px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #e1e5e9;
}

.metrics-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  font-size: 14px;
  color: #2c3e50;
  text-align: right;
}

.metrics-table td:first-child {
  text-align: left;
  font-weight: 500;
}

.metrics-table tr.grand-total {
  background: #f8f9fa;
  font-weight: 600;
}

.metrics-table tr.grand-total td {
  border-top: 2px solid #e1e5e9;
}

/* Issues Section */
.issues-section {
  padding: 0 24px 24px 24px;
}

.issues-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.issues-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.issues-info {
  font-size: 14px;
  color: #6c757d;
}

.issues-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Issues Table */
.issues-table {
  width: 100%;
  border-collapse: collapse;
}

.issues-table th {
  background: #f8f9fa;
  padding: 12px 16px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #e1e5e9;
}

.issues-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  font-size: 14px;
  color: #2c3e50;
}

.issue-row {
  cursor: pointer;
  transition: background-color 0.2s;
}

.issue-row:hover {
  background: #f8f9fa;
}

.issue-row.expanded {
  background: #e8f5e8;
  border-left: 3px solid #51D59C;
}

.file-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.icon-arrow {
  width: 0;
  height: 0;
  border-left: 6px solid #6c757d;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  transition: transform 0.2s;
}

.icon-arrow.expanded {
  transform: rotate(90deg);
}

.file-type {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 500;
}

.issue-count {
  font-weight: 600;
  text-align: center;
}

/* Issue Details */
.issue-details {
  background: #f8f9fa;
}

.issue-details-content {
  padding: 16px;
}

.issue-details-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.issue-details-table th {
  background: #f1f3f4;
  padding: 8px 12px;
  text-align: left;
  font-size: 11px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.issue-details-table td {
  padding: 8px 12px;
  border-bottom: 1px solid #f1f3f4;
  font-size: 12px;
  color: #2c3e50;
}

.issue-detail-row:hover {
  background: #f8f9fa;
}

.line-number {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #51D59C;
  text-align: center;
}

.issue-category {
  text-align: center;
}

.category-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-badge.security {
  background: #ff6b6b;
  color: white;
}

.category-badge.performance {
  background: #ffeaa7;
  color: #2c3e50;
}

.category-badge.best-practices {
  background: #dda0dd;
  color: white;
}

.category-badge.code-style {
  background: #96ceb4;
  color: white;
}

.category-badge.design {
  background: #4ecdc4;
  color: white;
}

.category-badge.documentation {
  background: #ff9800;
  color: white;
}

.category-badge.error-prone {
  background: #45b7d1;
  color: white;
}

.category-badge.code-quality {
  background: #98d8c8;
  color: #2c3e50;
}

.rule-name {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 11px;
  color: #6c757d;
}

.issue-description {
  max-width: 300px;
  line-height: 1.4;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  padding: 15px 0;
  border-top: 1px solid #e9ecef;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.pagination-button {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: #fff;
  color: #495057;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.pagination-button:disabled {
  color: #adb5bd;
  cursor: not-allowed;
  background: #f8f9fa;
}

.pagination-info {
  font-size: 14px;
  color: #6c757d;
  white-space: nowrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6c757d;
  white-space: nowrap;
}

.page-size-select {
  padding: 6px 8px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: #fff;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
}

/* Loading and Error States */
.code-quality-dashboard.loading,
.code-quality-dashboard.error,
.code-quality-dashboard.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 40px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #51D59C;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon,
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.code-quality-dashboard.empty h3 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.code-quality-dashboard.empty p {
  margin: 8px 0;
  font-size: 14px;
  color: #6c757d;
  max-width: 600px;
  line-height: 1.5;
}

.code-quality-dashboard.empty p:last-of-type {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  text-align: left;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.retry-button {
  background: #51D59C;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 16px;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #45c492;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-content {
    flex-direction: column;
    gap: 24px;
  }
  
  .rule-name {
    max-width: 120px; /* Smaller on medium screens */
  }
  
  .rule-bar {
    width: 100px; /* Smaller on medium screens */
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .dashboard-content {
    gap: 16px;
  }
  
  .rules-list {
    height: 150px; /* Fixed height on mobile */
  }
  
  .rule-name {
    max-width: 100px; /* Much smaller on mobile */
    font-size: 11px;
  }
  
  .rule-bar {
    width: 80px; /* Much smaller on mobile */
  }
  
  .rule-count {
    min-width: 20px;
    font-size: 11px;
  }
  
  .issues-table {
    font-size: 12px;
  }
  
  .issues-table th,
  .issues-table td {
    padding: 8px 12px;
  }
  
  .metrics-table {
    font-size: 12px;
  }
  
  .metrics-table th,
  .metrics-table td {
    padding: 8px 12px;
  }
  
  /* Pagination responsive styles */
  .pagination {
    flex-direction: column;
    gap: 15px;
    padding: 12px 0;
  }
  
  .pagination-controls {
    gap: 10px;
  }
  
  .pagination-button {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .pagination-info {
    font-size: 13px;
  }
  
  .page-size-selector {
    font-size: 13px;
    gap: 6px;
  }
  
  .page-size-select {
    padding: 4px 6px;
    font-size: 13px;
  }
} 

/* Empty Chart State */
.empty-chart-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: #6c757d;
}

.empty-chart-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-chart-state p {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #495057;
}

.empty-chart-subtitle {
  font-size: 14px !important;
  color: #6c757d !important;
  margin-top: 8px !important;
  font-weight: 400 !important;
} 

/* Empty Issues State */
.empty-issues-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
}

.empty-issues-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-issues-state h4 {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #495057;
}

.empty-issues-state p {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #6c757d;
}

.empty-issues-subtitle {
  font-size: 14px !important;
  color: #adb5bd !important;
  margin-top: 8px !important;
  font-weight: 400 !important;
} 

/* Code Editor Feature */
.view-code-button {
  background: #51D59C;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-code-button:hover {
  background: #45B7D1;
  transform: translateY(-1px);
}

.view-code-button:active {
  transform: translateY(0);
}

.loading-spinner {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.code-editor-row {
  background: #f8f9fa;
}

.code-editor-container {
  margin: 10px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.code-editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #2c3e50;
  color: white;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.code-file-name {
  font-weight: 600;
  color: #51D59C;
}

.code-line-number {
  color: #FF6B6B;
  font-weight: 500;
}

.close-code-button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.close-code-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.code-editor-content {
  max-height: 400px;
  overflow-y: auto;
  background: #1e1e1e;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.code-line {
  display: flex;
  padding: 2px 16px;
  border-bottom: 1px solid #2d2d2d;
  transition: background-color 0.2s ease;
}

.code-line:hover {
  background: #2d2d2d;
}

.code-line.issue-line {
  background: rgba(255, 107, 107, 0.1);
  border-left: 4px solid #FF6B6B;
  padding-left: 12px;
}

.code-line.issue-line:hover {
  background: rgba(255, 107, 107, 0.15);
}

.code-line .line-number {
  min-width: 50px;
  color: #6c757d;
  font-size: 12px;
  text-align: right;
  margin-right: 16px;
  user-select: none;
}

.code-line.issue-line .line-number {
  color: #FF6B6B;
  font-weight: 600;
}

.code-line .line-content {
  color: #d4d4d4;
  flex: 1;
  white-space: pre-wrap;
  word-break: break-word;
}

.code-line.issue-line .line-content {
  color: #ffffff;
  font-weight: 500;
}

/* Issue Action Column */
.issue-action {
  width: 80px;
  text-align: center;
}

/* Responsive adjustments for code editor */
@media (max-width: 768px) {
  .code-editor-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .code-editor-content {
    font-size: 12px;
  }
  
  .code-line {
    padding: 2px 12px;
  }
  
  .code-line .line-number {
    min-width: 40px;
    margin-right: 12px;
  }
} 