.auth-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.auth-loading-container {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
  max-width: 80%;
  width: 400px;
}

.auth-loading-spinner {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 5px solid rgba(81, 213, 156, 0.2);
  border-radius: 50%;
  border-top-color: #51D59C; /* Green */
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.auth-loading-container h3 {
  margin: 0.5rem 0;
  font-size: 1.2rem;
  color: #333;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
}

.auth-loading-container p {
  margin: 0.5rem 0;
  font-size: 1rem;
  color: #666;
  font-family: 'Lato', sans-serif;
}

.auth-loading-progress {
  width: 100%;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  margin: 1rem 0;
  overflow: hidden;
}

.auth-loading-progress-bar {
  height: 100%;
  background-color: #51D59C; /* Green */
  width: 0%;
  animation: progress 3s ease-in-out forwards;
}

@keyframes progress {
  0% { width: 0%; }
  20% { width: 30%; }
  50% { width: 70%; }
  80% { width: 90%; }
  100% { width: 95%; }
}

/* Debug section */
.auth-debug-section {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  text-align: center;
}

.auth-debug-section p {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 10px;
}

.auth-debug-button {
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.auth-debug-button:hover {
  background-color: #e0e0e0;
}
