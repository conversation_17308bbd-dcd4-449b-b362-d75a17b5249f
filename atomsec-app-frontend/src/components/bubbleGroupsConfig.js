// Bubble group config for Salesforce permissions analysis
// Edit this file to change bubble groupings and their mapped settings

const bubbleGroups = [
  {
    title: 'Administrative & System Permissions',
    settings: [
      { permission: 'ManageCases', label: 'Manage Cases' },
      { permission: 'CustomizeApplication', label: 'Customize Application' },
      { permission: 'ViewSetup', label: 'View Setup' },
      { permission: 'ManageLeads', label: 'Manage Leads' },
      { permission: 'TransferAnyLead', label: 'Transfer Any Lead' },
      { permission: 'EditHtmlTemplates', label: 'Edit HTML Templates' },
      { permission: 'ManageEncryptionKeys', label: 'Manage Encryption Keys' },
      { permission: 'ApiUserOnly', label: 'API User Only' },
      { permission: 'BulkApiHardDelete', label: 'Bulk API Hard Delete' },
      { permission: 'ManageEmailClientConfig', label: 'Manage Email Client Config' },
      { permission: 'ManageDataIntegrations', label: 'Manage Data Integrations' },
      { permission: 'ManageDataCategories', label: 'Manage Data Categories' },
      { permission: 'ApiEnabled', label: 'API Enabled' },
      { permission: 'EditCaseComments', label: 'Edit Case Comments' },
      { permission: 'ManageFilesAndAttachments', label: 'Manage Files and Attachments' },
      { permission: 'ManageRemoteAccess', label: 'Manage Remote Access' },
    ],
  },
  {
    title: 'Data & Content Management',
    settings: [
      { permission: 'ExportReport', label: 'Export Report' },
      { permission: 'DataExport', label: 'Data Export' },
      { permission: 'EditPublicTemplates', label: 'Edit Public Templates' },
      { permission: 'ModifyAllData', label: 'Modify All Data' },
      { permission: 'EditReadonlyFields', label: 'Edit Readonly Fields' },
      { permission: 'RunReports', label: 'Run Reports' },
      { permission: 'ViewAllData', label: 'View All Data' },
      { permission: 'EditPublicDocuments', label: 'Edit Public Documents' },
      { permission: 'ViewEncryptedData', label: 'View Encrypted Data' },
      { permission: 'ViewContent', label: 'View Content' },
      { permission: 'ViewDataCategories', label: 'View Data Categories' },
      { permission: 'QueryAllFiles', label: 'Query All Files' },
      { permission: 'PrivacyDataAccess', label: 'Privacy Data Access' },
    ],
  },
  {
    title: 'User & Record Access Control',
    settings: [
      { permission: 'ManageUsers', label: 'Manage Users' },
      { permission: 'PasswordNeverExpires', label: 'Password Never Expires' },
      { permission: 'ChatterOwnGroups', label: 'Chatter Own Groups' },
      { permission: 'ManageRoles', label: 'Manage Roles' },
      { permission: 'ManageSharing', label: 'Manage Sharing' },
      { permission: 'ResetPasswords', label: 'Reset Passwords' },
      { permission: 'ManagePasswordPolicies', label: 'Manage Password Policies' },
      { permission: 'ViewAllUsers', label: 'View All Users' },
      { permission: 'ViewAllProfiles', label: 'View All Profiles' },
    ],
  },
];

export default bubbleGroups;
