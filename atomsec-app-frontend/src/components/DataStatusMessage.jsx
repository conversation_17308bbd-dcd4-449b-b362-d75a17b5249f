import React, { useState, useEffect } from 'react';
import './DataStatusMessage.css';

/**
 * Component to display data status messages and refresh button
 *
 * @param {Object} props - Component props
 * @param {string} props.status - Data status ('pending', 'available', 'error', 'timeout', 'empty')
 * @param {string} props.message - Message to display
 * @param {Function} props.onRefresh - Function to call when refresh button is clicked
 * @param {boolean} props.isRefreshing - Whether data is currently being refreshed
 * @param {number} props.timeoutThreshold - Time in milliseconds before showing timeout message (default: 30000)
 * @returns {JSX.Element} - Rendered component
 */
const DataStatusMessage = ({
  status,
  message,
  onRefresh,
  isRefreshing,
  timeoutThreshold = 30000
}) => {
  const [showTimeout, setShowTimeout] = useState(false);

  // Set up timeout for long-running operations
  useEffect(() => {
    let timeoutId = null;

    if (status === 'pending') {
      timeoutId = setTimeout(() => {
        setShowTimeout(true);
      }, timeoutThreshold);
    } else {
      setShowTimeout(false);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [status, timeoutThreshold]);

  if (status === 'available') {
    return null; // Don't show anything if data is available
  }

  // Determine if this is a timeout message from the message content
  const isTimeoutMessage = message && message.toLowerCase().includes('taking longer');
  // Use the timeout state or the message content to determine if we should show timeout UI
  const isTimeout = showTimeout || isTimeoutMessage;
  const statusClass = isTimeout ? 'timeout' : status;

  return (
    <div className={`data-status-container ${statusClass}`}>
      <div className="data-status-content">
        {(status === 'pending' || isTimeout) && (
          <div className="data-status-icon">
            <div className="loading-spinner"></div>
          </div>
        )}
        {status === 'error' && !isTimeout && (
          <div className="data-status-icon error-icon">
            ⚠️
          </div>
        )}
        {status === 'empty' && (
          <div className="data-status-icon empty-icon">
            📊
          </div>
        )}
        <div className="data-status-message">
          <h3 className="status-title">
            {status === 'pending'
              ? 'Fetching Data...'
              : status === 'error'
                ? 'Error Fetching Data'
                : status === 'empty'
                  ? 'No Data Available'
                  : isTimeout
                    ? 'Operation Taking Longer Than Expected'
                    : 'Status Update'
            }
          </h3>
          <p>
            {message ||
              (status === 'pending'
                ? 'The results will be available shortly.'
                : status === 'error'
                  ? message || 'An error occurred while fetching data.'
                  : status === 'empty'
                    ? 'No data is available. Use the Rescan button at the top to fetch data.'
                    : 'An unknown status occurred.'
              )
            }
          </p>
          {isTimeout && (
            <p className="timeout-note">
              <small>Note: This operation is taking longer than expected. You can continue to wait or use the Rescan button at the top to try again.</small>
            </p>
          )}
          {/* Refresh button removed - use the global Rescan button instead */}
        </div>
      </div>
    </div>
  );
};

export default DataStatusMessage;
