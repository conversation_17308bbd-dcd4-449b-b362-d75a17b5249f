.accounts-container {
  animation: fadeIn var(--transition-normal);
  padding: var(--spacing-lg);
}

/* Page Header */
.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-header h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.page-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
}

.filter-badge {
  display: inline-flex;
  align-items: center;
  background-color: #E6F7F1;
  color: #51D59C;
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 14px;
  margin-top: 10px;
  font-weight: 500;
}

.clear-filter-btn {
  background: none;
  border: none;
  color: #51D59C;
  font-size: 18px;
  cursor: pointer;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.clear-filter-btn:hover {
  background-color: rgba(81, 213, 156, 0.2);
}

/* Action Bar */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  background-color: var(--color-bg-light);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
}

.search-filter-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.search-container {
  position: relative;
  width: 300px;
}

.search-input {
  padding-left: 40px;
  height: 40px;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-gray-300);
  background-color: var(--color-bg-main);
  transition: all var(--transition-fast);
  width: 100%;
}

.search-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 112, 243, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-500);
}

.view-toggle {
  display: flex;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.view-toggle-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background: none;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  transition: all var(--transition-fast);
}

.view-toggle-btn.active {
  background-color: var(--color-primary);
  color: white;
}

.add-account-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background-color: var(--color-primary);
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.add-account-btn:hover {
  background-color: #0062d9;
}

.btn-icon {
  font-size: var(--font-size-md);
  line-height: 1;
}

/* Accounts Grid */
.accounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-lg);
}

.account-card {
  background-color: var(--color-bg-main);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  cursor: pointer;
  border: 1px solid var(--color-gray-200);
}

.account-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.account-card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-gray-200);
}

.account-name-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.account-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0;
  color: var(--color-text-primary);
}

.account-type {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  background-color: var(--color-gray-100);
  padding: 2px 8px;
  border-radius: var(--border-radius-full);
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-left: var(--spacing-sm);
}

.status-indicator.active {
  background-color: var(--color-success);
}

.status-indicator.inactive {
  background-color: var(--color-gray-400);
}

/* Status text badge */
.status-text {
  display: inline-block;
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-text.active {
  background-color: rgba(0, 200, 83, 0.1);
  color: var(--color-success);
}

.status-text.inactive {
  background-color: rgba(255, 59, 48, 0.1);
  color: var(--color-danger);
}

.account-card-body {
  padding: var(--spacing-md);
}

.health-score-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.health-score-circle {
  position: relative;
  text-align: center;
}

.circle {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-md);
  color: white;
}

.circle.high {
  background-color: var(--color-success);
}

.circle.medium {
  background-color: var(--color-warning);
}

.circle.low {
  background-color: var(--color-danger);
}

.score {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-bold);
}

.score-label {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin-top: var(--spacing-xs);
}

.health-score-info {
  flex: 1;
}

.health-score-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.last-scan {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.account-card-footer {
  padding: var(--spacing-sm);
  border-top: 1px solid var(--color-gray-200);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.btn-icon-only {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-gray-200);
  background-color: transparent;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-icon-only:hover {
  background-color: var(--color-gray-100);
  color: var(--color-primary);
}

/* Account Details Modal */
.account-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn var(--transition-normal);
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  background-color: var(--color-bg-main);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: 1001;
}

.modal-header {
  padding: var(--spacing-lg);
  background-color: var(--color-primary);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.modal-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color var(--transition-fast);
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: var(--spacing-lg);
}

.account-summary {
  display: flex;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--color-gray-200);
}

.account-info {
  flex: 1;
}

.info-item {
  margin-bottom: var(--spacing-md);
}

.info-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.info-value {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  font-size: var(--font-size-md);
}

.account-tabs {
  display: flex;
  border-bottom: 1px solid var(--color-gray-200);
  margin-bottom: var(--spacing-lg);
}

.tab-btn {
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  transition: all var(--transition-fast);
}

.tab-btn.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

.tab-content {
  padding: var(--spacing-md) 0;
}

.tab-content h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.security-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.metric-card {
  background-color: var(--color-bg-light);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  text-align: center;
}

.metric-card h4 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.metric-score {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-full);
  display: inline-block;
}

.metric-score.high {
  background-color: rgba(0, 200, 83, 0.1);
  color: var(--color-success);
}

.metric-score.medium {
  background-color: rgba(255, 180, 0, 0.1);
  color: var(--color-warning);
}

.metric-score.low {
  background-color: rgba(255, 59, 48, 0.1);
  color: var(--color-danger);
}

.modal-footer {
  padding: var(--spacing-lg);
  display: flex;
  gap: var(--spacing-md);
  border-top: 1px solid var(--color-gray-200);
  justify-content: flex-end;
}

.health-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius-full);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-xs);
}

.health-badge.high {
  background-color: var(--color-success);
  color: white;
}

.health-badge.medium {
  background-color: var(--color-warning);
  color: white;
}

.health-badge.low {
  background-color: var(--color-danger);
  color: white;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-badge.active {
  background-color: rgba(0, 200, 83, 0.1);
  color: var(--color-success);
}

.status-badge.inactive {
  background-color: rgba(255, 59, 48, 0.1);
  color: var(--color-danger);
}

/* List View */
.accounts-list {
  background-color: var(--color-bg-main);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  border: 1px solid var(--color-gray-200);
}

.accounts-table {
  width: 100%;
  border-collapse: collapse;
}

.accounts-table th {
  text-align: left;
  padding: var(--spacing-md);
  background-color: var(--color-bg-light);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  border-bottom: 1px solid var(--color-gray-200);
}

.accounts-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-gray-200);
  font-size: var(--font-size-sm);
}

.accounts-table tr {
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.accounts-table tr:hover {
  background-color: var(--color-bg-light);
}

.account-name-cell {
  font-weight: var(--font-weight-medium);
}

.account-name-with-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.actions-cell {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

/* Loading and Error States */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--color-primary);
  font-size: var(--font-size-lg);
  background-color: var(--color-bg-light);
  border-radius: var(--border-radius-lg);
  margin-top: var(--spacing-lg);
}

.error-alert {
  background-color: rgba(255, 59, 48, 0.1);
  color: var(--color-danger);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-lg);
  border: 1px solid rgba(255, 59, 48, 0.2);
}

.no-accounts {
  grid-column: 1 / -1;
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--color-text-secondary);
  background-color: var(--color-bg-light);
  border-radius: var(--border-radius-lg);
  border: 1px dashed var(--color-gray-300);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive */
@media (max-width: 992px) {
  .account-summary {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .security-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .search-filter-container {
    flex-direction: column;
    width: 100%;
  }

  .search-container {
    width: 100%;
  }

  .view-toggle {
    align-self: flex-start;
  }

  .accounts-grid {
    grid-template-columns: 1fr;
  }

  .modal-footer {
    flex-direction: column;
  }

  .accounts-table th:nth-child(4),
  .accounts-table td:nth-child(4) {
    display: none; /* Hide Last Scan column on mobile */
  }

  .security-metrics {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .accounts-table th:nth-child(2),
  .accounts-table td:nth-child(2) {
    display: none; /* Hide Environment column on small mobile */
  }

  .account-card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .account-type {
    margin-top: var(--spacing-xs);
  }
}
