import React, { useEffect, useState } from 'react';
import './AuthLoadingOverlay.css';

const AuthLoadingOverlay = ({ isVisible }) => {
  const [showDebug, setShowDebug] = useState(false);

  // Simple effect to show debug button after a delay
  useEffect(() => {
    let debugTimer;

    if (isVisible) {
      // Show debug button after 15 seconds
      debugTimer = setTimeout(() => {
        setShowDebug(true);
      }, 15000);
    }

    return () => {
      clearTimeout(debugTimer);
    };
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div className="auth-loading-overlay">
      <div className="auth-loading-container">
        <div className="auth-loading-spinner"></div>
        <h3>Authenticating with Microsoft</h3>
        <p>Please wait while we complete the authentication process...</p>
        <div className="auth-loading-progress">
          <div className="auth-loading-progress-bar"></div>
        </div>
        <p><strong>Do not close this window or refresh the page</strong></p>

        {showDebug && (
          <div className="auth-debug-section">
            <p>Authentication taking too long? Try these options:</p>
            <button
              className="auth-debug-button"
              onClick={() => {
                // Clear all auth state and reload
                localStorage.removeItem('authInProgress');
                window.location.href = '/login';
              }}
            >
              Cancel and Return to Login
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthLoadingOverlay;
