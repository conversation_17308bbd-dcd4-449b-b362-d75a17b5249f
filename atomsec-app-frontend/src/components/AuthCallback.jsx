import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const AuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { setUser, setIsAuthenticated } = useAuth();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log('AuthCallback: Processing authentication callback...');
        
        // Get tokens from URL parameters
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const email = searchParams.get('email');
        const name = searchParams.get('name');

        console.log('AuthCallback: Tokens received:', { 
          hasAccessToken: !!accessToken, 
          hasRefreshToken: !!refreshToken, 
          email, 
          name 
        });

        if (accessToken && email) {
          console.log('AuthCallback: Valid tokens found, storing in localStorage...');
          
          // Clear logout flag since we found valid tokens
          localStorage.removeItem('logoutInProgress');
          
          // Store tokens in localStorage
          localStorage.setItem('accessToken', accessToken);
          localStorage.setItem('refreshToken', refreshToken || '');
          localStorage.setItem('userEmail', email);
          localStorage.setItem('userName', name || '');

          // Update auth context
          const userData = {
            accessToken,
            email,
            name: name || '',
            roles: [],
            isAdmin: false
          };
          
          console.log('AuthCallback: Setting user data:', userData);
          setUser(userData);
          setIsAuthenticated(true);

          // Get redirect path or default to tools
          const redirectPath = localStorage.getItem('loginRedirectPath') || '/tools';
          localStorage.removeItem('loginRedirectPath');
          
          console.log('AuthCallback: Redirecting to:', redirectPath);
          
          // Navigate to the intended destination
          navigate(redirectPath, { replace: true });
        } else {
          console.error('AuthCallback: Missing authentication tokens');
          navigate('/login?error=auth_failed', { replace: true });
        }
      } catch (error) {
        console.error('AuthCallback: Error handling auth callback:', error);
        navigate('/login?error=auth_failed', { replace: true });
      }
    };

    handleAuthCallback();
  }, [searchParams, navigate, setUser, setIsAuthenticated]);

  return (
    <div className="loading-indicator">
      <div className="loading-spinner"></div>
      <p>Completing authentication...</p>
    </div>
  );
};

export default AuthCallback;
