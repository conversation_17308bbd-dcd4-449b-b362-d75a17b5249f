import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import './IntegrationsTable.css';

/**
 * IntegrationsTable component for displaying a list of integrations
 * Updated to match the Figma design
 *
 * @param {Object} props - Component props
 * @param {Array} props.integrations - Array of integration objects
 * @param {Function} props.onRescan - Callback function when rescan button is clicked
 * @param {Function} props.onSelectOrg - Callback function when an organization is selected
 * @param {Object} props.selectedOrg - Currently selected organization
 */
const IntegrationsTable = ({ integrations, onRescan, onSelectOrg, selectedOrg }) => {
  const navigate = useNavigate();
  // Function to format date in relative time format
  const formatRelativeDate = (dateString) => {
    if (!dateString) return 'Not scanned yet';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';

      // For dates less than 7 days ago, use relative time
      const now = new Date();
      const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

      if (diffInDays < 7) {
        // Format without "about" prefix
        const formatted = formatDistanceToNow(date, { addSuffix: true });
        return formatted.replace('about ', '');
      } else {
        // For older dates, use the date format
        return date.toLocaleDateString();
      }
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  return (
    <div className="integrations-table-container">
      <table className="integrations-table">
        <thead>
          <tr>
            <th>Score</th>
            <th>Org Name</th>
            <th>Environment</th>
            <th>Last Scanned</th>
            <th>Status</th>
            <th>Links</th>
          </tr>
        </thead>
        <tbody>
          {integrations.map((integration, index) => {
            // Get normalized properties to handle different response formats
            const name = integration.name || integration.Name || '';
            const lastScan = integration.lastScan || integration.LastScan || '';
            const healthScore = integration.healthScore || integration.HealthScore || '0';
            const isActive = integration.isActive || integration.IsActive || false;
            const environment = integration.environment || integration.Environment || 'Production';

            // Format the date using relative time
            const formattedDate = formatRelativeDate(lastScan);

            // Parse health score as integer
            const scoreInt = parseInt(healthScore, 10);

            // Check if this integration is the selected one
            const isSelected = selectedOrg &&
              ((selectedOrg.id && integration.id && selectedOrg.id === integration.id) ||
               (selectedOrg.Id && integration.Id && selectedOrg.Id === integration.Id) ||
               (selectedOrg.integrationId && integration.integrationId && selectedOrg.integrationId === integration.integrationId) ||
               (selectedOrg.IntegrationId && integration.IntegrationId && selectedOrg.IntegrationId === integration.IntegrationId));

            return (
              <tr
                key={index}
                className={isSelected ? 'selected-row' : ''}
                onClick={() => onSelectOrg && onSelectOrg(integration)}
              >
                <td>
                  <div className="table-score-container">
                    <div className="score-ring">
                      <div className="score-ring-base"></div>
                      <div
                        className="score-ring-progress"
                        style={{
                          transform: `rotate(${scoreInt <= 50 ? 0 : (scoreInt - 50) * 3.6}deg)`,
                          clip: scoreInt <= 50
                            ? 'rect(0, 16px, 32px, 0)'
                            : 'rect(0, 32px, 32px, 16px)'
                        }}
                      ></div>
                      <div className="score-text">{scoreInt}%</div>
                    </div>
                  </div>
                </td>
                <td className="org-name">{name}</td>
                <td>{environment}</td>
                <td>{formattedDate}</td>
                <td>
                  <span className="status-badge">
                    {isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>
                  <div className="actions-cell">
                    <button
                      className="rescan-button"
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent row click
                        onRescan(integration);
                      }}
                    >
                      <img src="/assets/dashboard/refresh.svg" alt="Refresh" className="icon" />
                      <span>Re-Scan</span>
                    </button>
                    <button
                      className="view-details-button"
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent row click
                        // Get the integration ID
                        const integrationId = integration.id || integration.Id || integration.RowKey;
                        if (integrationId) {
                          // Navigate to the integration details page using the integration ID
                          navigate(`/integration/${integrationId}`);
                        }
                      }}
                    >
                      <span>View Details</span>
                    </button>
                  </div>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default IntegrationsTable;
