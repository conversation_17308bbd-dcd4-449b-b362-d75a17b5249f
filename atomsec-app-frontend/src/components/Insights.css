.insights-container {
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.main-content-card {
  background: #FFFFFF;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
}

.header-content h1 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 32px;
  line-height: 1.6;
  color: #020A07;
  margin: 0;
}

.header-content p {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #393E3C;
  margin: 0;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.filter-label {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #393E3C;
}

.filter-select {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  width: 280px;
}

.reset-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 24px;
  border: 1px solid #51D59C;
  border-radius: 4px;
  background: transparent;
  color: #020A07;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.6;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  border-color: #cccccc;
}

.reset-btn .spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.charts-container {
  display: flex;
  gap: 19px;
  margin-bottom: 24px;
}

.chart-card {
  flex: 1;
  background: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 12px 24px;
}

.chart-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 1.6;
  color: #000000;
  margin-bottom: 16px;
}

.details-section {
  margin-top: 24px;
}

.details-header h2 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 20px;
  line-height: 1.6;
  color: #020A07;
  margin: 0;
}

.details-header p {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #393E3C;
  margin: 0;
}

.table-container {
  margin-top: 16px;
}

.table-header {
  display: flex;
  background: #F1FCF7;
  border-bottom: 1px solid #B8D8CB;
}

.table-header-cell {
  padding: 14px 8px 14px 16px;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.6;
  color: #1D2433;
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #B8D8CB;
}

.table-cell {
  padding: 14px 8px 14px 16px;
  font-family: 'Lato', sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #1D2433;
}

.risk-type-cell {
  width: 160px;
}

.setting-cell {
  width: 320px;
}

.setting-group-cell {
  flex: 1;
}

.org-value-cell,
.standard-value-cell {
  width: 160px;
}

.reports-btn {
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.reports-btn:hover {
  background-color: #3DC488;
}

.config-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.config-btn:hover {
  background-color: #5a6268;
}

.btn-icon {
  font-size: 18px;
}

.page-title {
  font-size: 28px;
  margin-bottom: 8px;
  color: #020A07;
}

.page-description {
  font-size: 16px;
  color: #666;
}

.insights-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eaeaea;
}

.filter-btn {
  padding: 8px 16px;
  border-radius: 20px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #495057;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover {
  background-color: #e9ecef;
}

.filter-btn.active {
  background-color: #51D59C;
  color: #020A07;
  border-color: #51D59C;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(81, 213, 156, 0.3);
  transform: translateY(-2px);
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  position: relative;
  scroll-margin-top: 20px;
}

.insight-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid transparent;
}

.insight-card.severity-high {
  border-left-color: #FF3B30;
}

.insight-card.severity-medium {
  border-left-color: #FF9500;
}

.insight-card.severity-low {
  border-left-color: #34C759;
}

.insight-card.severity-info {
  border-left-color: #007AFF;
}

.insight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.insight-title {
  font-size: 18px;
  color: #020A07;
  margin: 0;
}

.insight-severity {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
}

.insight-severity.high {
  background-color: #FFEEEE;
  color: #FF3B30;
}

.insight-severity.medium {
  background-color: #FFF5E6;
  color: #FF9500;
}

.insight-severity.low {
  background-color: #E6F9ED;
  color: #34C759;
}

.insight-severity.info {
  background-color: #E6F9ED;
  color: #51D59C;
}

.insight-description {
  font-size: 14px;
  color: #495057;
  margin-bottom: 15px;
  line-height: 1.5;
}

.insight-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 15px;
}

.insight-action-btn {
  padding: 8px 16px;
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.insight-action-btn:hover {
  background-color: #3DC488;
}

.no-insights {
  padding: 30px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 18px;
  color: #666;
}

.error-message {
  background-color: #fff0f0;
  color: #FF3B30;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 300px;
}

.error-state h2 {
  font-size: 24px;
  color: #020A07;
  margin-bottom: 16px;
}

.error-state p {
  font-size: 16px;
  color: #6c757d;
  margin-bottom: 24px;
  max-width: 500px;
}

.error-actions, .empty-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 300px;
}

.empty-state h2 {
  font-size: 24px;
  color: #020A07;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  color: #6c757d;
  margin-bottom: 24px;
  max-width: 500px;
}

.btn-primary {
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #3DC488;
}

.btn-secondary {
  background-color: #6c757d;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

/* Security Health Check Styles */
.security-health-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
  margin-top: 20px;
}

.security-health-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.security-health-card h3 {
  font-size: 20px;
  color: #020A07;
  margin-top: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eaeaea;
}

.security-health-html {
  max-height: 600px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.security-health-html table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

.security-health-html th,
.security-health-html td {
  padding: 8px 12px;
  text-align: left;
  border: 1px solid #dee2e6;
}

.security-health-html th {
  background-color: #e9ecef;
  font-weight: 600;
}

.security-health-html tr:nth-child(even) {
  background-color: #f2f2f2;
}

.security-health-html tr:hover {
  background-color: #e9ecef;
}

.no-data-message {
  padding: 40px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
}

.no-data-message p {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 1.5;
}

.connect-btn {
  background-color: #51D59C;
  color: #020A07;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.connect-btn:hover {
  background-color: #3DC488;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .insights-filters {
    overflow-x: auto;
    padding-bottom: 10px;
    flex-wrap: nowrap;
  }

  .page-title {
    font-size: 24px;
  }

  .insight-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .insight-severity {
    align-self: flex-start;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .security-health-html {
    max-height: 400px;
  }
}
