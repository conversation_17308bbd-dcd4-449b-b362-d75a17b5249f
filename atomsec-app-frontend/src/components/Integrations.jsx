import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>Plus, FiSearch, FiFilter, FiGrid, FiList, FiX } from "react-icons/fi";
import IntegrationCard from "./IntegrationCard";
import { fetchIntegrations } from "../api";
import "./Integrations.css";

const Integrations = () => {
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState("grid");
  const [searchTerm, setSearchTerm] = useState("");
  const [filterOpen, setFilterOpen] = useState(false);
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [integrations, setIntegrations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeScanId, setActiveScanId] = useState(null);

  useEffect(() => {
    const getIntegrations = async () => {
      try {
        setLoading(true);
        // Include inactive integrations in the fetch
        const data = await fetchIntegrations({ include_inactive: true });
        console.log('Fetched integrations (including inactive):', data);

        // Check if data is an object with integrations property
        if (data && data.data && data.data.integrations) {
          setIntegrations(data.data.integrations);
        } else if (data && data.integrations) {
          setIntegrations(data.integrations);
        } else if (Array.isArray(data)) {
          // Handle case where data is directly an array
          setIntegrations(data);
        } else {
          // Handle unexpected data format
          console.error('Unexpected data format from fetchIntegrations:', data);
          setIntegrations([]);
          setError('Received invalid data format from server.');
        }

        setError(null);
      } catch (err) {
        console.error('Error fetching integrations:', err);
        setError('Failed to load integrations. Please try again.');
        setIntegrations([]); // Set empty array on error
      } finally {
        setLoading(false);
      }
    };

    getIntegrations();
  }, []);

  // Handle scan button click
  const handleScan = (integration, isFixingConnection = false) => {
    const integrationId = getIntegrationId(integration);

    if (isFixingConnection) {
      // Navigate to the scan wizard to fix the connection
      navigate('/scan', {
        state: {
          integrationToFix: integration,
          isFixingConnection: true
        }
      });
    } else {
      // Regular scan flow
      setActiveScanId(integrationId);
      // Navigate to scan page or perform scan action
      navigate(`/scan/${integrationId}`);
    }
  };

  // Handle close button click
  const handleClose = () => {
    setActiveScanId(null);
    // Navigate back to integrations page
    navigate('/integrations');
  };

  // Handle integration deletion
  const handleIntegrationDeleted = (integrationId) => {
    // Remove the deleted integration from the state
    setIntegrations(prevIntegrations =>
      prevIntegrations.filter(integration =>
        (integration.id !== integrationId && integration.Id !== integrationId)
      )
    );
  };

  // Use activeScanId to conditionally render UI elements if needed
  // For example, you could highlight the card that's being scanned
  const isScanning = (integrationId) => {
    // Handle different ID property names
    return integrationId === activeScanId;
  };

  // Helper function to get the ID from an integration object
  const getIntegrationId = (integration) => {
    return integration.id || integration.Id || integration._id || '';
  };

  // Filter integrations based on search term and category
  const filteredIntegrations = integrations.filter(integration => {
    // Get normalized properties to handle different response formats
    const name = (integration.name || integration.Name || '').toLowerCase();
    const description = (integration.description || integration.Description || '').toLowerCase();
    const type = (integration.type || integration.Type || '').toLowerCase();
    const searchTermLower = searchTerm.toLowerCase();

    const matchesSearch =
      name.includes(searchTermLower) ||
      description.includes(searchTermLower) ||
      type.includes(searchTermLower);

    const matchesCategory = categoryFilter === 'all' || type === categoryFilter.toLowerCase();

    return matchesSearch && matchesCategory;
  });

  // Available categories for filtering
  const categories = [
    { id: 'all', name: 'All Categories' },
    { id: 'salesforce', name: 'Salesforce' },
    { id: 'microsoft', name: 'Microsoft' },
    { id: 'aws', name: 'AWS' }
  ];

  return (
    <div className="integrations-container">
      <div className="page-header">
        <h1 className="page-title">All Integrations</h1>
        <button
          className="new-connection-btn"
          onClick={() => navigate("/scan")}
        >
          <FiPlus className="btn-icon" /> Create New Integration
        </button>
      </div>

      <div className="integrations-toolbar">
        <div className="search-container">
          <FiSearch className="search-icon" />
          <input
            type="text"
            className="search-input"
            placeholder="Search here"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="toolbar-actions">
          <div className="filter-container">
            <button
              className="filter-btn"
              onClick={() => setFilterOpen(!filterOpen)}
            >
              <FiFilter /> Filter
            </button>
            {filterOpen && (
              <div className="filter-dropdown">
                {categories.map(category => (
                  <button
                    key={category.id}
                    className={`filter-option ${categoryFilter === category.id ? 'active' : ''}`}
                    onClick={() => {
                      setCategoryFilter(category.id);
                      setFilterOpen(false);
                    }}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            )}
          </div>
          <div className="view-toggle">
            <button
              className={`view-btn ${viewMode === "grid" ? "active" : ""}`}
              onClick={() => setViewMode("grid")}
              aria-label="Grid view"
            >
              <FiGrid />
            </button>
            <button
              className={`view-btn ${viewMode === "list" ? "active" : ""}`}
              onClick={() => setViewMode("list")}
              aria-label="List view"
            >
              <FiList />
            </button>
          </div>
        </div>
      </div>

      <div className="main-content-card">
        {loading ? (
          <div className="loading-state">
            <p>Loading integrations...</p>
          </div>
        ) : error ? (
          <div className="error-state">
            <p>{error}</p>
            <button className="retry-btn" onClick={() => window.location.reload()}>
              Retry
            </button>
          </div>
        ) : filteredIntegrations.length > 0 ? (
          <div className={viewMode === "grid" ? "integrations-grid" : "integrations-list"}>
            {filteredIntegrations.map((integration) => (
              <IntegrationCard
                key={getIntegrationId(integration)}
                integration={integration}
                viewMode={viewMode}
                onScan={handleScan}
                onClose={handleClose}
                isActive={isScanning(getIntegrationId(integration))}
                onIntegrationDeleted={handleIntegrationDeleted}
              />
            ))}
          </div>
        ) : searchTerm || categoryFilter !== 'all' ? (
          <div className="no-results">
            <h3>No matching integrations found</h3>
            <p>Try adjusting your search or filter criteria</p>
            {(searchTerm || categoryFilter !== 'all') && (
              <button
                className="clear-filters-btn"
                onClick={() => {
                  setSearchTerm('');
                  setCategoryFilter('all');
                }}
              >
                <FiX /> Clear filters
              </button>
            )}
          </div>
        ) : (
          <div className="empty-state">
          <div className="empty-state-illustration">
            {/* <svg width="472" height="280" viewBox="0 0 472 280" fill="none" xmlns="http://www.w3.org/2000/svg"> */}
            <svg width="248" height="223" viewBox="0 0 248 223" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M246.327 175.258H1.72754V175.368H246.327V175.258Z" fill="#F1F4F9"/>
<path d="M221.818 182.338H214.528V182.448H221.818V182.338Z" fill="#F1F4F9"/>
<path d="M163.759 183.532H159.507V183.642H163.759V183.532Z" fill="#F1F4F9"/>
<path d="M212.463 178.254H195.736V178.364H212.463V178.254Z" fill="#F1F4F9"/>
<path d="M55.3582 180.523H34.2285V180.633H55.3582V180.523Z" fill="#F1F4F9"/>
<path d="M67.3342 180.523H59.7139V180.633H67.3342V180.523Z" fill="#F1F4F9"/>
<path d="M126.353 179.047H80.5273V179.157H126.353V179.047Z" fill="#F1F4F9"/>
<path d="M117.674 155.64H23.2095C22.4691 155.639 21.7593 155.374 21.2358 154.903C20.7122 154.433 20.4178 153.794 20.417 153.129V33.7298C20.418 33.0642 20.7126 32.4262 21.2361 31.9556C21.7596 31.4851 22.4693 31.2204 23.2095 31.2197H117.674C118.414 31.2207 119.124 31.4854 119.647 31.956C120.17 32.4265 120.465 33.0644 120.466 33.7298V153.127C120.465 153.793 120.171 154.431 119.648 154.903C119.124 155.374 118.415 155.639 117.674 155.64ZM23.2105 31.3298C22.5043 31.3317 21.8276 31.5847 21.3282 32.0338C20.8289 32.4828 20.5474 33.0913 20.5453 33.7263V153.127C20.5474 153.762 20.8289 154.37 21.3282 154.819C21.8276 155.268 22.5043 155.522 23.2105 155.523H117.675C118.381 155.522 119.058 155.268 119.557 154.819C120.057 154.37 120.338 153.762 120.34 153.127V33.7298C120.338 33.0948 120.057 32.4863 119.557 32.0373C119.058 31.5883 118.381 31.3352 117.675 31.3333L23.2105 31.3298Z" fill="#F1F4F9"/>
<path d="M223.487 155.64H129.022C128.282 155.639 127.572 155.374 127.049 154.903C126.526 154.432 126.231 153.794 126.23 153.129V33.7298C126.231 33.0643 126.526 32.4263 127.049 31.9557C127.572 31.4851 128.282 31.2204 129.022 31.2197H223.487C224.227 31.2204 224.936 31.4851 225.46 31.9556C225.983 32.4262 226.278 33.0642 226.279 33.7298V153.127C226.279 153.793 225.985 154.432 225.461 154.903C224.937 155.374 224.227 155.639 223.487 155.64ZM129.022 31.3298C128.316 31.3317 127.639 31.5847 127.14 32.0338C126.64 32.4828 126.359 33.0913 126.357 33.7263V153.127C126.359 153.762 126.64 154.37 127.14 154.819C127.639 155.268 128.316 155.522 129.022 155.523H223.487C224.194 155.523 224.872 155.27 225.372 154.821C225.872 154.372 226.155 153.763 226.157 153.127V33.7298C226.155 33.094 225.872 32.4849 225.372 32.0358C224.872 31.5866 224.194 31.334 223.487 31.3333L129.022 31.3298Z" fill="#F1F4F9"/>
<path d="M106.649 175.257H115.759L115.759 119.009H106.649V175.257Z" fill="#F1F4F9"/>
<path d="M109.855 175.257H106.648V164.57H113.218L109.855 175.257Z" fill="white"/>
<path d="M202.983 175.257H212.093V119.009H202.983V175.257Z" fill="#F1F4F9"/>
<path d="M106.649 172.339H205.781V119.008L106.649 119.008L106.649 172.339Z" fill="white"/>
<path d="M202.578 175.257H205.785V164.57H199.212L202.578 175.257Z" fill="white"/>
<path d="M173.099 123.424V167.541H202.485V123.424H173.099Z" fill="#F1F4F9"/>
<path d="M184.886 124.976H190.7C193.16 124.999 195.541 124.194 197.376 122.72H178.21C180.045 124.194 182.425 124.998 184.886 124.976Z" fill="white"/>
<path d="M141.521 123.424V167.541H170.908V123.424H141.521Z" fill="#F1F4F9"/>
<path d="M153.012 124.976H158.826C161.286 124.998 163.667 124.194 165.501 122.72H146.335C148.17 124.194 150.551 124.999 153.012 124.976Z" fill="white"/>
<path d="M109.944 123.424V167.541H139.331V123.424H109.944Z" fill="#F1F4F9"/>
<path d="M121.434 124.976H127.248C129.709 124.999 132.09 124.194 133.925 122.72H114.758C116.593 124.194 118.974 124.999 121.434 124.976Z" fill="white"/>
<path d="M35.962 175.257H98.1191L98.1191 49.6173H35.962L35.962 175.257Z" fill="white"/>
<path d="M35.9624 175.257H96.8076L96.8076 49.6173H35.9624L35.9624 175.257Z" fill="#F8FDFB"/>
<path d="M93.0996 172.405L93.0996 52.4678H39.6694L39.6694 172.405H93.0996Z" fill="white"/>
<path d="M87.8495 74.7739C87.8495 73.0538 87.4727 71.3506 86.7407 69.7614C86.0086 68.1723 84.9357 66.7284 83.583 65.5121C82.2304 64.2959 80.6246 63.3311 78.8573 62.6728C77.09 62.0146 75.1959 61.6758 73.283 61.6758H59.4874C55.6241 61.6758 51.9191 63.0558 49.1873 65.5121C46.4556 67.9685 44.9209 71.3 44.9209 74.7739H87.8495Z" fill="#F1F4F9"/>
<path d="M87.8496 164.951V136.298H44.921V164.951H87.8496Z" fill="#F8FDFB"/>
<path d="M87.8494 82.8525H68.9141V132.601H87.8494V82.8525Z" fill="#F8FDFB"/>
<path d="M63.8563 82.8525H44.9209V132.601H63.8563V82.8525Z" fill="#F8FDFB"/>
<path d="M51.7879 107.503H44.0324C43.7135 107.502 43.4079 107.388 43.1824 107.185C42.9569 106.982 42.8299 106.708 42.8291 106.421C42.8301 106.134 42.9573 105.86 43.1827 105.657C43.4081 105.454 43.7136 105.34 44.0324 105.339H51.7879C52.1067 105.34 52.4122 105.454 52.6376 105.657C52.8631 105.86 52.9902 106.134 52.9912 106.421C52.9904 106.708 52.8634 106.982 52.6379 107.185C52.4124 107.388 52.1068 107.502 51.7879 107.503Z" fill="#393E3C"/>
<path d="M40.6318 172.405L40.6318 52.4678H39.6704L39.6704 172.405H40.6318Z" fill="white"/>
<path d="M137.436 105.219L203.353 105.219V49.6168L137.436 49.6168V105.219Z" fill="#F1F4F9"/>
<path d="M135.774 105.219L201.69 105.219V49.6168L135.774 49.6168V105.219Z" fill="#F8FDFB"/>
<path d="M131.926 108.438L203.353 108.438V105.219L131.926 105.219V108.438Z" fill="#F1F4F9"/>
<path d="M130.055 108.438L197.803 108.438V105.219L130.055 105.219V108.438Z" fill="#F8FDFB"/>
<path d="M198.708 102.327V52.5098H138.756V102.327H198.708Z" fill="white"/>
<path d="M170.394 102.327V52.5098H167.07V102.327H170.394Z" fill="#F8FDFB"/>
<path d="M140.013 102.327V52.5098H138.756V102.327H140.013Z" fill="#F1F4F9"/>
<path d="M171.649 102.327V52.5098H170.393V102.327H171.649Z" fill="#F1F4F9"/>
<path d="M146.296 106.694C145.084 95.3991 144.24 77.0714 144.895 70.9417C144.907 70.8363 144.943 70.7344 145.002 70.6426C145.06 70.5508 145.14 70.4712 145.235 70.4091C145.362 70.3257 145.512 70.276 145.669 70.2657C145.825 70.2554 145.982 70.2848 146.121 70.3507C146.26 70.4166 146.375 70.5162 146.454 70.6383C146.533 70.7604 146.573 70.8999 146.568 71.0411C146.452 74.0784 146.319 81.6699 147.038 94.7663C147.397 101.286 147.913 105.75 147.559 106.692L146.296 106.694Z" fill="#F1F4F9"/>
<path d="M141.778 80.6611C138.323 77.2146 131.276 74.764 124.092 74.7764C121.272 74.7817 132.673 78.7856 137.418 83.2085C141.209 86.8813 144.331 91.0708 146.666 95.6214C145.75 88.8713 145.229 84.1058 141.778 80.6611Z" fill="#F1F4F9"/>
<path d="M148.62 81.7106C151.483 78.1212 158.103 75.3608 165.263 75.0483C168.073 74.9259 157.38 79.4304 153.389 84.0476C150.205 87.9067 147.786 92.2304 146.236 96.8288C146.026 90.0716 145.754 85.3008 148.62 81.7106Z" fill="#F1F4F9"/>
<path d="M141.623 68.0738C138.7 65.1554 132.73 63.0802 126.647 63.0909C124.259 63.0909 133.913 66.4868 137.927 70.2279C141.139 73.3379 143.782 76.8854 145.759 80.7387C144.987 75.028 144.544 70.9922 141.623 68.0738Z" fill="#F1F4F9"/>
<path d="M147.417 72.3897C149.844 69.3497 155.448 67.0127 161.509 66.7482C163.889 66.6443 154.835 70.4583 151.455 74.3681C148.758 77.6375 146.709 81.3008 145.397 85.1966C145.221 79.4699 144.989 75.4279 147.417 72.3897Z" fill="#F1F4F9"/>
<path d="M146.784 119.009C144.962 119.009 143.21 118.383 141.885 117.258C140.561 116.134 139.765 114.596 139.66 112.961L139.03 103.142H154.537L153.908 112.961C153.803 114.596 153.007 116.134 151.683 117.258C150.358 118.383 148.605 119.009 146.784 119.009Z" fill="#F1F4F9"/>
<path d="M155.542 105.374H138.03L137.528 101.291H156.047L155.542 105.374Z" fill="#F1F4F9"/>
<path d="M124.027 195.124C176.412 195.124 218.878 192.894 218.878 190.143C218.878 187.392 176.412 185.162 124.027 185.162C71.6428 185.162 29.1768 187.392 29.1768 190.143C29.1768 192.894 71.6428 195.124 124.027 195.124Z" fill="#F8FDFB"/>
<path d="M125.94 185.384L121.856 185.797L120.35 177.468L124.434 177.055L125.94 185.384Z" fill="#F9BDBC"/>
<path d="M144.228 186.187H140.149L139.134 177.693H143.212L144.228 186.187Z" fill="#F9BDBC"/>
<path d="M139.813 185.76H144.785C144.866 185.76 144.944 185.785 145.007 185.83C145.071 185.876 145.115 185.939 145.132 186.01L145.938 189.27C145.957 189.35 145.955 189.432 145.934 189.511C145.913 189.59 145.872 189.663 145.814 189.726C145.757 189.789 145.684 189.839 145.602 189.873C145.52 189.907 145.431 189.924 145.341 189.923C143.741 189.898 142.577 189.813 140.559 189.813C139.319 189.813 135.58 189.929 133.869 189.929C132.191 189.929 131.933 188.407 132.634 188.269C135.777 187.648 138.145 186.798 139.149 185.985C139.331 185.839 139.568 185.758 139.813 185.76Z" fill="#020A07"/>
<path d="M121.789 185.168L126.215 184.717C126.296 184.709 126.377 184.725 126.446 184.764C126.514 184.803 126.566 184.861 126.592 184.93L127.802 188.087C127.832 188.164 127.843 188.245 127.833 188.326C127.823 188.407 127.792 188.484 127.744 188.553C127.696 188.621 127.631 188.679 127.554 188.721C127.477 188.764 127.39 188.79 127.3 188.797C125.701 188.936 123.383 189.087 121.378 189.292C119.031 189.531 118.64 190.096 115.873 190.038C114.195 190.003 113.774 188.471 114.478 188.352C117.684 187.792 118.304 187.356 120.851 185.558C121.118 185.352 121.442 185.217 121.789 185.168Z" fill="#020A07"/>
<path d="M125.392 78.4186C124.022 79.4358 122.673 80.3198 121.278 81.2251C119.884 82.1305 118.459 82.971 116.994 83.7867C115.528 84.6024 114.023 85.3844 112.446 86.0944C110.833 86.8456 109.155 87.4756 107.427 87.9779C106.97 88.1093 106.524 88.2175 106.011 88.3329C105.509 88.4308 105.002 88.504 104.491 88.5522C103.572 88.6409 102.707 88.6809 101.846 88.7164C100.128 88.7874 98.4431 88.7856 96.7601 88.7749C93.3941 88.7385 90.0568 88.593 86.7046 88.3267L86.6543 85.5752C89.9462 85.1944 93.253 84.8651 96.5252 84.5341C98.1608 84.3743 99.7925 84.195 101.387 84.0077C102.176 83.911 102.966 83.7974 103.694 83.6758C104.008 83.6261 104.317 83.5606 104.622 83.4796C104.91 83.3909 105.262 83.2897 105.588 83.1716C106.987 82.6609 108.344 82.0636 109.652 81.384C111.005 80.6855 112.359 79.9355 113.69 79.1384C115.02 78.3414 116.345 77.5159 117.658 76.6736C118.971 75.8313 120.282 74.9393 121.514 74.0996L125.392 78.4186Z" fill="#F9BDBC"/>
<path d="M129.379 75.2845C128.792 78.7514 120.674 84.0548 120.674 84.0548L114.851 77.555C117.402 75.1067 120.107 72.792 122.953 70.622C125.573 68.696 130.016 71.5123 129.379 75.2845Z" fill="#51D59C"/>
<path d="M87.7784 85.8661L85.4627 84.3838L85.209 89.0667C85.559 89.0675 85.904 88.9913 86.2137 88.8446C86.5234 88.6979 86.7885 88.4853 86.9857 88.2253L87.7784 85.8661Z" fill="#F9BDBC"/>
<path d="M83.3703 83.0205L82.8965 87.6102L85.2102 89.0667L85.4639 84.3838L83.3703 83.0205Z" fill="#F9BDBC"/>
<path opacity="0.2" d="M120.352 177.471L121.127 181.764L125.213 181.351L124.437 177.058L120.352 177.471Z" fill="#2A292F"/>
<path opacity="0.2" d="M143.213 177.694H139.133L139.658 182.072H143.739L143.213 177.694Z" fill="#2A292F"/>
<path d="M143.273 70.7327C143.576 70.7939 143.863 70.9092 144.117 71.0717C144.37 71.2342 144.585 71.4407 144.749 71.6789C144.912 71.9172 145.021 72.1825 145.069 72.4591C145.116 72.7358 145.102 73.0182 145.027 73.2898C144.142 76.5929 143.475 79.9402 143.029 83.3142C142.643 86.1544 142.412 88.8678 142.268 91.3273C141.929 97.0922 142.07 101.487 141.879 102.976C138.69 102.777 126.671 102.025 120.117 101.614C117.905 86.0417 120.497 76.1043 121.953 72.0667C122.176 71.4375 122.593 70.8783 123.156 70.454C123.719 70.0298 124.404 69.7579 125.132 69.6702C125.967 69.5717 126.966 69.4767 128.02 69.4208C128.389 69.3995 128.763 69.388 129.144 69.3809C132.238 69.4138 135.327 69.607 138.396 69.9596C138.979 70.0217 139.567 70.0989 140.14 70.1815C141.284 70.3537 142.371 70.5516 143.273 70.7327Z" fill="#51D59C"/>
<path d="M138.208 60.54C137.418 63.1797 136.453 68.0472 138.397 69.9644C133.353 73.0151 130.133 77.3101 129.204 76.3772C128.718 75.8891 128.507 70.2396 129.146 69.3839C132.521 68.8798 132.607 66.6599 132.19 64.5439L138.208 60.54Z" fill="#F9BDBC"/>
<path d="M138.007 68.8701C138.007 68.8701 129.787 72.1409 128.697 79.4546C130.962 77.0971 135.963 74.532 135.963 74.532L133.757 73.9622C135.16 73.4834 136.613 73.1319 138.093 72.9131C137.849 71.903 139.154 69.8882 139.154 69.8882L138.007 68.8701Z" fill="#51D59C"/>
<path d="M130.59 68.752C130.59 68.752 131.686 72.9289 128.698 79.4545C128.109 77.1769 127.301 74.9495 126.283 72.7976L128.296 73.2902C128.296 73.2902 128.197 72.4479 126.567 71.3277C127.247 70.4408 128.094 69.6678 129.07 69.044L130.59 68.752Z" fill="#51D59C"/>
<path opacity="0.2" d="M135.755 62.1738L132.193 64.5419C132.299 65.0347 132.356 65.5348 132.364 66.0366C133.647 65.9478 135.483 64.8091 135.711 63.6241C135.823 63.147 135.838 62.6557 135.755 62.1738Z" fill="#2A292F"/>
<path d="M127.763 52.8955C125.774 52.9727 124.676 56.0562 126.695 58.5956C128.713 61.1349 130.443 52.7916 127.763 52.8955Z" fill="#020A07"/>
<path d="M137.572 55.6088C137.808 59.2426 138.151 61.3577 136.372 63.4284C133.697 66.543 128.525 65.4264 127.087 61.9293C125.793 58.7819 125.624 53.3685 129.357 51.509C130.178 51.0951 131.104 50.8768 132.045 50.875C132.987 50.8732 133.913 51.0879 134.736 51.4988C135.559 51.9096 136.252 52.5029 136.748 53.2222C137.245 53.9416 137.528 54.7632 137.572 55.6088Z" fill="#F9BDBC"/>
<path d="M138.165 58.4014C137.085 57.8318 136.141 57.0746 135.389 56.1739C134.637 55.2731 134.092 54.2469 133.786 53.1549C132.601 53.407 126.697 55.8496 124.882 53.1549C123.067 50.4602 125.351 48.8794 128.748 50.5383C127.245 48.1542 128.611 46.9879 133.332 46.885C138.052 46.782 137.417 49.2424 137.417 49.2424C137.417 49.2424 140.991 47.8436 142.254 50.3075C143.718 53.1389 141.017 57.9736 138.165 58.4014Z" fill="#020A07"/>
<path d="M136.609 48.5918C136.653 48.5918 138.397 48.9691 140.407 47.1504C140.046 48.7019 136.925 49.735 136.925 49.735L136.609 48.5918Z" fill="#020A07"/>
<path d="M138.955 53.9076C137.072 53.3226 134.793 55.8301 135.635 58.8576C136.477 61.8852 141.491 54.6957 138.955 53.9076Z" fill="#020A07"/>
<path d="M140.02 58.6311C139.847 59.5335 139.285 60.3378 138.454 60.8679C137.348 61.5619 136.351 60.7684 136.283 59.6199C136.225 58.5868 136.731 56.9802 138.021 56.7432C138.304 56.6957 138.596 56.7125 138.87 56.7921C139.144 56.8718 139.392 57.0117 139.59 57.1993C139.789 57.3869 139.932 57.6161 140.007 57.8662C140.082 58.1163 140.087 58.3792 140.02 58.6311Z" fill="#F9BDBC"/>
<path d="M126.251 142.934C127.231 132.207 134.421 102.511 134.421 102.511L120.114 101.613C120.114 101.613 114.597 131.091 114.175 142.278C113.735 153.91 118.938 179.762 118.938 179.762L125.799 179.06C125.799 179.06 125.267 153.706 126.251 142.934Z" fill="#51D59C"/>
<path opacity="0.5" d="M126.251 142.934C127.231 132.207 134.421 102.511 134.421 102.511L120.114 101.613C120.114 101.613 114.597 131.091 114.175 142.278C113.735 153.91 118.938 179.762 118.938 179.762L125.799 179.06C125.799 179.06 125.267 153.706 126.251 142.934Z" fill="white"/>
<path opacity="0.2" d="M129.031 112.567C126.147 115.883 126.095 127.529 127.211 136.134C128.274 129.927 129.949 122.044 131.417 115.473L129.031 112.567Z" fill="#2A292F"/>
<path d="M127.834 102.097C127.834 102.097 129.378 133.495 131.1 144.128C132.986 155.773 137.774 181.222 137.774 181.222H145.244C145.244 181.222 144.015 157.252 143.029 145.812C141.907 132.781 141.876 102.977 141.876 102.977L127.834 102.097Z" fill="#51D59C"/>
<path opacity="0.5" d="M127.834 102.097C127.834 102.097 129.378 133.495 131.1 144.128C132.986 155.773 137.774 181.222 137.774 181.222H145.244C145.244 181.222 144.015 157.252 143.029 145.812C141.907 132.781 141.876 102.977 141.876 102.977L127.834 102.097Z" fill="white"/>
<path d="M136.876 181.366H145.672V179.003L135.84 178.85L136.876 181.366Z" fill="#51D59C"/>
<path d="M118.639 180.457L126.816 179.623L126.52 177.247L117.362 178.054L118.639 180.457Z" fill="#51D59C"/>
<path d="M131.621 57.2748C131.661 57.5686 131.522 57.8251 131.305 57.8482C131.088 57.8713 130.887 57.652 130.848 57.3582C130.81 57.0645 130.947 56.8079 131.164 56.7849C131.381 56.7618 131.583 56.981 131.621 57.2748Z" fill="#020A07"/>
<path d="M127.931 57.6732C127.97 57.967 127.832 58.2235 127.615 58.2466C127.398 58.2697 127.197 58.0505 127.157 57.7567C127.118 57.4629 127.256 57.2064 127.473 57.1833C127.69 57.1602 127.892 57.383 127.931 57.6732Z" fill="#020A07"/>
<path d="M129.123 57.6934C128.854 58.6331 128.442 59.535 127.899 60.3739C128.142 60.4932 128.41 60.5635 128.685 60.5799C128.961 60.5963 129.237 60.5584 129.495 60.4688L129.123 57.6934Z" fill="#F9BDBC"/>
<path d="M132.626 56.0775C132.592 56.0868 132.555 56.0875 132.52 56.0795C132.485 56.0715 132.454 56.0552 132.428 56.0322C132.265 55.8708 132.062 55.7464 131.835 55.6693C131.608 55.5922 131.364 55.5644 131.123 55.5884C131.072 55.5961 131.021 55.5859 130.978 55.5601C130.936 55.5343 130.907 55.4948 130.896 55.45C130.891 55.4275 130.891 55.4043 130.896 55.3818C130.901 55.3593 130.911 55.338 130.926 55.319C130.94 55.3001 130.958 55.2839 130.98 55.2715C131.001 55.2591 131.025 55.2506 131.05 55.2467C131.355 55.2112 131.664 55.2421 131.953 55.3366C132.241 55.4311 132.5 55.5866 132.708 55.7899C132.743 55.823 132.763 55.867 132.763 55.9128C132.763 55.9587 132.743 56.0027 132.708 56.0358C132.685 56.0556 132.657 56.07 132.626 56.0775Z" fill="#020A07"/>
<path d="M131.207 61.6398C131.673 61.5516 132.108 61.364 132.476 61.0927C132.845 60.8215 133.135 60.4745 133.322 60.0812C133.332 60.0576 133.33 60.0318 133.318 60.0093C133.306 59.9868 133.284 59.9696 133.258 59.9613C133.232 59.9531 133.203 59.9545 133.178 59.9654C133.153 59.9762 133.134 59.9955 133.125 60.019C132.945 60.3808 132.674 60.6996 132.332 60.9497C131.99 61.1998 131.587 61.3741 131.157 61.4587C131.144 61.4614 131.131 61.4665 131.12 61.4736C131.109 61.4807 131.099 61.4897 131.092 61.5001C131.085 61.5105 131.08 61.522 131.078 61.5341C131.075 61.5462 131.076 61.5586 131.079 61.5705C131.082 61.5825 131.087 61.5938 131.095 61.6038C131.103 61.6137 131.113 61.6222 131.125 61.6287C131.136 61.6353 131.149 61.6397 131.163 61.6417C131.176 61.6437 131.19 61.6434 131.203 61.6406L131.207 61.6398Z" fill="#020A07"/>
<path d="M126.195 56.7029C126.156 56.7146 126.114 56.7146 126.075 56.7029C126.027 56.6871 125.988 56.6552 125.966 56.614C125.944 56.5728 125.94 56.5255 125.956 56.4819C126.048 56.2187 126.21 55.9794 126.428 55.7843C126.645 55.5893 126.911 55.4444 127.204 55.3618C127.254 55.3517 127.306 55.3592 127.35 55.3827C127.393 55.4061 127.425 55.4438 127.438 55.4878C127.45 55.5323 127.442 55.5789 127.416 55.618C127.39 55.6572 127.348 55.6859 127.299 55.6982C127.069 55.7673 126.86 55.8846 126.69 56.0404C126.52 56.1963 126.393 56.3862 126.321 56.5947C126.311 56.6202 126.294 56.6433 126.272 56.662C126.25 56.6808 126.224 56.6948 126.195 56.7029Z" fill="#020A07"/>
<path d="M138.47 186.4C138.191 186.42 137.912 186.354 137.68 186.213C137.608 186.157 137.552 186.086 137.518 186.006C137.483 185.926 137.471 185.839 137.483 185.754C137.483 185.704 137.498 185.654 137.527 185.61C137.557 185.567 137.598 185.532 137.649 185.508C138.102 185.299 139.415 186.027 139.564 186.11C139.58 186.119 139.593 186.133 139.6 186.149C139.608 186.164 139.611 186.182 139.608 186.199C139.605 186.216 139.596 186.232 139.583 186.244C139.57 186.257 139.553 186.266 139.535 186.27C139.186 186.349 138.829 186.393 138.47 186.4ZM137.864 185.642C137.821 185.64 137.778 185.647 137.738 185.663C137.718 185.673 137.702 185.688 137.691 185.706C137.68 185.724 137.675 185.744 137.677 185.765C137.668 185.823 137.676 185.882 137.699 185.937C137.722 185.992 137.759 186.041 137.808 186.08C138.264 186.267 138.782 186.291 139.257 186.146C138.831 185.898 138.357 185.726 137.859 185.642H137.864Z" fill="#51D59C"/>
<path d="M139.509 186.272C139.493 186.272 139.477 186.268 139.463 186.261C139.041 186.055 138.225 185.251 138.311 184.841C138.325 184.777 138.363 184.719 138.419 184.678C138.475 184.636 138.545 184.613 138.618 184.613C138.691 184.605 138.766 184.611 138.837 184.63C138.908 184.649 138.973 184.681 139.03 184.725C139.503 185.074 139.601 186.132 139.604 186.176C139.605 186.191 139.602 186.207 139.594 186.221C139.587 186.235 139.575 186.247 139.561 186.256C139.546 186.265 139.528 186.271 139.509 186.272ZM138.686 184.785H138.642C138.516 184.799 138.505 184.851 138.501 184.874C138.449 185.115 138.969 185.722 139.389 186.009C139.361 185.59 139.193 185.187 138.907 184.855C138.845 184.809 138.767 184.784 138.686 184.785Z" fill="#51D59C"/>
<path d="M121.457 185.711L121.442 185.717C120.803 185.901 119.66 186.161 119.171 185.854C119.099 185.809 119.04 185.749 119 185.679C118.959 185.609 118.938 185.531 118.938 185.453C118.934 185.405 118.944 185.357 118.967 185.314C118.99 185.271 119.024 185.234 119.068 185.207C119.527 184.917 121.251 185.487 121.447 185.554C121.463 185.56 121.478 185.571 121.488 185.584C121.499 185.598 121.505 185.613 121.506 185.63C121.507 185.646 121.503 185.662 121.494 185.677C121.486 185.691 121.473 185.703 121.457 185.712V185.711ZM119.204 185.335L119.181 185.347C119.163 185.358 119.149 185.372 119.141 185.39C119.132 185.407 119.129 185.427 119.132 185.445C119.131 185.498 119.144 185.549 119.17 185.596C119.196 185.642 119.234 185.682 119.282 185.712C119.882 185.88 120.53 185.849 121.106 185.623C120.509 185.385 119.854 185.285 119.203 185.335H119.204Z" fill="#51D59C"/>
<path d="M121.457 185.711C121.434 185.721 121.407 185.723 121.382 185.716C120.869 185.569 119.803 184.879 119.823 184.46C119.827 184.362 119.89 184.233 120.15 184.182C120.244 184.163 120.342 184.162 120.436 184.178C120.531 184.194 120.621 184.228 120.701 184.276C121.119 184.636 121.401 185.105 121.507 185.617C121.51 185.631 121.509 185.647 121.504 185.661C121.499 185.676 121.489 185.689 121.477 185.699C121.471 185.704 121.464 185.708 121.457 185.711ZM120.084 184.388C120.018 184.42 120.016 184.457 120.015 184.472C120.001 184.724 120.738 185.276 121.272 185.494C121.173 185.085 120.937 184.713 120.595 184.429C120.537 184.393 120.471 184.368 120.401 184.356C120.332 184.344 120.26 184.345 120.191 184.359C120.154 184.364 120.118 184.374 120.084 184.388Z" fill="#51D59C"/>
<path d="M146.713 76.0226C146.61 77.4552 146.422 78.7892 146.228 80.1668C146.033 81.5443 145.772 82.8943 145.484 84.2568C144.916 87.0395 144.077 89.772 142.976 92.4225L142.492 93.4495L142.369 93.706L142.339 93.7699L142.288 93.8675L142.146 94.1205C141.958 94.4279 141.733 94.7155 141.474 94.9779C141.048 95.4085 140.551 95.7768 139.999 96.0696C139.552 96.3088 139.084 96.5133 138.598 96.6812C136.982 97.2056 135.291 97.5229 133.576 97.6238C130.462 97.833 127.332 97.7279 124.244 97.3105L124.356 94.559L126.513 94.3397C127.237 94.2625 127.957 94.1622 128.672 94.0735C130.101 93.8844 131.514 93.6732 132.861 93.3918C134.108 93.1636 135.319 92.8025 136.47 92.316C136.781 92.1988 137.052 92.0121 137.26 91.7737C137.273 91.7107 137.117 91.9867 137.3 91.5456L137.623 90.6971C138.414 88.2645 139.019 85.7862 139.434 83.2795C139.665 82.0032 139.864 80.7153 140.055 79.4239C140.247 78.1324 140.405 76.8144 140.556 75.5762L146.713 76.0226Z" fill="#F9BDBC"/>
<path d="M147.069 73.3632C149.066 76.3747 147.365 85.8399 147.365 85.8399L136.902 84.1012C136.902 84.1012 136.335 78.6869 138.327 74.5206C140.49 69.9859 144.649 69.7143 147.069 73.3632Z" fill="#51D59C"/>
<path d="M121.426 103.071L115.873 79.8486C115.607 78.8569 114.989 77.9701 114.113 77.3217C113.237 76.6733 112.15 76.2985 111.017 76.2539H89.7422C90.8759 76.2983 91.9628 76.673 92.839 77.3214C93.7151 77.9698 94.3331 78.8568 94.5997 79.8486L100.152 103.071C100.419 104.063 101.036 104.95 101.912 105.598C102.788 106.247 103.875 106.622 105.009 106.666H126.282C125.149 106.622 124.062 106.247 123.186 105.598C122.31 104.95 121.692 104.063 121.426 103.071Z" fill="#020A07"/>
<path opacity="0.7" d="M121.426 103.071L115.873 79.8486C115.607 78.8569 114.989 77.9701 114.113 77.3217C113.237 76.6733 112.15 76.2985 111.017 76.2539H89.7422C90.8759 76.2983 91.9628 76.673 92.839 77.3214C93.7151 77.9698 94.3331 78.8568 94.5997 79.8486L100.152 103.071C100.419 104.063 101.036 104.95 101.912 105.598C102.788 106.247 103.875 106.622 105.009 106.666H126.282C125.149 106.622 124.062 106.247 123.186 105.598C122.31 104.95 121.692 104.063 121.426 103.071Z" fill="#51D59C"/>
<path d="M89.7418 76.2539H111.016C109.926 76.2722 108.882 76.6477 108.079 77.3096C107.276 77.9716 106.769 78.8745 106.655 79.8486L101.136 133.814C101.021 134.788 100.514 135.691 99.7112 136.353C98.9082 137.015 97.864 137.39 96.7749 137.409H83.4965C82.985 137.417 82.4774 137.327 82.0077 137.145C81.538 136.963 81.1169 136.693 80.7728 136.352C80.4287 136.012 80.1694 135.609 80.0123 135.172C79.8553 134.734 79.804 134.271 79.862 133.814L85.3819 79.8486C85.4967 78.8746 86.003 77.9719 86.8059 77.3099C87.6087 76.648 88.6528 76.2725 89.7418 76.2539Z" fill="#51D59C"/>
<path d="M93.1408 133.812C93.0828 134.27 93.134 134.732 93.2911 135.17C93.4482 135.608 93.7075 136.011 94.0516 136.351C94.3957 136.691 94.8167 136.962 95.2865 137.144C95.7562 137.326 96.2638 137.416 96.7753 137.407H75.5015C74.99 137.416 74.4823 137.326 74.0125 137.144C73.5427 136.962 73.1216 136.692 72.7774 136.351C72.4333 136.011 72.174 135.608 72.017 135.17C71.86 134.733 71.8089 134.27 71.867 133.812H93.1408Z" fill="#020A07"/>
<path opacity="0.7" d="M93.1408 133.812C93.0828 134.27 93.134 134.732 93.2911 135.17C93.4482 135.608 93.7075 136.011 94.0516 136.351C94.3957 136.691 94.8167 136.962 95.2865 137.144C95.7562 137.326 96.2638 137.416 96.7753 137.407H75.5015C74.99 137.416 74.4823 137.326 74.0125 137.144C73.5427 136.962 73.1216 136.692 72.7774 136.351C72.4333 136.011 72.174 135.608 72.017 135.17C71.86 134.733 71.8089 134.27 71.867 133.812H93.1408Z" fill="#51D59C"/>
<g opacity="0.5">
<path opacity="0.5" d="M102.968 82.3002H88.5718C88.4607 82.3015 88.3505 82.2816 88.2485 82.2418C88.1466 82.2019 88.0552 82.143 87.9806 82.0689C87.9059 81.9949 87.8496 81.9074 87.8154 81.8123C87.7812 81.7172 87.7699 81.6166 87.7822 81.5173C87.8078 81.3054 87.9182 81.1091 88.093 80.965C88.2677 80.821 88.4948 80.739 88.7318 80.7344H103.126C103.237 80.7331 103.347 80.753 103.449 80.7928C103.551 80.8327 103.642 80.8916 103.717 80.9656C103.791 81.0397 103.848 81.1272 103.882 81.2223C103.916 81.3174 103.928 81.418 103.915 81.5173C103.89 81.7289 103.779 81.925 103.605 82.069C103.431 82.213 103.204 82.2952 102.968 82.3002Z" fill="white"/>
<path opacity="0.5" d="M102.54 86.5345H88.1441C88.033 86.5359 87.9228 86.516 87.8208 86.4761C87.7189 86.4362 87.6275 86.3773 87.5528 86.3033C87.4782 86.2293 87.4218 86.1418 87.3876 86.0467C87.3534 85.9516 87.3421 85.851 87.3544 85.7517C87.38 85.5397 87.4905 85.3434 87.6652 85.1994C87.84 85.0553 88.067 84.9734 88.304 84.9688H102.7C102.811 84.9674 102.921 84.9873 103.023 85.0272C103.125 85.0671 103.216 85.126 103.291 85.2C103.366 85.2741 103.422 85.3615 103.456 85.4566C103.49 85.5517 103.502 85.6523 103.489 85.7517C103.464 85.9636 103.354 86.1601 103.179 86.3041C103.004 86.4482 102.777 86.5301 102.54 86.5345Z" fill="white"/>
<path opacity="0.5" d="M98.5823 90.7699H87.7154C87.6043 90.7712 87.4941 90.7513 87.3921 90.7115C87.2902 90.6716 87.1988 90.6127 87.1241 90.5387C87.0494 90.4646 86.9931 90.3771 86.9589 90.282C86.9247 90.1869 86.9134 90.0864 86.9257 89.987C86.9513 89.7751 87.0618 89.5788 87.2365 89.4347C87.4113 89.2907 87.6383 89.2087 87.8753 89.2042H98.7422C98.8534 89.2028 98.9636 89.2227 99.0655 89.2626C99.1675 89.3024 99.2588 89.3613 99.3335 89.4354C99.4082 89.5094 99.4645 89.5969 99.4987 89.692C99.5329 89.7871 99.5442 89.8877 99.5319 89.987C99.5063 90.1989 99.3958 90.3952 99.2211 90.5393C99.0464 90.6833 98.8193 90.7653 98.5823 90.7699Z" fill="white"/>
<path opacity="0.5" d="M90.9725 95.0033H87.2877C87.1765 95.0046 87.0663 94.9847 86.9644 94.9449C86.8624 94.905 86.7711 94.8461 86.6964 94.7721C86.6217 94.698 86.5654 94.6105 86.5312 94.5154C86.497 94.4203 86.4857 94.3198 86.498 94.2204C86.5234 94.0086 86.6336 93.8123 86.8082 93.6682C86.9828 93.5242 87.2097 93.4422 87.4466 93.4376H91.1324C91.2436 93.4362 91.3538 93.4561 91.4557 93.496C91.5576 93.5358 91.649 93.5947 91.7237 93.6688C91.7984 93.7428 91.8547 93.8303 91.8889 93.9254C91.9231 94.0205 91.9344 94.1211 91.9221 94.2204C91.8965 94.4323 91.786 94.6286 91.6113 94.7727C91.4365 94.9167 91.2095 94.9987 90.9725 95.0033Z" fill="white"/>
<path opacity="0.5" d="M101.255 99.2376H86.8551C86.7439 99.239 86.6337 99.2191 86.5318 99.1792C86.4298 99.1394 86.3385 99.0805 86.2638 99.0064C86.1891 98.9324 86.1328 98.8449 86.0986 98.7498C86.0644 98.6547 86.0531 98.5541 86.0654 98.4548C86.091 98.2429 86.2015 98.0466 86.3762 97.9025C86.5509 97.7585 86.778 97.6765 87.015 97.6719H101.411C101.522 97.6706 101.632 97.6905 101.734 97.7303C101.836 97.7702 101.927 97.8291 102.002 97.9031C102.077 97.9772 102.133 98.0647 102.167 98.1598C102.201 98.2549 102.213 98.3555 102.2 98.4548C102.175 98.6661 102.065 98.8619 101.891 99.0059C101.717 99.1499 101.491 99.2322 101.255 99.2376Z" fill="white"/>
<path opacity="0.5" d="M95.1571 103.473H86.4312C86.3201 103.474 86.2099 103.454 86.1079 103.415C86.006 103.375 85.9146 103.316 85.8399 103.242C85.7653 103.168 85.7089 103.08 85.6747 102.985C85.6405 102.89 85.6292 102.789 85.6416 102.69C85.6669 102.478 85.7772 102.282 85.9518 102.138C86.1263 101.994 86.3533 101.912 86.5901 101.907H95.316C95.4272 101.906 95.5374 101.926 95.6393 101.966C95.7413 102.006 95.8326 102.064 95.9073 102.139C95.982 102.213 96.0383 102.3 96.0725 102.395C96.1067 102.49 96.118 102.591 96.1057 102.69C96.0801 102.902 95.9698 103.098 95.7953 103.242C95.6208 103.386 95.3939 103.468 95.1571 103.473Z" fill="white"/>
<path opacity="0.5" d="M100.398 107.706H86.0025C85.8914 107.708 85.7811 107.688 85.6792 107.648C85.5773 107.608 85.4859 107.549 85.4112 107.475C85.3366 107.401 85.2802 107.314 85.246 107.219C85.2118 107.123 85.2005 107.023 85.2128 106.924C85.2384 106.712 85.3489 106.515 85.5236 106.371C85.6984 106.227 85.9254 106.145 86.1624 106.141H100.559C100.67 106.139 100.781 106.159 100.882 106.199C100.984 106.239 101.076 106.298 101.15 106.372C101.225 106.446 101.281 106.533 101.316 106.629C101.35 106.724 101.361 106.824 101.349 106.924C101.323 107.136 101.213 107.332 101.038 107.476C100.863 107.62 100.635 107.702 100.398 107.706Z" fill="white"/>
<path opacity="0.5" d="M96.4417 111.941H85.5748C85.4636 111.942 85.3534 111.922 85.2515 111.882C85.1495 111.843 85.0582 111.784 84.9835 111.71C84.9088 111.636 84.8525 111.548 84.8183 111.453C84.7841 111.358 84.7728 111.257 84.7851 111.158C84.8107 110.946 84.9212 110.75 85.0959 110.606C85.2706 110.462 85.4977 110.38 85.7347 110.375H96.6016C96.7127 110.374 96.8229 110.394 96.9249 110.433C97.0268 110.473 97.1182 110.532 97.1929 110.606C97.2675 110.68 97.3239 110.768 97.3581 110.863C97.3923 110.958 97.4036 111.059 97.3912 111.158C97.3657 111.37 97.2552 111.566 97.0805 111.71C96.9057 111.854 96.6787 111.936 96.4417 111.941Z" fill="white"/>
<path opacity="0.5" d="M88.8319 116.175H85.1441C85.033 116.177 84.9228 116.157 84.8208 116.117C84.7189 116.077 84.6275 116.018 84.5528 115.944C84.4782 115.87 84.4218 115.782 84.3876 115.687C84.3534 115.592 84.3421 115.492 84.3544 115.392C84.38 115.18 84.4905 114.984 84.6652 114.84C84.84 114.696 85.067 114.614 85.304 114.609H88.9938C89.1049 114.608 89.2151 114.628 89.3171 114.668C89.419 114.708 89.5104 114.767 89.5851 114.841C89.6598 114.915 89.7161 115.002 89.7503 115.097C89.7845 115.192 89.7958 115.293 89.7834 115.392C89.7578 115.605 89.6471 115.801 89.4719 115.945C89.2968 116.089 89.0692 116.171 88.8319 116.175Z" fill="white"/>
<path opacity="0.5" d="M99.1131 120.411H84.7174C84.6062 120.412 84.496 120.392 84.3941 120.352C84.2921 120.312 84.2008 120.253 84.1261 120.179C84.0514 120.105 83.9951 120.018 83.9609 119.923C83.9267 119.828 83.9154 119.727 83.9277 119.628C83.9533 119.416 84.0638 119.219 84.2385 119.075C84.4132 118.931 84.6403 118.849 84.8773 118.845H99.273C99.3842 118.843 99.4944 118.863 99.5963 118.903C99.6983 118.943 99.7896 119.002 99.8643 119.076C99.939 119.15 99.9953 119.238 100.03 119.333C100.064 119.428 100.075 119.528 100.063 119.628C100.037 119.84 99.9266 120.036 99.7519 120.18C99.5771 120.324 99.3501 120.406 99.1131 120.411Z" fill="white"/>
<path opacity="0.5" d="M93.0165 124.644H84.2906C84.1794 124.645 84.0692 124.625 83.9673 124.586C83.8654 124.546 83.774 124.487 83.6993 124.413C83.6246 124.339 83.5683 124.251 83.5341 124.156C83.4999 124.061 83.4886 123.96 83.5009 123.861C83.5263 123.649 83.6367 123.453 83.8115 123.309C83.9863 123.164 84.2135 123.083 84.4505 123.078H93.1754C93.2866 123.077 93.3968 123.097 93.4987 123.137C93.6007 123.176 93.692 123.235 93.7667 123.309C93.8414 123.383 93.8977 123.471 93.9319 123.566C93.9661 123.661 93.9774 123.762 93.9651 123.861C93.9395 124.073 93.8292 124.269 93.6547 124.413C93.4801 124.557 93.2533 124.639 93.0165 124.644Z" fill="white"/>
<path opacity="0.5" d="M98.2566 128.879H83.8609C83.7498 128.881 83.6395 128.861 83.5376 128.821C83.4357 128.781 83.3443 128.722 83.2696 128.648C83.195 128.574 83.1386 128.487 83.1044 128.391C83.0702 128.296 83.0589 128.196 83.0712 128.096C83.0968 127.884 83.2073 127.688 83.382 127.544C83.5568 127.4 83.7838 127.318 84.0208 127.314H98.4146C98.5257 127.312 98.6359 127.332 98.7379 127.372C98.8398 127.412 98.9312 127.471 99.0059 127.545C99.0806 127.619 99.1369 127.706 99.1711 127.801C99.2053 127.896 99.2166 127.997 99.2042 128.096C99.1787 128.308 99.0685 128.504 98.8942 128.648C98.7199 128.792 98.4933 128.874 98.2566 128.879Z" fill="white"/>
</g>
<path d="M125.61 94.8587L122.485 92.417L121.021 96.9117C121.021 96.9117 124.195 98.4597 125.295 97.0387L125.61 94.8587Z" fill="#F9BDBC"/>
<path d="M119.08 92.2412L118.063 96.0081L121.017 96.9135L122.482 92.4178L119.08 92.2412Z" fill="#F9BDBC"/>


            </svg>
          </div>
          <div className="empty-state-content">
            <h2>Let's get started</h2>
            <p>
              Begin by adding your project organizations to the system by clicking
              the "Create New Integration" button located in the top-right corner.
            </p>
          </div>
        </div>
        )}
      </div>
    </div>
  );
};

export default Integrations;
