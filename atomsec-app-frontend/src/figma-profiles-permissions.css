@import './settings-risk-table.css';

/* Main content area */
.figma-main-content {
  padding: 32px;
  background: #fff;
  min-width: 1136px;
}

/* Card styling */
.figma-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.1);
  padding: 40px 32px 32px 32px;
  margin-bottom: 24px;
  overflow: hidden;
}

.figma-card .ag-root-wrapper {
  border-radius: 16px;
  overflow: hidden;
}

/* AG Grid overrides for Figma look */
.ag-theme-quartz {
  --ag-background-color: #fff;
  --ag-header-background-color: #fff;
  --ag-header-foreground-color: #222;
  --ag-row-hover-color: #f5f7fa;
  --ag-font-family: 'Inter', sans-serif;
  --ag-border-radius: 12px;
  --ag-card-shadow: 0px 4px 20px 0px rgba(0,0,0,0.1);
  font-size: 15px;
  border: 1.5px solid #E0E0E0;
  border-radius: 12px;
  overflow: visible;
  box-sizing: border-box;
}

.ag-theme-quartz .ag-header-cell-label {
  font-weight: 600;
  font-size: 16px;
}

.ag-theme-quartz .ag-cell {
  border-radius: 4px;
}

.ag-theme-quartz .ag-root-wrapper {
  border-radius: 12px;
  overflow: visible;
  border: none;
}

.bubble-chart-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 24px;
  justify-content: left;
  align-items: stretch;
  width: 100%;
  min-height: 0;
  margin-left: auto;
  margin-right: auto;
}

.bubble-chart-card, .assignment-chart-card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 4px 20px 0px rgba(0,0,0,0.1);
  padding: 24px;
  min-width: 320px;
  min-height: 320px;
  margin-bottom: 24px;
}

.bubble-chart-card-inner {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.bubble-chart-title {
  font-weight: 900;
  font-size: 20px;
  margin-bottom: 12px;
  text-align: center;
}

.bubble-chart-svg {
  width: 100%;
  max-width: 100%;
  height: 360px !important;
  display: flex;
  justify-content: center;
  overflow: visible !important;
}

.bubble-chart-svg text {
  white-space: pre;
  text-anchor: middle;
  dominant-baseline: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.highlight-row {
  background: #e6f7f0 !important;
}

.bubble-chart-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.figma-dropdown {
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 8px 32px 8px 16px;
  font-size: 16px;
  color: #393E3C;
  background: #fff;
  appearance: none;
  outline: none;
  min-width: 180px;
  margin-right: 8px;
}

.settings-risk-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 4px 20px 0px rgba(0,0,0,0.1);
  font-size: 15px;
  margin-top: 0;
  overflow: hidden;
}
.settings-risk-table th, .settings-risk-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #F0F0F0;
  text-align: left;
}
.settings-risk-table th {
  background: #F8FDFB;
  color: #393E3C;
  font-weight: 600;
}
.settings-risk-table tr:last-child td {
  border-bottom: none;
}
.settings-risk-table td a {
  color: #51D59C;
  text-decoration: underline;
  font-weight: 500;
  transition: color 0.2s;
}
.settings-risk-table td a:hover {
  color: #393E3C;
}

@media (max-width: 1000px) {
  .bubble-chart-card {
    max-width: 100%;
    min-width: 90vw;
    flex: 1 1 100%;
  }
  .bubble-chart-row {
    flex-direction: column;
    gap: 16px;
  }
}

.bubble-chart-section,
.assignment-chart-section {
  border: 1.5px solid #E0E0E0;
  border-radius: 12px;
  overflow: visible !important;
}

.figma-picklist-row {
  position: relative;
  display: flex;
  align-items: center;
  gap: 4px;
  width: fit-content;
  height: fit-content;
  margin-bottom: 8px;
}

.figma-dropdown-container {
  position: relative;
  display: inline-block;
}

.figma-dropdown-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  pointer-events: none;
  z-index: 1;
}

.figma-section-padding {
  padding-left: 32px;
  padding-right: 32px;
}

.assignment-donut-card {
  background: #fff;
  border: 1.5px solid #E0E0E0;
  border-radius: 12px;
  padding: 32px;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0;
  min-width: 320px;
  min-height: unset;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  box-shadow: 0 1.5px 4px rgba(0,0,0,0.06);
  margin: 25px 0 25px 0;
}
.assignment-donut-chart-container {
  flex: 1;
  min-height: 260px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.assignment-donut-title {
  font-weight: 700;
  font-size: 28px;
  color: #181C1A;
  margin-bottom: 24px;
  text-align: left;
} 