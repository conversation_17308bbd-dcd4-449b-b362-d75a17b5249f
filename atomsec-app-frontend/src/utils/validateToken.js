const jwt = require('jsonwebtoken');
const jwksClient = require('jwks-rsa');

// Configure the JWKS client
const client = jwksClient({
  jwksUri: `https://login.microsoftonline.com/41b676db-bf6f-46ae-a354-a83a1362533f/discovery/v2.0/keys`,
});

// Function to get the signing key
function getSigningKey(kid) {
  return new Promise((resolve, reject) => {
    client.getSigningKey(kid, (err, key) => {
      if (err) {
        reject(err);
        return;
      }
      const signingKey = key.getPublicKey() || key.rsaPublicKey;
      resolve(signingKey);
    });
  });
}

// Middleware to validate the token
async function validateToken(req, context) {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return { isValid: false, error: 'No authorization header' };
  }

  const token = authHeader.replace('Bearer ', '');

  try {
    const decodedToken = jwt.decode(token, { complete: true });

    if (!decodedToken) {
      return { isValid: false, error: 'Invalid token format' };
    }

    const kid = decodedToken.header.kid;
    const signingKey = await getSigningKey(kid);

    const verifiedToken = jwt.verify(token, signingKey, {
      algorithms: ['RS256'],
      audience: process.env.REACT_APP_AZURE_CLIENT_ID,
    });

    return { isValid: true, user: verifiedToken };
  } catch (error) {
    context.log.error('Token validation error:', error);
    return { isValid: false, error: error.message };
  }
}

module.exports = validateToken;