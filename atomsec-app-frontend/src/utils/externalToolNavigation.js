/**
 * Utility functions for external tool navigation with SSO integration
 */

/**
 * Navigate to an external tool with SSO authentication
 * @param {Object} tool - Tool configuration object
 * @param {string} tool.externalUrl - The external tool URL
 * @param {string} tool.name - Tool name for user feedback
 * @param {Object} user - Current user object from AuthContext
 * @param {Function} getAccessToken - Function to get current access token
 * @returns {Promise<void>}
 */
export const navigateToExternalTool = async (tool, user, getAccessToken) => {
  try {
    // Get the current access token
    const accessToken = await getAccessToken();
    
    if (!accessToken) {
      throw new Error('No access token available');
    }

    // Prepare the external URL with authentication parameters
    const url = new URL(tool.externalUrl);
    
    // Add authentication token as URL parameter (for tools that support it)
    url.searchParams.append('token', accessToken);
    url.searchParams.append('user_email', user?.email || '');
    url.searchParams.append('user_name', user?.name || '');
    
    // Add callback URL for seamless return
    const callbackUrl = `${window.location.origin}/tools`;
    url.searchParams.append('callback_url', callbackUrl);
    
    // Add timestamp to prevent caching issues
    url.searchParams.append('t', Date.now());
    
    // Add source identifier
    url.searchParams.append('source', 'atomsec-platform');
    
    // Open in new tab with security attributes
    const newTab = window.open(
      url.toString(), 
      '_blank', 
      'noopener,noreferrer'
    );
    
    if (!newTab) {
      throw new Error('Unable to open new tab. Please allow popups for this site.');
    }
    
    return newTab;
  } catch (error) {
    console.error('Error preparing external tool navigation:', error);
    throw error;
  }
};

/**
 * Validate external tool configuration
 * @param {Object} tool - Tool configuration object
 * @returns {boolean}
 */
export const validateToolConfig = (tool) => {
  if (!tool.externalUrl) {
    console.error('External tool URL is required');
    return false;
  }
  
  try {
    new URL(tool.externalUrl);
    return true;
  } catch (error) {
    console.error('Invalid external tool URL:', tool.externalUrl);
    return false;
  }
};

/**
 * Get tool-specific configuration based on environment
 * @param {string} toolId - Tool identifier
 * @returns {Object} Tool configuration
 */
export const getToolConfig = (toolId) => {
  const isProduction = window.location.hostname !== 'localhost';
  
  const toolConfigs = {
    'open-search': {
      development: {
        url: 'http://localhost:8080',
        authMethod: 'token'
      },
      production: {
        url: process.env.REACT_APP_OPEN_SEARCH_URL || 'https://opensearch.atomsec.com',
        authMethod: 'token'
      }
    },
    'grey-log': {
      development: {
        url: 'http://localhost:9000',
        authMethod: 'token'
      },
      production: {
        url: process.env.REACT_APP_GREY_LOG_URL || 'https://greylog.atomsec.com',
        authMethod: 'token'
      }
    }
  };
  
  const config = toolConfigs[toolId];
  if (!config) {
    throw new Error(`Unknown tool: ${toolId}`);
  }
  
  return isProduction ? config.production : config.development;
};

/**
 * Handle external tool authentication errors
 * @param {Error} error - Error object
 * @param {string} toolName - Name of the tool
 * @returns {string} User-friendly error message
 */
export const handleExternalToolError = (error, toolName) => {
  if (error.message.includes('Unable to open new tab')) {
    return `Unable to open ${toolName} in a new tab. Please allow popups for this site and try again.`;
  }
  
  if (error.message.includes('access token')) {
    return `Authentication failed. Please log in again to access ${toolName}.`;
  }
  
  if (error.message.includes('network')) {
    return `Unable to connect to ${toolName}. Please check your internet connection and try again.`;
  }
  
  return `Failed to access ${toolName}. Please try again or contact support.`;
}; 