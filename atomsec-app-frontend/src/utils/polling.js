/**
 * Utility functions for polling data
 */

/**
 * Creates a polling function that will call the provided fetch function at regular intervals
 * until the condition is met or the maximum number of attempts is reached.
 * 
 * @param {Function} fetchFn - The function to call to fetch data
 * @param {Function} conditionFn - Function that takes the response and returns true when polling should stop
 * @param {Object} options - Configuration options
 * @param {number} options.interval - Polling interval in milliseconds (default: 5000)
 * @param {number} options.maxAttempts - Maximum number of polling attempts (default: 12, which is 1 minute with default interval)
 * @param {Function} options.onSuccess - Function to call when polling succeeds
 * @param {Function} options.onError - Function to call when polling fails
 * @param {Function} options.onTimeout - Function to call when polling times out
 * @returns {Object} - An object with start and stop methods
 */
export const createPoller = (fetchFn, conditionFn, options = {}) => {
  const {
    interval = 5000,
    maxAttempts = 12,
    onSuccess = () => {},
    onError = () => {},
    onTimeout = () => {}
  } = options;

  let attempts = 0;
  let timeoutId = null;
  let isStopped = false;

  const poll = async () => {
    if (isStopped) return;

    attempts++;
    
    try {
      const response = await fetchFn();
      
      // Check if the condition is met
      if (conditionFn(response)) {
        onSuccess(response);
        stop();
        return;
      }
      
      // Check if we've reached the maximum number of attempts
      if (attempts >= maxAttempts) {
        onTimeout();
        stop();
        return;
      }
      
      // Schedule the next poll
      timeoutId = setTimeout(poll, interval);
    } catch (error) {
      onError(error);
      stop();
    }
  };

  const start = () => {
    attempts = 0;
    isStopped = false;
    poll();
    return { stop };
  };

  const stop = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
    isStopped = true;
  };

  return {
    start,
    stop
  };
};

/**
 * Creates a poller specifically for handling dataStatus responses
 * 
 * @param {Function} fetchFn - The function to call to fetch data
 * @param {Object} options - Configuration options
 * @param {Function} options.onPending - Function to call when data is pending
 * @param {Function} options.onAvailable - Function to call when data is available
 * @param {Function} options.onError - Function to call when there's an error
 * @param {Function} options.onEmpty - Function to call when data is empty
 * @param {Function} options.onTimeout - Function to call when polling times out
 * @returns {Object} - An object with start and stop methods
 */
export const createDataStatusPoller = (fetchFn, options = {}) => {
  const {
    onPending = () => {},
    onAvailable = () => {},
    onError = () => {},
    onEmpty = () => {},
    onTimeout = () => {}
  } = options;

  return createPoller(
    fetchFn,
    (response) => {
      const dataStatus = response?.dataStatus;
      
      if (dataStatus === 'available' || !dataStatus) {
        onAvailable(response);
        return true;
      } else if (dataStatus === 'error') {
        onError(response);
        return true;
      } else if (dataStatus === 'empty') {
        onEmpty(response);
        return true;
      } else if (dataStatus === 'pending') {
        onPending(response);
        return false; // Continue polling
      }
      
      return false;
    },
    {
      onTimeout,
      ...options
    }
  );
};
