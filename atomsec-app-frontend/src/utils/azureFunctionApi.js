import { API_CONFIG } from '../config';
import api from '../api';

/**
 * Utility functions for calling Azure Function APIs with authentication
 */

/**
 * Call an Azure Function API with authentication
 * @param {string} endpoint - The API endpoint to call (from API_CONFIG.endpoints)
 * @param {string} method - The HTTP method to use (GET, POST, PUT, DELETE)
 * @param {object} data - The data to send in the request body (for POST, PUT)
 * @param {object} params - The query parameters to include in the request
 * @returns {Promise} - The API response
 */
export const callProtectedApi = async (endpoint, method = 'GET', data = null, params = null) => {
  try {
    // The api instance will automatically include the token in the Authorization header
    // thanks to the request interceptor in api.js

    let response;

    switch (method.toUpperCase()) {
      case 'GET':
        response = await api.get(endpoint, { params });
        break;
      case 'POST':
        response = await api.post(endpoint, data);
        break;
      case 'PUT':
        response = await api.put(endpoint, data);
        break;
      case 'DELETE':
        response = await api.delete(endpoint, { data });
        break;
      default:
        throw new Error(`Unsupported method: ${method}`);
    }

    return response.data;
  } catch (error) {
    console.error(`Error calling protected API (${endpoint}):`, error);
    throw error;
  }
};

/**
 * Example function to get user profile from Azure Function
 * Using the standardized API prefix structure
 */
export const getUserProfile = async () => {
  return callProtectedApi('/api/user/profile');
};

/**
 * Example function to get data from Azure Function
 * Replace with your actual Azure Function endpoints
 */
export const getData = async (dataType) => {
  return callProtectedApi(`/api/data/${dataType}`);
};

/**
 * Example function to submit data to Azure Function
 * Replace with your actual Azure Function endpoints
 */
export const submitData = async (data) => {
  return callProtectedApi('/api/submit-data', 'POST', data);
};

export default {
  callProtectedApi,
  getUserProfile,
  getData,
  submitData
};
