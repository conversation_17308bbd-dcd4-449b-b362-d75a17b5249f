# Azure API Configuration for Production Deployment

## Overview

This document explains how the frontend React application is configured to communicate with the DB service hosted in the Azure portal through Azure API Management.

## Route Configuration Changes

The DB service route prefix has been updated from `/api/db` to `/v1` to match the Azure API Management routing expectations. This ensures that:

1. **Frontend calls**: `**************************************/v1/auth/login`
2. **DB service receives**: `/v1/auth/login` (handled by the function with route `auth/login`)
3. **APIM routing**: Routes requests from `/v1/*` to the DB service function app

## Current Configuration

### Production Environment Variables

The frontend is configured with the following production environment variables:

```bash
# Production environment settings
REACT_APP_API_BASE_URL=**************************************/v1
REACT_APP_DB_SERVICE_URL=**************************************/v1
REACT_APP_REDIRECT_URI=https://app-atomsec-dev01.azurewebsites.net
REACT_APP_AZURE_CLIENT_ID=2d313c1a-d62d-492c-869e-cf8cb9258204
REACT_APP_AZURE_TENANT_ID=41b676db-bf6f-46ae-a354-a83a1362533f
```

### API Endpoint Mapping

Based on the OpenAPI specification (`func-atomsec-dbconnect-dev.openapi.yaml`), the following endpoints are available:

#### Authentication Endpoints
- `GET /auth/azure/login` - Azure AD login
- `GET /auth/azure/callback` - Azure AD callback
- `GET /auth/azure/me` - Get current user info
- `POST /auth/login` - Standard login
- `POST /auth/signup` - User registration

#### Integration Management
- `GET /integrations` - List integrations
- `POST /integrations` - Create integration
- `GET /integrations/{integration_id}` - Get integration by ID
- `PUT /integrations/{integration_id}` - Update integration
- `DELETE /integrations/{integration_id}` - Delete integration
- `GET /integrations/{integration_id}/overview` - Get integration overview
- `GET /integrations/{integration_id}/health-check` - Get health check data
- `GET /integrations/{integration_id}/profiles` - Get profiles data
- `GET /integrations/{integration_id}/pmd-issues` - Get PMD issues
- `GET /integrations/{integration_id}/credentials` - Get credentials
- `POST /integration/test-connection` - Test connection
- `POST /integration/connect` - Connect integration
- `POST /integration/scan/{integration_id}` - Scan integration

#### User Management
- `GET /users` - List users
- `POST /users` - Create user
- `GET /users/{user_id}` - Get user by ID
- `PUT /users/{user_id}` - Update user
- `DELETE /users/{user_id}` - Delete user
- `GET /users/email/{email}` - Get user by email
- `GET /user/profile` - Get user profile
- `PUT /user/profile` - Update user profile
- `POST /users/{user_id}/login` - Create user login
- `POST /users/login/verify` - Verify login

#### Account Management
- `GET /accounts` - List accounts
- `POST /accounts` - Create account
- `GET /accounts/{account_id}` - Get account by ID
- `PUT /accounts/{account_id}` - Update account
- `DELETE /accounts/{account_id}` - Delete account

#### Security Data
- `GET /security/health-checks` - List health checks
- `POST /security/health-checks` - Store health checks
- `GET /security/profile-assignment-counts` - List profile assignment counts
- `POST /security/profile-assignment-counts` - Store profile assignment counts
- `GET /security/policies-result` - List policies results
- `POST /security/policies-result` - Store policies results

#### Task Management
- `GET /tasks` - List tasks
- `POST /tasks` - Create task
- `GET /tasks/{task_id}` - Get task by ID
- `PUT /tasks/{task_id}/status` - Update task status

#### System Endpoints
- `GET /health` - Health check
- `GET /info` - Service information

#### Organizations
- `GET /organizations` - List organizations

## Key Configuration Changes Made

### 1. Environment Variable Updates

**Before:**
```bash
REACT_APP_API_BASE_URL=https://func-atomsec-sfdc-dev.azurewebsites.net
```

**After:**
```bash
REACT_APP_API_BASE_URL=**************************************/v1
REACT_APP_DB_SERVICE_URL=**************************************/v1
```

### 2. Smart URL Handling

The frontend now includes intelligent URL handling that:

- **Production**: Uses Azure API Management URL directly (`**************************************/v1`)
- **Local Development**: Uses localhost with `/api/db` prefix (`http://localhost:7072/api/db`)

### 3. API Path Normalization

The OpenAPI specification shows paths without `/api/` prefix, but local development expects it. The configuration now handles this automatically:

```javascript
const getDbServiceUrl = (endpoint = '') => {
  if (isProduction) {
    // Production: Use Azure API Management URL directly
    return `${DB_SERVICE_URL}${endpoint}`;
  } else {
    // Local development: Use localhost with /api/db prefix
    return `${DB_SERVICE_URL}${endpoint}`;
  }
};
```

## New Policy Management Endpoints

The following new endpoints have been added for policy and rule management:

```javascript
policies: {
  list: '/policies',                                    // GET/POST
  create: '/policies',
  rules: {
    list: '/rules',                                     // GET/POST
    create: '/rules'
  },
  settings: {
    create: '/policy-rule-settings',                    // POST
    enabledTasks: '/policy-rule-settings/enabled-tasks' // GET
  }
}
```

## Verification Steps

To ensure the frontend communicates properly with the Azure-hosted DB service:

### 1. Check Environment Variables
Verify that the production build uses the correct environment variables:
```bash
echo $REACT_APP_API_BASE_URL
echo $REACT_APP_DB_SERVICE_URL
```

### 2. Test API Connectivity
Open browser developer tools and check:
- Network tab shows requests going to `**************************************/v1`
- No CORS errors in console
- API responses return expected data

### 3. Verify Authentication Flow
Test the authentication endpoints:
- Azure AD login redirects work correctly
- JWT tokens are properly handled
- User profile data loads correctly

### 4. Test Integration Management
Verify integration operations:
- List integrations loads data
- Create/update/delete operations work
- Integration details pages load correctly

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure Azure API Management is configured to allow requests from the frontend domain
2. **404 Errors**: Verify API paths match the OpenAPI specification exactly
3. **Authentication Issues**: Check Azure AD configuration and client IDs
4. **Timeout Errors**: Verify Azure Function Apps are running and responsive

### Debug Steps

1. Check browser console for errors
2. Verify network requests in developer tools
3. Test API endpoints directly using tools like Postman
4. Check Azure Function App logs for backend errors

## Security Considerations

- All API communication uses HTTPS in production
- Azure API Management provides additional security layer
- JWT tokens are used for authentication
- CORS is properly configured for the frontend domain

## Deployment Pipeline Updates

The deployment pipeline has been updated to set the correct environment variables during build:

```yaml
echo "REACT_APP_API_BASE_URL=**************************************/v1" >> .env.production
echo "REACT_APP_DB_SERVICE_URL=**************************************/v1" >> .env.production
```

This ensures the production build uses the correct Azure API Management URLs.
