# Code Quality Dashboard

## Overview

The Code Quality Dashboard is a comprehensive interface for monitoring, analyzing, and improving codebase quality. It displays PMD (Programming Mistake Detector) scan results in a modern, Atom-branded UI that provides insights into code quality issues, security vulnerabilities, and technical debt.

## Features

### 1. Summary Metrics
- **Code Health Score**: Overall code quality rating (0-10 scale)
- **Critical Issues**: High-priority issues requiring immediate attention
- **Technical Debt**: Estimated time to fix all issues
- **Security Issues**: Security vulnerabilities found
- **Code Coverage**: Test coverage percentage
- **Total Issues**: All issues across the codebase

### 2. Interactive Dashboard
- **Search Functionality**: Search files, rules, or issues
- **Filter Buttons**: Filter by needs attention, new issues, high impact, easy fix
- **Real-time Updates**: Dynamic filtering and sorting

### 3. Code Health Score Breakdown
- **Maintainability**: Code maintainability metrics
- **Reliability**: Code reliability assessment
- **Security**: Security-focused analysis
- **Performance**: Performance-related issues
- **Documentation**: Documentation quality metrics

### 4. Priority Issues Panel
- **Critical Issues**: High-severity problems
- **High Priority Issues**: Medium-severity concerns
- **Issue Cards**: Detailed view of each issue with metadata

### 5. Data Visualization
- **Pie Charts**: Issues by severity distribution
- **Bar Charts**: Issues by category breakdown
- **Interactive Charts**: Clickable chart elements

### 6. Comprehensive Issues Table
- **File Information**: File name and path
- **Line Numbers**: Exact location of issues
- **Rule Details**: PMD rule information
- **Category Tags**: Issue categorization
- **Severity Badges**: Visual severity indicators
- **Action Buttons**: View and fix actions

## Technical Implementation

### Components
- **CodeQualityDashboard.jsx**: Main dashboard component
- **CodeQualityDashboard.css**: Styling with Atom branding

### Data Source
- **PMDScans Table**: Database table containing PMD scan results
- **API Endpoint**: `/integrations/{integration_id}/pmd-issues`
- **Data Format**: JSON response with issues array

### Key Features
- **Responsive Design**: Works on desktop and mobile devices
- **Atom Branding**: Consistent with AtomSec design system
- **Performance Optimized**: Efficient data processing and rendering
- **Error Handling**: Graceful error states and loading indicators

## Data Structure

### PMD Finding Object
```json
{
  "id": "unique-identifier",
  "rule": "PMD Rule Name",
  "severity": "high|medium|low",
  "message": "Issue description",
  "file": "filename.cls",
  "line": 42,
  "package": "package.name",
  "class": "ClassName",
  "method": "methodName",
  "priority": 1,
  "ruleSet": "Best Practices",
  "description": "Detailed description",
  "timestamp": "2025-08-05T13:15:44.685383"
}
```

### Dashboard Metrics
```json
{
  "summary": {
    "codeHealthScore": "8.2",
    "criticalIssues": 12,
    "technicalDebtHours": "2.3",
    "securityIssues": 3,
    "codeCoverage": 87,
    "totalIssues": 2847,
    "filesWithIssues": 45
  }
}
```

## Usage

### Accessing the Dashboard
1. Navigate to an integration detail page
2. Click on the "Code Quality" tab
3. View comprehensive code quality metrics

### Filtering and Searching
1. Use the search bar to find specific files or issues
2. Click filter buttons to focus on specific issue types
3. Use severity and category dropdowns in the table
4. Sort table columns for better organization

### Understanding Metrics
- **Code Health Score**: Higher is better (0-10 scale)
- **Critical Issues**: Red indicators require immediate attention
- **Technical Debt**: Time estimate to resolve all issues
- **Security Issues**: Count of security-related problems

## Integration

### Tab Integration
The Code Quality Dashboard is integrated into the existing IntegrationTabs component, replacing the previous PMD Issues tab with a more comprehensive interface.

### API Integration
- Uses existing PMD API endpoints
- Leverages PMDScans database table
- Compatible with current data structure

### Styling Integration
- Follows AtomSec design system
- Uses consistent color palette (#51D59C primary)
- Responsive design patterns

## Future Enhancements

### Planned Features
- **Auto-fix Suggestions**: Automated code fixes
- **Trend Analysis**: Historical code quality trends
- **Team Collaboration**: Issue assignment and tracking
- **Integration with IDEs**: Direct IDE integration
- **Custom Rules**: User-defined PMD rules

### Performance Improvements
- **Pagination**: Large dataset handling
- **Caching**: Improved data loading
- **Real-time Updates**: Live issue monitoring

## Troubleshooting

### Common Issues
1. **No Data Displayed**: Check if PMD scan has been run
2. **Loading Errors**: Verify API endpoint accessibility
3. **Styling Issues**: Ensure CSS is properly loaded

### Debug Information
- Check browser console for API errors
- Verify integration ID in URL
- Confirm PMD scan completion status

## Support

For technical support or feature requests, contact the development team or refer to the main AtomSec documentation. 