# Manifest.json CORS Issue Fix

## Problem
The manifest.json file is being intercepted by Azure App Service authentication and redirected to Azure AD login, causing CORS errors in the browser console.

## Root Cause
Azure App Service authentication is intercepting requests to static files despite them being in the excluded paths configuration.

## Solution

### 1. Updated Configuration Files

I've updated the following files to fix the issue:

#### `authentication.json`
- Added more comprehensive excluded paths
- Included font files and additional static assets
- Added specific asset directories

#### `web.config`
- Enhanced static file handling
- Added font MIME types
- Improved URL rewrite rules

#### `staticwebapp.config.json` (NEW)
- Added Static Web App configuration for better static file handling
- Explicit routes for manifest.json and other static files
- Proper CORS headers for static assets

### 2. Deployment Steps

1. **Deploy the updated files to Azure App Service:**
   ```bash
   # Navigate to the frontend directory
   cd atomsec-app-frontend
   
   # Build the application
   npm run build
   
   # Deploy to Azure (using your existing deployment method)
   # This could be via Azure DevOps, GitHub Actions, or direct deployment
   ```

2. **Verify the configuration in Azure Portal:**
   - Go to your Azure App Service
   - Navigate to Configuration > General settings
   - Ensure "Authentication" is enabled
   - Check that the authentication.json file is properly configured

3. **Test the fix:**
   ```bash
   # Run the test script
   node test-manifest.js
   ```

### 3. Additional Verification

After deployment, test the following URLs directly in your browser:

- `https://app-atomsec-dev01.azurewebsites.net/manifest.json`
- `https://app-atomsec-dev01.azurewebsites.net/favicon.ico`
- `https://app-atomsec-dev01.azurewebsites.net/static/`

These should return the actual files without authentication redirects.

### 4. Browser Cache Clear

After deployment, clear your browser cache or test in an incognito window to ensure the changes take effect.

### 5. Monitoring

Monitor the browser console for any remaining CORS errors after deployment. If issues persist, check:

1. Azure App Service logs
2. Network tab in browser developer tools
3. Authentication configuration in Azure Portal

## Expected Result

After deployment, the manifest.json file should be accessible without authentication redirects, and the CORS error should be resolved.

## Troubleshooting

If the issue persists:

1. **Check Azure App Service logs** for authentication-related errors
2. **Verify the authentication.json** file is properly deployed
3. **Test with curl** to see the actual response:
   ```bash
   curl -I https://app-atomsec-dev01.azurewebsites.net/manifest.json
   ```
4. **Check Azure Portal** authentication settings for the App Service
5. **Verify staticwebapp.config.json** is deployed to the root directory

## Files Modified

- `authentication.json` - Enhanced excluded paths
- `web.config` - Improved static file handling
- `staticwebapp.config.json` - NEW Static Web App configuration
- `test-manifest.js` - Test script for verification

## Alternative Solutions

If the issue persists after deployment, consider these additional steps:

1. **Azure Portal Configuration:**
   - Go to Azure App Service > Configuration > General settings
   - Set "Authentication" to "Off" temporarily to test
   - Re-enable with proper configuration

2. **Custom Authentication Handler:**
   - Create a custom authentication handler that explicitly excludes static files
   - Modify the authentication flow to check file extensions before authentication

3. **CDN Configuration:**
   - Consider using Azure CDN for static files
   - Configure CDN to serve static files without authentication

## Testing Commands

```bash
# Test manifest.json accessibility
curl -I https://app-atomsec-dev01.azurewebsites.net/manifest.json

# Test with authentication headers
curl -H "Authorization: Bearer" https://app-atomsec-dev01.azurewebsites.net/manifest.json

# Test static files
curl -I https://app-atomsec-dev01.azurewebsites.net/favicon.ico
``` 