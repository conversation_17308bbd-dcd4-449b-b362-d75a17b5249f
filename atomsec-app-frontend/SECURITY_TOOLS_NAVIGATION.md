# Security Tools Navigation System

## Overview

The Security Tools Navigation System provides a unified interface for accessing multiple security tools within the AtomSec platform. After successful SSO authentication, users are redirected to a "Choose Application" page where they can select which security tool to access.

## Architecture

### Components

1. **SecurityToolsSelector** (`/src/components/SecurityToolsSelector.jsx`)
   - Main component for the tool selection interface
   - Displays security tools as interactive cards
   - Handles navigation to both internal and external tools

2. **External Tool Navigation Utilities** (`/src/utils/externalToolNavigation.js`)
   - Handles SSO token management for external tools
   - Provides error handling and validation
   - Manages popup windows with security attributes

3. **External Tools Configuration** (`/src/config/externalTools.js`)
   - Centralized configuration for external tool URLs
   - Environment-specific settings
   - Feature descriptions and metadata

4. **External Tool Modal** (`/src/components/ExternalToolModal.jsx`)
   - Modal that appears after opening external tools
   - Provides navigation options and user control
   - Auto-closes after 5 seconds with countdown

### Routing Changes

- **Post-login redirect**: Users are now redirected to `/tools` instead of `/`
- **New route**: `/tools` - Security tools selection page
- **Dashboard route**: `/dashboard` - Existing dashboard with layout
- **Root route**: `/` - Redirects to `/tools`

## Security Tools

### Internal Tools

1. **Attack Surface** (`/dashboard`)
   - Built-in vulnerability management platform
   - Includes dashboard, integrations, reports, and analytics
   - Uses existing AtomSec functionality

### External Tools

1. **Open Search**
   - Advanced search and discovery platform
   - URL: Configurable via `REACT_APP_OPEN_SEARCH_URL`
   - Features: Threat Intelligence, Data Discovery, Advanced Analytics

2. **Grey Log**
   - Logging and monitoring solution
   - URL: Configurable via `REACT_APP_GREY_LOG_URL`
   - Features: Log Management, Event Correlation, Alerting

## SSO Integration

### Authentication Flow

1. User authenticates via Azure SSO
2. Access token is obtained and stored
3. User is redirected to `/tools` page
4. When accessing external tools:
   - Token is passed as URL parameter
   - User information is included
   - Callback URL is provided for seamless return

### Token Management

- Access tokens are retrieved from AuthContext
- Tokens are passed to external tools via URL parameters
- Secure popup windows are used for external tool access
- Error handling for token expiration and authentication failures

## Configuration

### Environment Variables

```bash
# External tool URLs
REACT_APP_OPEN_SEARCH_URL=https://opensearch.atomsec.com
REACT_APP_GREY_LOG_URL=https://greylog.atomsec.com
```

### Development vs Production

- **Development**: Uses localhost URLs for external tools
- **Production**: Uses configured production URLs
- Environment detection is automatic based on hostname

## Usage

### For Users

1. Log in via Azure SSO
2. Select desired security tool from the grid
3. Internal tools open in the same tab
4. External tools open in new tabs with SSO authentication
5. A modal appears after opening external tools with options to:
   - Open the tool again in a new tab
   - Navigate to the Attack Surface dashboard
   - Close the modal (auto-closes after 5 seconds)

### For Developers

#### Adding a New External Tool

1. Update `externalToolsConfig` in `/src/config/externalTools.js`:

```javascript
'new-tool': {
  name: 'New Tool',
  description: 'Description of the new tool',
  development: {
    url: 'http://localhost:3001',
    authMethod: 'token',
    features: ['Feature 1', 'Feature 2']
  },
  production: {
    url: process.env.REACT_APP_NEW_TOOL_URL || 'https://newtool.atomsec.com',
    authMethod: 'token',
    features: ['Feature 1', 'Feature 2']
  }
}
```

2. Add the corresponding icon mapping in `SecurityToolsSelector.jsx`

3. Set the environment variable for production:
```bash
REACT_APP_NEW_TOOL_URL=https://newtool.atomsec.com
```

#### Modifying Tool Appearance

- Update colors in the `securityTools` array
- Modify icons by changing the icon mapping
- Update feature lists in the configuration

## Error Handling

### Common Issues

1. **New Tab Blocked**: Browser prevents opening external tools in new tabs
   - Solution: Allow popups for the AtomSec domain

2. **Authentication Failed**: Token expired or invalid
   - Solution: User is prompted to log in again

3. **Network Error**: External tool unavailable
   - Solution: Check tool availability and network connectivity

4. **Modal Not Appearing**: External tool modal doesn't show after opening tool
   - Solution: Check browser console for errors, ensure modal component is properly imported

### Error Messages

- User-friendly error messages are displayed via toast notifications
- Detailed error logging for debugging
- Graceful fallbacks for configuration errors

## Security Considerations

1. **Token Security**: Access tokens are passed via URL parameters
   - Consider implementing more secure token exchange mechanisms
   - Monitor token usage and implement proper expiration

2. **Tab Security**: External tools open in new tabs
   - Use `noopener,noreferrer` attributes
   - Validate external URLs before opening
   - Modal provides user control and navigation options

3. **CORS and CSP**: Ensure proper cross-origin policies
   - Configure Content Security Policy headers
   - Handle CORS issues for external tool integration

## Testing

### Manual Testing

1. Test SSO authentication flow
2. Verify tool selection and navigation
3. Test external tool popup functionality
4. Validate error handling scenarios

### Automated Testing

- Unit tests for utility functions
- Integration tests for authentication flow
- E2E tests for complete user journey

## Future Enhancements

1. **Enhanced SSO**: Implement more secure token exchange
2. **Tool Status**: Add health checks for external tools
3. **User Preferences**: Remember user's preferred tools
4. **Analytics**: Track tool usage and performance
5. **Customization**: Allow users to customize tool grid layout 