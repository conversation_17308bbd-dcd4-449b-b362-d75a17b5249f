# Azure APIM Migration Guide

## Overview

The AtomSec frontend application has been migrated to support both local development (direct function app calls) and production (Azure APIM). This provides flexibility for development while maintaining the benefits of APIM in production.

## Environment-Based Configuration

The application automatically detects the environment and uses the appropriate configuration:

- **Local Development** (`localhost`): Uses direct function app calls
- **Production** (non-localhost): Uses Azure APIM

## Changes Made

### 1. Configuration Updates (`src/config.js`)

**Before:**
```javascript
baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:7072',
```

**After:**
```javascript
// Environment-based configuration
const isProduction = window.location.hostname !== 'localhost';

baseURL: isProduction 
  ? (process.env.REACT_APP_APIM_BASE_URL || '**************************************/db')
  : (process.env.REACT_APP_DB_SERVICE_URL || 'http://localhost:7072'),
```

### 2. Authentication Configuration Updates (`src/authConfig.js`)

**Supports both environments:**
```javascript
// Local development: use direct function app calls
// Production: use Azure APIM
export const API_BASE_URL = isProduction 
  ? (process.env.REACT_APP_APIM_BASE_URL || '**************************************/db')
  : (process.env.REACT_APP_DB_SERVICE_URL || 'http://localhost:7072');
```

### 3. API Endpoints

Endpoints are automatically adjusted based on environment:

**Local Development:**
```javascript
// Direct function app calls
const response = await api.post('/api/db/auth/login', credentials);
```

**Production (APIM):**
```javascript
// APIM calls
const response = await api.post('/auth/login', credentials);
```

### 4. Authentication Headers

APIM subscription key is only added in production:

```javascript
// Add APIM subscription key if available (production only)
if (API_CONFIG.apimSubscriptionKey && isProduction) {
  config.headers['Ocp-Apim-Subscription-Key'] = API_CONFIG.apimSubscriptionKey;
}
```

## Environment Variables

### **Local Development** (`.env.local` or `.env.development`):
```bash
# Local development - direct function app calls
REACT_APP_DB_SERVICE_URL=http://localhost:7072
REACT_APP_ENABLE_API_LOGGING=true
```

### **Production** (`.env.production`):
```bash
# Production - Azure APIM
REACT_APP_APIM_BASE_URL=**************************************/db
REACT_APP_API_VERSION=v1
REACT_APP_APIM_SUBSCRIPTION_KEY=bd50cc1018444ae987a04c465534e428
```

## URL Generation

### **Local Development URLs:**
- Base: `http://localhost:7072`
- Endpoint: `/api/db/auth/login`
- Final: `http://localhost:7072/api/db/auth/login`

### **Production URLs:**
- Base: `**************************************/db`
- Version: `v1`
- Endpoint: `/auth/login`
- Final: `**************************************/db/v1/auth/login`

## API Endpoints Mapping

| Function | Local Development | Production (APIM) |
|----------|------------------|-------------------|
| Login | `/api/db/auth/login` | `/auth/login` |
| Signup | `/api/db/auth/signup` | `/auth/signup` |
| Azure Login | `/api/db/auth/azure/login` | `/auth/azure/login` |
| Azure Callback | `/api/db/auth/azure/callback` | `/auth/azure/callback` |
| Get User | `/api/db/users/{id}` | `/users/{id}` |
| Get Users | `/api/db/users` | `/users` |
| Get Accounts | `/api/db/accounts` | `/accounts` |
| Get Integrations | `/api/db/integrations` | `/integrations` |
| Get Integration | `/api/db/integrations/{id}` | `/integrations/{id}` |
| Test Connection | `/api/db/integration/test-connection` | `/integration/test-connection` |
| Connect Integration | `/api/db/integration/connect` | `/integration/connect` |
| Scan Integration | `/api/db/integrations/{id}/scan` | `/integration/scan/{id}` |
| Get Tasks | `/api/db/tasks` | `/tasks` |
| Get Health Checks | `/api/db/security/health-checks` | `/security/health-checks` |
| Get Policies Results | `/api/db/security/policies-result` | `/security/policies-result` |
| Health Check | `/api/db/health` | `/health` |
| Info | `/api/db/info` | `/info` |

## Benefits of This Approach

1. **Development Flexibility**: Easy local development with direct function app calls
2. **Production Security**: APIM provides security, monitoring, and governance
3. **Automatic Switching**: No manual configuration changes needed
4. **Backward Compatibility**: Existing local development workflow preserved
5. **Environment Isolation**: Clear separation between dev and prod

## Testing

### **Local Development Testing:**
```bash
# Set local environment variables
export REACT_APP_DB_SERVICE_URL=http://localhost:7072
export REACT_APP_ENABLE_API_LOGGING=true

# Start the development server
npm start
```

### **Production Testing:**
```bash
# Set production environment variables
export REACT_APP_APIM_BASE_URL=**************************************/db
export REACT_APP_API_VERSION=v1
export REACT_APP_APIM_SUBSCRIPTION_KEY=your-key-here

# Start the development server
npm start
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized (Production)**: Check if the APIM subscription key is correct
2. **404 Not Found**: Verify the endpoint paths match the environment
3. **CORS Issues**: Ensure APIM is configured to allow requests from your domain
4. **Local Development Issues**: Verify the function app is running on localhost:7072

### Debug Mode

Enable API logging in development:

```bash
export REACT_APP_ENABLE_API_LOGGING=true
```

This will log all API requests and responses to the console.

## OpenAPI Specification

The production migration is based on the OpenAPI specification in `DB.openapi.yaml`, which defines:

- Base URL: `**************************************/db/v1`
- Authentication: API Key (`Ocp-Apim-Subscription-Key`)
- Endpoints: All CRUD operations for users, accounts, integrations, tasks, etc.

## Next Steps

1. **Set Environment Variables**: Configure appropriate variables for your environment
2. **Test Both Environments**: Verify functionality works in both local and production
3. **Monitor Performance**: Check API response times and error rates
4. **Update Documentation**: Update any external documentation referencing the old endpoints 