{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}, "logLevel": {"default": "Information", "Worker.Rpc": "Warning", "Function": "Information"}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "functionTimeout": "00:10:00", "concurrency": {"dynamicConcurrencyEnabled": false, "snapshotPersistenceEnabled": false}, "extensions": {"http": {"routePrefix": "api", "maxOutstandingRequests": 200, "maxConcurrentRequests": 100, "dynamicThrottlesEnabled": true, "cors": {"allowedOrigins": ["http://localhost:3000"], "allowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowedHeaders": ["Content-Type", "Authorization", "X-Requested-With"], "allowCredentials": true}}, "queues": {"maxPollingInterval": "00:00:02", "visibilityTimeout": "00:00:30", "batchSize": 16, "maxDequeueCount": 5, "newBatchThreshold": 8}, "blobs": {"maxDegreeOfParallelism": 4, "useStorageEmulator": true, "apiVersion": "2020-10-02"}}}