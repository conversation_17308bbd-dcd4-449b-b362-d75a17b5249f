# Updated Authentication Flow

## Overview

The authentication flow has been updated to redirect directly to Microsoft Azure AD instead of going through the backend first. This provides a more secure and standard Azure AD authentication experience.

## Authentication Flow

### 1. **User Clicks "Sign in with Microsoft"**

### 2. **Frontend Redirects to Azure AD**
```
Frontend → Microsoft Azure AD Login Page
```

**URL Structure:**
```
https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/authorize
?client_id={client-id}
&response_type=code
&redirect_uri={redirect-uri}
&scope=openid profile email User.Read
&response_mode=query
&state={random-state}
```

### 3. **User Authenticates with Azure AD**
- User enters credentials on Microsoft's secure login page
- Azure AD validates credentials
- Azure AD redirects back to application with authorization code

### 4. **Frontend Receives Authorization Code**
```
Azure AD → Frontend (with authorization code)
```

### 5. **Frontend Exchanges Code for Tokens**
```
Frontend → Backend API → Azure AD → Backend API → Frontend
```

### 6. **User is Authenticated**
- Tokens are stored in localStorage
- User state is updated
- User is redirected to the intended page

## Environment-Specific Behavior

### **Production Environment**
- Uses Azure App Service platform authentication
- Redirects to `/.auth/login/aad`
- Platform handles the entire OAuth flow

### **Development Environment**
- Uses direct Azure AD authentication
- Redirects to `https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/authorize`
- Frontend handles the OAuth flow with backend token exchange

## Configuration

### **Environment Variables**
```bash
REACT_APP_AZURE_CLIENT_ID=123d7a1b-7b24-4924-a73c-1fbcff016b12
REACT_APP_AZURE_TENANT_ID=41b676db-bf6f-46ae-a354-a83a1362533f
REACT_APP_REDIRECT_URI=http://localhost:3000
```

### **Backend Endpoints**
```javascript
authEndpoints.azure.callback = '/auth/azure/callback'  // Exchange code for tokens
authEndpoints.azure.me = '/auth/azure/me'              // Get user info
```

## Security Benefits

### ✅ **Direct Azure AD Integration**
- No custom authentication logic
- Uses Microsoft's secure authentication infrastructure
- Supports MFA, conditional access, and other Azure AD features

### ✅ **Proper OAuth 2.0 Flow**
- Authorization code flow (most secure)
- Tokens never exposed in frontend
- Client secret handled securely in backend

### ✅ **Environment Isolation**
- Production uses platform authentication
- Development uses direct Azure AD
- No cross-environment security issues

## Testing

### **Local Development**
1. Start the application: `npm start`
2. Click "Sign in with Microsoft"
3. Should redirect to: `https://login.microsoftonline.com/{tenant-id}/oauth2/v2.0/authorize`
4. Complete authentication on Microsoft's page
5. Should redirect back to localhost with authorization code
6. Backend exchanges code for tokens
7. User is authenticated and redirected to `/tools`

### **Production**
1. Deploy to Azure App Service
2. Click "Sign in with Microsoft"
3. Should redirect to: `https://your-app.azurewebsites.net/.auth/login/aad`
4. Platform handles the entire authentication flow
5. User is authenticated and redirected to intended page

## Troubleshooting

### **Common Issues**

1. **"Invalid redirect URI"**
   - Check `REACT_APP_REDIRECT_URI` environment variable
   - Ensure redirect URI is registered in Azure AD app

2. **"Invalid client ID"**
   - Check `REACT_APP_AZURE_CLIENT_ID` environment variable
   - Ensure client ID matches Azure AD app registration

3. **"Authorization code exchange failed"**
   - Check backend logs for token exchange errors
   - Verify client secret is configured in backend

### **Debug Logging**
Enable console logging to see the authentication flow:
```javascript
console.log('Authentication flow:', {
  clientId: process.env.REACT_APP_AZURE_CLIENT_ID,
  tenantId: process.env.REACT_APP_AZURE_TENANT_ID,
  redirectUri: process.env.REACT_APP_REDIRECT_URI
});
```

## Migration Notes

### **What Changed**
- ❌ Old: Frontend → Backend → Azure AD
- ✅ New: Frontend → Azure AD → Backend (for token exchange)

### **Benefits**
- More secure authentication flow
- Better user experience (direct Microsoft login)
- Standard OAuth 2.0 implementation
- Support for Azure AD enterprise features 