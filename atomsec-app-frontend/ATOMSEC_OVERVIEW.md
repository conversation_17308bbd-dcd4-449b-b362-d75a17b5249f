# AtomSec App Frontend - Complete Overview

## 🎯 Core Purpose
**Comprehensive Salesforce security operations platform** - A React-based web application providing automated security scanning, compliance monitoring, and risk management for Salesforce organizations, hosted on Azure infrastructure.

## 🏗️ Architecture & Technology Stack

### Frontend Framework
- **React 18.2.0** with Create React App
- **React Router** for navigation
- **Context API** for state management
- **Material-UI & Ant Design** for components

### Security & Authentication
- **Azure Active Directory SSO** integration
- **JWT token-based** authentication
- **OAuth 2.0** flows (JWT and OAuth)

### Azure Infrastructure
- **Frontend Hosting**: Azure App Service (`app-atomsec-dev01.azurewebsites.net`)
- **API Gateway**: Azure API Management (`apim-atomsec-dev.azure-api.net`)
- **Backend Services**: Azure Function Apps
- **External Tools**: Azure VMs with security platforms

## 🔐 Security Features

### Salesforce Security Scanning
- **Health Check System**: 0-100 scoring for security posture
- **Vulnerability Detection**: OWASP compliance, Apex code analysis
- **Permission Management**: Profile and permission set analysis
- **Compliance Reporting**: Automated security compliance reports

### Security Tools Integration
- **AtomSecIQ**: Security intelligence dashboard
- **OpenCTI**: Threat intelligence platform
- **SIEM/XDR**: Extended detection and response
- **OpenSearch**: Log analysis and SIEM capabilities

### External Tools Configuration
```javascript
// src/config/externalTools.js
{
  'atomseciq': {
    name: 'AtomSecIQ',
    url: 'dashboards-prod-tn01.eastus.cloudapp.azure.com:5601/',
    features: ['Security Analytics', 'Dashboards', 'Reporting']
  },
  'opencti': {
    name: 'OpenCTI',
    url: 'opencti-prod-tn01.eastus.cloudapp.azure.com/',
    features: ['Threat Intelligence', 'Knowledge Base', 'Incident Management']
  },
  'siem-xdr': {
    name: 'SIEM/XDR',
    url: 'xdr-prod-tn01.eastus.cloudapp.azure.com/',
    features: ['Event Monitoring', 'Detection & Response', 'Log Management']
  }
}
```

## 🚀 Key Functional Components

### Core Modules
- **Dashboard**: Real-time security metrics and health scores
- **Integrations**: Manage Salesforce connections (Production, Sandbox, Developer orgs)
- **ScanWizard**: Multi-step wizard for connecting new Salesforce orgs
- **Reports**: Historical security tracking and compliance reports
- **Settings**: User preferences, notification management, security settings

### Authentication Flow
- **Production**: Azure App Service platform authentication
- **Development**: Direct Azure AD authentication with backend token exchange
- **SSO Integration**: Unified login across all security tools

## 🌐 Azure Deployment Configuration

### Infrastructure URLs
```
Frontend: https://app-atomsec-dev01.azurewebsites.net/
API Gateway: **************************************/v1/
External Tools:
  - AtomSecIQ: dashboards-prod-tn01.eastus.cloudapp.azure.com:5601
  - OpenCTI: opencti-prod-tn01.eastus.cloudapp.azure.com
  - SIEM/XDR: xdr-prod-tn01.eastus.cloudapp.azure.com
```

### Environment Variables
```bash
# Production
REACT_APP_API_BASE_URL=**************************************/v1
REACT_APP_DB_SERVICE_URL=**************************************/v1
REACT_APP_REDIRECT_URI=https://app-atomsec-dev01.azurewebsites.net/.auth/login/aad/callback
REACT_APP_AZURE_CLIENT_ID=2d313c1a-d62d-492c-869e-cf8cb9258204
REACT_APP_AZURE_TENANT_ID=41b676db-bf6f-46ae-a354-a83a1362533f
REACT_APP_APIM_SUBSCRIPTION_KEY=bd50cc1018444ae987a04c465534e428

# Development
REACT_APP_API_BASE_URL=http://localhost:7072
REACT_APP_DB_SERVICE_URL=http://localhost:7072
REACT_APP_REDIRECT_URI=http://localhost:3000
```

### Deployment Pipeline
- **CI/CD**: Azure DevOps pipeline with automated builds
- **Build Process**: React build with Azure-specific optimizations
- **Security Headers**: CSP, HSTS, and CORS configured
- **Archive**: ZIP deployment to Azure App Service

## 📊 Application Flow

1. **User Authentication**: Azure AD SSO login
2. **Tool Selection**: Choose from internal or external security tools
3. **Integration Setup**: Connect Salesforce organizations
4. **Security Scanning**: Automated vulnerability assessment
5. **Monitoring**: Real-time security posture tracking
6. **Reporting**: Exportable compliance and risk reports

## 🔧 Development vs Production

### Development Environment
- **Local server**: `localhost:3000`
- **Direct API**: `localhost:7072`
- **Relaxed security**: For debugging

### Production Environment
- **Azure App Service**: `app-atomsec-dev01.azurewebsites.net`
- **API Management**: APIM gateway
- **Enhanced security**: Production-grade features

## 📁 Key Files & Configuration

### Core Configuration Files
- `src/authConfig.js` - Authentication configuration
- `src/context/AuthContext.js` - Authentication state management
- `src/config/externalTools.js` - External tools configuration
- `.env.production` - Production environment variables
- `.env` - Development environment variables

### Deployment Files
- `pipeline-frontend-dev.yml` - Azure DevOps CI/CD pipeline
- `web.config` - Azure App Service configuration
- `host.json.new` - Azure Functions configuration

### Documentation
- `AZURE_API_CONFIGURATION.md` - API setup documentation
- `AUTHENTICATION_FLOW.md` - Authentication process documentation
- `SECURITY_TOOLS_NAVIGATION.md` - Security tools integration guide

## 🔍 API Endpoints Structure

### Authentication
- `/auth/azure/login` - Azure AD login
- `/auth/azure/callback` - OAuth callback
- `/auth/azure/me` - User profile

### Integrations Management
- `/integrations` - List/Create integrations
- `/integrations/{id}` - Get/Update/Delete specific integration
- `/integrations/{id}/health-check` - Security assessment results
- `/integrations/{id}/scan` - Trigger security scan

### Security Data
- `/security/health-checks` - Health check results
- `/security/profile-assignment-counts` - Permission analysis
- `/security/policies-result` - Policy compliance results

## 🎯 Summary

This application serves as a **centralized security operations center** for Salesforce environments, providing enterprise-grade security monitoring, compliance management, and threat intelligence capabilities through a unified Azure-hosted platform.