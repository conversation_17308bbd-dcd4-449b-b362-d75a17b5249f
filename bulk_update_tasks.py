#!/usr/bin/env python3
"""
Script to bulk update task states in Azure DevOps
"""

import json
import subprocess
import sys

def run_az_command(command):
    """Run Azure CLI command and return JSON output"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            print(f"Error running command: {result.stderr}")
            return None
    except json.JSONDecodeError:
        return None

def update_task_state(task_id, new_state, assigned_to=None, organization="https://dev.azure.com/AtomSec"):
    """Update a single task's state"""
    command = f'az boards work-item update --id {task_id} --state "{new_state}"'
    
    if assigned_to:
        command += f' --assigned-to "{assigned_to}"'
    
    command += f' --organization "{organization}" --output json'
    
    result = run_az_command(command)
    return result is not None

def get_tasks_by_state(current_state):
    """Get all tasks with a specific state"""
    command = f'az boards query --wiql "SELECT [System.Id], [System.Title], [System.State] FROM WorkItems WHERE [System.WorkItemType] = \'Task\' AND [System.State] = \'{current_state}\'" --organization "https://dev.azure.com/AtomSec" --project "atomsec_app" --output json'
    return run_az_command(command)

def main():
    print("🔄 Bulk Task State Update Tool")
    print("=" * 50)
    
    # Show current task states
    print("\n📊 Current Task Distribution:")
    states = ["New", "Active", "Doing", "Closed"]
    
    for state in states:
        tasks = get_tasks_by_state(state)
        if tasks:
            print(f"   • {state}: {len(tasks)} tasks")
    
    print("\n🔄 Update Options:")
    print("1. Update all 'New' tasks to 'Active'")
    print("2. Update all 'Doing' tasks to 'Active'")
    print("3. Update specific task by ID")
    print("4. Update tasks by current state")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == "1":
        # Update all New tasks to Active
        new_tasks = get_tasks_by_state("New")
        if new_tasks:
            print(f"\n🔄 Updating {len(new_tasks)} 'New' tasks to 'Active'...")
            success_count = 0
            for task in new_tasks:
                task_id = task['fields']['System.Id']
                task_title = task['fields']['System.Title']
                print(f"   Updating task {task_id}: {task_title[:40]}...")
                if update_task_state(task_id, "Active"):
                    success_count += 1
                    print(f"     ✅ Updated")
                else:
                    print(f"     ❌ Failed")
            print(f"\n✅ Successfully updated {success_count}/{len(new_tasks)} tasks")
        else:
            print("❌ No 'New' tasks found")
    
    elif choice == "2":
        # Update all Doing tasks to Active
        doing_tasks = get_tasks_by_state("Doing")
        if doing_tasks:
            print(f"\n🔄 Updating {len(doing_tasks)} 'Doing' tasks to 'Active'...")
            success_count = 0
            for task in doing_tasks:
                task_id = task['fields']['System.Id']
                task_title = task['fields']['System.Title']
                print(f"   Updating task {task_id}: {task_title[:40]}...")
                if update_task_state(task_id, "Active"):
                    success_count += 1
                    print(f"     ✅ Updated")
                else:
                    print(f"     ❌ Failed")
            print(f"\n✅ Successfully updated {success_count}/{len(doing_tasks)} tasks")
        else:
            print("❌ No 'Doing' tasks found")
    
    elif choice == "3":
        # Update specific task
        task_id = input("Enter task ID: ").strip()
        new_state = input("Enter new state (New/Active/Doing/Closed): ").strip()
        assigned_to = input("Enter assigned to (email) or press Enter to skip: ").strip()
        
        if assigned_to:
            success = update_task_state(task_id, new_state, assigned_to)
        else:
            success = update_task_state(task_id, new_state)
        
        if success:
            print(f"✅ Task {task_id} updated successfully")
        else:
            print(f"❌ Failed to update task {task_id}")
    
    elif choice == "4":
        # Update tasks by current state
        current_state = input("Enter current state to update (New/Active/Doing/Closed): ").strip()
        new_state = input("Enter new state (New/Active/Doing/Closed): ").strip()
        
        tasks = get_tasks_by_state(current_state)
        if tasks:
            print(f"\n🔄 Updating {len(tasks)} '{current_state}' tasks to '{new_state}'...")
            success_count = 0
            for task in tasks:
                task_id = task['fields']['System.Id']
                task_title = task['fields']['System.Title']
                print(f"   Updating task {task_id}: {task_title[:40]}...")
                if update_task_state(task_id, new_state):
                    success_count += 1
                    print(f"     ✅ Updated")
                else:
                    print(f"     ❌ Failed")
            print(f"\n✅ Successfully updated {success_count}/{len(tasks)} tasks")
        else:
            print(f"❌ No '{current_state}' tasks found")
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main() 