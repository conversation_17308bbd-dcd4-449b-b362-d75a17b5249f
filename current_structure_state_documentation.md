# Current Structure State Documentation

**Created:** $(date)  
**Purpose:** Backup documentation before folder structure maintenance  
**Backup Branches:** 
- SFDC Service: `backup/before-folder-restructure`
- DB Service: `backup/before-folder-restructure`

## SFDC Service (atomsec-func-sfdc) Current State

### Current Branch
- Branch: `dev-db-queue-updates`
- Backup Branch: `backup/before-folder-restructure`

### Root Directory Files
```
atomsec-func-sfdc/
├── function_app.py                    # Main entry point
├── function_app_production.py         # Production entry point
├── host.json                         # Function configuration
├── host.optimized.json               # Optimized configuration
├── requirements.txt                  # Dependencies
├── pytest.ini                       # Test configuration
├── check_credentials.py              # Utility script (to be moved)
├── task_management.py                # Utility script (to be moved)
├── service_bus_processor.py          # Utility script (to be moved)
├── test_account_management.py        # Test file (to be moved)
├── test_local_startup.py             # Test file (to be moved)
├── test_enhanced_error_handling.py   # Test file (to be moved)
├── test_queue_message_processor.py   # Test file (to be moved)
└── account_management_asgi.py        # ASGI application
```

### Well-Organized Existing Directories
```
├── src/                              # New source structure
│   ├── api/                         # API endpoints
│   ├── shared/                      # Shared utilities
│   ├── repositories/                # Data repositories
│   ├── blueprints/                  # Flask blueprints
│   ├── task_processor/              # Task processing
│   └── pmd_components/              # PMD components
├── tests/                           # Test structure
│   ├── unit/                       # Unit tests
│   ├── integration/                # Integration tests
│   ├── performance/                # Performance tests
│   ├── security/                   # Security tests
│   └── architecture/               # Architecture tests
├── docs/                           # Documentation
│   ├── api/                        # API documentation
│   ├── architecture/               # Architecture docs
│   ├── development/                # Development guides
│   ├── operations/                 # Operations guides
│   └── openapi/                    # OpenAPI specs
├── config/                         # Configuration files
├── infrastructure/                 # Infrastructure as code
├── scripts/                        # Utility scripts
├── examples/                       # Example code
└── pipeline-templates/             # CI/CD templates
```

### Files/Directories to be Cleaned Up
- `assets_project_maintenance/` (renamed from problematic name)
- Various `*.disabled.old` files (deleted)
- Cache directories: `c:azurite/`, `__blobstorage__/` (deleted)
- Log files: `c:azuritedebug.log`, `SFDC-local-testing.log` (deleted)
- Azurite database files: `__azurite_db_*.json` (deleted)

## DB Service (atomsec-func-db-r) Current State

### Current Branch
- Branch: `dev-db-queue-updates`
- Backup Branch: `backup/before-folder-restructure`

### Root Directory Files
```
atomsec-func-db-r/
├── function_app.py                           # Main entry point
├── host.json                                # Function configuration
├── requirements.txt                         # Dependencies
├── run_integration_tests.py                 # Integration test runner (to be moved)
├── test_enhanced_database_security.py       # Test file (to be moved)
├── test_enhanced_queue_manager.py           # Test file (to be moved)
├── test_enhanced_task_coordination_service.py # Test file (to be moved)
├── test_enhanced_task_status_service.py     # Test file (to be moved)
├── test_execution_context_manager.py        # Test file (to be moved)
├── ENHANCED_DATABASE_SECURITY_IMPLEMENTATION.md # Doc (to be moved)
└── QUEUE_BASED_PROCESSING_DEPLOYMENT_GUIDE.md   # Doc (to be moved)
```

### Well-Organized Existing Directories
```
├── src/                            # New source structure
│   ├── api/                       # API endpoints
│   ├── shared/                    # Shared utilities
│   └── repositories/              # Data repositories
├── tests/                         # Test structure (minimal)
├── scripts/                       # Utility scripts
├── cache/                         # Cache directory
└── logs/                          # Log directory
```

### Files/Directories to be Cleaned Up
- Log files: `backendfunction.log`, `dbfunction.log`, `func.log` (deleted)
- Temp files: `func copy.txt` (deleted)
- Example files at root: `example_task_coordination_usage.py` (to be moved)

## Git Status Summary

### SFDC Service Changes
- Many files deleted from old structure (api/, shared/, blueprints/, etc.)
- New src/ structure created with organized code
- Test files scattered at root level
- New documentation and infrastructure added
- Configuration properly organized

### DB Service Changes
- Similar cleanup of old structure
- New src/ structure created
- Test files at root level
- Documentation files at root level
- Minimal but clean structure

## Recovery Plan

### Rollback Procedure
1. **Switch to backup branch:**
   ```bash
   cd atomsec-func-sfdc
   git checkout backup/before-folder-restructure
   
   cd ../atomsec-func-db-r
   git checkout backup/before-folder-restructure
   ```

2. **Reset to original state:**
   ```bash
   # If needed, reset to exact backup state
   git reset --hard backup/before-folder-restructure
   ```

3. **Restore working directory:**
   ```bash
   # Clean any untracked files if needed
   git clean -fd
   ```

### Validation Steps After Rollback
1. Test function app startup: `func start`
2. Run test suite: `python -m pytest`
3. Verify API endpoints respond correctly
4. Check CI/CD pipeline functionality

## File Movement Documentation

### Files to be Moved (SFDC Service)
- `test_*.py` files → `tests/unit/` or `tests/integration/`
- Utility scripts → `scripts/`
- Documentation files → `docs/`

### Files to be Moved (DB Service)
- `test_*.py` files → `tests/unit/shared/`
- `run_integration_tests.py` → `tests/integration/`
- `*.md` documentation → `docs/`
- Example files → `examples/`

## Risk Assessment

### Low Risk Areas
- Current src/ structure is well-organized
- Main application files are properly placed
- Configuration and infrastructure are good

### Medium Risk Areas
- Test file movements (import statements need updates)
- Utility script movements (references need updates)

### High Risk Areas
- None identified - structure is already well-organized

## Success Criteria

### Immediate Validation
- [ ] All tests pass after reorganization
- [ ] Function apps start successfully
- [ ] No broken imports
- [ ] API endpoints respond correctly

### Long-term Goals
- [ ] Clean root directories
- [ ] Consistent test organization
- [ ] Proper separation of concerns
- [ ] Maintainable structure

---

**Note:** This documentation serves as a comprehensive backup and reference for the folder structure maintenance task. The backup branches contain the exact state before any changes were made.