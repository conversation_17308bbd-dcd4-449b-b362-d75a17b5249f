#!/usr/bin/env python3
"""
Diagnostic script for Azure AD authentication issues
This script helps diagnose authentication problems in the database service
"""

import os
import sys
import logging
import requests
import jwt
from shared.common import is_local_dev
from shared.auth_utils import decode_azure_ad_token, get_current_user
from shared.config import get_azure_ad_config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def diagnose_environment():
    """Diagnose environment detection"""
    print("=== Environment Diagnosis ===")
    print(f"is_local_dev(): {is_local_dev()}")
    
    # Check key environment variables
    azure_vars = [
        'WEBSITE_SITE_NAME',
        'WEBSITE_INSTANCE_ID', 
        'FUNCTIONS_WORKER_RUNTIME',
        'FUNCTIONS_WORKER_RUNTIME_VERSION',
        'WEBSITE_HOSTNAME',
        'IS_LOCAL_DEV',
        'AZURE_FUNCTIONS_ENVIRONMENT'
    ]
    
    print("\nEnvironment Variables:")
    for var in azure_vars:
        value = os.environ.get(var, 'NOT SET')
        print(f"  {var}: {value}")
    
    print()

def diagnose_azure_ad_config():
    """Diagnose Azure AD configuration"""
    print("=== Azure AD Configuration Diagnosis ===")
    
    try:
        config = get_azure_ad_config()
        print("Azure AD Configuration:")
        for key, value in config.items():
            if 'secret' in key.lower():
                print(f"  {key}: {'***REDACTED***' if value else 'NOT SET'}")
            else:
                print(f"  {key}: {value}")
    except Exception as e:
        print(f"Error getting Azure AD config: {e}")
    
    print()

def test_jwt_validation():
    """Test JWT validation with a sample token"""
    print("=== JWT Validation Test ===")
    
    # Check if we have a test token
    test_token = os.environ.get('TEST_AZURE_AD_TOKEN')
    if not test_token:
        print("No TEST_AZURE_AD_TOKEN environment variable found")
        print("To test JWT validation, set TEST_AZURE_AD_TOKEN environment variable")
        return
    
    print("Testing Azure AD token validation...")
    try:
        payload = decode_azure_ad_token(test_token)
        if payload:
            print("SUCCESS: Token validation successful!")
            print(f"Token payload keys: {list(payload.keys())}")
            print(f"Token client ID: {payload.get('appid', payload.get('aud'))}")
            print(f"Token tenant ID: {payload.get('tid')}")
        else:
            print("ERROR: Token validation failed")
    except Exception as e:
        print(f"ERROR: Token validation error: {e}")
    
    print()

def test_jwks_endpoint():
    """Test Azure AD JWKS endpoint"""
    print("=== JWKS Endpoint Test ===")
    
    tenant_id = os.environ.get("AZURE_AD_TENANT_ID", "41b676db-bf6f-46ae-a354-a83a1362533f")
    jwks_url = f"https://login.microsoftonline.com/{tenant_id}/discovery/v2.0/keys"
    
    print(f"Testing JWKS endpoint: {jwks_url}")
    
    try:
        response = requests.get(jwks_url, timeout=10)
        response.raise_for_status()
        jwks = response.json()
        
        print("SUCCESS: JWKS endpoint accessible")
        print(f"Available keys: {len(jwks.get('keys', []))}")
        
        for key in jwks.get('keys', []):
            print(f"  Key ID: {key.get('kid')}")
            print(f"  Algorithm: {key.get('alg')}")
            print(f"  Use: {key.get('use')}")
            
    except Exception as e:
        print(f"ERROR: JWKS endpoint error: {e}")
    
    print()

def main():
    """Run all diagnostic tests"""
    print("Azure AD Authentication Diagnostic Tool")
    print("=" * 50)
    
    diagnose_environment()
    diagnose_azure_ad_config()
    test_jwks_endpoint()
    test_jwt_validation()
    
    print("=== Recommendations ===")
    print("1. Check that Azure AD tenant ID is correct")
    print("2. Verify client ID matches your Azure AD app registration")
    print("3. Ensure the database service has network access to Azure AD")
    print("4. Check that the token is not expired")
    print("5. Verify the token audience matches your app registration")

if __name__ == "__main__":
    # Add the shared directory to the path
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    main()