#!/usr/bin/env python3
"""
Test script to verify Azure AD authentication configuration
"""

import os
import sys
import requests
import json
from urllib.parse import u<PERSON><PERSON><PERSON>

def test_azure_ad_config():
    """Test Azure AD configuration"""
    print("Testing Azure AD Configuration...")
    
    # Get configuration
    client_id = os.environ.get('AZURE_AD_CLIENT_ID')
    tenant_id = os.environ.get('AZURE_AD_TENANT_ID')
    client_secret = os.environ.get('AZURE_AD_CLIENT_SECRET')
    
    print(f"Client ID: {client_id}")
    print(f"Tenant ID: {tenant_id}")
    print(f"Client Secret: {'*' * len(client_secret) if client_secret else 'NOT SET'}")
    
    if not all([client_id, tenant_id, client_secret]):
        print("❌ Missing required environment variables")
        return False
    
    # Test token endpoint
    token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
    
    # Try to get a client credentials token (for testing purposes)
    token_data = {
        'grant_type': 'client_credentials',
        'client_id': client_id,
        'client_secret': client_secret,
        'scope': 'https://graph.microsoft.com/.default'
    }
    
    try:
        print(f"\nTesting token endpoint: {token_url}")
        response = requests.post(token_url, data=token_data)
        
        if response.status_code == 200:
            print("✅ Client secret is valid!")
            return True
        else:
            print(f"❌ Token request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing authentication: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_azure_ad_config()
    sys.exit(0 if success else 1) 