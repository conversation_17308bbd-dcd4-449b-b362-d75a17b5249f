"""
Database Migration Testing Script

This script tests all the migrated database endpoints to ensure they work correctly
with both local (Azurite) and production (SQL) storage.

Usage:
    python test_migration.py [--environment local|production] [--endpoint all|specific]
"""

import requests
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List
import argparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseMigrationTester:
    """Test suite for database migration validation"""
    
    def __init__(self, base_url: str = "http://localhost:7072/api"):
        """Initialize the tester with base URL"""
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'DatabaseMigrationTester/1.0.0'
        })
        
        # Test data storage
        self.test_data = {}
        
        logger.info(f"Initialized tester with base URL: {self.base_url}")
    
    def make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict[str, Any]:
        """Make HTTP request to the database service"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                timeout=30
            )
            
            logger.info(f"{method} {url} -> {response.status_code}")
            
            if response.status_code >= 200 and response.status_code < 300:
                return response.json()
            else:
                logger.error(f"Request failed: {response.status_code} - {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"Request error: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def test_service_info(self) -> bool:
        """Test the service info endpoint"""
        logger.info("Testing service info endpoint...")
        
        response = self.make_request('GET', '/')
        
        if response.get('success'):
            logger.info("✅ Service info endpoint working")
            logger.info(f"Service: {response.get('service')}")
            logger.info(f"Version: {response.get('version')}")
            return True
        else:
            logger.error("❌ Service info endpoint failed")
            return False
    
    def test_account_operations(self) -> bool:
        """Test account CRUD operations"""
        logger.info("Testing account operations...")
        
        # Create account
        account_data = {
            "name": f"Test Account {uuid.uuid4().hex[:8]}",
            "description": "Test account for migration validation",
            "contact_email": "<EMAIL>",
            "is_active": True
        }
        
        response = self.make_request('POST', 'accounts', data=account_data)
        
        if not response.get('success'):
            logger.error("❌ Account creation failed")
            return False
        
        account_id = response.get('data', {}).get('account_id')
        if not account_id:
            logger.error("❌ Account creation didn't return account ID")
            return False
        
        self.test_data['account_id'] = account_id
        logger.info(f"✅ Account created: {account_id}")
        
        # Get account
        response = self.make_request('GET', f'accounts/{account_id}')
        
        if not response.get('success'):
            logger.error("❌ Account retrieval failed")
            return False
        
        logger.info("✅ Account retrieval working")
        
        # Update account
        update_data = {"description": "Updated test account"}
        response = self.make_request('PUT', f'accounts/{account_id}', data=update_data)
        
        if not response.get('success'):
            logger.error("❌ Account update failed")
            return False
        
        logger.info("✅ Account update working")
        
        # List accounts
        response = self.make_request('GET', 'accounts')
        
        if not response.get('success'):
            logger.error("❌ Account listing failed")
            return False
        
        logger.info("✅ Account listing working")
        
        return True
    
    def test_integration_operations(self) -> bool:
        """Test integration CRUD operations"""
        logger.info("Testing integration operations...")
        
        # Create integration
        integration_data = {
            "name": f"Test Integration {uuid.uuid4().hex[:8]}",
            "tenant_url": "https://test.salesforce.com",
            "integration_type": "Salesforce",
            "description": "Test integration for migration validation",
            "environment": "sandbox",
            "user_email": "<EMAIL>",
            "user_id": "test-user-123",
            "account_id": self.test_data.get('account_id'),
            "is_active": True
        }
        
        response = self.make_request('POST', 'integrations', data=integration_data)
        
        if not response.get('success'):
            logger.error("❌ Integration creation failed")
            return False
        
        integration_id = response.get('data', {}).get('integration_id')
        if not integration_id:
            logger.error("❌ Integration creation didn't return integration ID")
            return False
        
        self.test_data['integration_id'] = integration_id
        logger.info(f"✅ Integration created: {integration_id}")
        
        # Get integration
        response = self.make_request('GET', f'integrations/{integration_id}')
        
        if not response.get('success'):
            logger.error("❌ Integration retrieval failed")
            return False
        
        logger.info("✅ Integration retrieval working")
        
        # List integrations
        response = self.make_request('GET', 'integrations')
        
        if not response.get('success'):
            logger.error("❌ Integration listing failed")
            return False
        
        logger.info("✅ Integration listing working")
        
        return True
    
    def test_task_operations(self) -> bool:
        """Test task management operations"""
        logger.info("Testing task operations...")
        
        # Create task
        task_data = {
            "task_type": "health_check",
            "org_id": self.test_data.get('integration_id', 'test-org'),
            "user_id": "test-user-123",
            "params": {"test": True},
            "priority": "medium",
            "message": "Test task for migration validation"
        }
        
        response = self.make_request('POST', 'tasks', data=task_data)
        
        if not response.get('success'):
            logger.error("❌ Task creation failed")
            return False
        
        task_id = response.get('data', {}).get('task_id')
        if not task_id:
            logger.error("❌ Task creation didn't return task ID")
            return False
        
        self.test_data['task_id'] = task_id
        logger.info(f"✅ Task created: {task_id}")
        
        # Get task
        response = self.make_request('GET', f'tasks/{task_id}')
        
        if not response.get('success'):
            logger.error("❌ Task retrieval failed")
            return False
        
        logger.info("✅ Task retrieval working")
        
        # Update task status
        status_data = {
            "status": "running",
            "progress": 50,
            "message": "Task is running"
        }
        
        response = self.make_request('PUT', f'tasks/{task_id}/status', data=status_data)
        
        if not response.get('success'):
            logger.error("❌ Task status update failed")
            return False
        
        logger.info("✅ Task status update working")
        
        return True
    
    def test_security_operations(self) -> bool:
        """Test security data operations"""
        logger.info("Testing security operations...")
        
        # Test health check data
        health_check_data = {
            "org_id": 1,
            "execution_log_id": 1,
            "risks_data": [
                {
                    "risk_type": "high",
                    "setting": "test_setting",
                    "setting_group": "test_group",
                    "org_value": "test_value",
                    "standard_value": "standard_value"
                }
            ]
        }
        
        response = self.make_request('POST', 'security/health-checks', data=health_check_data)
        
        if not response.get('success'):
            logger.error("❌ Security health check data storage failed")
            return False
        
        logger.info("✅ Security health check data storage working")
        
        # Get health check data
        response = self.make_request('GET', 'security/health-checks', params={"org_id": 1})
        
        if not response.get('success'):
            logger.error("❌ Security health check data retrieval failed")
            return False
        
        logger.info("✅ Security health check data retrieval working")
        
        return True
    
    def run_all_tests(self) -> bool:
        """Run all migration tests"""
        logger.info("🚀 Starting database migration validation tests...")
        
        tests = [
            ("Service Info", self.test_service_info),
            ("Account Operations", self.test_account_operations),
            ("Integration Operations", self.test_integration_operations),
            ("Task Operations", self.test_task_operations),
            ("Security Operations", self.test_security_operations)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n--- Running {test_name} Tests ---")
            try:
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} tests passed")
                else:
                    logger.error(f"❌ {test_name} tests failed")
            except Exception as e:
                logger.error(f"❌ {test_name} tests failed with exception: {str(e)}")
        
        logger.info(f"\n🏁 Test Results: {passed}/{total} test suites passed")
        
        if passed == total:
            logger.info("🎉 All tests passed! Database migration is working correctly.")
            return True
        else:
            logger.error("💥 Some tests failed. Please check the logs above.")
            return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Test database migration')
    parser.add_argument('--environment', choices=['local', 'production'], default='local',
                       help='Environment to test (default: local)')
    parser.add_argument('--base-url', default='http://localhost:7072/api',
                       help='Base URL for the database service')
    
    args = parser.parse_args()
    
    # Set base URL based on environment
    if args.environment == 'production':
        base_url = 'https://atomsec-func-db-r.azurewebsites.net/api'
    else:
        base_url = args.base_url
    
    logger.info(f"Testing {args.environment} environment at {base_url}")
    
    # Run tests
    tester = DatabaseMigrationTester(base_url)
    success = tester.run_all_tests()
    
    exit(0 if success else 1)


if __name__ == "__main__":
    main()
