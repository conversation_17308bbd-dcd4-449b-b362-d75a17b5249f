#!/bin/bash

# Azure AD Role Management Script
# Lists, assigns, and removes role assignments for AtomSec

set -e

echo "🔐 AtomSec Role Management"
echo "========================="

# Check if Azure CLI is ready
if ! az account show &> /dev/null; then
    echo "❌ You are not logged into Azure CLI. Please run: az login"
    exit 1
fi

# Find AtomSec application
echo "🔍 Finding AtomSec application..."

APP_NAMES=("AtomSec" "atomsec" "AtomSec Function App" "atomsec-func" "atomsec-db")
APP_ID=""

for app_name in "${APP_NAMES[@]}"; do
    result=$(az ad app list --display-name "$app_name" --query "[0].appId" -o tsv 2>/dev/null || echo "")
    
    if [ -n "$result" ] && [ "$result" != "null" ]; then
        APP_ID="$result"
        echo "✅ Found application: $app_name ($APP_ID)"
        break
    fi
done

# Try known client IDs if not found by name
if [ -z "$APP_ID" ]; then
    KNOWN_CLIENT_IDS=("2d313c1a-d62d-492c-869e-cf8cb9258204" "82e79715-7451-4680-bd1c-53453bfd45ea")
    
    for client_id in "${KNOWN_CLIENT_IDS[@]}"; do
        if az ad app show --id "$client_id" &> /dev/null; then
            APP_ID="$client_id"
            echo "✅ Found application by client ID: $APP_ID"
            break
        fi
    done
fi

if [ -z "$APP_ID" ]; then
    echo "❌ Could not find AtomSec application. Please run create-azure-ad-roles.sh first."
    exit 1
fi

# Get service principal
SERVICE_PRINCIPAL_ID=$(az ad sp list --filter "appId eq '$APP_ID'" --query "[0].id" -o tsv)

if [ -z "$SERVICE_PRINCIPAL_ID" ]; then
    echo "❌ Service principal not found. Please run create-azure-ad-roles.sh first."
    exit 1
fi

# Function to show menu
show_menu() {
    echo ""
    echo "🎯 Choose an action:"
    echo "=================="
    echo "1. List all available roles"
    echo "2. List all role assignments" 
    echo "3. Assign role to user"
    echo "4. Remove role from user"
    echo "5. Show user's current roles"
    echo "6. Exit"
    echo ""
}

# Function to list available roles
list_roles() {
    echo ""
    echo "📋 Available AtomSec Roles:"
    echo "=========================="
    az ad sp show --id "$SERVICE_PRINCIPAL_ID" --query "appRoles[].{Role:value,Name:displayName,Description:description,Permissions:description}" -o table
}

# Function to list all role assignments
list_assignments() {
    echo ""
    echo "👥 Current Role Assignments:"
    echo "==========================="
    
    # Get all role assignments for this service principal
    assignments=$(az ad app role assignment list --resource-service-principal-id "$SERVICE_PRINCIPAL_ID" --query "[].{User:userDisplayName,Email:userPrincipalName,Role:roleDisplayName,AssignmentId:id}" -o json)
    
    if [ "$(echo "$assignments" | jq length)" -eq 0 ]; then
        echo "ℹ️  No role assignments found"
    else
        echo "$assignments" | jq -r '.[] | "\(.Email) → \(.Role)"' | sort
        echo ""
        echo "Total assignments: $(echo "$assignments" | jq length)"
    fi
}

# Function to assign role
assign_role() {
    echo ""
    read -p "Enter user email: " user_email
    
    if [ -z "$user_email" ]; then
        echo "❌ User email is required"
        return
    fi
    
    echo ""
    echo "Available roles:"
    echo "1. atomsec.viewer      - View only"
    echo "2. atomsec.user        - Read/write"  
    echo "3. atomsec.admin       - Full access (can delete)"
    echo "4. atomsec.system_admin - System admin"
    echo ""
    read -p "Choose role (1-4): " role_choice
    
    case $role_choice in
        1) role_value="atomsec.viewer" ;;
        2) role_value="atomsec.user" ;;
        3) role_value="atomsec.admin" ;;
        4) role_value="atomsec.system_admin" ;;
        *) echo "❌ Invalid choice"; return ;;
    esac
    
    echo ""
    echo "🎯 Assigning $role_value to $user_email..."
    
    # Call the assign script
    ./assign-user-role.sh "$user_email" "$role_value"
}

# Function to remove role
remove_role() {
    echo ""
    read -p "Enter user email: " user_email
    
    if [ -z "$user_email" ]; then
        echo "❌ User email is required"
        return
    fi
    
    # Get user object ID
    user_object_id=$(az ad user show --id "$user_email" --query "id" -o tsv 2>/dev/null || echo "")
    
    if [ -z "$user_object_id" ]; then
        echo "❌ User not found: $user_email"
        return
    fi
    
    # Get user's current assignments
    assignments=$(az ad app role assignment list --assignee "$user_object_id" --resource-service-principal-id "$SERVICE_PRINCIPAL_ID" --query "[].{id:id,roleDisplayName:roleDisplayName}" -o json)
    
    if [ "$(echo "$assignments" | jq length)" -eq 0 ]; then
        echo "ℹ️  User has no role assignments"
        return
    fi
    
    echo ""
    echo "Current assignments for $user_email:"
    echo "$assignments" | jq -r '.[] | "\(.roleDisplayName) (\(.id))"' | nl
    
    echo ""
    read -p "Enter assignment number to remove: " assignment_num
    
    assignment_id=$(echo "$assignments" | jq -r ".[$((assignment_num-1))].id")
    
    if [ "$assignment_id" = "null" ] || [ -z "$assignment_id" ]; then
        echo "❌ Invalid assignment number"
        return
    fi
    
    role_name=$(echo "$assignments" | jq -r ".[$((assignment_num-1))].roleDisplayName")
    
    echo ""
    echo "🗑️  Removing $role_name from $user_email..."
    
    az ad app role assignment delete --id "$assignment_id"
    
    echo "✅ Role removed successfully!"
}

# Function to show user roles
show_user_roles() {
    echo ""
    read -p "Enter user email: " user_email
    
    if [ -z "$user_email" ]; then
        echo "❌ User email is required"
        return
    fi
    
    # Get user object ID
    user_object_id=$(az ad user show --id "$user_email" --query "id" -o tsv 2>/dev/null || echo "")
    
    if [ -z "$user_object_id" ]; then
        echo "❌ User not found: $user_email"
        return
    fi
    
    echo ""
    echo "👤 Roles for $user_email:"
    echo "========================"
    
    assignments=$(az ad app role assignment list --assignee "$user_object_id" --resource-service-principal-id "$SERVICE_PRINCIPAL_ID" --query "[].{Role:roleDisplayName,Value:roleId,AssignedDate:createdDateTime}" -o json)
    
    if [ "$(echo "$assignments" | jq length)" -eq 0 ]; then
        echo "ℹ️  User has no AtomSec role assignments"
        echo ""
        echo "💡 To assign a role, use option 3 from the main menu"
    else
        echo "$assignments" | jq -r '.[] | "✅ \(.Role)"'
        echo ""
        echo "🔍 What this user can do:"
        
        # Check capabilities based on roles
        roles=$(echo "$assignments" | jq -r '.[].Role')
        
        can_read=false
        can_write=false  
        can_delete=false
        is_admin=false
        
        while IFS= read -r role; do
            case "$role" in
                *"Viewer"*) can_read=true ;;
                *"User"*) can_read=true; can_write=true ;;
                *"Admin"*) can_read=true; can_write=true; can_delete=true; is_admin=true ;;
                *"System Admin"*) can_read=true; can_write=true; can_delete=true; is_admin=true ;;
            esac
        done <<< "$roles"
        
        echo "   📖 Read integrations: $([ "$can_read" = true ] && echo "✅ Yes" || echo "❌ No")"
        echo "   ✏️  Write integrations: $([ "$can_write" = true ] && echo "✅ Yes" || echo "❌ No")"
        echo "   🗑️  Delete integrations: $([ "$can_delete" = true ] && echo "✅ Yes" || echo "❌ No")"
        echo "   🔧 Admin functions: $([ "$is_admin" = true ] && echo "✅ Yes" || echo "❌ No")"
    fi
}

# Main menu loop
while true; do
    show_menu
    read -p "Enter your choice (1-6): " choice
    
    case $choice in
        1) list_roles ;;
        2) list_assignments ;;
        3) assign_role ;;
        4) remove_role ;;
        5) show_user_roles ;;
        6) echo "👋 Goodbye!"; exit 0 ;;
        *) echo "❌ Invalid choice. Please enter 1-6." ;;
    esac
done
