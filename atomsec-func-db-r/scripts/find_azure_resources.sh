#!/bin/bash

# Azure Resource Discovery Script
# This script helps find Azure resources and their connection strings

set -e

echo "=== Azure Resource Discovery Script ==="
echo "Finding resources in your Azure subscription..."

# Check if Azure CLI is available
if ! command -v az &> /dev/null; then
    echo "Error: Azure CLI is not installed or not in PATH"
    exit 1
fi

# Check if logged in
if ! az account show &> /dev/null; then
    echo "Error: Not logged into Azure CLI"
    echo "Please run: az login"
    exit 1
fi

echo "Current subscription:"
az account show --query "{name:name, id:id}" -o table

echo ""
echo "=== Finding Storage Accounts ==="
echo "Storage accounts in your subscription:"
az storage account list --query "[].{name:name, resourceGroup:resourceGroup, location:location}" -o table

echo ""
echo "=== Finding Service Bus Namespaces ==="
echo "Service Bus namespaces in your subscription:"
az servicebus namespace list --query "[].{name:name, resourceGroup:resourceGroup, location:location}" -o table

echo ""
echo "=== Finding Key Vaults ==="
echo "Key Vaults in your subscription:"
az keyvault list --query "[].{name:name, resourceGroup:resourceGroup, location:location}" -o table

echo ""
echo "=== Finding Function Apps ==="
echo "Function Apps in your subscription:"
az functionapp list --query "[].{name:name, resourceGroup:resourceGroup, location:location}" -o table

echo ""
echo "=== Getting Connection Strings ==="

# Get storage account connection strings
echo "Storage Account Connection Strings:"
STORAGE_ACCOUNTS=$(az storage account list --query "[].name" -o tsv)
for account in $STORAGE_ACCOUNTS; do
    echo "Storage Account: $account"
    RESOURCE_GROUP=$(az storage account show --name $account --query "resourceGroup" -o tsv)
    echo "Resource Group: $RESOURCE_GROUP"
    echo "Connection String:"
    az storage account show-connection-string --name $account --resource-group $RESOURCE_GROUP --query "connectionString" -o tsv
    echo ""
done

# Get Service Bus connection strings
echo "Service Bus Connection Strings:"
SERVICE_BUS_NAMESPACES=$(az servicebus namespace list --query "[].name" -o tsv)
for namespace in $SERVICE_BUS_NAMESPACES; do
    echo "Service Bus Namespace: $namespace"
    RESOURCE_GROUP=$(az servicebus namespace show --name $namespace --query "resourceGroup" -o tsv)
    echo "Resource Group: $RESOURCE_GROUP"
    echo "Connection String:"
    az servicebus namespace authorization-rule keys list --namespace-name $namespace --resource-group $RESOURCE_GROUP --name RootManageSharedAccessKey --query "primaryConnectionString" -o tsv
    echo ""
done

# Get Key Vault URLs
echo "Key Vault URLs:"
KEY_VAULTS=$(az keyvault list --query "[].name" -o tsv)
for vault in $KEY_VAULTS; do
    echo "Key Vault: $vault"
    RESOURCE_GROUP=$(az keyvault show --name $vault --query "resourceGroup" -o tsv)
    echo "Resource Group: $RESOURCE_GROUP"
    echo "URL: https://$vault.vault.azure.net/"
    echo ""
done

echo "=== Resource Discovery Complete ==="
echo ""
echo "Next steps:"
echo "1. Copy the connection strings above"
echo "2. Update your pipeline configuration with these values"
echo "3. Or set them as environment variables before running the fix script" 