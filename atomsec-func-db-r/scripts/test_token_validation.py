#!/usr/bin/env python3
"""
Test script to verify JWT token validation with the updated authentication logic
"""

import sys
import os
import json

# Add the parent directory to sys.path to import shared modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_token_validation():
    """Test token validation with the new authentication logic"""
    print("Testing JWT token validation...")
    
    # Your token from the request
    token = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    
    try:
        # Import authentication modules
        from shared.auth_utils import decode_token, extract_user_from_payload
        
        print("Step 1: Decoding token...")
        payload = decode_token(token)
        
        if payload:
            print("✅ Token decoded successfully!")
            print(f"   Token type: {'App-only' if payload.get('appidacr') == '1' else 'User token'}")
            print(f"   Client ID: {payload.get('appid')}")
            print(f"   Tenant ID: {payload.get('tid')}")
            print(f"   Object ID: {payload.get('oid')}")
            
            print("\nStep 2: Extracting user information...")
            user_info = extract_user_from_payload(payload)
            
            if user_info:
                print("✅ User information extracted successfully!")
                print(f"   Email: {user_info.get('email')}")
                print(f"   User ID: {user_info.get('user_id')}")
                print(f"   Name: {user_info.get('name')}")
                print(f"   Is Application: {user_info.get('is_application')}")
                print(f"   Roles: {user_info.get('roles')}")
                print(f"   Is Admin: {user_info.get('isAdmin')}")
                
                print("\n🎉 Authentication should now work!")
                return True
            else:
                print("❌ Failed to extract user information")
                return False
        else:
            print("❌ Failed to decode token")
            return False
            
    except Exception as e:
        print(f"❌ Error testing token validation: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_mock_request():
    """Test with a mock Azure Functions request"""
    print("\n" + "="*50)
    print("Testing with mock Azure Functions request...")
    
    try:
        # Create a mock request object
        class MockHttpRequest:
            def __init__(self, token):
                self.headers = {
                    'Authorization': f'Bearer {token}'
                }
                
        # Your token
        token = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        
        mock_req = MockHttpRequest(token)
        
        # Import and test the get_current_user function
        from shared.auth_utils import get_current_user
        
        print("Testing get_current_user function...")
        user = get_current_user(mock_req)
        
        if user:
            print("✅ get_current_user worked successfully!")
            print(f"   Returned user: {json.dumps(user, indent=2)}")
            print("\n🎉 Your APIM request should now work!")
            return True
        else:
            print("❌ get_current_user returned None")
            return False
            
    except Exception as e:
        print(f"❌ Error testing mock request: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("JWT Token Validation Test")
    print("=" * 50)
    
    # Set environment variables for testing
    os.environ["AZURE_AD_TENANT_ID"] = "41b676db-bf6f-46ae-a354-a83a1362533f"
    os.environ["AZURE_AD_CLIENT_ID"] = "2d313c1a-d62d-492c-869e-cf8cb9258204"
    os.environ["AZURE_AD_ADDITIONAL_CLIENT_IDS"] = "82e79715-7451-4680-bd1c-53453bfd45ea"
    
    success1 = test_token_validation()
    success2 = test_mock_request()
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"Token validation: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"Mock request test: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎉 All tests passed! Your authentication should now work.")
        print("\nNext steps:")
        print("1. Deploy the updated authentication logic")
        print("2. Set AZURE_AD_ADDITIONAL_CLIENT_IDS environment variable with value: '82e79715-7451-4680-bd1c-53453bfd45ea'")
        print("3. Test your APIM request again")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
