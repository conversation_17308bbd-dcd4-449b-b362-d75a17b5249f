#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to test JWT configuration in production environment.
This script simulates the production environment and tests JWT token creation/validation.
"""

import os
import sys
import requests
import json

def test_production_jwt():
    """Test JWT configuration in production"""
    print("🔍 Testing JWT configuration in production...")
    
    # Test the /auth/azure/me endpoint with a valid token
    # This will help us understand if the JWT configuration is working
    
    # First, let's test if the Function App is accessible
    base_url = "https://apim-atomsec-dev.azure-api.net/db/v1"
    
    print(f"Testing endpoint: {base_url}/auth/azure/me")
    
    try:
        # Test without authentication first
        response = requests.get(f"{base_url}/auth/azure/me", timeout=10)
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text[:200]}...")
        
        if response.status_code == 401:
            print("✅ Expected 401 - authentication required")
            print("This confirms the endpoint is working and requires authentication")
        else:
            print(f"⚠️ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing endpoint: {str(e)}")
    
    print()
    print("📋 Next Steps:")
    print("1. User needs to clear browser storage and re-authenticate")
    print("2. This will get a new JWT token from production environment")
    print("3. The new token will work with the production backend")

if __name__ == "__main__":
    test_production_jwt()
