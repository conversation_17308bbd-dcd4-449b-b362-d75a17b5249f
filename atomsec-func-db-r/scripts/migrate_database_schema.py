"""
Database Schema Migration Script

This script migrates the database schema from the old monolithic architecture (dev branch)
to the new microservices architecture (dev-db branch).

It adds all missing tables and columns required for:
- Task management and execution logging
- Policy and rule management
- PMD scan results and analysis
- Security analysis and reporting
- Integration management

Usage:
    python scripts/migrate_database_schema.py
"""

import logging
import sys
import os
from datetime import datetime

# Add the parent directory to the path so we can import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.data_access import SqlDatabaseRepository, TableStorageRepository
from shared.common import is_local_dev
from shared.azure_services import get_secret

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatabaseSchemaMigrator:
    """Handles database schema migration from dev to dev-db branch"""
    
    def __init__(self):
        """Initialize the migrator"""
        self.sql_repo = None
        self.table_repos = {}
        self._initialize_repositories()
    
    def _initialize_repositories(self):
        """Initialize database repositories"""
        try:
            if not is_local_dev():
                # Initialize SQL repository for production
                self.sql_repo = SqlDatabaseRepository("migration")
                logger.info("Initialized SQL repository for production migration")
            else:
                logger.info("Local development mode - will create Table Storage repositories as needed")
        except Exception as e:
            logger.error(f"Error initializing repositories: {str(e)}")
    
    def migrate_execution_log_table(self) -> bool:
        """Create App_ExecutionLog table if it doesn't exist"""
        logger.info("Migrating App_ExecutionLog table...")
        
        if is_local_dev():
            # For local development, we already have execution log service
            logger.info("App_ExecutionLog: Using existing execution log service for local development")
            return True
        
        try:
            query = """
            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'App_ExecutionLog')
            BEGIN
                CREATE TABLE [dbo].[App_ExecutionLog] (
                    [Id] INT IDENTITY(1,1) PRIMARY KEY,
                    [OrgId] INT NOT NULL,
                    [ExecutionType] VARCHAR(100) NOT NULL,
                    [Status] VARCHAR(50) NOT NULL DEFAULT 'Pending',
                    [StartTime] DATETIME NOT NULL DEFAULT GETDATE(),
                    [EndTime] DATETIME NULL,
                    [ExecutedBy] INT NOT NULL,
                    [CreatedAt] DATETIME NOT NULL DEFAULT GETDATE(),
                    [UpdatedAt] DATETIME NOT NULL DEFAULT GETDATE(),
                    [ErrorMessage] TEXT NULL,
                    [Result] TEXT NULL
                );
                
                -- Create indexes for better performance
                CREATE INDEX IX_App_ExecutionLog_OrgId ON [dbo].[App_ExecutionLog] ([OrgId]);
                CREATE INDEX IX_App_ExecutionLog_Status ON [dbo].[App_ExecutionLog] ([Status]);
                CREATE INDEX IX_App_ExecutionLog_StartTime ON [dbo].[App_ExecutionLog] ([StartTime]);
                
                PRINT 'Created App_ExecutionLog table with indexes';
            END
            ELSE
            BEGIN
                PRINT 'App_ExecutionLog table already exists';
            END
            """
            
            success = self.sql_repo.execute_non_query(query)
            if success:
                logger.info("✅ App_ExecutionLog table migration completed")
                return True
            else:
                logger.error("❌ Failed to migrate App_ExecutionLog table")
                return False
                
        except Exception as e:
            logger.error(f"Error migrating App_ExecutionLog table: {str(e)}")
            return False
    
    def migrate_policy_table(self) -> bool:
        """Create Policy table for security policy management"""
        logger.info("Migrating Policy table...")
        
        if is_local_dev():
            # Create Table Storage repository for local development
            try:
                table_repo = TableStorageRepository("Policy")
                self.table_repos["Policy"] = table_repo
                logger.info("✅ Policy table repository created for local development")
                return True
            except Exception as e:
                logger.error(f"Error creating Policy table repository: {str(e)}")
                return False
        
        try:
            query = """
            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Policy')
            BEGIN
                CREATE TABLE [dbo].[Policy] (
                    [PolicyId] VARCHAR(50) PRIMARY KEY,
                    [Name] VARCHAR(255) NOT NULL,
                    [UserId] VARCHAR(50) NOT NULL,
                    [IntegrationId] VARCHAR(50) NOT NULL,
                    [Description] TEXT NULL,
                    [IsActive] BIT NOT NULL DEFAULT 1,
                    [CreatedAt] DATETIME NOT NULL DEFAULT GETDATE(),
                    [UpdatedAt] DATETIME NOT NULL DEFAULT GETDATE(),
                    [CreatedBy] VARCHAR(50) NULL
                );
                
                -- Create indexes
                CREATE INDEX IX_Policy_IntegrationId ON [dbo].[Policy] ([IntegrationId]);
                CREATE INDEX IX_Policy_UserId ON [dbo].[Policy] ([UserId]);
                CREATE INDEX IX_Policy_IsActive ON [dbo].[Policy] ([IsActive]);
                
                PRINT 'Created Policy table with indexes';
            END
            ELSE
            BEGIN
                PRINT 'Policy table already exists';
            END
            """
            
            success = self.sql_repo.execute_non_query(query)
            if success:
                logger.info("✅ Policy table migration completed")
                return True
            else:
                logger.error("❌ Failed to migrate Policy table")
                return False
                
        except Exception as e:
            logger.error(f"Error migrating Policy table: {str(e)}")
            return False
    
    def migrate_rule_table(self) -> bool:
        """Create Rule table for policy rules"""
        logger.info("Migrating Rule table...")
        
        if is_local_dev():
            # Create Table Storage repository for local development
            try:
                table_repo = TableStorageRepository("Rule")
                self.table_repos["Rule"] = table_repo
                logger.info("✅ Rule table repository created for local development")
                return True
            except Exception as e:
                logger.error(f"Error creating Rule table repository: {str(e)}")
                return False
        
        try:
            query = """
            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Rule')
            BEGIN
                CREATE TABLE [dbo].[Rule] (
                    [RuleId] VARCHAR(50) PRIMARY KEY,
                    [PolicyId] VARCHAR(50) NOT NULL,
                    [TaskType] VARCHAR(100) NOT NULL,
                    [RuleName] VARCHAR(255) NULL,
                    [Description] TEXT NULL,
                    [Enabled] BIT NOT NULL DEFAULT 1,
                    [Priority] INT NOT NULL DEFAULT 1,
                    [CreatedAt] DATETIME NOT NULL DEFAULT GETDATE(),
                    [UpdatedAt] DATETIME NOT NULL DEFAULT GETDATE(),
                    [CreatedBy] VARCHAR(50) NULL,
                    
                    -- Foreign key constraint
                    CONSTRAINT FK_Rule_Policy FOREIGN KEY ([PolicyId]) REFERENCES [dbo].[Policy]([PolicyId])
                );
                
                -- Create indexes
                CREATE INDEX IX_Rule_PolicyId ON [dbo].[Rule] ([PolicyId]);
                CREATE INDEX IX_Rule_TaskType ON [dbo].[Rule] ([TaskType]);
                CREATE INDEX IX_Rule_Enabled ON [dbo].[Rule] ([Enabled]);
                
                PRINT 'Created Rule table with indexes and foreign key';
            END
            ELSE
            BEGIN
                PRINT 'Rule table already exists';
            END
            """
            
            success = self.sql_repo.execute_non_query(query)
            if success:
                logger.info("✅ Rule table migration completed")
                return True
            else:
                logger.error("❌ Failed to migrate Rule table")
                return False
                
        except Exception as e:
            logger.error(f"Error migrating Rule table: {str(e)}")
            return False

    def migrate_pmd_scans_table(self) -> bool:
        """Create PMDScans table for PMD scan results"""
        logger.info("Migrating PMDScans table...")

        if is_local_dev():
            # Create Table Storage repository for local development
            try:
                table_repo = TableStorageRepository("PMDScans")
                self.table_repos["PMDScans"] = table_repo
                logger.info("✅ PMDScans table repository created for local development")
                return True
            except Exception as e:
                logger.error(f"Error creating PMDScans table repository: {str(e)}")
                return False

        try:
            query = """
            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PMDScans')
            BEGIN
                CREATE TABLE [dbo].[PMDScans] (
                    [Id] INT IDENTITY(1,1) PRIMARY KEY,
                    [TaskStatusId] VARCHAR(50) NULL,
                    [IntegrationId] VARCHAR(50) NOT NULL,
                    [TaskId] VARCHAR(50) NULL,
                    [ExecutionLogId] VARCHAR(50) NULL,
                    [BlobPrefix] VARCHAR(255) NULL,
                    [ScanType] VARCHAR(50) NULL,
                    [Language] VARCHAR(50) NULL,
                    [Tool] VARCHAR(50) NULL,
                    [PolicyName] VARCHAR(255) NULL,
                    [RuleName] VARCHAR(255) NULL,
                    [Status] VARCHAR(50) NULL,
                    [Severity] VARCHAR(50) NULL,
                    [FileName] VARCHAR(255) NULL,
                    [FilePath] VARCHAR(500) NULL,
                    [LineNumber] VARCHAR(10) NULL,
                    [Package] VARCHAR(255) NULL,
                    [IssueType] VARCHAR(100) NULL,
                    [IssueCategory] VARCHAR(100) NULL,
                    [IssueDescription] TEXT NULL,
                    [IssuePriority] VARCHAR(50) NULL,
                    [PMDRuleSet] VARCHAR(255) NULL,
                    [PMDProblem] TEXT NULL,
                    [WeaknessType] VARCHAR(100) NULL,
                    [WeaknessTypeDescription] TEXT NULL,
                    [CreatedAt] DATETIME NOT NULL DEFAULT GETDATE()
                );

                -- Create indexes for better performance
                CREATE INDEX IX_PMDScans_IntegrationId ON [dbo].[PMDScans] ([IntegrationId]);
                CREATE INDEX IX_PMDScans_TaskId ON [dbo].[PMDScans] ([TaskId]);
                CREATE INDEX IX_PMDScans_ExecutionLogId ON [dbo].[PMDScans] ([ExecutionLogId]);
                CREATE INDEX IX_PMDScans_Severity ON [dbo].[PMDScans] ([Severity]);
                CREATE INDEX IX_PMDScans_CreatedAt ON [dbo].[PMDScans] ([CreatedAt]);

                PRINT 'Created PMDScans table with indexes';
            END
            ELSE
            BEGIN
                PRINT 'PMDScans table already exists';
            END
            """

            success = self.sql_repo.execute_non_query(query)
            if success:
                logger.info("✅ PMDScans table migration completed")
                return True
            else:
                logger.error("❌ Failed to migrate PMDScans table")
                return False

        except Exception as e:
            logger.error(f"Error migrating PMDScans table: {str(e)}")
            return False

    def migrate_pmd_subtask_table(self) -> bool:
        """Create PMDSubtask table for PMD subtask management"""
        logger.info("Migrating PMDSubtask table...")

        if is_local_dev():
            # Create Table Storage repository for local development
            try:
                table_repo = TableStorageRepository("PMDSubtask")
                self.table_repos["PMDSubtask"] = table_repo
                logger.info("✅ PMDSubtask table repository created for local development")
                return True
            except Exception as e:
                logger.error(f"Error creating PMDSubtask table repository: {str(e)}")
                return False

        try:
            query = """
            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'PMDSubtask')
            BEGIN
                CREATE TABLE [dbo].[PMDSubtask] (
                    [Id] INT IDENTITY(1,1) PRIMARY KEY,
                    [SubtaskId] VARCHAR(50) NOT NULL UNIQUE,
                    [RuleId] VARCHAR(50) NOT NULL,
                    [TaskType] VARCHAR(100) NOT NULL,
                    [SubtaskName] VARCHAR(255) NOT NULL,
                    [SubtaskDescription] TEXT NULL,
                    [Enabled] BIT NOT NULL DEFAULT 1,
                    [Priority] INT NOT NULL DEFAULT 1,
                    [CreatedAt] DATETIME NOT NULL DEFAULT GETDATE(),
                    [UpdatedAt] DATETIME NOT NULL DEFAULT GETDATE(),

                    -- Foreign key constraint
                    CONSTRAINT FK_PMDSubtask_Rule FOREIGN KEY ([RuleId]) REFERENCES [dbo].[Rule]([RuleId])
                );

                -- Create indexes
                CREATE INDEX IX_PMDSubtask_RuleId ON [dbo].[PMDSubtask] ([RuleId]);
                CREATE INDEX IX_PMDSubtask_TaskType ON [dbo].[PMDSubtask] ([TaskType]);
                CREATE INDEX IX_PMDSubtask_Enabled ON [dbo].[PMDSubtask] ([Enabled]);

                PRINT 'Created PMDSubtask table with indexes and foreign key';
            END
            ELSE
            BEGIN
                PRINT 'PMDSubtask table already exists';
            END
            """

            success = self.sql_repo.execute_non_query(query)
            if success:
                logger.info("✅ PMDSubtask table migration completed")
                return True
            else:
                logger.error("❌ Failed to migrate PMDSubtask table")
                return False

        except Exception as e:
            logger.error(f"Error migrating PMDSubtask table: {str(e)}")
            return False


    def migrate_policies_result_table(self) -> bool:
        """Create PoliciesResult table for security analysis results"""
        logger.info("Migrating PoliciesResult table...")

        if is_local_dev():
            # Create Table Storage repository for local development
            try:
                table_repo = TableStorageRepository("PoliciesResult")
                self.table_repos["PoliciesResult"] = table_repo
                logger.info("✅ PoliciesResult table repository created for local development")
                return True
            except Exception as e:
                logger.error(f"Error creating PoliciesResult table repository: {str(e)}")
                return False

        try:
            query = """
            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'App_PoliciesResult')
            BEGIN
                CREATE TABLE [dbo].[App_PoliciesResult] (
                    [Id] INT IDENTITY(1,1) PRIMARY KEY,
                    [OrgPolicyId] VARCHAR(50) NULL,
                    [PolicyId] VARCHAR(50) NULL,
                    [ExecutionLogId] VARCHAR(50) NULL,
                    [OrgValue] TEXT NULL,
                    [OWASPCategory] VARCHAR(100) NULL,
                    [StandardValue] TEXT NULL,
                    [IssueDescription] TEXT NULL,
                    [Recommendations] TEXT NULL,
                    [Severity] VARCHAR(50) NULL,
                    [Weakness] VARCHAR(100) NULL,
                    [IntegrationId] VARCHAR(50) NOT NULL,
                    [Type] VARCHAR(50) NULL,
                    [ProfileName] VARCHAR(255) NULL,
                    [PermissionSetName] VARCHAR(255) NULL,
                    [Setting] VARCHAR(255) NULL,
                    [SettingGroup] VARCHAR(255) NULL,
                    [UserLicense] VARCHAR(255) NULL,
                    [CreatedAt] DATETIME NOT NULL DEFAULT GETDATE(),
                    [UpdatedAt] DATETIME NOT NULL DEFAULT GETDATE()
                );

                -- Create indexes for better performance
                CREATE INDEX IX_App_PoliciesResult_IntegrationId ON [dbo].[App_PoliciesResult] ([IntegrationId]);
                CREATE INDEX IX_App_PoliciesResult_ExecutionLogId ON [dbo].[App_PoliciesResult] ([ExecutionLogId]);
                CREATE INDEX IX_App_PoliciesResult_PolicyId ON [dbo].[App_PoliciesResult] ([PolicyId]);
                CREATE INDEX IX_App_PoliciesResult_Severity ON [dbo].[App_PoliciesResult] ([Severity]);
                CREATE INDEX IX_App_PoliciesResult_CreatedAt ON [dbo].[App_PoliciesResult] ([CreatedAt]);
                CREATE INDEX IX_App_PoliciesResult_ProfileName ON [dbo].[App_PoliciesResult] ([ProfileName]);
                CREATE INDEX IX_App_PoliciesResult_PermissionSetName ON [dbo].[App_PoliciesResult] ([PermissionSetName]);
                CREATE INDEX IX_App_PoliciesResult_Type ON [dbo].[App_PoliciesResult] ([Type]);

                PRINT 'Created App_PoliciesResult table with indexes';
            END
            ELSE
            BEGIN
                -- Check if we need to add missing columns for old monolithic format
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('App_PoliciesResult') AND name = 'SettingGroup')
                BEGIN
                    ALTER TABLE [dbo].[App_PoliciesResult] ADD [SettingGroup] VARCHAR(255) NULL;
                    PRINT 'Added SettingGroup column to App_PoliciesResult table';
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('App_PoliciesResult') AND name = 'UserLicense')
                BEGIN
                    ALTER TABLE [dbo].[App_PoliciesResult] ADD [UserLicense] VARCHAR(255) NULL;
                    PRINT 'Added UserLicense column to App_PoliciesResult table';
                END
                
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('App_PoliciesResult') AND name = 'Setting')
                BEGIN
                    ALTER TABLE [dbo].[App_PoliciesResult] ADD [Setting] VARCHAR(255) NULL;
                    PRINT 'Added Setting column to App_PoliciesResult table';
                END
                
                PRINT 'App_PoliciesResult table already exists - checked for missing columns';
            END
            """

            success = self.sql_repo.execute_non_query(query)
            if success:
                logger.info("✅ App_PoliciesResult table migration completed")
                return True
            else:
                logger.error("❌ Failed to migrate App_PoliciesResult table")
                return False

        except Exception as e:
            logger.error(f"Error migrating App_PoliciesResult table: {str(e)}")
            return False

    def migrate_tasks_table(self) -> bool:
        """Create Tasks table for task management (if not using existing task_status_service)"""
        logger.info("Migrating Tasks table...")

        if is_local_dev():
            # For local development, we already have task status service
            logger.info("Tasks: Using existing task status service for local development")
            return True

        try:
            query = """
            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Tasks')
            BEGIN
                CREATE TABLE [dbo].[Tasks] (
                    [Id] INT IDENTITY(1,1) PRIMARY KEY,
                    [TaskId] VARCHAR(50) NOT NULL UNIQUE,
                    [TaskType] VARCHAR(100) NOT NULL,
                    [OrgId] VARCHAR(50) NOT NULL,
                    [UserId] VARCHAR(50) NOT NULL,
                    [Status] VARCHAR(50) NOT NULL DEFAULT 'pending',
                    [Priority] VARCHAR(20) NOT NULL DEFAULT 'medium',
                    [Progress] INT NOT NULL DEFAULT 0,
                    [Message] TEXT NULL,
                    [Result] TEXT NULL,
                    [CreatedAt] DATETIME NOT NULL DEFAULT GETDATE(),
                    [UpdatedAt] DATETIME NOT NULL DEFAULT GETDATE(),
                    [CompletedAt] DATETIME NULL,
                    [ScheduledTime] DATETIME NULL,
                    [RetryCount] INT NOT NULL DEFAULT 0,
                    [ExecutionLogId] VARCHAR(50) NULL,
                    [Params] TEXT NULL
                );

                -- Create indexes for better performance
                CREATE INDEX IX_Tasks_TaskId ON [dbo].[Tasks] ([TaskId]);
                CREATE INDEX IX_Tasks_OrgId ON [dbo].[Tasks] ([OrgId]);
                CREATE INDEX IX_Tasks_Status ON [dbo].[Tasks] ([Status]);
                CREATE INDEX IX_Tasks_Priority ON [dbo].[Tasks] ([Priority]);
                CREATE INDEX IX_Tasks_CreatedAt ON [dbo].[Tasks] ([CreatedAt]);
                CREATE INDEX IX_Tasks_ExecutionLogId ON [dbo].[Tasks] ([ExecutionLogId]);

                PRINT 'Created Tasks table with indexes';
            END
            ELSE
            BEGIN
                PRINT 'Tasks table already exists';
            END
            """

            success = self.sql_repo.execute_non_query(query)
            if success:
                logger.info("✅ Tasks table migration completed")
                return True
            else:
                logger.error("❌ Failed to migrate Tasks table")
                return False

        except Exception as e:
            logger.error(f"Error migrating Tasks table: {str(e)}")
            return False

    def migrate_profile_assignment_count_table(self) -> bool:
        """Create ProfileAssignmentCount table for profile assignment tracking"""
        logger.info("Migrating ProfileAssignmentCount table...")

        if is_local_dev():
            # Create Table Storage repository for local development
            try:
                table_repo = TableStorageRepository("ProfileAssignmentCount")
                self.table_repos["ProfileAssignmentCount"] = table_repo
                logger.info("✅ ProfileAssignmentCount table repository created for local development")
                return True
            except Exception as e:
                logger.error(f"Error creating ProfileAssignmentCount table repository: {str(e)}")
                return False

        try:
            query = """
            IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'ProfileAssignmentCount')
            BEGIN
                CREATE TABLE [dbo].[ProfileAssignmentCount] (
                    [Id] INT IDENTITY(1,1) PRIMARY KEY,
                    [IntegrationId] VARCHAR(50) NOT NULL,
                    [ExecutionLogId] VARCHAR(50) NULL,
                    [ProfileName] VARCHAR(255) NOT NULL,
                    [ActiveUserCount] INT NOT NULL DEFAULT 0,
                    [TotalUserCount] INT NOT NULL DEFAULT 0,
                    [LastUpdated] DATETIME NOT NULL DEFAULT GETDATE(),
                    [CreatedAt] DATETIME NOT NULL DEFAULT GETDATE()
                );

                -- Create indexes
                CREATE INDEX IX_ProfileAssignmentCount_IntegrationId ON [dbo].[ProfileAssignmentCount] ([IntegrationId]);
                CREATE INDEX IX_ProfileAssignmentCount_ExecutionLogId ON [dbo].[ProfileAssignmentCount] ([ExecutionLogId]);
                CREATE INDEX IX_ProfileAssignmentCount_ProfileName ON [dbo].[ProfileAssignmentCount] ([ProfileName]);
                CREATE INDEX IX_ProfileAssignmentCount_LastUpdated ON [dbo].[ProfileAssignmentCount] ([LastUpdated]);

                PRINT 'Created ProfileAssignmentCount table with indexes';
            END
            ELSE
            BEGIN
                PRINT 'ProfileAssignmentCount table already exists';
            END
            """

            success = self.sql_repo.execute_non_query(query)
            if success:
                logger.info("✅ ProfileAssignmentCount table migration completed")
                return True
            else:
                logger.error("❌ Failed to migrate ProfileAssignmentCount table")
                return False

        except Exception as e:
            logger.error(f"Error migrating ProfileAssignmentCount table: {str(e)}")
            return False


def main():
    """Main migration function"""
    logger.info("🚀 Starting database schema migration...")
    
    migrator = DatabaseSchemaMigrator()
    
    # Track migration results
    migrations = [
        ("App_ExecutionLog", migrator.migrate_execution_log_table),
        ("Policy", migrator.migrate_policy_table),
        ("Rule", migrator.migrate_rule_table),
        ("PMDScans", migrator.migrate_pmd_scans_table),
        ("PMDSubtask", migrator.migrate_pmd_subtask_table),

        ("PoliciesResult", migrator.migrate_policies_result_table),
        ("Tasks", migrator.migrate_tasks_table),
        ("ProfileAssignmentCount", migrator.migrate_profile_assignment_count_table)
    ]
    
    successful_migrations = 0
    total_migrations = len(migrations)
    
    for table_name, migration_func in migrations:
        logger.info(f"\n--- Migrating {table_name} ---")
        try:
            if migration_func():
                successful_migrations += 1
                logger.info(f"✅ {table_name} migration successful")
            else:
                logger.error(f"❌ {table_name} migration failed")
        except Exception as e:
            logger.error(f"❌ {table_name} migration failed with exception: {str(e)}")
    
    # Summary
    logger.info(f"\n📊 Migration Summary:")
    logger.info(f"Successful: {successful_migrations}/{total_migrations}")
    logger.info(f"Failed: {total_migrations - successful_migrations}/{total_migrations}")
    
    if successful_migrations == total_migrations:
        logger.info("🎉 All migrations completed successfully!")
        return 0
    else:
        logger.error("⚠️ Some migrations failed. Please check the logs.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
