#!/usr/bin/env python3
"""
Test script to verify Azure AD role-based permission system
"""

import sys
import os
import json

# Add the parent directory to sys.path to import shared modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_azure_ad_roles():
    """Test Azure AD role-based permission mapping"""
    print("Testing Azure AD Role-Based Permission System...")
    print("=" * 60)
    
    from shared.permissions import (
        get_user_permissions, can_delete_integrations, get_user_role_summary, 
        Permission, require_action_permission, AZURE_AD_ROLE_MAPPING
    )
    from shared.auth_utils import extract_user_from_payload
    
    print(f"\n📋 AZURE AD ROLE MAPPINGS:")
    print("-" * 30)
    for azure_role, internal_role in AZURE_AD_ROLE_MAPPING.items():
        print(f"   '{azure_role}' → {internal_role.value}")
    
    # Test different Azure AD role scenarios
    test_users = [
        {
            "name": "No Azure AD Roles",
            "payload": {
                "upn": "<EMAIL>",
                "email": "<EMAIL>",
                "name": "No Roles User"
                # No 'roles' field - should get default USER role
            }
        },
        {
            "name": "AtomSec Viewer Role",
            "payload": {
                "upn": "<EMAIL>", 
                "email": "<EMAIL>",
                "name": "Viewer User",
                "roles": ["atomsec.viewer"]  # Custom app role
            }
        },
        {
            "name": "AtomSec User Role",
            "payload": {
                "upn": "<EMAIL>",
                "email": "<EMAIL>", 
                "name": "Regular User",
                "roles": ["atomsec.user"]  # Custom app role
            }
        },
        {
            "name": "AtomSec Admin Role",
            "payload": {
                "upn": "<EMAIL>",
                "email": "<EMAIL>",
                "name": "Admin User", 
                "roles": ["atomsec.admin"]  # Custom app role - CAN DELETE!
            }
        },
        {
            "name": "AtomSec System Admin Role",
            "payload": {
                "upn": "<EMAIL>",
                "email": "<EMAIL>",
                "name": "System Admin",
                "roles": ["atomsec.system_admin"]  # Custom app role - FULL ACCESS!
            }
        },
        {
            "name": "Legacy Admin Role",
            "payload": {
                "upn": "<EMAIL>",
                "email": "<EMAIL>",
                "name": "Legacy Admin",
                "roles": ["admin"]  # Legacy role support
            }
        },
        {
            "name": "Azure Built-in Owner Role",
            "payload": {
                "upn": "<EMAIL>",
                "email": "<EMAIL>",
                "name": "Owner User",
                "roles": ["Owner"]  # Azure built-in role
            }
        },
        {
            "name": "Multiple Roles (Cumulative)",
            "payload": {
                "upn": "<EMAIL>",
                "email": "<EMAIL>",
                "name": "Multi Role User",
                "roles": ["atomsec.user", "atomsec.admin"]  # Multiple roles
            }
        }
    ]
    
    print(f"\n🔐 TESTING AZURE AD ROLE PERMISSIONS")
    print("-" * 50)
    
    for test_user in test_users:
        print(f"\n👤 {test_user['name']}")
        print(f"   Email: {test_user['payload'].get('email')}")
        print(f"   Azure AD Roles: {test_user['payload'].get('roles', 'None')}")
        
        try:
            user_info = extract_user_from_payload(test_user['payload'])
            if not user_info:
                print("   ❌ User extraction failed")
                continue
            
            # Get role summary
            summary = get_user_role_summary(user_info)
            
            # Test delete capability
            can_delete = can_delete_integrations(user_info)
            delete_icon = "✅ YES" if can_delete else "❌ NO"
            
            print(f"   🗑️  Can Delete Integrations: {delete_icon}")
            print(f"   📊 Permission Summary:")
            print(f"      - Read: {'✅' if summary['can_read'] else '❌'}")
            print(f"      - Write: {'✅' if summary['can_write'] else '❌'}")
            print(f"      - Delete: {'✅' if summary['can_delete'] else '❌'}")
            print(f"      - Admin: {'✅' if summary['can_admin'] else '❌'}")
            print(f"      - System Admin: {'✅' if summary['is_system_admin'] else '❌'}")
            print(f"      - Total Permissions: {summary['permissions_count']}")
            
            # Test specific actions
            actions = ["list_integrations", "create_integration", "delete_integration"]
            print(f"   🎯 Action Tests:")
            for action in actions:
                try:
                    require_action_permission(user_info, action)
                    print(f"      ✅ {action}: ALLOWED")
                except Exception:
                    print(f"      ❌ {action}: DENIED")
                
        except Exception as e:
            print(f"   ❌ Error testing user: {str(e)}")

def show_azure_ad_setup_instructions():
    """Show step-by-step Azure AD setup instructions"""
    print("\n" + "=" * 60)
    print("AZURE AD ROLE ASSIGNMENT SETUP")
    print("=" * 60)
    
    print("\n🎯 STEP 1: Create App Roles in Azure AD")
    print("-" * 40)
    print("1. Go to Azure Portal → Azure Active Directory")
    print("2. Navigate to 'App registrations'")
    print("3. Find your application")
    print("4. Click 'App roles' → 'Create app role'")
    print("5. Create these roles:")
    
    roles = [
        ("atomsec.viewer", "Can view integrations and security data"),
        ("atomsec.user", "Can read and write integrations"),
        ("atomsec.admin", "Full access including delete operations"),
        ("atomsec.system_admin", "System administrator with all permissions")
    ]
    
    for role_value, description in roles:
        print(f"   • Role: {role_value}")
        print(f"     Description: {description}")
        print(f"     Value: {role_value}")
        print(f"     Allowed Members: User")
    
    print("\n🎯 STEP 2: Assign Users to Roles")
    print("-" * 40)
    print("1. Go to Azure Active Directory → Enterprise applications")
    print("2. Find your application")
    print("3. Click 'Users and groups' → 'Add user/group'")
    print("4. Select users and assign roles:")
    print("   • For delete operations: Assign 'atomsec.admin' role")
    print("   • For full access: Assign 'atomsec.system_admin' role")
    
    print("\n🎯 STEP 3: Configure Token Claims")
    print("-" * 40)
    print("1. Go to your app registration → 'Token configuration'")
    print("2. Click 'Add optional claim'")
    print("3. Select 'Access' and 'ID' tokens")
    print("4. Add 'roles' claim")
    print("5. Save configuration")
    
    print("\n🎯 STEP 4: Test Role Assignment")
    print("-" * 40)
    print("1. Assign yourself the 'atomsec.admin' role")
    print("2. Get a new user token (not application token)")
    print("3. Decode the token - it should include:")
    print('   "roles": ["atomsec.admin"]')
    print("4. Use this token for delete operations")

def test_token_with_roles():
    """Show how to test a token with Azure AD roles"""
    print("\n" + "=" * 60)
    print("TESTING YOUR USER TOKEN WITH AZURE AD ROLES")
    print("=" * 60)
    
    print("\n📝 TO TEST WITH YOUR USER TOKEN:")
    print("1. Get a user token (not application token) from Azure AD")
    print("2. Ensure you're assigned an admin role in Azure AD")
    print("3. Replace the token in this script and run:")
    
    print("\n🔍 SAMPLE USER TOKEN TEST:")
    print("```python")
    print("# Your user token after role assignment")
    print('user_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiI..."')
    print("")
    print("# Test the token")
    print("from shared.auth_utils import decode_token, extract_user_from_payload")
    print("from shared.permissions import can_delete_integrations")
    print("")
    print("payload = decode_token(user_token)")
    print("user_info = extract_user_from_payload(payload)")
    print("can_delete = can_delete_integrations(user_info)")
    print('print(f"Can delete: {can_delete}")')
    print("```")
    
    print("\n✅ EXPECTED RESULT WITH ADMIN ROLE:")
    print("   • Token roles: ['atomsec.admin']")
    print("   • Can delete: True")
    print("   • All CRUD operations: Allowed")

if __name__ == "__main__":
    print("🔐 AZURE AD ROLE-BASED PERMISSION TESTING")
    print("=" * 60)
    
    # Set environment variables for testing
    os.environ["AZURE_AD_TENANT_ID"] = "41b676db-bf6f-46ae-a354-a83a1362533f"
    os.environ["AZURE_AD_CLIENT_ID"] = "2d313c1a-d62d-492c-869e-cf8cb9258204"
    os.environ["AZURE_AD_ADDITIONAL_CLIENT_IDS"] = "82e79715-7451-4680-bd1c-53453bfd45ea"
    
    try:
        test_azure_ad_roles()
        show_azure_ad_setup_instructions()
        test_token_with_roles()
        
        print("\n" + "=" * 60)
        print("✅ AZURE AD ROLE SYSTEM READY")
        print("=" * 60)
        print("🎯 DELETE OPERATIONS ENABLED FOR:")
        print("   ✅ Users with 'atomsec.admin' Azure AD role")
        print("   ✅ Users with 'atomsec.system_admin' Azure AD role")
        print("   ✅ Users with legacy 'admin' role")
        print("   ✅ Users with Azure 'Owner' role")
        print("   ❌ Users with 'atomsec.user' or 'atomsec.viewer' roles")
        
        print("\n💡 NEXT STEPS:")
        print("   1. Create app roles in Azure AD (see setup guide)")
        print("   2. Assign users to 'atomsec.admin' role for delete access")
        print("   3. Configure token to include roles claim")
        print("   4. Test with user tokens (not application tokens)")
        print("   5. Deploy the updated permission system")
        
    except Exception as e:
        print(f"❌ Error in Azure AD role testing: {str(e)}")
        import traceback
        traceback.print_exc()
