#!/bin/bash

# Database Service Deployment Fix Script
# This script addresses the immediate issues found in the pipeline logs

set -e

echo "=== Database Service Deployment Fix Script ==="
echo "Addressing Azure CLI deprecation and configuration issues..."

# Check if Azure CLI is available
if ! command -v az &> /dev/null; then
    echo "Error: Azure CLI is not installed or not in PATH"
    exit 1
fi

# Function app details
FUNCTION_APP_NAME="func-atomsec-dbconnect-dev"
RESOURCE_GROUP="atomsec-dev-data"

echo "Function App: $FUNCTION_APP_NAME"
echo "Resource Group: $RESOURCE_GROUP"

# 1. Check function app status
echo "=== Checking Function App Status ==="
az functionapp show --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP --query "state" || echo "Failed to get function app state"

# 2. Configure core Azure Functions settings
echo "=== Configuring Core Settings ==="
az functionapp config appsettings set \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --settings \
    FUNCTIONS_WORKER_RUNTIME=python \
    FUNCTIONS_EXTENSION_VERSION=~4 \
    WEBSITE_RUN_FROM_PACKAGE=1 \
    SCM_DO_BUILD_DURING_DEPLOYMENT=false \
    AzureWebJobsFeatureFlags=EnableWorkerIndexing \
    WEBSITE_ENABLE_SYNC_UPDATE_SITE=true \
    WEBSITE_WEBSOCKETS_ENABLED=false \
    WEBSITE_HTTPLOGGING_RETENTION_DAYS=7 \
    WEBSITE_ENABLE_APP_SERVICE_STORAGE=false \
  || echo "Failed to set core settings"

# 3. Enable Always On
echo "=== Enabling Always On ==="
az functionapp config set \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --always-on true \
  || echo "Failed to enable Always On"

# 4. Test modern deployment commands
echo "=== Testing Modern Deployment Commands ==="

# Test functionapp deployment list (modern command)
echo "Testing functionapp deployment list..."
az functionapp deployment list --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP --query "[0].{status:status, message:message}" || echo "Deployment list command not available"

# Test functionapp log commands
echo "Testing functionapp log commands..."
az functionapp log download --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP --log-file deployment-logs.zip || echo "Could not download logs"

# 5. Test health endpoints
echo "=== Testing Health Endpoints ==="

# Test health endpoint with route prefix
echo "Testing health endpoint with route prefix..."
curl -s "https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/health" || echo "Health endpoint with prefix failed"

# Test health endpoint without route prefix
echo "Testing health endpoint without route prefix..."
curl -s "https://$FUNCTION_APP_NAME.azurewebsites.net/api/health" || echo "Health endpoint without prefix failed"

# 6. Check SCM accessibility
echo "=== Checking SCM Accessibility ==="
curl -I "https://$FUNCTION_APP_NAME.scm.azurewebsites.net/" || echo "SCM endpoint not accessible"

# 7. Configure application settings (if connection strings are available)
echo "=== Configuring Application Settings ==="

# Check if we have the required environment variables
if [ -n "$AZURE_STORAGE_CONNECTION_STRING" ]; then
    echo "Setting storage connection string..."
    az functionapp config appsettings set \
      --name $FUNCTION_APP_NAME \
      --resource-group $RESOURCE_GROUP \
      --settings \
        AZURE_STORAGE_CONNECTION_STRING="$AZURE_STORAGE_CONNECTION_STRING" \
        AZURE_TABLE_STORAGE_CONNECTION_STRING="$AZURE_STORAGE_CONNECTION_STRING" \
      || echo "Failed to set storage connection string"
else
    echo "AZURE_STORAGE_CONNECTION_STRING not set, skipping storage configuration"
fi

# Set other application settings
az functionapp config appsettings set \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --settings \
    KEY_VAULT_URL="https://akv-atomsec-dev.vault.azure.net/" \
    ENVIRONMENT="development" \
    LOG_LEVEL="INFO" \
  || echo "Failed to set application settings"

# 8. Test function app logs with timeout
echo "=== Testing Live Logs ==="
timeout 10s az functionapp log tail --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP || echo "Live logs timeout"

echo "=== Deployment Fix Script Completed ==="
echo ""
echo "Next steps:"
echo "1. Check the function app in Azure portal"
echo "2. Test the health endpoints manually"
echo "3. Review Application Insights for detailed logs"
echo "4. Update your pipeline with the modern Azure CLI commands" 