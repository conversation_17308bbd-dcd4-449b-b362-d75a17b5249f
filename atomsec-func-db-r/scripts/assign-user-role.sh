#!/bin/bash

# Azure AD User Role Assignment Script
# Usage: ./assign-user-role.sh <EMAIL> atomsec.admin

set -e

echo "👤 Azure AD User Role Assignment"
echo "==============================="

# Check parameters
if [ $# -ne 2 ]; then
    echo "❌ Usage: $0 <user_email> <role_value>"
    echo ""
    echo "Available roles:"
    echo "  • atomsec.viewer      - View only access"
    echo "  • atomsec.user        - Read/write access"
    echo "  • atomsec.admin       - Full access including delete"
    echo "  • atomsec.system_admin - System administrator"
    echo ""
    echo "Example:"
    echo "  $0 <EMAIL> atomsec.admin"
    exit 1
fi

USER_EMAIL="$1"
ROLE_VALUE="$2"

echo "📋 User: $USER_EMAIL"
echo "📋 Role: $ROLE_VALUE"

# Check if Azure CLI is ready
if ! az account show &> /dev/null; then
    echo "❌ You are not logged into Azure CLI. Please run: az login"
    exit 1
fi

echo ""
echo "🔍 Finding AtomSec application..."

# Try to find the application (same logic as create script)
APP_NAMES=("AtomSec" "atomsec" "AtomSec Function App" "atomsec-func" "atomsec-db")
APP_ID=""

for app_name in "${APP_NAMES[@]}"; do
    result=$(az ad app list --display-name "$app_name" --query "[0].appId" -o tsv 2>/dev/null || echo "")
    
    if [ -n "$result" ] && [ "$result" != "null" ]; then
        APP_ID="$result"
        echo "✅ Found application: $app_name ($APP_ID)"
        break
    fi
done

# Try known client IDs if not found by name
if [ -z "$APP_ID" ]; then
    KNOWN_CLIENT_IDS=("2d313c1a-d62d-492c-869e-cf8cb9258204" "82e79715-7451-4680-bd1c-53453bfd45ea")
    
    for client_id in "${KNOWN_CLIENT_IDS[@]}"; do
        if az ad app show --id "$client_id" &> /dev/null; then
            APP_ID="$client_id"
            echo "✅ Found application by client ID: $APP_ID"
            break
        fi
    done
fi

if [ -z "$APP_ID" ]; then
    echo "❌ Could not find AtomSec application. Please run create-azure-ad-roles.sh first."
    exit 1
fi

echo ""
echo "🔍 Getting application details..."

# Get service principal
SERVICE_PRINCIPAL_ID=$(az ad sp list --filter "appId eq '$APP_ID'" --query "[0].id" -o tsv)

if [ -z "$SERVICE_PRINCIPAL_ID" ]; then
    echo "❌ Service principal not found. Please run create-azure-ad-roles.sh first."
    exit 1
fi

echo "✅ Service principal: $SERVICE_PRINCIPAL_ID"

echo ""
echo "🔍 Finding user..."

# Get user object ID
USER_OBJECT_ID=$(az ad user show --id "$USER_EMAIL" --query "id" -o tsv 2>/dev/null || echo "")

if [ -z "$USER_OBJECT_ID" ]; then
    echo "❌ User not found: $USER_EMAIL"
    echo "💡 Make sure the user exists in your Azure AD tenant"
    exit 1
fi

echo "✅ User found: $USER_EMAIL ($USER_OBJECT_ID)"

echo ""
echo "🔍 Finding app role..."

# Get the role ID for the specified role value
ROLE_ID=$(az ad sp show --id "$SERVICE_PRINCIPAL_ID" --query "appRoles[?value=='$ROLE_VALUE'].id" -o tsv)

if [ -z "$ROLE_ID" ]; then
    echo "❌ Role not found: $ROLE_VALUE"
    echo ""
    echo "Available roles in the application:"
    az ad sp show --id "$SERVICE_PRINCIPAL_ID" --query "appRoles[].{value:value,displayName:displayName,description:description}" -o table
    exit 1
fi

echo "✅ Role found: $ROLE_VALUE ($ROLE_ID)"

echo ""
echo "🎯 Assigning role to user..."

# Check if user already has this role
existing_assignment=$(az ad app role assignment list --assignee "$USER_OBJECT_ID" --resource-service-principal-id "$SERVICE_PRINCIPAL_ID" --query "[?roleId=='$ROLE_ID'].id" -o tsv)

if [ -n "$existing_assignment" ]; then
    echo "ℹ️  User already has this role assigned"
    echo "   Assignment ID: $existing_assignment"
else
    # Assign the role
    assignment_id=$(az ad app role assignment create \
        --role "$ROLE_ID" \
        --assignee "$USER_OBJECT_ID" \
        --resource-service-principal-id "$SERVICE_PRINCIPAL_ID" \
        --query "id" -o tsv)
    
    echo "✅ Role assigned successfully!"
    echo "   Assignment ID: $assignment_id"
fi

echo ""
echo "🎉 ASSIGNMENT COMPLETE!"
echo "======================"
echo ""
echo "✅ User: $USER_EMAIL"
echo "✅ Role: $ROLE_VALUE"
echo "✅ Application: $APP_ID"
echo ""
echo "🔧 NEXT STEPS:"
echo "=============="
echo ""
echo "1. USER SHOULD GET NEW TOKEN:"
echo "   • The user needs to get a fresh access token"
echo "   • The new token will include: \"roles\": [\"$ROLE_VALUE\"]"
echo ""
echo "2. TEST THE PERMISSIONS:"
echo "   • Use the new user token (not application token)"
echo "   • Try the delete operations if assigned admin role"
echo ""
echo "3. VERIFY ROLE IN TOKEN:"
echo "   • Decode the JWT token to verify roles are included"
echo "   • Look for: \"roles\": [\"$ROLE_VALUE\"]"
echo ""

# Show current role assignments for this user
echo "📋 Current role assignments for $USER_EMAIL:"
az ad app role assignment list --assignee "$USER_OBJECT_ID" --resource-service-principal-id "$SERVICE_PRINCIPAL_ID" --query "[].{roleDisplayName:roleDisplayName,roleValue:roleId}" -o table

echo ""
echo "🎊 User is ready to use AtomSec with $ROLE_VALUE permissions!"
