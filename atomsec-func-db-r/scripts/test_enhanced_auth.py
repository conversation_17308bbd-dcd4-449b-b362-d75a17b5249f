#!/usr/bin/env python3
"""
Test script to verify enhanced JWT token validation with proper permission validation
"""

import sys
import os
import json

# Add the parent directory to sys.path to import shared modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_enhanced_authentication():
    """Test enhanced authentication with permission validation"""
    print("Testing Enhanced JWT Authentication with Permissions...")
    
    # Your token from the request
    token = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    
    try:
        # Import authentication modules
        from shared.auth_utils import decode_token, extract_user_from_payload
        from shared.permissions import (
            get_user_permissions, has_permission, Permission, 
            require_action_permission, get_application_info
        )
        
        print("Step 1: Decoding token...")
        payload = decode_token(token)
        
        if not payload:
            print("❌ Token decoding failed")
            return False
        
        print("✅ Token decoded successfully!")
        print(f"   Client ID: {payload.get('appid')}")
        print(f"   Tenant ID: {payload.get('tid')}")
        
        print("\nStep 2: Extracting user information with enhanced validation...")
        user_info = extract_user_from_payload(payload)
        
        if not user_info:
            print("❌ User extraction failed - application not authorized")
            return False
        
        print("✅ User information extracted with authorization!")
        print(f"   Name: {user_info.get('name')}")
        print(f"   Role: {user_info.get('roles')}")
        print(f"   Is Application: {user_info.get('is_application')}")
        print(f"   Is Admin: {user_info.get('isAdmin')}")
        print(f"   Description: {user_info.get('app_description')}")
        
        print("\nStep 3: Testing permission system...")
        user_permissions = get_user_permissions(user_info)
        print(f"   Granted permissions: {[p.value for p in user_permissions]}")
        
        # Test specific permissions
        test_permissions = [
            Permission.INTEGRATIONS_READ,
            Permission.INTEGRATIONS_WRITE,
            Permission.INTEGRATIONS_DELETE,
            Permission.USERS_ADMIN,
            Permission.SYSTEM_ADMIN
        ]
        
        print("\nStep 4: Permission checks:")
        for perm in test_permissions:
            has_perm = has_permission(user_info, perm)
            status = "✅" if has_perm else "❌"
            print(f"   {status} {perm.value}: {has_perm}")
        
        print("\nStep 5: Testing action permissions:")
        test_actions = [
            "list_integrations",
            "create_integration", 
            "delete_integration",
            "manage_policies",
            "system_configuration"
        ]
        
        for action in test_actions:
            try:
                require_action_permission(user_info, action)
                print(f"   ✅ {action}: ALLOWED")
            except Exception as e:
                print(f"   ❌ {action}: DENIED ({str(e)})")
        
        print("\nStep 6: Application information:")
        app_info = get_application_info(user_info.get('appid'))
        if app_info:
            print(f"   Application: {app_info['name']}")
            print(f"   Role: {app_info['role'].value}")
            print(f"   Description: {app_info['description']}")
        
        print("\n🎉 Enhanced authentication system working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing enhanced authentication: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_unauthorized_application():
    """Test with an unauthorized application"""
    print("\n" + "="*60)
    print("Testing Unauthorized Application Rejection...")
    
    # Simulate a token from an unauthorized application
    fake_payload = {
        "appid": "unauthorized-app-id",
        "appidacr": "1",
        "tid": "41b676db-bf6f-46ae-a354-a83a1362533f",
        "oid": "some-object-id"
    }
    
    try:
        from shared.auth_utils import extract_user_from_payload
        
        user_info = extract_user_from_payload(fake_payload)
        
        if user_info is None:
            print("✅ Unauthorized application correctly rejected!")
            return True
        else:
            print("❌ Unauthorized application was allowed - security issue!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing unauthorized application: {str(e)}")
        return False

def test_user_token():
    """Test with a mock user token"""
    print("\n" + "="*60)
    print("Testing User Token Authentication...")
    
    # Simulate a user token
    user_payload = {
        "upn": "<EMAIL>",
        "email": "<EMAIL>", 
        "name": "Test User",
        "oid": "user-object-id",
        "roles": ["admin"],
        "tid": "41b676db-bf6f-46ae-a354-a83a1362533f"
    }
    
    try:
        from shared.auth_utils import extract_user_from_payload
        from shared.permissions import get_user_permissions, Permission
        
        user_info = extract_user_from_payload(user_payload)
        
        if not user_info:
            print("❌ User token extraction failed")
            return False
        
        print("✅ User token processed successfully!")
        print(f"   Email: {user_info.get('email')}")
        print(f"   Name: {user_info.get('name')}")
        print(f"   Is Application: {user_info.get('is_application')}")
        
        user_permissions = get_user_permissions(user_info)
        print(f"   User permissions: {[p.value for p in user_permissions]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing user token: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Enhanced JWT Authentication Test Suite")
    print("=" * 60)
    
    # Set environment variables for testing
    os.environ["AZURE_AD_TENANT_ID"] = "41b676db-bf6f-46ae-a354-a83a1362533f"
    os.environ["AZURE_AD_CLIENT_ID"] = "2d313c1a-d62d-492c-869e-cf8cb9258204"
    os.environ["AZURE_AD_ADDITIONAL_CLIENT_IDS"] = "82e79715-7451-4680-bd1c-53453bfd45ea"
    
    success1 = test_enhanced_authentication()
    success2 = test_unauthorized_application() 
    success3 = test_user_token()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"Enhanced app token test: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"Unauthorized app rejection: {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"User token test: {'✅ PASS' if success3 else '❌ FAIL'}")
    
    if success1 and success2 and success3:
        print("\n🎉 All enhanced authentication tests passed!")
        print("\nSecurity improvements:")
        print("✅ Application authorization with allowlist")
        print("✅ Role-based permission system")
        print("✅ Action-based access control")
        print("✅ Comprehensive audit logging")
        print("✅ Unauthorized application rejection")
        print("\nYour APIM request will now have proper security validation!")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        
    print(f"\nNext steps:")
    print("1. Deploy the enhanced authentication system")
    print("2. Test your APIM request")
    print("3. Monitor audit logs for access patterns")
