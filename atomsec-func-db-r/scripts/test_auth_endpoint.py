#!/usr/bin/env python3
"""
Test script to verify the authentication endpoint
"""

import requests
import json
import sys
import os

def test_auth_endpoint():
    """Test the authentication endpoint"""
    print("Testing Authentication Endpoint...")
    
    # Load local settings
    try:
        with open('local.settings.json', 'r') as f:
            settings = json.load(f)
        
        # Set environment variables
        if 'Values' in settings:
            for key, value in settings['Values'].items():
                os.environ[key] = value
    except Exception as e:
        print(f"Error loading local settings: {e}")
        return False
    
    # Test the login endpoint
    base_url = "http://localhost:7072"
    login_url = f"{base_url}/api/db/auth/azure/login"
    
    try:
        print(f"Testing login endpoint: {login_url}")
        response = requests.get(login_url, allow_redirects=False)
        
        if response.status_code == 302:
            print("✅ Login endpoint is working!")
            print(f"Redirect URL: {response.headers.get('Location', 'No Location header')}")
            return True
        else:
            print(f"❌ Login endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the server. Make sure the Azure Function is running:")
        print("   func start")
        return False
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        return False

if __name__ == "__main__":
    success = test_auth_endpoint()
    sys.exit(0 if success else 1) 