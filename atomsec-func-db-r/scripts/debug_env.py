#!/usr/bin/env python3
"""
Debug script to check environment variables
"""

import os
import sys

def debug_environment():
    """Debug environment variables"""
    print("=== Environment Variables Debug ===")
    
    # Check Azure AD related environment variables
    azure_vars = [
        'AZURE_AD_CLIENT_ID',
        'AZURE_AD_CLIENT_SECRET', 
        'AZURE_AD_TENANT_ID',
        'AZURE_CLIENT_ID',
        'AZURE_CLIENT_SECRET',
        'AZURE_TENANT_ID'
    ]
    
    for var in azure_vars:
        value = os.environ.get(var)
        if value:
            # Mask the secret for security
            if 'SECRET' in var:
                masked_value = '*' * len(value) if value else 'NOT SET'
                print(f"{var}: {masked_value} (length: {len(value)})")
            else:
                print(f"{var}: {value}")
        else:
            print(f"{var}: NOT SET")
    
    print("\n=== Testing Configuration Function ===")
    
    # Test the actual configuration function
    try:
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from api.auth_endpoints import get_azure_ad_config
        
        config = get_azure_ad_config()
        print("Configuration from get_azure_ad_config():")
        for key, value in config.items():
            if 'secret' in key.lower():
                masked_value = '*' * len(value) if value else 'NOT SET'
                print(f"  {key}: {masked_value} (length: {len(value)})")
            else:
                print(f"  {key}: {value}")
                
    except Exception as e:
        print(f"Error testing configuration: {str(e)}")

if __name__ == "__main__":
    debug_environment() 