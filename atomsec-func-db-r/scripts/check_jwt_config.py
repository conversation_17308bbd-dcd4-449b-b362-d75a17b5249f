#!/usr/bin/env python3
"""
Script to check JWT configuration in production environment.
This script verifies that the JWT secret is properly configured.
"""

import os
import sys

# Add the parent directory to the path so we can import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.config import get_jwt_config
from shared.common import is_local_dev

def check_jwt_config():
    """Check JWT configuration"""
    print("🔍 Checking JWT configuration...")
    print(f"Environment: {'Local Development' if is_local_dev() else 'Production'}")
    print()
    
    try:
        jwt_config = get_jwt_config()
        
        print("✅ JWT Configuration:")
        print(f"  Algorithm: {jwt_config.get('algorithm')}")
        print(f"  Secret Length: {len(jwt_config.get('secret', ''))}")
        print(f"  Secret Preview: {jwt_config.get('secret', '')[:10]}..." if jwt_config.get('secret') else "  Secret: None")
        print(f"  Access Token Expire Minutes: {jwt_config.get('access_token_expire_minutes')}")
        print(f"  Refresh Token Expire Days: {jwt_config.get('refresh_token_expire_days')}")
        
        # Test token creation
        import jwt
        test_payload = {"sub": "<EMAIL>", "test": True}
        test_token = jwt.encode(test_payload, jwt_config['secret'], algorithm=jwt_config['algorithm'])
        
        print()
        print("✅ Test token created successfully")
        print(f"  Token Length: {len(test_token)}")
        print(f"  Token Preview: {test_token[:20]}...")
        
        # Test token validation
        try:
            decoded_payload = jwt.decode(test_token, jwt_config['secret'], algorithms=[jwt_config['algorithm']])
            print("✅ Test token validation successful")
            print(f"  Decoded Payload: {decoded_payload}")
        except Exception as e:
            print(f"❌ Test token validation failed: {str(e)}")
            return False
        
        print()
        print("🎉 JWT configuration is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error checking JWT configuration: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    success = check_jwt_config()
    
    if success:
        print()
        print("✅ JWT configuration is ready for production!")
    else:
        print()
        print("❌ JWT configuration needs to be fixed!")
        print("Please check:")
        print("1. JWT secret is set in Azure Key Vault (jwt-secret)")
        print("2. Function app has access to Key Vault")
        print("3. Managed Identity is properly configured")

if __name__ == "__main__":
    main()
