#!/usr/bin/env python3
"""
Test script to demonstrate different privilege levels and delete operations
"""

import sys
import os
import json

# Add the parent directory to sys.path to import shared modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_user_privilege_levels():
    """Test different user privilege levels and their delete capabilities"""
    print("Testing User Privilege Levels for Delete Operations...")
    print("=" * 60)
    
    from shared.permissions import (
        get_user_permissions, can_delete_integrations, get_user_role_summary, 
        Permission, require_action_permission
    )
    from shared.auth_utils import extract_user_from_payload
    
    # Test different user scenarios
    test_users = [
        {
            "name": "Regular User",
            "payload": {
                "upn": "<EMAIL>",
                "email": "<EMAIL>",
                "name": "Regular User"
            }
        },
        {
            "name": "Admin User (from token)",
            "payload": {
                "upn": "<EMAIL>", 
                "email": "<EMAIL>",
                "name": "Admin User",
                "roles": ["admin"]  # Admin role from Azure AD
            }
        },
        {
            "name": "Admin User (isAdmin flag)",
            "payload": {
                "upn": "<EMAIL>",
                "email": "<EMAIL>", 
                "name": "Flag Admin",
                "isAdmin": True  # Admin flag from token
            }
        },
        {
            "name": "Configured Admin User",
            "payload": {
                "upn": "<EMAIL>",
                "email": "<EMAIL>",  # In ADMIN_USERS list
                "name": "Configured Admin"
            }
        },
        {
            "name": "System Admin User",
            "payload": {
                "upn": "<EMAIL>",
                "email": "<EMAIL>",  # In SUPER_ADMIN_USERS list
                "name": "System Admin"
            }
        }
    ]
    
    print("\n🔐 USER PRIVILEGE TESTING")
    print("-" * 40)
    
    for test_user in test_users:
        print(f"\n👤 {test_user['name']}")
        print(f"   Email: {test_user['payload'].get('email')}")
        
        try:
            user_info = extract_user_from_payload(test_user['payload'])
            if not user_info:
                print("   ❌ User extraction failed")
                continue
            
            # Get role summary
            summary = get_user_role_summary(user_info)
            
            # Test delete capability
            can_delete = can_delete_integrations(user_info)
            delete_icon = "✅" if can_delete else "❌"
            
            print(f"   {delete_icon} Can Delete Integrations: {can_delete}")
            print(f"   📊 Permissions Summary:")
            print(f"      - Read: {summary['can_read']}")
            print(f"      - Write: {summary['can_write']}")
            print(f"      - Delete: {summary['can_delete']}")
            print(f"      - Admin: {summary['can_admin']}")
            print(f"      - System Admin: {summary['is_system_admin']}")
            print(f"      - Total Permissions: {summary['permissions_count']}")
            
            # Test specific delete action
            try:
                require_action_permission(user_info, "delete_integration")
                print(f"   ✅ delete_integration action: ALLOWED")
            except Exception as e:
                print(f"   ❌ delete_integration action: DENIED ({str(e)})")
                
        except Exception as e:
            print(f"   ❌ Error testing user: {str(e)}")

def test_application_vs_user_privileges():
    """Compare application vs user privileges"""
    print("\n" + "=" * 60)
    print("COMPARING APPLICATION vs USER PRIVILEGES")
    print("=" * 60)
    
    from shared.permissions import get_user_role_summary
    from shared.auth_utils import extract_user_from_payload
    
    # Your APIM Gateway application
    app_payload = {
        "appid": "82e79715-7451-4680-bd1c-53453bfd45ea",
        "appidacr": "1",
        "tid": "41b676db-bf6f-46ae-a354-a83a1362533f",
        "oid": "c40b9183-5ceb-43f4-ab98-0ae7f10c1e24"
    }
    
    # Admin user
    admin_payload = {
        "upn": "<EMAIL>",
        "email": "<EMAIL>",
        "name": "Admin User"
    }
    
    print("\n🤖 APPLICATION (APIM Gateway)")
    app_info = extract_user_from_payload(app_payload)
    if app_info:
        app_summary = get_user_role_summary(app_info)
        print(f"   Name: {app_info.get('name')}")
        print(f"   Role: {app_info.get('roles')}")
        print(f"   Can Delete: {app_summary['can_delete']} ❌")
        print(f"   Reason: API Gateway role has limited permissions")
        
    print("\n👤 ADMIN USER")
    user_info = extract_user_from_payload(admin_payload)
    if user_info:
        user_summary = get_user_role_summary(user_info)
        print(f"   Email: {user_info.get('email')}")
        print(f"   Can Delete: {user_summary['can_delete']} ✅")
        print(f"   Reason: Admin role includes delete permissions")

def test_how_to_enable_delete():
    """Show practical examples of enabling delete for users"""
    print("\n" + "=" * 60)
    print("HOW TO ENABLE DELETE OPERATIONS FOR USERS")
    print("=" * 60)
    
    print("\n📋 METHOD 1: Add user to ADMIN_USERS list")
    print("   File: atomsec-func-db-r/shared/permissions.py")
    print("   Add your email to ADMIN_USERS:")
    print('   ADMIN_USERS = {')
    print('       "<EMAIL>",')
    print('       "<EMAIL>",  # <- Add this')
    print('   }')
    
    print("\n📋 METHOD 2: Azure AD role assignment")
    print("   In Azure AD, assign 'admin' role to user")
    print("   Token will include: 'roles': ['admin']")
    
    print("\n📋 METHOD 3: Application configuration")
    print("   Configure application token to include:")
    print('   "isAdmin": true')
    
    print("\n📋 METHOD 4: Domain-based admin")
    print("   Allow all users from specific domain:")
    print('   ADMIN_USERS = {')
    print('       "*@atomsec.ai",  # All users from atomsec.ai')
    print('   }')
    
    print("\n🚀 IMMEDIATE SOLUTION:")
    print("   1. Edit atomsec-func-db-r/shared/permissions.py")
    print("   2. Add your email to ADMIN_USERS")
    print("   3. Deploy the changes")
    print("   4. Your user tokens will now have delete permissions!")

if __name__ == "__main__":
    print("🔐 PRIVILEGE LEVELS & DELETE OPERATIONS TEST")
    print("=" * 60)
    
    # Set environment variables for testing
    os.environ["AZURE_AD_TENANT_ID"] = "41b676db-bf6f-46ae-a354-a83a1362533f"
    os.environ["AZURE_AD_CLIENT_ID"] = "2d313c1a-d62d-492c-869e-cf8cb9258204"
    os.environ["AZURE_AD_ADDITIONAL_CLIENT_IDS"] = "82e79715-7451-4680-bd1c-53453bfd45ea"
    
    try:
        test_user_privilege_levels()
        test_application_vs_user_privileges()
        test_how_to_enable_delete()
        
        print("\n" + "=" * 60)
        print("✅ SUMMARY: DELETE OPERATIONS ARE AVAILABLE")
        print("=" * 60)
        print("🎯 Who can delete integrations:")
        print("   ✅ Users with 'admin' role in Azure AD token")
        print("   ✅ Users with 'isAdmin': true in token")  
        print("   ✅ Users listed in ADMIN_USERS configuration")
        print("   ✅ Users listed in SUPER_ADMIN_USERS configuration")
        print("   ❌ Regular users (read/write only)")
        print("   ❌ Applications (unless configured with delete permissions)")
        
        print("\n💡 TO ENABLE DELETE FOR YOUR USER:")
        print("   1. Add your email to ADMIN_USERS in permissions.py")
        print("   2. Deploy the updated authentication system")
        print("   3. Use a user token (not application token) for delete operations")
        
    except Exception as e:
        print(f"❌ Error in privilege testing: {str(e)}")
        import traceback
        traceback.print_exc()
