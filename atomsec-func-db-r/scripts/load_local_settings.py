#!/usr/bin/env python3
"""
Script to load local.settings.json and set environment variables
"""

import json
import os
import sys

def load_local_settings():
    """Load local.settings.json and set environment variables"""
    try:
        # Read local.settings.json
        with open('local.settings.json', 'r') as f:
            settings = json.load(f)
        
        # Set environment variables
        if 'Values' in settings:
            for key, value in settings['Values'].items():
                os.environ[key] = value
                print(f"Set {key}: {'*' * len(value) if 'SECRET' in key else value}")
        
        print(f"\nLoaded {len(settings.get('Values', {}))} environment variables")
        return True
        
    except FileNotFoundError:
        print("Error: local.settings.json not found")
        return False
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in local.settings.json: {e}")
        return False
    except Exception as e:
        print(f"Error loading settings: {e}")
        return False

if __name__ == "__main__":
    success = load_local_settings()
    if success:
        print("\nNow testing configuration...")
        # Import and test the configuration
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        try:
            from api.auth_endpoints import get_azure_ad_config
            config = get_azure_ad_config()
            print("\nConfiguration after loading settings:")
            for key, value in config.items():
                if 'secret' in key.lower():
                    masked_value = '*' * len(value) if value else 'NOT SET'
                    print(f"  {key}: {masked_value} (length: {len(value)})")
                else:
                    print(f"  {key}: {value}")
        except Exception as e:
            print(f"Error testing configuration: {e}")
    else:
        sys.exit(1) 