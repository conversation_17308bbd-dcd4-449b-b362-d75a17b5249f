#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix missing policies for automatically created users.
This script creates default policies and rules for users who were created automatically
during the authentication process but don't have any policies configured.
"""

import os
import sys
import uuid
from datetime import datetime

# Add the parent directory to the path so we can import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.data_access import create_default_policies_and_rules_for_integration
from shared.common import is_local_dev

def fix_user_policies(user_id: str, integration_id: str):
    """
    Create default policies and rules for a user who was created automatically
    
    Args:
        user_id: The user ID (e.g., "6484")
        integration_id: The integration ID (e.g., "27ad026b-c3ad-4c0b-9a1a-00f9460584d7")
    """
    print(f"Creating default policies for user {user_id} and integration {integration_id}")
    
    try:
        # Create default policies and rules
        success = create_default_policies_and_rules_for_integration(user_id, integration_id)
        
        if success:
            print(f"✅ Successfully created default policies for user {user_id} and integration {integration_id}")
        else:
            print(f"❌ Failed to create default policies for user {user_id} and integration {integration_id}")
            
    except Exception as e:
        print(f"❌ Error creating default policies: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Main function to fix policies for the specific user"""
    
    # The user ID and integration ID from the logs
    USER_ID = "6484"  # From the logs: user 6484
    INTEGRATION_ID = "27ad026b-c3ad-4c0b-9a1a-00f9460584d7"  # From the logs
    
    print("🔧 Fixing missing policies for automatically created user")
    print(f"User ID: {USER_ID}")
    print(f"Integration ID: {INTEGRATION_ID}")
    print(f"Environment: {'Local Development' if is_local_dev() else 'Production'}")
    print()
    
    # Create default policies
    fix_user_policies(USER_ID, INTEGRATION_ID)
    
    print()
    print("🎉 Policy fix completed!")
    print("Now try the rescan button again - the Health Check task should be enabled.")

if __name__ == "__main__":
    main()
