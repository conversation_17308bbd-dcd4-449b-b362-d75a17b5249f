# Default policies and rules for new integrations
# You can update this file to evolve the rule set without code changes

- name: Profiles and Permissions
  rules:
    - task_type: profiles_permission_sets
      enabled: true
    - task_type: mfa_enforcement
      enabled: true
    - task_type: device_activation
      enabled: true
    - task_type: login_ip_ranges
      enabled: true
    - task_type: login_hours
      enabled: true
    - task_type: session_timeout
      enabled: true
    - task_type: api_whitelisting
      enabled: true
    - task_type: password_policy
      enabled: true

- name: Health Check
  rules:
    - task_type: health_check
      enabled: true

- name: Static Code Analysis (PMD)
  rules:
    - task_type: pmd_apex_security
      enabled: true
      subtasks:
        - name: security
          description: Security-related PMD rules
          enabled: true
          rules:
            - name: ApexBadCrypto
              description: Bad crypto practices
              enabled: true
            - name: ApexCRUDViolation
              description: CRUD permission violations
              enabled: true
            - name: ApexDangerousMethods
              description: Dangerous method usage
              enabled: true
            - name: ApexInsecureEndpoint
              description: Insecure endpoint configurations
              enabled: true
            - name: ApexOpenRedirect
              description: Open redirect vulnerabilities
              enabled: true
            - name: ApexSharingViolations
              description: Sharing and security violations
              enabled: true
            - name: ApexSOQLInjection
              description: SOQL injection vulnerabilities
              enabled: true
            - name: ApexSuggestUsingNamedCred
              description: Suggest using named credentials
              enabled: true
            - name: ApexXSSFromEscapeFalse
              description: XSS from escape=false
              enabled: true
            - name: ApexXSSFromURLParam
              description: XSS from URL parameters
              enabled: true
            # JavaScript Security Rules (LWC/Aura)
            - name: NoEval
              description: Avoid eval() function which can lead to code injection
              enabled: true
            - name: NoInnerHtml
              description: Avoid innerHTML which can lead to XSS vulnerabilities
              enabled: true
            - name: NoDocumentWrite
              description: Avoid document.write which can lead to XSS vulnerabilities
              enabled: true
            - name: NoScriptURL
              description: "Avoid javascript: URLs which can lead to code injection"
              enabled: true
            - name: NoUnsafeEval
              description: Avoid unsafe eval-like functions
              enabled: true
            - name: NoUnsafeInnerHtml
              description: Avoid unsafe innerHTML assignments
              enabled: true
            # XML Security Rules (Visualforce)
            - name: XPathInjection
              description: XPath injection vulnerabilities from unvalidated user input
              enabled: true
            - name: XxeProcessing
              description: XML External Entity processing vulnerabilities
              enabled: true
            - name: XxeVulnerable
              description: XML External Entity vulnerabilities in XML processing
              enabled: true
            - name: XxeVulnerableSchema
              description: XML External Entity vulnerabilities in XML schema processing
              enabled: true
            - name: XxeVulnerableStylesheet
              description: XML External Entity vulnerabilities in XML stylesheet processing
              enabled: true
        - name: design
          description: Design-related PMD rules
          enabled: true
          rules:
            # Apex Design Rules
            - name: AvoidDeeplyNestedIfStmts
              description: Avoid deeply nested if statements
              enabled: true
            - name: UnusedMethod
              description: Unused methods
              enabled: true
            - name: CyclomaticComplexity
              description: Cyclomatic complexity
              enabled: true
            - name: CognitiveComplexity
              description: Cognitive complexity
              enabled: true
            - name: ExcessiveClassLength
              description: Excessive class length
              enabled: true
            - name: ExcessiveParameterList
              description: Excessive parameter list
              enabled: true
            - name: ExcessivePublicCount
              description: Excessive public count
              enabled: true
            - name: NcssConstructorCount
              description: NCSS constructor count
              enabled: true
            - name: NcssMethodCount
              description: NCSS method count
              enabled: true
            - name: NcssTypeCount
              description: NCSS type count
              enabled: true
            - name: StdCyclomaticComplexity
              description: Standard cyclomatic complexity
              enabled: true
            - name: TooManyFields
              description: Too many fields
              enabled: true
            # JavaScript Design Rules (LWC/Aura)
            - name: CognitiveComplexity
              description: Functions with high cognitive complexity making them difficult to understand
              enabled: true
            - name: CyclomaticComplexity
              description: Functions with high cyclomatic complexity making them difficult to test
              enabled: true
            - name: ExcessiveClassLength
              description: Classes that are too long and violate single responsibility principle
              enabled: true
            - name: ExcessiveMethodLength
              description: Methods that are too long and difficult to understand
              enabled: true
            - name: ExcessiveParameterList
              description: Functions with too many parameters affecting maintainability
              enabled: true
            - name: NcssMethodCount
              description: Classes with too many methods violating single responsibility principle
              enabled: true
        - name: performance
          description: Performance-related PMD rules
          enabled: true
          rules:
            # Apex Performance Rules
            - name: AvoidDebugStatements
              description: Avoid debug statements
              enabled: true
            - name: AvoidNonRestrictiveQueries
              description: Avoid non-restrictive queries
              enabled: true
            - name: EagerlyLoadedDescribeSObjectResult
              description: Eagerly loaded describe SObject result
              enabled: true
            - name: OperationWithHighCostInLoop
              description: Operation with high cost in loop
              enabled: true
            - name: OperationWithLimitsInLoop
              description: Operation with limits in loop
              enabled: true
            # JavaScript Performance Rules (LWC/Aura)
            - name: NoJQuery
              description: Use of jQuery in Lightning Web Components (use native DOM APIs instead)
              enabled: true
            - name: NoConsoleLog
              description: Console.log statements should be removed from production code
              enabled: true
            - name: NoDebugger
              description: Debugger statements should be removed from production code
              enabled: true
            - name: NoAlert
              description: Alert statements should be removed from production code
              enabled: true
        - name: bestpractices
          description: Best practices PMD rules
          enabled: true
          rules:
            # Apex Best Practices
            - name: ApexAssertionsShouldIncludeMessage
              description: Apex assertions should include message
              enabled: true
            - name: ApexUnitTestClassShouldHaveAsserts
              description: Apex unit test class should have asserts
              enabled: true
            - name: ApexUnitTestClassShouldHaveRunAs
              description: Apex unit test class should have run as
              enabled: true
            - name: ApexUnitTestMethodShouldHaveIsTestAnnotation
              description: Apex unit test method should have isTest annotation
              enabled: true
            - name: ApexUnitTestShouldNotUseSeeAllDataTrue
              description: Apex unit test should not use seeAllData true
              enabled: true
            - name: AvoidGlobalModifier
              description: Avoid global modifier
              enabled: true
            - name: AvoidLogicInTrigger
              description: Avoid logic in trigger
              enabled: true
            - name: DebugsShouldUseLoggingLevel
              description: Debugs should use logging level
              enabled: true
            - name: UnusedLocalVariable
              description: Unused local variable
              enabled: true
            - name: QueueableWithoutFinalizer
              description: Queueable without finalizer
              enabled: true
            # JavaScript Best Practices (LWC/Aura)
            - name: NoUnusedVars
              description: Variables that are declared but never used
              enabled: true
            - name: NoUnusedPrivateMembers
              description: Private class members that are declared but never used
              enabled: true
            - name: NoUnusedParameters
              description: Function parameters that are declared but never used
              enabled: true
            - name: NoUnusedExpressions
              description: Expressions that have no effect
              enabled: true
            - name: NoUnreachableCode
              description: Code that can never be executed
              enabled: true
            - name: NoDuplicateImports
              description: Duplicate import statements
              enabled: true
            - name: NoUnusedImports
              description: Import statements for modules that are never used
              enabled: true
        - name: codestyle
          description: Code style PMD rules
          enabled: true
          rules:
            # Apex Code Style Rules
            - name: ClassNamingConventions
              description: Class naming conventions
              enabled: true
            - name: IfElseStmtsMustUseBraces
              description: If-else statements must use braces
              enabled: true
            - name: IfStmtsMustUseBraces
              description: If statements must use braces
              enabled: true
            - name: FieldDeclarationsShouldBeAtStart
              description: Field declarations should be at start
              enabled: true
            - name: FieldNamingConventions
              description: Field naming conventions
              enabled: true
            - name: ForLoopsMustUseBraces
              description: For loops must use braces
              enabled: true
            - name: FormalParameterNamingConventions
              description: Formal parameter naming conventions
              enabled: true
            - name: LocalVariableNamingConventions
              description: Local variable naming conventions
              enabled: true
            - name: MethodNamingConventions
              description: Method naming conventions
              enabled: true
            - name: OneDeclarationPerLine
              description: One declaration per line
              enabled: true
            - name: PropertyNamingConventions
              description: Property naming conventions
              enabled: true
            - name: WhileLoopsMustUseBraces
              description: While loops must use braces
              enabled: true
            # JavaScript Code Style Rules (LWC/Aura)
            - name: NoUnusedVars
              description: Variables that are declared but never used
              enabled: true
            - name: NoUnusedPrivateMembers
              description: Private class members that are declared but never used
              enabled: true
            - name: NoUnusedParameters
              description: Function parameters that are declared but never used
              enabled: true
            - name: NoUnusedExpressions
              description: Expressions that have no effect
              enabled: true
            - name: NoUnreachableCode
              description: Code that can never be executed
              enabled: true
            - name: NoDuplicateImports
              description: Duplicate import statements
              enabled: true
            - name: NoUnusedImports
              description: Import statements for modules that are never used
              enabled: true
            - name: NoTernary
              description: Avoid ternary operators for better readability
              enabled: true
            - name: NoElseReturn
              description: Avoid else after return
              enabled: true
            - name: NoNegatedCondition
              description: Avoid negated conditions
              enabled: true
            - name: NoPlusplus
              description: Avoid ++ and -- operators
              enabled: true
            - name: InitDeclarations
              description: Initialize declarations at declaration time
              enabled: true
            - name: BlockScopedVar
              description: Use block-scoped variables
              enabled: true
            # Apex Trigger Rules (Design/Best Practices)
            - name: AvoidLogicInTrigger
              description: Avoid business logic in triggers
              enabled: true
            - name: TriggerNamingConventions
              description: Trigger naming conventions
              enabled: true
            - name: TriggerHandlerPattern
              description: Use trigger handler pattern
              enabled: true
            - name: BulkTriggerPattern
              description: Implement bulk trigger pattern
              enabled: true
            # LWC/Aura Specific Rules (Performance/Best Practices)
            - name: LwcNoAsyncAwait
              description: Avoid async/await in LWC components
              enabled: true
            - name: LwcNoUnsupportedSsrProperties
              description: Avoid unsupported SSR properties in LWC
              enabled: true
            - name: LwcNoRestrictedBrowserGlobalsDuringSsr
              description: Avoid restricted browser globals during SSR
              enabled: true
            - name: LwcNoJQuery
              description: Avoid jQuery in LWC (use native DOM APIs)
              enabled: true
            - name: AuraNoJQuery
              description: Avoid jQuery in Aura components
              enabled: true
            - name: AuraNoEval
              description: Avoid eval() in Aura components
              enabled: true
            - name: AuraNoInnerHtml
              description: Avoid innerHTML in Aura components
              enabled: true
            # Visualforce Rules (Security/Best Practices)
            - name: VfNoInlineScript
              description: Avoid inline scripts in Visualforce pages
              enabled: true
            - name: VfNoInlineStyle
              description: Avoid inline styles in Visualforce pages
              enabled: true
            - name: VfNoHardcodedIds
              description: Avoid hardcoded IDs in Visualforce pages
              enabled: true
            - name: VfNoDirectDatabaseQueries
              description: Avoid direct database queries in Visualforce
              enabled: true
            - name: VfNoXssVulnerabilities
              description: Avoid XSS vulnerabilities in Visualforce
              enabled: true
            - name: VfNoSensitiveDataExposure
              description: Avoid exposing sensitive data in Visualforce
              enabled: true
# Add more policies and rules as needed
# - name: Another Policy
#   rules:
#     - task_type: some_task
#       enabled: false 