{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://1********:10000/devstoreaccount1;QueueEndpoint=http://1********:10001/devstoreaccount1;TableEndpoint=http://1********:10002/devstoreaccount1;", "FUNCTIONS_WORKER_RUNTIME": "python", "AZURE_STORAGE_CONNECTION_STRING": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://1********:10000/devstoreaccount1;QueueEndpoint=http://1********:10001/devstoreaccount1;TableEndpoint=http://1********:10002/devstoreaccount1;", "AZURE_AD_TENANT_ID": "41b676db-bf6f-46ae-a354-a83a1362533f", "AZURE_AD_CLIENT_ID": "2d313c1a-d62d-492c-869e-cf8cb9258204", "AZURE_AD_CLIENT_SECRET": "****************************************", "KEY_VAULT_NAME": "akv-atomsec-dev", "ENVIRONMENT": "local", "IS_LOCAL_DEV": "true", "JWT_SECRET": "dev_secret_key_do_not_use_in_production", "SFDC_SERVICE_URL": "http://localhost:7071"}, "Host": {"LocalHttpPort": 7072, "CORS": "http://localhost:3000,https://app-atomsec-dev01.azurewebsites.net,**************************************", "CORSCredentials": true}}