# Azure DevOps Pipeline for AtomSec DB Function App (dev) - ULTRA FAST
# For rapid development cycles with minimal verification

trigger:
- dev
- dev-db

pool:
  vmImage: ubuntu-latest

variables:
- group: vg-atomsec-db-dev  # Variable group for dev environment
- name: PIP_CACHE_DIR
  value: '$(Pipeline.Workspace)/.pip-cache'

stages:
- stage: Build
  displayName: 'Build and Deploy'
  jobs:
  - job: BuildAndDeploy
    displayName: 'Build and Deploy Function App'
    steps:
    - task: UsePythonVersion@0
      inputs:
        versionSpec: '3.12'
      displayName: 'Set up Python 3.12'

    # Cache dependencies
    - task: Cache@2
      inputs:
        key: 'pip | "$(Agent.OS)" | requirements.txt'
        restoreKeys: |
          pip | "$(Agent.OS)"
        path: $(PIP_CACHE_DIR)
      displayName: 'Cache pip dependencies'

    # Quick setup
    - task: AzureCLI@2
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          npm install -g azure-functions-core-tools@4 --unsafe-perm true
      displayName: 'Setup Functions Tools'

    # Fast dependency installation
    - script: |
        export PIP_CACHE_DIR=$(PIP_CACHE_DIR)
        
        # Install system dependencies
        sudo apt-get update -qq
        sudo apt-get install -y -qq build-essential python3-dev pkg-config curl gnupg2
        
        # Install ODBC driver
        curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | sudo gpg --dearmor -o /usr/share/keyrings/microsoft-archive-keyring.gpg
        echo "deb [arch=amd64,arm64,armhf signed-by=/usr/share/keyrings/microsoft-archive-keyring.gpg] https://packages.microsoft.com/ubuntu/$(lsb_release -rs)/prod $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/microsoft-prod.list
        sudo apt-get update -qq
        sudo ACCEPT_EULA=Y apt-get install -y -qq msodbcsql18 unixodbc-dev
        
        # Install Python dependencies with cache
        pip install --cache-dir $(PIP_CACHE_DIR) --upgrade pip setuptools wheel
        pip install --cache-dir $(PIP_CACHE_DIR) -r requirements.txt -t .
      displayName: 'Fast Dependency Installation'

    # Quick archive
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
        replaceExistingArchive: true
        verbose: false
      displayName: 'Archive Function App'

    # Deploy immediately
    - task: AzureFunctionApp@2
      inputs:
        connectedServiceNameARM: '$(AZURE_SUBSCRIPTION)'
        appType: 'functionAppLinux'
        appName: '$(FUNCTION_APP_NAME)'
        package: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
        runtimeStack: 'PYTHON|3.12'
        deploymentMethod: 'zipDeploy'
        resourceGroupName: '$(RESOURCE_GROUP)'
      displayName: 'Deploy Function App'

    # Minimal configuration
    - task: AzureCLI@2
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          # Set essential settings only
          echo "Configuring essential settings..."
          if ! az functionapp config appsettings set \
            --name $(FUNCTION_APP_NAME) \
            --resource-group $(RESOURCE_GROUP) \
            --settings \
              FUNCTIONS_WORKER_RUNTIME=python \
              FUNCTIONS_EXTENSION_VERSION=~4 \
              WEBSITE_RUN_FROM_PACKAGE=1 \
              SCM_DO_BUILD_DURING_DEPLOYMENT=false \
              AZURE_AD_CLIENT_ID="@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/azure-ad-client-id/)" \
              AZURE_AD_TENANT_ID="@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/azure-ad-tenant-id/)" \
              AZURE_AD_CLIENT_SECRET="@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/azure-ad-client-secret/)" \
              AZURE_AD_ADDITIONAL_CLIENT_IDS="82e79715-7451-4680-bd1c-53453bfd45ea" \
              JWT_SECRET="@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/jwt-secret/)" \
              ENVIRONMENT="$(ENVIRONMENT)" \
              IS_LOCAL_DEV="$(IS_LOCAL_DEV)" \
              AZURE_STORAGE_CONNECTION_STRING="$(AZURE_STORAGE_CONNECTION_STRING)" \
              AZURE_TABLE_STORAGE_CONNECTION_STRING="$(AZURE_TABLE_STORAGE_CONNECTION_STRING)" \
              AZURE_SERVICE_BUS_CONNECTION_STRING="$(AZURE_SERVICE_BUS_CONNECTION_STRING)" \
              KEY_VAULT_URL="$(KEY_VAULT_URL)" \
              KEY_VAULT_NAME="$(KEY_VAULT_NAME)" \
              LOG_LEVEL="$(LOG_LEVEL)"; then
            echo "❌ ERROR: Settings configuration failed"
            exit 1
          fi
          
          echo '✅ Fast deployment completed successfully'
      displayName: 'Configure Essential Settings'

    # Critical health check
    - task: AzureCLI@2
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo "Waiting for deployment to stabilize..."
          sleep 30
          
          echo "Testing health endpoint..."
          HEALTH_URL="$(FUNCTION_APP_URL)/api/db/health"
          
          # Try health check twice
          for i in {1..2}; do
            echo "Health check attempt $i/2..."
            if curl -s "$HEALTH_URL" > /dev/null; then
              echo "✅ Health check passed"
              exit 0
            elif [ $i -eq 2 ]; then
              echo "❌ ERROR: Health check failed after 2 attempts"
              echo "URL tested: $HEALTH_URL"
              exit 1
            else
              echo "⚠️ Attempt $i failed, retrying in 15s..."
              sleep 15
            fi
          done
      displayName: 'Critical Health Check'


