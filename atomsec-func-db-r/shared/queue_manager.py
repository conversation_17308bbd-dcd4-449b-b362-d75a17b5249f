"""
Queue Manager Module

This module provides queue management capabilities for the AtomSec database service.
It handles priority-based task queuing using Azure Storage Queues.

Features:
- Priority-based queue routing (high, medium, low)
- Queue creation and management
- Message serialization and deserialization
- Retry mechanisms with exponential backoff
- Dead letter queue handling
- Environment-aware configuration

Best practices implemented:
- Clean separation of concerns
- Comprehensive error handling
- Proper logging and monitoring
- Type hints for better code clarity
- Environment-aware configuration
"""

import logging
import json
import base64
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import os

# Import shared modules
from shared.azure_services import get_queue_client, is_local_dev
from shared.common import is_local_dev

# Configure module-level logger
logger = logging.getLogger(__name__)

# Queue configuration constants
DEFAULT_QUEUE_NAME = "task-queue"
PRIORITY_QUEUES = {
    "high": "task-queue-high",
    "medium": "task-queue-medium", 
    "low": "task-queue-low"
}

# Message configuration
MAX_MESSAGE_SIZE = 64 * 1024  # 64KB limit for Azure Storage Queue messages
DEFAULT_VISIBILITY_TIMEOUT = 30  # seconds
DEFAULT_MESSAGE_TTL = 7 * 24 * 60 * 60  # 7 days in seconds


class QueueManager:
    """
    Queue manager for handling priority-based task queuing
    
    This class provides a clean interface for managing Azure Storage Queues
    with priority-based routing and proper error handling.
    """
    
    def __init__(self):
        """Initialize the queue manager"""
        self.queue_clients = {}
        self.is_local = is_local_dev()
        self._initialize_queues()
        
        logger.info(f"Queue manager initialized for {'local' if self.is_local else 'production'} environment")
    
    def _initialize_queues(self):
        """Initialize queue clients for all priority levels"""
        try:
            # Initialize default queue
            self.queue_clients[DEFAULT_QUEUE_NAME] = self._get_queue_client(DEFAULT_QUEUE_NAME)
            
            # Initialize priority queues
            for priority, queue_name in PRIORITY_QUEUES.items():
                self.queue_clients[queue_name] = self._get_queue_client(queue_name)
                logger.debug(f"Initialized {priority} priority queue: {queue_name}")
            
            logger.info("All queues initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing queues: {str(e)}")
    
    def _get_queue_client(self, queue_name: str):
        """
        Get or create a queue client
        
        Args:
            queue_name: Name of the queue
        
        Returns:
            Queue client instance
        """
        try:
            # Get the queue service client first
            from shared.azure_services import get_queue_client as get_queue_service_client
            queue_service_client = get_queue_service_client()
            
            if not queue_service_client:
                logger.error(f"Queue service client not available")
                return None
            
            # Get the specific queue client
            queue_client = queue_service_client.get_queue_client(queue_name)
            
            # Create queue if it doesn't exist
            if queue_client:
                try:
                    queue_client.create_queue()
                    logger.debug(f"Queue {queue_name} created or already exists")
                except Exception as e:
                    # Queue might already exist, which is fine
                    logger.debug(f"Queue {queue_name} creation result: {str(e)}")
            
            return queue_client
            
        except Exception as e:
            logger.error(f"Error getting queue client for {queue_name}: {str(e)}")
            return None
    
    def send_message(
        self,
        queue_name: str,
        message_data: Dict[str, Any],
        visibility_timeout: Optional[int] = None,
        time_to_live: Optional[int] = None
    ) -> bool:
        """
        Send a message to a specific queue
        
        Args:
            queue_name: Name of the queue
            message_data: Message data to send
            visibility_timeout: Visibility timeout in seconds
            time_to_live: Message TTL in seconds
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return False
            
            # Serialize message data
            message_content = json.dumps(message_data)
            
            # Check message size
            if len(message_content.encode('utf-8')) > MAX_MESSAGE_SIZE:
                logger.error(f"Message too large for queue {queue_name}: {len(message_content)} bytes")
                return False
            
            # Don't base64 encode since SFDC Function App uses messageEncoding=none
            # encoded_message = base64.b64encode(message_content.encode('utf-8')).decode('utf-8')
            
            # Set default values
            if visibility_timeout is None:
                visibility_timeout = DEFAULT_VISIBILITY_TIMEOUT
            if time_to_live is None:
                time_to_live = DEFAULT_MESSAGE_TTL
            
            # Send message
            queue_client.send_message(
                content=message_content,  # Send as plain text, not base64 encoded
                visibility_timeout=visibility_timeout,
                time_to_live=time_to_live
            )
            
            logger.info(f"Message sent to queue {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending message to queue {queue_name}: {str(e)}")
            return False
    
    def send_task_message(
        self,
        task_data: Dict[str, Any],
        priority: str = "medium",
        delay_seconds: Optional[int] = None
    ) -> bool:
        """
        Send a task message to the appropriate priority queue
        
        Args:
            task_data: Task data to send
            priority: Task priority (high, medium, low)
            delay_seconds: Delay before message becomes visible
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Validate priority
            if priority not in PRIORITY_QUEUES:
                logger.warning(f"Invalid priority '{priority}', using medium")
                priority = "medium"
            
            queue_name = PRIORITY_QUEUES[priority]
            
            # Add metadata to task data
            enhanced_task_data = {
                **task_data,
                "queue_metadata": {
                    "priority": priority,
                    "queue_name": queue_name,
                    "enqueued_at": datetime.now().isoformat(),
                    "retry_count": task_data.get("retry_count", 0)
                }
            }
            
            # Calculate visibility timeout for delayed messages
            visibility_timeout = delay_seconds if delay_seconds else DEFAULT_VISIBILITY_TIMEOUT
            
            success = self.send_message(
                queue_name=queue_name,
                message_data=enhanced_task_data,
                visibility_timeout=visibility_timeout
            )
            
            if success:
                task_id = task_data.get("task_id", "unknown")
                logger.info(f"Task {task_id} sent to {priority} priority queue")
                return True
            else:
                logger.error(f"Failed to send task to {priority} priority queue")
                return False
                
        except Exception as e:
            logger.error(f"Error sending task message: {str(e)}")
            return False
    
    def receive_messages(
        self,
        queue_name: str,
        max_messages: int = 1,
        visibility_timeout: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Receive messages from a queue
        
        Args:
            queue_name: Name of the queue
            max_messages: Maximum number of messages to receive
            visibility_timeout: Visibility timeout in seconds
        
        Returns:
            List[Dict[str, Any]]: List of received messages
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return []
            
            if visibility_timeout is None:
                visibility_timeout = DEFAULT_VISIBILITY_TIMEOUT
            
            # Receive messages
            messages = queue_client.receive_messages(
                max_messages=max_messages,
                visibility_timeout=visibility_timeout
            )
            
            decoded_messages = []
            for message in messages:
                try:
                    # Decode message content
                    decoded_content = base64.b64decode(message.content).decode('utf-8')
                    message_data = json.loads(decoded_content)
                    
                    # Add message metadata
                    message_data["_queue_metadata"] = {
                        "message_id": message.id,
                        "pop_receipt": message.pop_receipt,
                        "dequeue_count": message.dequeue_count,
                        "next_visible_on": message.next_visible_on.isoformat() if message.next_visible_on else None
                    }
                    
                    decoded_messages.append(message_data)
                    
                except Exception as e:
                    logger.error(f"Error decoding message from {queue_name}: {str(e)}")
                    continue
            
            logger.debug(f"Received {len(decoded_messages)} messages from {queue_name}")
            return decoded_messages
            
        except Exception as e:
            logger.error(f"Error receiving messages from {queue_name}: {str(e)}")
            return []
    
    def delete_message(self, queue_name: str, message_id: str, pop_receipt: str) -> bool:
        """
        Delete a message from the queue
        
        Args:
            queue_name: Name of the queue
            message_id: Message ID
            pop_receipt: Pop receipt from the message
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return False
            
            queue_client.delete_message(message_id, pop_receipt)
            logger.debug(f"Message {message_id} deleted from {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting message from {queue_name}: {str(e)}")
            return False
    
    def get_queue_properties(self, queue_name: str) -> Optional[Dict[str, Any]]:
        """
        Get queue properties including message count
        
        Args:
            queue_name: Name of the queue
        
        Returns:
            Dict[str, Any]: Queue properties if successful, None otherwise
        """
        try:
            queue_client = self.queue_clients.get(queue_name)
            if not queue_client:
                logger.error(f"Queue client not found for {queue_name}")
                return None
            
            properties = queue_client.get_queue_properties()
            
            return {
                "name": queue_name,
                "approximate_message_count": properties.approximate_message_count,
                "metadata": properties.metadata
            }
            
        except Exception as e:
            logger.error(f"Error getting properties for queue {queue_name}: {str(e)}")
            return None


# Global queue manager instance
_queue_manager = None


def get_queue_manager() -> QueueManager:
    """
    Get the global queue manager instance
    
    Returns:
        QueueManager: The queue manager instance
    """
    global _queue_manager
    
    if _queue_manager is None:
        _queue_manager = QueueManager()
        logger.debug("Created global queue manager instance")
    
    return _queue_manager
