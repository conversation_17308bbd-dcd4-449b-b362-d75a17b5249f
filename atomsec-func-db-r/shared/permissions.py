"""
Permission and Authorization Management

This module provides comprehensive permission validation for both user and application tokens.
Implements role-based access control (RBAC) with application-specific permissions.
"""

import logging
from typing import Dict, List, Any, Optional
from enum import Enum

logger = logging.getLogger(__name__)

class Permission(Enum):
    """Define available permissions in the system"""
    # Integration permissions
    INTEGRATIONS_READ = "integrations:read"
    INTEGRATIONS_WRITE = "integrations:write"
    INTEGRATIONS_DELETE = "integrations:delete"
    INTEGRATIONS_ADMIN = "integrations:admin"
    
    # User management permissions
    USERS_READ = "users:read"
    USERS_WRITE = "users:write"
    USERS_DELETE = "users:delete"
    USERS_ADMIN = "users:admin"
    
    # Security assessment permissions
    SECURITY_READ = "security:read"
    SECURITY_WRITE = "security:write"
    SECURITY_SCAN = "security:scan"
    
    # Admin permissions
    ADMIN_READ = "admin:read"
    ADMIN_WRITE = "admin:write"
    SYSTEM_ADMIN = "admin:system"

class Role(Enum):
    """Define roles with associated permissions"""
    # User roles
    VIEWER = "viewer"
    USER = "user"
    ADMIN = "admin"
    SYSTEM_ADMIN = "system_admin"
    
    # Application roles
    API_GATEWAY = "api_gateway"
    INTEGRATION_SERVICE = "integration_service"
    SECURITY_SCANNER = "security_scanner"

# Role to permissions mapping
ROLE_PERMISSIONS = {
    Role.VIEWER: [
        Permission.INTEGRATIONS_READ,
        Permission.SECURITY_READ,
    ],
    Role.USER: [
        Permission.INTEGRATIONS_READ,
        Permission.INTEGRATIONS_WRITE,
        Permission.SECURITY_READ,
        Permission.SECURITY_WRITE,
        Permission.USERS_READ,
    ],
    Role.ADMIN: [
        Permission.INTEGRATIONS_READ,
        Permission.INTEGRATIONS_WRITE,
        Permission.INTEGRATIONS_DELETE,
        Permission.INTEGRATIONS_ADMIN,
        Permission.SECURITY_READ,
        Permission.SECURITY_WRITE,
        Permission.SECURITY_SCAN,
        Permission.USERS_READ,
        Permission.USERS_WRITE,
        Permission.ADMIN_READ,
        Permission.ADMIN_WRITE,
    ],
    Role.SYSTEM_ADMIN: [perm for perm in Permission],  # All permissions
    
    # Application roles
    Role.API_GATEWAY: [
        Permission.INTEGRATIONS_READ,
        Permission.INTEGRATIONS_WRITE,
        Permission.SECURITY_READ,
        Permission.USERS_READ,
    ],
    Role.INTEGRATION_SERVICE: [
        Permission.INTEGRATIONS_READ,
        Permission.INTEGRATIONS_WRITE,
        Permission.INTEGRATIONS_ADMIN,
        Permission.SECURITY_READ,
        Permission.SECURITY_WRITE,
    ],
    Role.SECURITY_SCANNER: [
        Permission.INTEGRATIONS_READ,
        Permission.SECURITY_READ,
        Permission.SECURITY_WRITE,
        Permission.SECURITY_SCAN,
    ],
}

# Application ID to role mapping
APPLICATION_ROLES = {
    "82e79715-7451-4680-bd1c-53453bfd45ea": {
        "name": "APIM Gateway", 
        "role": Role.API_GATEWAY,
        "description": "Azure API Management Gateway for frontend access"
    },
    "2d313c1a-d62d-492c-869e-cf8cb9258204": {
        "name": "AtomSec Frontend", 
        "role": Role.INTEGRATION_SERVICE,
        "description": "Primary AtomSec frontend application"
    },
}

# Azure AD App Role to Internal Role Mapping
# These map the Azure AD app role values to internal permission roles
AZURE_AD_ROLE_MAPPING = {
    "atomsec.viewer": Role.VIEWER,
    "atomsec.user": Role.USER, 
    "atomsec.admin": Role.ADMIN,
    "atomsec.system_admin": Role.SYSTEM_ADMIN,
    
    # Legacy support for Azure AD built-in roles
    "viewer": Role.VIEWER,
    "user": Role.USER,
    "admin": Role.ADMIN,
    "system_admin": Role.SYSTEM_ADMIN,
    
    # Support for generic Azure AD roles (case-insensitive)
    "reader": Role.VIEWER,
    "contributor": Role.USER,
    "owner": Role.ADMIN,
}

# Action to required permission mapping
ACTION_PERMISSIONS = {
    # Integration actions
    "list_integrations": Permission.INTEGRATIONS_READ,
    "get_integration": Permission.INTEGRATIONS_READ,
    "create_integration": Permission.INTEGRATIONS_WRITE,
    "update_integration": Permission.INTEGRATIONS_WRITE,
    "delete_integration": Permission.INTEGRATIONS_DELETE,
    "sync_integration": Permission.INTEGRATIONS_WRITE,
    
    # User actions
    "list_users": Permission.USERS_READ,
    "get_user": Permission.USERS_READ,
    "create_user": Permission.USERS_WRITE,
    "update_user": Permission.USERS_WRITE,
    "delete_user": Permission.USERS_DELETE,
    
    # Security actions
    "security_assessment": Permission.SECURITY_READ,
    "run_security_scan": Permission.SECURITY_SCAN,
    "view_security_results": Permission.SECURITY_READ,
    
    # Admin actions
    "view_admin_dashboard": Permission.ADMIN_READ,
    "manage_policies": Permission.ADMIN_WRITE,
    "system_configuration": Permission.SYSTEM_ADMIN,
}

class AuthorizationError(Exception):
    """Raised when authorization fails"""
    pass

def get_user_permissions(user_info: Dict[str, Any]) -> List[Permission]:
    """
    Get all permissions for a user or application
    
    Args:
        user_info: User information dictionary
        
    Returns:
        List of permissions the user/application has
    """
    permissions = set()
    
    if user_info.get("is_application"):
        # Handle application permissions
        app_id = user_info.get("appid")
        app_config = APPLICATION_ROLES.get(app_id)
        
        if not app_config:
            logger.warning(f"Unknown application ID: {app_id}")
            return []
            
        app_role = app_config["role"]
        permissions.update(ROLE_PERMISSIONS.get(app_role, []))
        
        logger.info(f"Application {app_config['name']} has role {app_role.value}")
        
    else:
        # Handle user permissions based on Azure AD roles
        user_email = user_info.get("email", "").lower()
        user_roles = user_info.get("roles", [])
        
        # Process Azure AD roles and map to internal roles
        mapped_roles = set()
        highest_role = None
        
        for role_name in user_roles:
            # Try to map Azure AD role to internal role
            internal_role = AZURE_AD_ROLE_MAPPING.get(role_name.lower())
            if internal_role:
                mapped_roles.add(internal_role)
                logger.info(f"User {user_email} has Azure AD role '{role_name}' mapped to '{internal_role.value}'")
                
                # Track highest privilege role
                if internal_role == Role.SYSTEM_ADMIN:
                    highest_role = Role.SYSTEM_ADMIN
                elif internal_role == Role.ADMIN and highest_role != Role.SYSTEM_ADMIN:
                    highest_role = Role.ADMIN
                elif internal_role == Role.USER and highest_role not in [Role.ADMIN, Role.SYSTEM_ADMIN]:
                    highest_role = Role.USER
                elif internal_role == Role.VIEWER and highest_role is None:
                    highest_role = Role.VIEWER
            else:
                logger.warning(f"Unknown Azure AD role for user {user_email}: {role_name}")
        
        # Add permissions for all mapped roles (cumulative permissions)
        for role in mapped_roles:
            permissions.update(ROLE_PERMISSIONS.get(role, []))
            
        # If no roles are mapped, assign default USER role
        if not mapped_roles:
            highest_role = Role.USER
            permissions.update(ROLE_PERMISSIONS.get(Role.USER, []))
            logger.info(f"User {user_email} has no mapped Azure AD roles, granted default USER role")
        
        # Log final role assignment
        if highest_role:
            logger.info(f"User {user_email} effective role: {highest_role.value} (can delete: {Permission.INTEGRATIONS_DELETE in permissions})")
        
        logger.debug(f"User {user_email} final permissions: {[p.value for p in permissions]}")
    
    return list(permissions)

def has_permission(user_info: Dict[str, Any], permission: Permission) -> bool:
    """
    Check if user/application has a specific permission
    
    Args:
        user_info: User information dictionary
        permission: Permission to check
        
    Returns:
        True if user has permission, False otherwise
    """
    user_permissions = get_user_permissions(user_info)
    return permission in user_permissions

def require_permission(user_info: Dict[str, Any], permission: Permission) -> None:
    """
    Require a specific permission, raise AuthorizationError if not present
    
    Args:
        user_info: User information dictionary
        permission: Required permission
        
    Raises:
        AuthorizationError: If user doesn't have required permission
    """
    if not has_permission(user_info, permission):
        user_id = user_info.get("email", user_info.get("appid", "unknown"))
        app_name = ""
        
        if user_info.get("is_application"):
            app_id = user_info.get("appid")
            app_config = APPLICATION_ROLES.get(app_id, {})
            app_name = f" ({app_config.get('name', 'Unknown App')})"
        
        logger.warning(f"Authorization denied: {user_id}{app_name} lacks permission {permission.value}")
        raise AuthorizationError(f"Insufficient permissions. Required: {permission.value}")

def require_action_permission(user_info: Dict[str, Any], action: str) -> None:
    """
    Require permission for a specific action
    
    Args:
        user_info: User information dictionary
        action: Action name (e.g., 'list_integrations')
        
    Raises:
        AuthorizationError: If user doesn't have required permission for action
    """
    required_permission = ACTION_PERMISSIONS.get(action)
    
    if not required_permission:
        logger.warning(f"Unknown action: {action}")
        raise AuthorizationError(f"Unknown action: {action}")
    
    require_permission(user_info, required_permission)

def get_application_info(app_id: str) -> Optional[Dict[str, Any]]:
    """
    Get application information by ID
    
    Args:
        app_id: Application ID
        
    Returns:
        Application configuration or None if not found
    """
    return APPLICATION_ROLES.get(app_id)

def is_application_authorized(app_id: str) -> bool:
    """
    Check if an application is authorized
    
    Args:
        app_id: Application ID
        
    Returns:
        True if application is authorized, False otherwise
    """
    return app_id in APPLICATION_ROLES

def can_delete_integrations(user_info: Dict[str, Any]) -> bool:
    """
    Check if user can delete integrations (convenience function)
    
    Args:
        user_info: User information dictionary
        
    Returns:
        True if user can delete integrations, False otherwise
    """
    return has_permission(user_info, Permission.INTEGRATIONS_DELETE)

def can_manage_users(user_info: Dict[str, Any]) -> bool:
    """
    Check if user can manage other users (convenience function)
    
    Args:
        user_info: User information dictionary
        
    Returns:
        True if user can manage users, False otherwise
    """
    return has_permission(user_info, Permission.USERS_ADMIN)

def is_system_admin(user_info: Dict[str, Any]) -> bool:
    """
    Check if user is a system administrator (convenience function)
    
    Args:
        user_info: User information dictionary
        
    Returns:
        True if user is system admin, False otherwise
    """
    return has_permission(user_info, Permission.SYSTEM_ADMIN)

def get_user_role_summary(user_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get a summary of user's roles and key permissions
    
    Args:
        user_info: User information dictionary
        
    Returns:
        Dictionary with role summary
    """
    permissions = get_user_permissions(user_info)
    
    return {
        "user_id": user_info.get("email", user_info.get("appid", "unknown")),
        "is_application": user_info.get("is_application", False),
        "can_read": has_permission(user_info, Permission.INTEGRATIONS_READ),
        "can_write": has_permission(user_info, Permission.INTEGRATIONS_WRITE), 
        "can_delete": has_permission(user_info, Permission.INTEGRATIONS_DELETE),
        "can_admin": has_permission(user_info, Permission.INTEGRATIONS_ADMIN),
        "is_system_admin": has_permission(user_info, Permission.SYSTEM_ADMIN),
        "permissions_count": len(permissions),
        "all_permissions": [p.value for p in permissions]
    }

def log_access_attempt(user_info: Dict[str, Any], action: str, success: bool, 
                      additional_info: Optional[Dict[str, Any]] = None):
    """
    Log access attempts for audit purposes
    
    Args:
        user_info: User information dictionary
        action: Action attempted
        success: Whether the action was successful
        additional_info: Additional information to log
    """
    user_id = user_info.get("email", user_info.get("appid", "unknown"))
    user_type = "application" if user_info.get("is_application") else "user"
    status = "SUCCESS" if success else "DENIED"
    
    log_data = {
        "user_id": user_id,
        "user_type": user_type,
        "action": action,
        "status": status,
        "tenant_id": user_info.get("tenant_id"),
    }
    
    if additional_info:
        log_data.update(additional_info)
    
    if user_info.get("is_application"):
        app_config = APPLICATION_ROLES.get(user_info.get("appid"), {})
        log_data["application_name"] = app_config.get("name", "Unknown")
        log_data["application_role"] = app_config.get("role", {}).value if app_config.get("role") else "Unknown"
    
    if success:
        logger.info(f"Access granted: {user_type} {user_id} performed {action}")
    else:
        logger.warning(f"Access denied: {user_type} {user_id} attempted {action}")
    
    # TODO: Send to audit system
    logger.debug(f"Audit log: {log_data}")
