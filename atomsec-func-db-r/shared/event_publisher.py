"""
Event Publisher for Service Bus Integration

This module handles publishing events to Azure Service Bus for inter-service communication.
Events are published when tasks are created, updated, or completed to trigger processing
in the SFDC service.
"""

import logging
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
from azure.servicebus import ServiceBusClient, ServiceBusMessage

logger = logging.getLogger(__name__)

class EventPublisher:
    """Handles publishing events to Azure Service Bus"""

    def __init__(self):
        """Initialize the event publisher with Service Bus connection"""
        # Skip Service Bus initialization in local development
        from shared.common import is_local_dev
        if is_local_dev():
            logger.info("Local development detected - skipping Service Bus event publisher initialization")
            self.client = None
            return

        self.connection_string = os.getenv('AZURE_SERVICE_BUS_CONNECTION_STRING')
        self.topic_name = os.getenv('AZURE_SERVICE_BUS_TOPIC_NAME', 'atomsec-tasks')
        self.client = None

        if self.connection_string:
            try:
                self.client = ServiceBusClient.from_connection_string(self.connection_string)
                logger.info("Service Bus client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Service Bus client: {str(e)}")
                self.client = None
        else:
            logger.warning("Service Bus connection string not found. Events will not be published.")

    def publish_event(self, event_type: str, event_data: Dict[str, Any]) -> bool:
        """
        Publish an event to Service Bus

        Args:
            event_type: Type of event (TaskCreated, TaskUpdated, TaskCompleted)
            event_data: Event payload data

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.client:
            logger.warning("Service Bus client not available. Skipping event publication.")
            return False

        try:
            # Create event message
            event_message = {
                "eventType": event_type,
                "eventId": event_data.get("task_id", "unknown"),
                "timestamp": datetime.now().isoformat(),
                "data": event_data
            }

            # Create Service Bus message
            message = ServiceBusMessage(
                body=json.dumps(event_message),
                content_type="application/json"
            )

            # Add message properties for routing
            message.application_properties = {
                "eventType": event_type,
                "taskType": event_data.get("task_type", "unknown"),
                "priority": event_data.get("priority", "medium")
            }

            # Send message to topic
            with self.client.get_topic_sender(topic_name=self.topic_name) as sender:
                sender.send_messages(message)

            logger.info(f"Published {event_type} event for task {event_data.get('task_id')}")
            return True

        except Exception as e:
            logger.error(f"Failed to publish event {event_type}: {str(e)}")
            return False

    def publish_task_created(self, task_data: Dict[str, Any]) -> bool:
        """Publish TaskCreated event"""
        return self.publish_event("TaskCreated", task_data)

    def publish_task_updated(self, task_id: str, status: str, progress: int = None,
                           message: str = None) -> bool:
        """Publish TaskUpdated event"""
        event_data = {
            "task_id": task_id,
            "status": status,
            "progress": progress,
            "message": message,
            "updated_at": datetime.now().isoformat()
        }
        return self.publish_event("TaskUpdated", event_data)

    def publish_task_completed(self, task_id: str, result: Dict[str, Any] = None) -> bool:
        """Publish TaskCompleted event"""
        event_data = {
            "task_id": task_id,
            "status": "completed",
            "result": result,
            "completed_at": datetime.now().isoformat()
        }
        return self.publish_event("TaskCompleted", event_data)

class MockEventPublisher(EventPublisher):
    """Mock event publisher for local testing"""

    def __init__(self):
        """Initialize mock event publisher"""
        self.connection_string = None
        self.topic_name = os.getenv('AZURE_SERVICE_BUS_TOPIC_NAME', 'atomsec-tasks')
        self.client = None

        # Import here to avoid circular imports
        try:
            from .mock_service_bus import get_mock_service_bus_client
            self.mock_client = get_mock_service_bus_client()
            logger.info("Mock Service Bus client initialized for local testing")
        except ImportError:
            logger.warning("Mock Service Bus not available - events will be logged only")
            self.mock_client = None

    def publish_event(self, event_type: str, event_data: Dict[str, Any]) -> bool:
        """Publish event using mock Service Bus"""
        try:
            event_message = {
                "eventType": event_type,
                "eventId": event_data.get("task_id", "unknown"),
                "timestamp": datetime.now().isoformat(),
                "data": event_data
            }

            logger.info(f"Publishing mock event: {event_type} for task {event_data.get('task_id')}")

            if self.mock_client:
                return self.mock_client.send_message(self.topic_name, event_message)
            else:
                logger.info(f"Mock Service Bus not available - would publish: {event_type}")
                return True  # Return True for local development

        except Exception as e:
            logger.error(f"Failed to publish mock event {event_type}: {str(e)}")
            return True  # Return True for local development

# Global event publisher instance
_event_publisher = None

def get_event_publisher() -> EventPublisher:
    """Get the global event publisher instance"""
    global _event_publisher
    if _event_publisher is None:
        # Use mock Service Bus for local development
        from shared.common import is_local_dev
        if is_local_dev():
            _event_publisher = MockEventPublisher()
        else:
            _event_publisher = EventPublisher()
    return _event_publisher

def publish_task_event(event_type: str, task_data: Dict[str, Any]) -> bool:
    """
    Convenience function to publish task events

    Args:
        event_type: Type of event (created, updated, completed)
        task_data: Task data dictionary

    Returns:
        bool: True if successful, False otherwise
    """
    publisher = get_event_publisher()

    if event_type == "created":
        return publisher.publish_task_created(task_data)
    elif event_type == "updated":
        return publisher.publish_task_updated(
            task_data.get("task_id"),
            task_data.get("status"),
            task_data.get("progress"),
            task_data.get("message")
        )
    elif event_type == "completed":
        return publisher.publish_task_completed(
            task_data.get("task_id"),
            task_data.get("result")
        )
    else:
        logger.error(f"Unknown event type: {event_type}")
        return False
