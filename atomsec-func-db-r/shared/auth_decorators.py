"""
Authentication and Authorization Decorators

This module provides decorators for easy permission enforcement in Azure Functions.
"""

import json
import logging
from functools import wraps
from typing import Callable, Union, List
import azure.functions as func

from shared.auth_utils import get_current_user
from shared.permissions import (
    Permission, require_permission, require_action_permission, 
    AuthorizationError, log_access_attempt
)
from shared.cors_middleware import add_cors_headers

logger = logging.getLogger(__name__)

def require_auth(req: func.HttpRequest) -> tuple:
    """
    Get authenticated user from request
    
    Args:
        req: Azure Functions HTTP request
        
    Returns:
        Tuple of (user_info, error_response)
        If user_info is None, error_response contains the error
    """
    try:
        user = get_current_user(req)
        if not user:
            error_response = func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Authentication required",
                    "code": "AUTH_REQUIRED"
                }),
                mimetype="application/json",
                status_code=401
            )
            return None, add_cors_headers(req, error_response)
        
        return user, None
        
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        error_response = func.HttpResponse(
            json.dumps({
                "success": False,
                "error": "Authentication failed",
                "code": "AUTH_FAILED"
            }),
            mimetype="application/json",
            status_code=401
        )
        return None, add_cors_headers(req, error_response)

def requires_permission(permission: Union[Permission, str]):
    """
    Decorator that requires a specific permission
    
    Args:
        permission: Required permission (Permission enum or string)
    """
    def decorator(func_handler: Callable):
        @wraps(func_handler)
        def wrapper(req: func.HttpRequest) -> func.HttpResponse:
            # Handle CORS preflight
            if req.method == "OPTIONS":
                from shared.cors_middleware import handle_cors_preflight
                return handle_cors_preflight(req)
            
            # Authenticate user
            user, auth_error = require_auth(req)
            if auth_error:
                return auth_error
            
            # Convert string to Permission enum if needed
            required_perm = permission
            if isinstance(permission, str):
                try:
                    required_perm = Permission(permission)
                except ValueError:
                    logger.error(f"Invalid permission string: {permission}")
                    error_response = func.HttpResponse(
                        json.dumps({
                            "success": False,
                            "error": "Invalid permission configuration",
                            "code": "INVALID_PERMISSION"
                        }),
                        mimetype="application/json",
                        status_code=500
                    )
                    return add_cors_headers(req, error_response)
            
            # Check permission
            try:
                require_permission(user, required_perm)
                log_access_attempt(user, func_handler.__name__, True)
                
                # Call the original function
                return func_handler(req)
                
            except AuthorizationError as e:
                log_access_attempt(user, func_handler.__name__, False, {
                    "required_permission": required_perm.value,
                    "error": str(e)
                })
                
                error_response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": str(e),
                        "code": "INSUFFICIENT_PERMISSIONS",
                        "required_permission": required_perm.value
                    }),
                    mimetype="application/json",
                    status_code=403
                )
                return add_cors_headers(req, error_response)
            
            except Exception as e:
                logger.error(f"Permission check error: {str(e)}")
                log_access_attempt(user, func_handler.__name__, False, {
                    "error": str(e)
                })
                
                error_response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Authorization check failed",
                        "code": "AUTH_CHECK_FAILED"
                    }),
                    mimetype="application/json",
                    status_code=500
                )
                return add_cors_headers(req, error_response)
                
        return wrapper
    return decorator

def requires_action(action: str):
    """
    Decorator that requires permission for a specific action
    
    Args:
        action: Action name (e.g., 'list_integrations')
    """
    def decorator(func_handler: Callable):
        @wraps(func_handler)
        def wrapper(req: func.HttpRequest) -> func.HttpResponse:
            # Handle CORS preflight
            if req.method == "OPTIONS":
                from shared.cors_middleware import handle_cors_preflight
                return handle_cors_preflight(req)
            
            # Authenticate user
            user, auth_error = require_auth(req)
            if auth_error:
                return auth_error
            
            # Check action permission
            try:
                require_action_permission(user, action)
                log_access_attempt(user, action, True)
                
                # Call the original function with user context
                return func_handler(req)
                
            except AuthorizationError as e:
                log_access_attempt(user, action, False, {
                    "error": str(e)
                })
                
                error_response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": str(e),
                        "code": "INSUFFICIENT_PERMISSIONS",
                        "required_action": action
                    }),
                    mimetype="application/json",
                    status_code=403
                )
                return add_cors_headers(req, error_response)
            
            except Exception as e:
                logger.error(f"Action permission check error: {str(e)}")
                log_access_attempt(user, action, False, {
                    "error": str(e)
                })
                
                error_response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Authorization check failed",
                        "code": "AUTH_CHECK_FAILED"
                    }),
                    mimetype="application/json",
                    status_code=500
                )
                return add_cors_headers(req, error_response)
                
        return wrapper
    return decorator

def requires_any_permission(permissions: List[Union[Permission, str]]):
    """
    Decorator that requires any of the specified permissions
    
    Args:
        permissions: List of permissions (any one is sufficient)
    """
    def decorator(func_handler: Callable):
        @wraps(func_handler)
        def wrapper(req: func.HttpRequest) -> func.HttpResponse:
            # Handle CORS preflight
            if req.method == "OPTIONS":
                from shared.cors_middleware import handle_cors_preflight
                return handle_cors_preflight(req)
            
            # Authenticate user
            user, auth_error = require_auth(req)
            if auth_error:
                return auth_error
            
            # Check if user has any of the required permissions
            from shared.permissions import has_permission
            
            has_any_permission = False
            valid_permissions = []
            
            for perm in permissions:
                try:
                    # Convert string to Permission enum if needed
                    if isinstance(perm, str):
                        perm = Permission(perm)
                    valid_permissions.append(perm)
                    
                    if has_permission(user, perm):
                        has_any_permission = True
                        break
                        
                except ValueError:
                    logger.warning(f"Invalid permission: {perm}")
                    continue
            
            if not has_any_permission:
                log_access_attempt(user, func_handler.__name__, False, {
                    "required_permissions": [p.value for p in valid_permissions]
                })
                
                error_response = func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Insufficient permissions",
                        "code": "INSUFFICIENT_PERMISSIONS",
                        "required_permissions": [p.value for p in valid_permissions]
                    }),
                    mimetype="application/json",
                    status_code=403
                )
                return add_cors_headers(req, error_response)
            
            log_access_attempt(user, func_handler.__name__, True)
            return func_handler(req)
                
        return wrapper
    return decorator

def get_user_from_request(req: func.HttpRequest) -> dict:
    """
    Helper function to get authenticated user from request
    Use this in function handlers that already have authentication decorators
    
    Args:
        req: Azure Functions HTTP request
        
    Returns:
        User information dictionary
    """
    # This should only be called from functions that already have auth decorators
    # so we can assume the user is authenticated
    user = get_current_user(req)
    if not user:
        raise Exception("User context not available - ensure function has authentication decorator")
    return user
