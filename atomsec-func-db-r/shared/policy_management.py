"""
Policy Management Module

This module provides policy and rule management functionality for the AtomSec database service.
It handles the creation, retrieval, and management of policies and rules that control task execution.

Features:
- Policy creation and management
- Rule creation and management with subtasks
- Task enablement checking based on policies
- Integration-specific policy configuration

This matches the functionality from the dev branch exactly.
"""

import logging
import uuid
import json
from datetime import datetime
from typing import Dict, Any, Optional, List

# Import shared modules
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.common import is_local_dev

# Configure module-level logger
logger = logging.getLogger(__name__)


class PolicyManagementService:
    """
    Service for managing policies and rules
    
    This service provides functionality for managing policies and rules that control
    which tasks are enabled for specific integrations and users.
    """
    
    def __init__(self):
        """Initialize the policy management service"""
        self.is_local = is_local_dev()
        
        if self.is_local:
            # Use Table Storage for local development
            self.policy_repo = TableStorageRepository("Policy")
            self.rule_repo = TableStorageRepository("Rule")
            self.subtask_repo = TableStorageRepository("PMDSubtask")
            logger.info("Initialized policy management service with Table Storage")
        else:
            # Use SQL Database for production
            self.sql_repo = SqlDatabaseRepository("policy_management_service")
            logger.info("Initialized policy management service with SQL Database")
    
    def create_policy_with_rules(
        self,
        integration_id: str,
        user_id: str,
        policy_name: str,
        policy_description: str,
        rules: List[Dict[str, Any]],
        created_by: Optional[str] = None
    ) -> Optional[str]:
        """
        Create a policy with associated rules and subtasks
        
        Args:
            integration_id: Integration ID
            user_id: User ID
            policy_name: Name of the policy
            policy_description: Description of the policy
            rules: List of rules to create
            created_by: User who created the policy
        
        Returns:
            str: Policy ID if successful, None otherwise
        """
        try:
            policy_id = str(uuid.uuid4())
            
            # Create policy
            if self.is_local:
                policy_entity = {
                    "PartitionKey": "policy",
                    "RowKey": policy_id,
                    "PolicyId": policy_id,
                    "Name": policy_name,
                    "UserId": user_id,
                    "IntegrationId": integration_id,
                    "Description": policy_description,
                    "IsActive": True,
                    "CreatedAt": datetime.now().isoformat(),
                    "UpdatedAt": datetime.now().isoformat(),
                    "CreatedBy": created_by or user_id
                }
                
                success = self.policy_repo.insert_entity(policy_entity)
                
            else:
                query = """
                    INSERT INTO Policy (PolicyId, Name, UserId, IntegrationId, Description, IsActive, CreatedAt, UpdatedAt, CreatedBy)
                    VALUES (?, ?, ?, ?, ?, ?, GETDATE(), GETDATE(), ?)
                """
                params = (policy_id, policy_name, user_id, integration_id, policy_description, 1, created_by or user_id)
                success = self.sql_repo.execute_non_query(query, params)
            
            if not success:
                logger.error(f"Failed to create policy: {policy_name}")
                return None
            
            # Create rules
            for rule in rules:
                rule_id = self.create_rule(
                    policy_id=policy_id,
                    task_type=rule.get("task_type"),
                    rule_name=rule.get("name"),
                    description=rule.get("description"),
                    enabled=rule.get("enabled", True),
                    priority=rule.get("priority", 1),
                    created_by=created_by or user_id
                )
                
                if rule_id and "subtasks" in rule:
                    # Create subtasks for PMD rules
                    for subtask in rule.get("subtasks", []):
                        self.create_subtask(
                            rule_id=rule_id,
                            subtask_name=subtask.get("name"),
                            subtask_description=subtask.get("description"),
                            enabled=subtask.get("enabled", True)
                        )
            
            logger.info(f"Created policy {policy_name} with ID {policy_id} and {len(rules)} rules")
            return policy_id
            
        except Exception as e:
            logger.error(f"Error creating policy with rules: {str(e)}")
            return None
    
    def create_rule(
        self,
        policy_id: str,
        task_type: str,
        rule_name: str = "",
        description: str = "",
        enabled: bool = True,
        priority: int = 1,
        created_by: Optional[str] = None
    ) -> Optional[str]:
        """
        Create a rule for a policy
        
        Args:
            policy_id: Policy ID
            task_type: Task type this rule controls
            rule_name: Name of the rule
            description: Description of the rule
            enabled: Whether the rule is enabled
            priority: Rule priority
            created_by: User who created the rule
        
        Returns:
            str: Rule ID if successful, None otherwise
        """
        try:
            rule_id = str(uuid.uuid4())
            
            if self.is_local:
                rule_entity = {
                    "PartitionKey": "rule",
                    "RowKey": rule_id,
                    "RuleId": rule_id,
                    "PolicyId": policy_id,
                    "TaskType": task_type,
                    "RuleName": rule_name,
                    "Description": description,
                    "Enabled": int(enabled),
                    "Priority": priority,
                    "CreatedAt": datetime.now().isoformat(),
                    "UpdatedAt": datetime.now().isoformat(),
                    "CreatedBy": created_by or ""
                }
                
                success = self.rule_repo.insert_entity(rule_entity)
                
            else:
                query = """
                    INSERT INTO Rule (RuleId, PolicyId, TaskType, RuleName, Description, Enabled, Priority, CreatedAt, UpdatedAt, CreatedBy)
                    VALUES (?, ?, ?, ?, ?, ?, ?, GETDATE(), GETDATE(), ?)
                """
                params = (rule_id, policy_id, task_type, rule_name, description, int(enabled), priority, created_by or "")
                success = self.sql_repo.execute_non_query(query, params)
            
            if success:
                logger.info(f"Created rule {rule_name} with ID {rule_id} for task type {task_type}")
                return rule_id
            else:
                logger.error(f"Failed to create rule: {rule_name}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating rule: {str(e)}")
            return None
    
    def create_subtask(
        self,
        rule_id: str,
        subtask_name: str,
        subtask_description: str = "",
        enabled: bool = True
    ) -> Optional[str]:
        """
        Create a subtask for a rule
        
        Args:
            rule_id: Rule ID
            subtask_name: Name of the subtask
            subtask_description: Description of the subtask
            enabled: Whether the subtask is enabled
        
        Returns:
            str: Subtask ID if successful, None otherwise
        """
        try:
            subtask_id = str(uuid.uuid4())
            
            if self.is_local:
                subtask_entity = {
                    "PartitionKey": rule_id,
                    "RowKey": subtask_id,
                    "SubtaskId": subtask_id,
                    "RuleId": rule_id,
                    "TaskType": "pmd_apex_security",
                    "SubtaskName": subtask_name,
                    "SubtaskDescription": subtask_description,
                    "Enabled": int(enabled),
                    "CreatedAt": datetime.now().isoformat()
                }
                
                success = self.subtask_repo.insert_entity(subtask_entity)
                
            else:
                query = """
                    INSERT INTO PMDSubtask (SubtaskId, RuleId, TaskType, SubtaskName, SubtaskDescription, Enabled, CreatedAt, UpdatedAt)
                    VALUES (?, ?, ?, ?, ?, ?, GETDATE(), GETDATE())
                """
                params = (subtask_id, rule_id, "pmd_apex_security", subtask_name, subtask_description, int(enabled))
                success = self.sql_repo.execute_non_query(query, params)
            
            if success:
                logger.info(f"Created subtask {subtask_name} with ID {subtask_id}")
                return subtask_id
            else:
                logger.error(f"Failed to create subtask: {subtask_name}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating subtask: {str(e)}")
            return None
    
    def enqueue_policy_tasks(self, processor, org_id: str, user_id: str, policy_name: str, params: Dict[str, Any], priority: Optional[str] = None):
        """
        Enqueue all enabled rules for a given policy name and integration.
        
        Args:
            processor: BackgroundProcessor instance
            org_id: Integration/Org ID
            user_id: User ID
            policy_name: Name of the policy (e.g., 'Profiles and Permissions')
            params: Parameters to pass to each task
            priority: Optional task priority
        """
        try:
            if self.is_local:
                policies = self.policy_repo.query_entities(f"IntegrationId eq '{org_id}' and Name eq '{policy_name}'")
            else:
                query = "SELECT PolicyId FROM Policy WHERE IntegrationId = ? AND Name = ?"
                policies = self.sql_repo.execute_query(query, (org_id, policy_name))
                policies = [{"PolicyId": p[0]} for p in policies]
            
            if policies:
                policy_id = policies[0].get("PolicyId")
                
                if self.is_local:
                    rules = self.rule_repo.query_entities(f"PolicyId eq '{policy_id}' and Enabled eq 1")
                else:
                    query = "SELECT TaskType FROM Rule WHERE PolicyId = ? AND Enabled = 1"
                    rules = self.sql_repo.execute_query(query, (policy_id,))
                    rules = [{"TaskType": r[0]} for r in rules]
                
                for rule in rules:
                    task_type = rule.get("TaskType")
                    processor.enqueue_task(
                        task_type=task_type,
                        org_id=org_id,
                        user_id=user_id,
                        params=params,
                        priority=priority
                    )
                    
                logger.info(f"Enqueued {len(rules)} tasks for policy {policy_name}")
            else:
                logger.error(f"No policy named '{policy_name}' found for integration {org_id}.")
                
        except Exception as e:
            logger.error(f"Error enqueuing policy tasks: {str(e)}")
    
    def get_policies_for_integration(self, integration_id: str) -> List[Dict[str, Any]]:
        """
        Get all policies for an integration
        
        Args:
            integration_id: Integration ID
        
        Returns:
            List[Dict[str, Any]]: List of policies
        """
        try:
            if self.is_local:
                return self.policy_repo.query_entities(f"IntegrationId eq '{integration_id}'")
            else:
                query = """
                    SELECT PolicyId, Name, UserId, IntegrationId, Description, IsActive, CreatedAt, UpdatedAt, CreatedBy
                    FROM Policy WHERE IntegrationId = ?
                """
                policies = self.sql_repo.execute_query(query, (integration_id,))
                
                result = []
                for policy in policies:
                    result.append({
                        "PolicyId": policy[0],
                        "Name": policy[1],
                        "UserId": policy[2],
                        "IntegrationId": policy[3],
                        "Description": policy[4],
                        "IsActive": bool(policy[5]),
                        "CreatedAt": policy[6],
                        "UpdatedAt": policy[7],
                        "CreatedBy": policy[8]
                    })
                
                return result
                
        except Exception as e:
            logger.error(f"Error getting policies for integration {integration_id}: {str(e)}")
            return []
    
    def get_rules_for_policy(self, policy_id: str) -> List[Dict[str, Any]]:
        """
        Get all rules for a policy
        
        Args:
            policy_id: Policy ID
        
        Returns:
            List[Dict[str, Any]]: List of rules
        """
        try:
            if self.is_local:
                return self.rule_repo.query_entities(f"PolicyId eq '{policy_id}'")
            else:
                query = """
                    SELECT RuleId, PolicyId, TaskType, RuleName, Description, Enabled, Priority, CreatedAt, UpdatedAt, CreatedBy
                    FROM Rule WHERE PolicyId = ?
                """
                rules = self.sql_repo.execute_query(query, (policy_id,))
                
                result = []
                for rule in rules:
                    result.append({
                        "RuleId": rule[0],
                        "PolicyId": rule[1],
                        "TaskType": rule[2],
                        "RuleName": rule[3],
                        "Description": rule[4],
                        "Enabled": bool(rule[5]),
                        "Priority": rule[6],
                        "CreatedAt": rule[7],
                        "UpdatedAt": rule[8],
                        "CreatedBy": rule[9]
                    })
                
                return result
                
        except Exception as e:
            logger.error(f"Error getting rules for policy {policy_id}: {str(e)}")
            return []


# Global service instance
_policy_management_service = None


def get_policy_management_service() -> PolicyManagementService:
    """
    Get the global policy management service instance
    
    Returns:
        PolicyManagementService: The service instance
    """
    global _policy_management_service
    
    if _policy_management_service is None:
        _policy_management_service = PolicyManagementService()
        logger.debug("Created global policy management service instance")
    
    return _policy_management_service


# Convenience functions for backward compatibility
def enqueue_policy_tasks(processor, org_id: str, user_id: str, policy_name: str, params: Dict[str, Any], priority: Optional[str] = None):
    """Convenience function for enqueuing policy tasks"""
    service = get_policy_management_service()
    return service.enqueue_policy_tasks(processor, org_id, user_id, policy_name, params, priority)
