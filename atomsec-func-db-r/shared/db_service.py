from typing import Optional, List
from shared.database_models_new import ExecutionLog
from shared.azure_services import is_local_dev
from shared.data_access import get_sql_database_repository, get_table_storage_repository
from datetime import datetime

class DBService:
    def __init__(self):
        self.table_name = "ExecutionLog"
        if is_local_dev():
            self.repo = get_table_storage_repository(self.table_name)
        else:
            self.repo = get_sql_database_repository(self.table_name)

    def create_execution_log(self, execution_log: ExecutionLog) -> bool:
        if is_local_dev():
            entity = execution_log.to_dict()
            entity['PartitionKey'] = execution_log.OrgId
            entity['RowKey'] = execution_log.ExecutionLogId
            entity['StartedAt'] = entity['StartedAt'].isoformat()
            if entity.get('CompletedAt'):
                entity['CompletedAt'] = entity['CompletedAt'].isoformat()
            return self.repo.insert_entity(entity)
        else:
            # Build insert SQL
            fields = []
            values = []
            params = []
            for k, v in execution_log.to_dict().items():
                if v is not None:
                    fields.append(k)
                    values.append("?")
                    if isinstance(v, datetime):
                        params.append(v)
                    else:
                        params.append(v)
            sql = f"INSERT INTO {self.table_name} ({', '.join(fields)}) VALUES ({', '.join(values)})"
            return self.repo.execute_non_query(sql, tuple(params))

    def update_execution_log(self, execution_log_id: str, update_data: dict) -> bool:
        if is_local_dev():
            # Find entity by RowKey
            entities = self.repo.query_entities(f"RowKey eq '{execution_log_id}'")
            entity = next(iter(entities), None)
            if not entity:
                return False
            for k, v in update_data.items():
                if isinstance(v, datetime):
                    v = v.isoformat()
                entity[k] = v
            return self.repo.update_entity(entity)
        else:
            # Build update SQL
            set_clauses = []
            params = []
            for k, v in update_data.items():
                set_clauses.append(f"{k} = ?")
                if isinstance(v, datetime):
                    params.append(v)
                else:
                    params.append(v)
            sql = f"UPDATE {self.table_name} SET {', '.join(set_clauses)} WHERE ExecutionLogId = ?"
            params.append(execution_log_id)
            return self.repo.execute_non_query(sql, tuple(params))

    def get_execution_log_by_id(self, execution_log_id: str) -> Optional[ExecutionLog]:
        if is_local_dev():
            entities = self.repo.query_entities(f"RowKey eq '{execution_log_id}'")
            entity = next(iter(entities), None)
            if not entity:
                return None
            return ExecutionLog.from_dict(entity)
        else:
            sql = f"SELECT * FROM {self.table_name} WHERE ExecutionLogId = ?"
            rows = self.repo.execute_query(sql, (execution_log_id,))
            if not rows:
                return None
            # Assume columns match ExecutionLog fields
            columns = [col[0] for col in self.repo.execute_query(f"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{self.table_name}'")]
            row_dict = dict(zip(columns, rows[0]))
            return ExecutionLog.from_dict(row_dict)

    def list_execution_logs_by_org(self, org_id: str) -> List[ExecutionLog]:
        if is_local_dev():
            entities = self.repo.query_entities(f"PartitionKey eq '{org_id}'")
            return [ExecutionLog.from_dict(e) for e in entities]
        else:
            sql = f"SELECT * FROM {self.table_name} WHERE OrgId = ? ORDER BY StartedAt DESC"
            rows = self.repo.execute_query(sql, (org_id,))
            if not rows:
                return []
            columns = [col[0] for col in self.repo.execute_query(f"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{self.table_name}'")]
            return [ExecutionLog.from_dict(dict(zip(columns, row))) for row in rows] 