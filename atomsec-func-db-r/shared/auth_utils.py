"""
Authentication Utilities for Database Service

This module provides authentication utilities for the database service,
including JWT token validation and user extraction from requests.
"""

import logging
import jwt
import requests
import os
from typing import Dict, Any, Optional
import azure.functions as func
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

def get_jwt_secret() -> str:
    """Get JWT secret from environment variables"""
    try:
        # Use the same configuration as the main config module for consistency
        from shared.config import get_jwt_config
        jwt_config = get_jwt_config()
        return jwt_config['secret']
    except Exception as e:
        logger.error(f"Error getting JWT secret: {str(e)}")
        raise ValueError(f"JWT secret not found in Key Vault: {str(e)}")

def get_token_from_header(req: func.HttpRequest) -> Optional[str]:
    """
    Extract JWT token from Authorization header

    Args:
        req: HTTP request

    Returns:
        str: JWT token or None if not found
    """
    auth_header = req.headers.get("Authorization")

    if not auth_header:
        logger.debug("No Authorization header found")
        return None

    if not auth_header.startswith("Bearer "):
        logger.debug("Authorization header does not start with 'Bearer '")
        return None

    token = auth_header[7:]  # Remove "Bearer " prefix
    logger.debug(f"Extracted token from header (length: {len(token)})")
    return token

def decode_azure_ad_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Decode and validate Azure AD RS256 JWT token
    
    Args:
        token: JWT token string
        
    Returns:
        Dict: Token payload or None if invalid
    """
    try:
        # Get Azure AD tenant ID from environment or use common
        tenant_id = os.environ.get("AZURE_AD_TENANT_ID", "41b676db-bf6f-46ae-a354-a83a1362533f")
        
        # Get allowed client IDs - support multiple applications
        primary_client_id = os.environ.get("AZURE_AD_CLIENT_ID", "2d313c1a-d62d-492c-869e-cf8cb9258204")
        additional_client_ids = os.environ.get("AZURE_AD_ADDITIONAL_CLIENT_IDS", "").split(",")
        allowed_client_ids = {primary_client_id} | {cid.strip() for cid in additional_client_ids if cid.strip()}
        
        logger.info(f"Validating Azure AD token for tenant: {tenant_id}")
        logger.info(f"Allowed client IDs: {allowed_client_ids}")
        logger.info(f"Environment variables - AZURE_AD_TENANT_ID: {os.environ.get('AZURE_AD_TENANT_ID')}, AZURE_AD_CLIENT_ID: {os.environ.get('AZURE_AD_CLIENT_ID')}")
        
        # Get the signing key from Azure AD
        unverified_header = jwt.get_unverified_header(token)
        kid = unverified_header.get('kid')
        alg = unverified_header.get('alg')
        
        logger.info(f"Token header - alg: {alg}, kid: {kid}")
        logger.info(f"Full token header: {unverified_header}")
        
        if not kid:
            logger.warning("No 'kid' found in Azure AD token header")
            logger.debug(f"Token header: {unverified_header}")
            return None
            
        # Get public keys from Azure AD
        jwks_url = f"https://login.microsoftonline.com/{tenant_id}/discovery/v2.0/keys"
        logger.info(f"Fetching Azure AD public keys from: {jwks_url}")
        
        response = requests.get(jwks_url, timeout=10)
        response.raise_for_status()
        jwks = response.json()
        
        # Find the correct key
        signing_key = None
        for key in jwks.get('keys', []):
            if key.get('kid') == kid:
                signing_key = key
                logger.info(f"Found matching key for kid: {kid}")
                break
                
        if not signing_key:
            logger.warning(f"No matching key found for kid: {kid}")
            logger.info(f"Available keys: {[k.get('kid') for k in jwks.get('keys', [])]}")
            return None
            
        # Convert JWK to PEM format for PyJWT
        try:
            # Use PyJWT's built-in JWK support
            import json
            jwk_json = json.dumps(signing_key)
            
            # Try PyJWT's from_jwk method
            try:
                from jwt import PyJWK
                jwk = PyJWK.from_dict(signing_key)
                pem_key = jwk.key
                logger.info("Successfully converted JWK using PyJWK")
            except (ImportError, AttributeError):
                # Fallback to manual PEM conversion
                from cryptography.hazmat.primitives import serialization
                from cryptography.hazmat.primitives.asymmetric import rsa
                from cryptography.hazmat.backends import default_backend
                import base64
                
                # Extract modulus and exponent from JWK
                n = signing_key.get('n')
                e = signing_key.get('e')
                
                if not n or not e:
                    logger.error("Missing modulus or exponent in JWK")
                    return None
                
                # Decode base64url encoded values
                def base64url_decode(input_str):
                    padding = '=' * (4 - len(input_str) % 4)
                    return base64.urlsafe_b64decode(input_str + padding)
                
                modulus = base64url_decode(n)
                exponent = base64url_decode(e)
                
                # Convert to integers
                modulus_int = int.from_bytes(modulus, byteorder='big')
                exponent_int = int.from_bytes(exponent, byteorder='big')
                
                # Create RSA public key
                public_key = rsa.RSAPublicNumbers(exponent_int, modulus_int).public_key(default_backend())
                
                # Convert to PEM format
                pem_key = public_key.public_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                )
                
                logger.info("Successfully converted JWK to PEM format")
                
        except Exception as e:
            logger.error(f"Error converting JWK to PEM: {str(e)}")
            return None
            
        # Verify the token using the PEM key
        logger.info("Attempting to verify token with Azure AD public key")
        
        try:
            payload = jwt.decode(
                token,
                pem_key,
                algorithms=['RS256'],
                options={"verify_aud": False}
            )
            logger.info("Successfully decoded token with RS256 algorithm")
        except jwt.InvalidTokenError as e:
            logger.error(f"JWT validation error: {str(e)}")
            return None
        
        logger.info(f"Token payload keys: {list(payload.keys())}")
        
        # Validate client ID and tenant ID
        token_client_id = payload.get("appid") or payload.get("aud")
        token_tenant_id = payload.get("tid")
        
        logger.info(f"Token client ID: {token_client_id}, tenant ID: {token_tenant_id}")
        
        # Check tenant ID
        if token_tenant_id != tenant_id:
            logger.warning(f"Token tenant ID {token_tenant_id} doesn't match expected {tenant_id}")
            return None
            
        # Check client ID (flexible - allow multiple applications)
        if token_client_id not in allowed_client_ids:
            logger.warning(f"Token client ID {token_client_id} not in allowed list: {allowed_client_ids}")
            # Log for debugging but still allow (for backward compatibility)
            logger.info(f"Allowing token from client ID {token_client_id} for backward compatibility")
        
        logger.info(f"Successfully validated Azure AD token for client {token_client_id}")
        return payload
        
    except requests.RequestException as e:
        logger.error(f"Failed to fetch Azure AD public keys: {str(e)}")
        return None
    except jwt.InvalidTokenError as e:
        logger.error(f"JWT validation error: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error validating Azure AD token: {str(e)}")
        logger.exception("Full exception details:")
        return None

def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Decode and validate JWT token
    Supports both custom HS256 tokens and Azure AD RS256 tokens

    Args:
        token: JWT token string

    Returns:
        Dict: Token payload or None if invalid
    """
    try:
        # First, try to decode the token without verification to check the algorithm
        unverified_payload = jwt.decode(token, options={"verify_signature": False})
        unverified_header = jwt.get_unverified_header(token)
        
        algorithm = unverified_header.get('alg', 'HS256')
        logger.info(f"Token algorithm detected: {algorithm}")
        logger.info(f"Full token header: {unverified_header}")
        
        # Get allowed algorithms from config
        from shared.config import get_jwt_config
        jwt_config = get_jwt_config()
        allowed_algorithms = jwt_config.get('algorithms', ['RS256', 'HS256'])
        
        logger.info(f"JWT Config: {jwt_config}")
        logger.info(f"Allowed algorithms from config: {allowed_algorithms}")
        
        if algorithm not in allowed_algorithms:
            logger.warning(f"Token algorithm '{algorithm}' not in allowed algorithms: {allowed_algorithms}")
            logger.warning(f"Invalid token: The specified alg value is not allowed")
            return None
        
        if algorithm == 'HS256':
            # Custom token signed with our secret
            jwt_secret = get_jwt_secret()
            payload = jwt.decode(token, jwt_secret, algorithms=['HS256'])
            logger.debug("Successfully decoded HS256 token (custom)")
            
        elif algorithm == 'RS256':
            # Azure AD token - validate using Azure AD public keys
            payload = decode_azure_ad_token(token)
            if not payload:
                logger.warning("Failed to validate Azure AD token")
                return None
            logger.debug("Successfully decoded RS256 token (Azure AD)")
            
        else:
            logger.warning(f"Unsupported token algorithm: {algorithm}")
            return None

        return payload
        
    except jwt.InvalidTokenError as e:
        logger.error(f"JWT validation error: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error decoding JWT token: {str(e)}")
        logger.exception("Full exception details:")
        return None

def extract_user_from_payload(payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Extract user information from JWT payload
    Handles both custom tokens and Azure AD tokens (including app-only tokens)
    
    Args:
        payload: JWT token payload
        
    Returns:
        Dict: User information or None if invalid
    """
    from shared.permissions import get_application_info, is_application_authorized
    
    # Check if this is an app-only token (client credentials flow)
    is_app_only = payload.get("appidacr") == "1"
    
    if is_app_only:
        # Handle app-only tokens - use application identity
        logger.info("Processing app-only Azure AD token")
        
        # For app-only tokens, use the application ID and object ID
        app_id = payload.get("appid")
        object_id = payload.get("oid")
        
        if not app_id:
            logger.warning("No application ID found in app-only token")
            logger.debug(f"Available payload fields: {list(payload.keys())}")
            return None
            
        # Validate application is authorized
        if not is_application_authorized(app_id):
            logger.error(f"Unauthorized application attempted access: {app_id}")
            return None
            
        # Get application configuration
        app_config = get_application_info(app_id)
        app_name = app_config["name"] if app_config else f"Unknown Application {app_id}"
        
        # Create application identity with proper role-based permissions
        user_info = {
            "email": f"app:{app_id}@{payload.get('tid', 'unknown')}.onmicrosoft.com",
            "user_id": object_id or app_id,
            "name": app_name,
            "roles": [app_config["role"].value] if app_config else ["unknown"],
            "isAdmin": False,  # Remove automatic admin privileges
            "is_application": True,
            "appid": app_id,
            "tenant_id": payload.get("tid"),
            "app_description": app_config.get("description", "") if app_config else ""
        }
        
        logger.info(f"Authenticated application: {app_name} ({app_id})")
        logger.debug(f"Application role: {app_config.get('role', 'unknown') if app_config else 'unknown'}")
        return user_info
    
    # Handle user tokens (delegated permissions)
    # For Azure AD tokens, email is typically in 'upn' or 'email' field
    # For custom tokens, email is typically in 'sub' field
    email = (payload.get("upn") or 
             payload.get("email") or 
             payload.get("sub") or 
             payload.get("preferred_username"))
    
    if not email:
        logger.warning("No email/user identifier found in user token payload")
        logger.debug(f"Available payload fields: {list(payload.keys())}")
        return None
    
    # Extract name from various possible fields
    name = (payload.get("name") or 
            payload.get("given_name", "") + " " + payload.get("family_name", "") or
            "").strip()
    
    # Extract user ID
    user_id = (payload.get("user_id") or 
               payload.get("oid") or  # Azure AD object ID
               payload.get("sub"))
    
    # Extract roles (custom tokens) or Azure AD roles
    roles = payload.get("roles", [])
    if not roles and "roles" in payload:
        roles = payload["roles"] if isinstance(payload["roles"], list) else []
    
    # Check admin status
    is_admin = payload.get("isAdmin", False)
    if not is_admin and roles:
        # Check if any role indicates admin status
        admin_roles = ["admin", "administrator", "owner", "global_admin"]
        is_admin = any(role.lower() in admin_roles for role in roles if isinstance(role, str))
    
    user_info = {
        "email": email,
        "user_id": user_id,
        "name": name,
        "roles": roles,
        "isAdmin": is_admin,
        "is_application": False
    }
    
    logger.debug(f"Extracted user info: email={email}, user_id={user_id}, roles={roles}")
    return user_info

def get_current_user(req: func.HttpRequest) -> Optional[Dict[str, Any]]:
    """
    Get current user information from request

    Args:
        req: HTTP request

    Returns:
        Dict: User information or None if not authenticated
    """
    # Check if we're in local development mode
    from shared.azure_services import is_local_dev

    # Log environment detection
    is_dev = is_local_dev()
    logger.info(f"Environment detection: is_local_dev() = {is_dev}")
    
    # Log all relevant environment variables for debugging
    env_vars = ['WEBSITE_SITE_NAME', 'WEBSITE_INSTANCE_ID', 'FUNCTIONS_WORKER_RUNTIME',
                'FUNCTIONS_WORKER_RUNTIME_VERSION', 'WEBSITE_HOSTNAME', 'IS_LOCAL_DEV']
    for var in env_vars:
        value = os.environ.get(var)
        logger.info(f"Environment variable {var}: {value}")

    if is_dev:
        # In local development, require proper authentication
        logger.info("Local development mode: requiring proper authentication")
        
        # Get token from header
        token = get_token_from_header(req)
        if not token:
            logger.warning("No token found in request")
            return None

        # Decode token
        payload = decode_token(token)
        if not payload:
            logger.warning("Invalid or expired token")
            return None

        # Extract user information from token
        user_info = extract_user_from_payload(payload)
        if not user_info:
            logger.warning("Failed to extract user info from token")
            return None

        logger.info(f"Retrieved user info for: {user_info['email']}")
        return user_info

    # Production mode
    logger.info("Production mode: requiring Azure AD authentication")
    
    # Get token from header
    token = get_token_from_header(req)
    if not token:
        logger.warning("No token found in request")
        return None

    logger.info(f"Token found in request (length: {len(token)})")
    
    # Decode token
    payload = decode_token(token)
    if not payload:
        logger.warning("Invalid or expired token")
        return None

    # Extract user information
    user_info = extract_user_from_payload(payload)
    if not user_info:
        logger.warning("Failed to extract user info from token")
        return None

    logger.info(f"Retrieved user info for: {user_info['email']}")
    return user_info

def get_user_id_from_request(req: func.HttpRequest) -> Optional[str]:
    """
    Extract user ID from authenticated request

    Args:
        req: HTTP request

    Returns:
        str: User ID or None if not authenticated
    """
    user = get_current_user(req)
    if not user:
        return None

    # Try to get user_id from token first
    user_id = user.get("user_id")
    if user_id:
        logger.debug(f"Found user_id in token: {user_id}")
        return str(user_id)

    # Fallback to email as user identifier
    email = user.get("email")
    if email:
        logger.debug(f"Using email as user identifier: {email}")
        return email

    logger.warning("No user identifier found in token")
    return None

def require_auth(original_func):
    """
    Decorator to require authentication for endpoint functions

    Args:
        original_func: Original function to wrap

    Returns:
        Wrapped function that requires authentication
    """
    def wrapper(req: func.HttpRequest) -> func.HttpResponse:
        # Check if we're in local development mode
        from shared.azure_services import is_local_dev

        if is_local_dev():
            # In local development, skip authentication
            logger.debug("Local development mode: skipping authentication")
            return original_func(req)

        # Get current user for production
        current_user = get_current_user(req)
        if not current_user:
            import json
            logger.warning("Authentication required but no valid user found")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Unauthorized - valid authentication required"
                }),
                mimetype="application/json",
                status_code=401
            )

        # Add user to request for use in the endpoint
        setattr(req, "user", current_user)

        # Call original function
        return original_func(req)

    # Copy function attributes to wrapper
    import functools
    functools.update_wrapper(wrapper, original_func)

    return wrapper

def get_user_from_request_or_default(req: func.HttpRequest, default_user_id: str = "system") -> str:
    """
    Get user ID from request or return default

    Args:
        req: HTTP request
        default_user_id: Default user ID to use if authentication fails

    Returns:
        str: User ID or default value
    """
    user_id = get_user_id_from_request(req)
    if user_id:
        return user_id

    logger.debug(f"No authenticated user found, using default: {default_user_id}")
    return default_user_id
