"""
Database Models for AtomSec Application

This module defines the data models for the AtomSec application database.
Consolidated from atomsec-func-sfdc to centralize all database models.
"""

from dataclasses import dataclass
from datetime import datetime, date
from typing import Dict, Any, Optional, List


@dataclass
class Account:
    """Model for dbo.App_Account table"""
    AccountId: Optional[int] = None
    Name: str = ""
    Description: str = ""
    IsActive: bool = True
    CreatedAt: datetime = datetime.now()
    CreatedBy: int = 0
    UpdatedAt: Optional[datetime] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Account':
        """Create an Account instance from a dictionary"""
        return cls(
            AccountId=data.get('AccountId'),
            Name=data.get('Name', ''),
            Description=data.get('Description', ''),
            IsActive=data.get('IsActive', True),
            CreatedAt=data.get('CreatedAt', datetime.now()),
            CreatedBy=data.get('CreatedBy', 0),
            UpdatedAt=data.get('UpdatedAt')
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'AccountId': self.AccountId,
            'Name': self.Name,
            'Description': self.Description,
            'IsActive': self.IsActive,
            'CreatedAt': self.CreatedAt,
            'CreatedBy': self.CreatedBy,
            'UpdatedAt': self.UpdatedAt
        }

    def to_table_entity(self) -> Dict[str, Any]:
        """Convert to Azure Table Storage entity"""
        return {
            'PartitionKey': 'account',
            'RowKey': str(self.AccountId) if self.AccountId else '',
            'AccountId': self.AccountId,
            'Name': self.Name,
            'Description': self.Description,
            'IsActive': self.IsActive,
            'CreatedAt': self.CreatedAt.isoformat() if self.CreatedAt else '',
            'CreatedBy': self.CreatedBy,
            'UpdatedAt': self.UpdatedAt.isoformat() if self.UpdatedAt else ''
        }


@dataclass
class User:
    """Model for dbo.App_User table"""
    UserId: Optional[int] = None
    Name: str = ""
    Email: str = ""
    Phone: Optional[str] = None
    AccountId: int = 0
    CreatedAt: datetime = datetime.now()
    IsActive: bool = True

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create a User instance from a dictionary"""
        return cls(
            UserId=data.get('UserId'),
            Name=data['Name'],
            Email=data['Email'],
            Phone=data.get('Phone'),
            AccountId=data['AccountId'],
            CreatedAt=data.get('CreatedAt', datetime.now()),
            IsActive=data.get('IsActive', True)
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'UserId': self.UserId,
            'Name': self.Name,
            'Email': self.Email,
            'Phone': self.Phone,
            'AccountId': self.AccountId,
            'CreatedAt': self.CreatedAt,
            'IsActive': self.IsActive
        }


@dataclass
class Role:
    """Model for dbo.Role table"""
    RoleId: Optional[int] = None
    Rolename: str = ""
    Description: Optional[str] = None
    CreatedDate: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Role':
        """Create a Role instance from a dictionary"""
        return cls(
            RoleId=data.get('RoleId'),
            Rolename=data['Rolename'],
            Description=data.get('Description'),
            CreatedDate=data.get('CreatedDate', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'RoleId': self.RoleId,
            'Rolename': self.Rolename,
            'Description': self.Description,
            'CreatedDate': self.CreatedDate
        }


@dataclass
class Permission:
    """Model for dbo.Permission table"""
    PermissionId: Optional[int] = None
    PermissionLabel: str = ""
    Action: str = ""
    Description: Optional[str] = None
    CreatedDate: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Permission':
        """Create a Permission instance from a dictionary"""
        return cls(
            PermissionId=data.get('PermissionId'),
            PermissionLabel=data['PermissionLabel'],
            Action=data['Action'],
            Description=data.get('Description'),
            CreatedDate=data.get('CreatedDate', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'PermissionId': self.PermissionId,
            'PermissionLabel': self.PermissionLabel,
            'Action': self.Action,
            'Description': self.Description,
            'CreatedDate': self.CreatedDate
        }


@dataclass
class UserRole:
    """Model for dbo.App_User_Roles table"""
    UserId: int
    RoleId: int
    CreatedDate: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserRole':
        """Create a UserRole instance from a dictionary"""
        return cls(
            UserId=data['UserId'],
            RoleId=data['RoleId'],
            CreatedDate=data.get('CreatedDate', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'UserId': self.UserId,
            'RoleId': self.RoleId,
            'CreatedDate': self.CreatedDate
        }


@dataclass
class RolePermission:
    """Model for dbo.Role_Permissions table"""
    RoleId: int
    PermissionId: int
    CreatedDate: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RolePermission':
        """Create a RolePermission instance from a dictionary"""
        return cls(
            RoleId=data['RoleId'],
            PermissionId=data['PermissionId'],
            CreatedDate=data.get('CreatedDate', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'RoleId': self.RoleId,
            'PermissionId': self.PermissionId,
            'CreatedDate': self.CreatedDate
        }


@dataclass
class Organization:
    """Model for dbo.App_Organization table"""
    Id: Optional[int] = None
    CloudProvider: str = "Salesforce"
    Type: str = "Salesforce"
    Name: str = ""
    TenantUrl: str = ""
    Description: Optional[str] = None
    Environment: str = "production"
    AccountId: int = 0
    IsActive: bool = True
    LastScan: Optional[datetime] = None
    HealthScore: Optional[int] = None
    CreatedAt: datetime = datetime.now()
    CreatedBy: int = 0

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Organization':
        """Create an Organization instance from a dictionary"""
        return cls(
            Id=data.get('Id'),
            CloudProvider=data.get('CloudProvider', "Salesforce"),
            Type=data.get('Type', "Salesforce"),
            Name=data['Name'],
            TenantUrl=data['TenantUrl'],
            Description=data.get('Description'),
            Environment=data.get('Environment', "production"),
            AccountId=data['AccountId'],
            IsActive=data.get('IsActive', True),
            LastScan=data.get('LastScan'),
            HealthScore=data.get('HealthScore'),
            CreatedAt=data.get('CreatedAt', datetime.now()),
            CreatedBy=data['CreatedBy']
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'Id': self.Id,
            'CloudProvider': self.CloudProvider,
            'Type': self.Type,
            'Name': self.Name,
            'TenantUrl': self.TenantUrl,
            'Description': self.Description,
            'Environment': self.Environment,
            'AccountId': self.AccountId,
            'IsActive': self.IsActive,
            'LastScan': self.LastScan,
            'HealthScore': self.HealthScore,
            'CreatedAt': self.CreatedAt,
            'CreatedBy': self.CreatedBy
        }


@dataclass
class OrgAccess:
    """Model for dbo.App_OrgAccess table"""
    Id: Optional[int] = None
    OrgId: int = 0
    UserId: int = 0
    Role: str = "Viewer"
    CreatedAt: datetime = datetime.now()
    CreatedBy: int = 0

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OrgAccess':
        """Create an OrgAccess instance from a dictionary"""
        return cls(
            Id=data.get('Id'),
            OrgId=data['OrgId'],
            UserId=data['UserId'],
            Role=data.get('Role', "Viewer"),
            CreatedAt=data.get('CreatedAt', datetime.now()),
            CreatedBy=data['CreatedBy']
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'Id': self.Id,
            'OrgId': self.OrgId,
            'UserId': self.UserId,
            'Role': self.Role,
            'CreatedAt': self.CreatedAt,
            'CreatedBy': self.CreatedBy
        }


@dataclass
class Credentials:
    """Model for dbo.App_Credentials table"""
    Id: Optional[int] = None
    OrgId: int = 0
    KeyVaultId: Optional[str] = None
    ClientId: Optional[str] = None
    ClientSecret: Optional[str] = None
    CreatedAt: datetime = datetime.now()
    UpdatedAt: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Credentials':
        """Create a Credentials instance from a dictionary"""
        return cls(
            Id=data.get('Id'),
            OrgId=data['OrgId'],
            KeyVaultId=data.get('KeyVaultId'),
            ClientId=data.get('ClientId'),
            ClientSecret=data.get('ClientSecret'),
            CreatedAt=data.get('CreatedAt', datetime.now()),
            UpdatedAt=data.get('UpdatedAt', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'Id': self.Id,
            'OrgId': self.OrgId,
            'KeyVaultId': self.KeyVaultId,
            'ClientId': self.ClientId,
            'ClientSecret': self.ClientSecret,
            'CreatedAt': self.CreatedAt,
            'UpdatedAt': self.UpdatedAt
        }


@dataclass
class ExecutionLog:
    """Model for scan/workflow execution log"""
    ExecutionLogId: str
    OrgId: str
    TriggeredBy: str = "system"
    ScanType: str = "manual"
    Status: str = "running"
    StartedAt: datetime = datetime.now()
    CompletedAt: Optional[datetime] = None
    Message: Optional[str] = None
    TaskCount: Optional[int] = None
    SuccessCount: Optional[int] = None
    FailureCount: Optional[int] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExecutionLog':
        return cls(
            ExecutionLogId=data.get('ExecutionLogId'),
            OrgId=data.get('OrgId'),
            TriggeredBy=data.get('TriggeredBy', 'system'),
            ScanType=data.get('ScanType', 'manual'),
            Status=data.get('Status', 'running'),
            StartedAt=data.get('StartedAt', datetime.now()),
            CompletedAt=data.get('CompletedAt'),
            Message=data.get('Message'),
            TaskCount=data.get('TaskCount'),
            SuccessCount=data.get('SuccessCount'),
            FailureCount=data.get('FailureCount')
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            'ExecutionLogId': self.ExecutionLogId,
            'OrgId': self.OrgId,
            'TriggeredBy': self.TriggeredBy,
            'ScanType': self.ScanType,
            'Status': self.Status,
            'StartedAt': self.StartedAt,
            'CompletedAt': self.CompletedAt,
            'Message': self.Message,
            'TaskCount': self.TaskCount,
            'SuccessCount': self.SuccessCount,
            'FailureCount': self.FailureCount
        }


@dataclass
class Overview:
    """Model for dbo.App_Overview table"""
    Id: Optional[int] = None
    OrgId: int = 0
    ExecutionLogId: int = 0
    HealthScore: Optional[int] = None
    TotalProfiles: Optional[int] = None
    TotalPermissionSets: Optional[int] = None
    TotalRisks: Optional[int] = None
    HighRisks: Optional[int] = None
    MediumRisks: Optional[int] = None
    LowRisks: Optional[int] = None
    LastUpdated: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Overview':
        """Create an Overview instance from a dictionary"""
        return cls(
            Id=data.get('Id'),
            OrgId=data['OrgId'],
            ExecutionLogId=data['ExecutionLogId'],
            HealthScore=data.get('HealthScore'),
            TotalProfiles=data.get('TotalProfiles'),
            TotalPermissionSets=data.get('TotalPermissionSets'),
            TotalRisks=data.get('TotalRisks'),
            HighRisks=data.get('HighRisks'),
            MediumRisks=data.get('MediumRisks'),
            LowRisks=data.get('LowRisks'),
            LastUpdated=data.get('LastUpdated', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'Id': self.Id,
            'OrgId': self.OrgId,
            'ExecutionLogId': self.ExecutionLogId,
            'HealthScore': self.HealthScore,
            'TotalProfiles': self.TotalProfiles,
            'TotalPermissionSets': self.TotalPermissionSets,
            'TotalRisks': self.TotalRisks,
            'HighRisks': self.HighRisks,
            'MediumRisks': self.MediumRisks,
            'LowRisks': self.LowRisks,
            'LastUpdated': self.LastUpdated
        }


@dataclass
class HealthCheck:
    """Model for dbo.App_HealthCheck table"""
    Id: Optional[int] = None
    OrgId: int = 0
    ExecutionLogId: str = ""  # String to handle both UUID (local) and integer (production)
    RiskType: str = ""
    Setting: str = ""
    SettingGroup: str = ""
    OrgValue: Optional[str] = None
    StandardValue: Optional[str] = None
    LastUpdated: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HealthCheck':
        """Create a HealthCheck instance from a dictionary"""
        return cls(
            Id=data.get('Id'),
            OrgId=data['OrgId'],
            ExecutionLogId=str(data['ExecutionLogId']),  # Ensure it's always a string
            RiskType=data['RiskType'],
            Setting=data['Setting'],
            SettingGroup=data['SettingGroup'],
            OrgValue=data.get('OrgValue'),
            StandardValue=data.get('StandardValue'),
            LastUpdated=data.get('LastUpdated', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'Id': self.Id,
            'OrgId': self.OrgId,
            'ExecutionLogId': self.ExecutionLogId,
            'RiskType': self.RiskType,
            'Setting': self.Setting,
            'SettingGroup': self.SettingGroup,
            'OrgValue': self.OrgValue,
            'StandardValue': self.StandardValue,
            'LastUpdated': self.LastUpdated
        }

    def to_table_entity(self) -> Dict[str, Any]:
        """Convert to Azure Table Storage entity"""
        return {
            'PartitionKey': str(self.OrgId),
            'RowKey': f"{self.Setting}-{self.LastUpdated.strftime('%Y%m%d%H%M%S%f') if self.LastUpdated else ''}",
            'OrgId': str(self.OrgId),
            'ExecutionLogId': self.ExecutionLogId,  # Already a string
            'RiskType': self.RiskType,
            'Setting': self.Setting,
            'SettingGroup': self.SettingGroup,
            'OrgValue': self.OrgValue or '',
            'StandardValue': self.StandardValue or '',
            'LastUpdated': self.LastUpdated.isoformat() if self.LastUpdated else ''
        }


@dataclass
class ProfilePermission:
    """Model for dbo.App_ProfilePermissions table"""
    Id: Optional[int] = None
    OrgId: int = 0
    ExecutionLogId: str = ""  # String to handle both UUID (local) and integer (production)
    SalesforceId: str = ""
    ProfileName: str = ""
    ObjectName: str = ""
    Read: bool = False
    Create: bool = False
    Edit: bool = False
    Delete: bool = False
    ViewAllRecords: bool = False
    ModifyAllRecords: bool = False
    SecurityRisk: Optional[str] = None
    LastUpdated: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProfilePermission':
        """Create a ProfilePermission instance from a dictionary"""
        return cls(
            Id=data.get('Id'),
            OrgId=data['OrgId'],
            ExecutionLogId=str(data['ExecutionLogId']),  # Ensure it's always a string
            SalesforceId=data['SalesforceId'],
            ProfileName=data['ProfileName'],
            ObjectName=data['ObjectName'],
            Read=data.get('Read', False),
            Create=data.get('Create', False),
            Edit=data.get('Edit', False),
            Delete=data.get('Delete', False),
            ViewAllRecords=data.get('ViewAllRecords', False),
            ModifyAllRecords=data.get('ModifyAllRecords', False),
            SecurityRisk=data.get('SecurityRisk'),
            LastUpdated=data.get('LastUpdated', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'Id': self.Id,
            'OrgId': self.OrgId,
            'ExecutionLogId': self.ExecutionLogId,
            'SalesforceId': self.SalesforceId,
            'ProfileName': self.ProfileName,
            'ObjectName': self.ObjectName,
            'Read': self.Read,
            'Create': self.Create,
            'Edit': self.Edit,
            'Delete': self.Delete,
            'ViewAllRecords': self.ViewAllRecords,
            'ModifyAllRecords': self.ModifyAllRecords,
            'SecurityRisk': self.SecurityRisk,
            'LastUpdated': self.LastUpdated
        }


@dataclass
class Task:
    """Model for background task management"""
    TaskId: Optional[str] = None
    TaskType: str = ""
    OrgId: str = ""
    UserId: str = ""
    Status: str = "pending"
    Priority: str = "medium"
    Progress: int = 0
    Message: str = ""
    Result: Optional[str] = None
    CreatedAt: datetime = datetime.now()
    UpdatedAt: datetime = datetime.now()
    CompletedAt: Optional[datetime] = None
    ScheduledTime: Optional[datetime] = None
    RetryCount: int = 0
    ExecutionLogId: Optional[str] = None
    Params: Optional[Dict[str, Any]] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        """Create a Task instance from a dictionary"""
        return cls(
            TaskId=data.get('TaskId'),
            TaskType=data.get('TaskType', ''),
            OrgId=data.get('OrgId', ''),
            UserId=data.get('UserId', ''),
            Status=data.get('Status', 'pending'),
            Priority=data.get('Priority', 'medium'),
            Progress=data.get('Progress', 0),
            Message=data.get('Message', ''),
            Result=data.get('Result'),
            CreatedAt=data.get('CreatedAt', datetime.now()),
            UpdatedAt=data.get('UpdatedAt', datetime.now()),
            CompletedAt=data.get('CompletedAt'),
            ScheduledTime=data.get('ScheduledTime'),
            RetryCount=data.get('RetryCount', 0),
            ExecutionLogId=data.get('ExecutionLogId'),
            Params=data.get('Params')
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'TaskId': self.TaskId,
            'TaskType': self.TaskType,
            'OrgId': self.OrgId,
            'UserId': self.UserId,
            'Status': self.Status,
            'Priority': self.Priority,
            'Progress': self.Progress,
            'Message': self.Message,
            'Result': self.Result,
            'CreatedAt': self.CreatedAt,
            'UpdatedAt': self.UpdatedAt,
            'CompletedAt': self.CompletedAt,
            'ScheduledTime': self.ScheduledTime,
            'RetryCount': self.RetryCount,
            'ExecutionLogId': self.ExecutionLogId,
            'Params': self.Params
        }

    def to_table_entity(self) -> Dict[str, Any]:
        """Convert to Azure Table Storage entity"""
        import json
        return {
            'PartitionKey': f'task_{self.OrgId}',
            'RowKey': self.TaskId,
            'TaskType': self.TaskType,
            'OrgId': self.OrgId,
            'UserId': self.UserId,
            'Status': self.Status,
            'Priority': self.Priority,
            'Progress': self.Progress,
            'Message': self.Message,
            'Result': self.Result or '',
            'CreatedAt': self.CreatedAt.isoformat() if self.CreatedAt else '',
            'UpdatedAt': self.UpdatedAt.isoformat() if self.UpdatedAt else '',
            'CompletedAt': self.CompletedAt.isoformat() if self.CompletedAt else '',
            'ScheduledTime': self.ScheduledTime.isoformat() if self.ScheduledTime else '',
            'RetryCount': self.RetryCount,
            'ExecutionLogId': self.ExecutionLogId or '',
            'Params': json.dumps(self.Params) if self.Params else ''
        }


@dataclass
class GuestUserRisk:
    """Model for dbo.App_GuestUserRisks table"""
    Id: Optional[int] = None
    OrgId: int = 0
    ExecutionLogId: str = ""  # String to handle both UUID (local) and integer (production)
    RiskType: str = ""
    Description: str = ""
    Impact: str = ""
    Recommendation: str = ""
    LastUpdated: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GuestUserRisk':
        """Create a GuestUserRisk instance from a dictionary"""
        return cls(
            Id=data.get('Id'),
            OrgId=data['OrgId'],
            ExecutionLogId=str(data['ExecutionLogId']),  # Ensure it's always a string
            RiskType=data['RiskType'],
            Description=data['Description'],
            Impact=data['Impact'],
            Recommendation=data['Recommendation'],
            LastUpdated=data.get('LastUpdated', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'Id': self.Id,
            'OrgId': self.OrgId,
            'ExecutionLogId': self.ExecutionLogId,
            'RiskType': self.RiskType,
            'Description': self.Description,
            'Impact': self.Impact,
            'Recommendation': self.Recommendation,
            'LastUpdated': self.LastUpdated
        }


@dataclass
class ProfileAssignmentCount:
    """Model for dbo.App_ProfileAssignmentCount table"""
    Id: Optional[int] = None
    ExecutionLogId: Optional[str] = None
    ProfileName: str = ""
    PermissionSetName: str = ""
    AssignmentCount: int = 0
    AssignedProfiles: Optional[str] = None
    Type: str = ""
    CreatedAt: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProfileAssignmentCount':
        """Create a ProfileAssignmentCount instance from a dictionary"""
        return cls(
            Id=data.get('Id'),
            ExecutionLogId=data.get('ExecutionLogId'),
            ProfileName=data.get('ProfileName', ''),
            PermissionSetName=data.get('PermissionSetName', ''),
            AssignmentCount=data.get('AssignmentCount', 0),
            AssignedProfiles=data.get('AssignedProfiles'),
            Type=data.get('Type', ''),
            CreatedAt=data.get('CreatedAt', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'Id': self.Id,
            'ExecutionLogId': self.ExecutionLogId,
            'ProfileName': self.ProfileName,
            'PermissionSetName': self.PermissionSetName,
            'AssignmentCount': self.AssignmentCount,
            'AssignedProfiles': self.AssignedProfiles,
            'Type': self.Type,
            'CreatedAt': self.CreatedAt
        }

    def to_table_entity(self) -> Dict[str, Any]:
        """Convert to Azure Table Storage entity"""
        return {
            'PartitionKey': 'profile_assignment_count',
            'RowKey': f"{self.ProfileName}-{self.PermissionSetName}-{self.CreatedAt.strftime('%Y%m%d%H%M%S%f') if self.CreatedAt else ''}",
            'ExecutionLogId': self.ExecutionLogId or '',
            'ProfileName': self.ProfileName,
            'PermissionSetName': self.PermissionSetName,
            'AssignmentCount': self.AssignmentCount,
            'AssignedProfiles': self.AssignedProfiles or '',
            'Type': self.Type,
            'CreatedAt': self.CreatedAt.isoformat() if self.CreatedAt else ''
        }


@dataclass
class PoliciesResult:
    """Model for dbo.App_PoliciesResult table - Updated for old monolithic format"""
    Id: Optional[int] = None
    OrgPolicyId: Optional[str] = None
    PolicyId: Optional[str] = None
    ExecutionLogId: Optional[str] = None
    OrgValue: Optional[str] = None
    OWASPCategory: Optional[str] = None
    StandardValue: Optional[str] = None
    IssueDescription: Optional[str] = None
    Recommendations: Optional[str] = None
    Severity: Optional[str] = None
    Weakness: Optional[str] = None
    IntegrationId: Optional[str] = None
    CreatedAt: datetime = datetime.now()
    Type: Optional[str] = None
    ProfileName: Optional[str] = None
    PermissionSetName: Optional[str] = None
    Setting: Optional[str] = None
    SettingGroup: Optional[str] = None
    UserLicense: Optional[str] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PoliciesResult':
        """Create a PoliciesResult instance from a dictionary"""
        return cls(
            Id=data.get('Id'),
            OrgPolicyId=data.get('OrgPolicyId'),
            PolicyId=data.get('PolicyId'),
            ExecutionLogId=data.get('ExecutionLogId'),
            OrgValue=data.get('OrgValue'),
            OWASPCategory=data.get('OWASPCategory'),
            StandardValue=data.get('StandardValue'),
            IssueDescription=data.get('IssueDescription'),
            Recommendations=data.get('Recommendations'),
            Severity=data.get('Severity'),
            Weakness=data.get('Weakness'),
            IntegrationId=data.get('IntegrationId'),
            CreatedAt=data.get('CreatedAt', datetime.now()),
            Type=data.get('Type'),
            ProfileName=data.get('ProfileName'),
            PermissionSetName=data.get('PermissionSetName'),
            Setting=data.get('Setting'),
            SettingGroup=data.get('SettingGroup'),
            UserLicense=data.get('UserLicense')
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'Id': self.Id,
            'OrgPolicyId': self.OrgPolicyId,
            'PolicyId': self.PolicyId,
            'ExecutionLogId': self.ExecutionLogId,
            'OrgValue': self.OrgValue,
            'OWASPCategory': self.OWASPCategory,
            'StandardValue': self.StandardValue,
            'IssueDescription': self.IssueDescription,
            'Recommendations': self.Recommendations,
            'Severity': self.Severity,
            'Weakness': self.Weakness,
            'IntegrationId': self.IntegrationId,
            'CreatedAt': self.CreatedAt,
            'Type': self.Type,
            'ProfileName': self.ProfileName,
            'PermissionSetName': self.PermissionSetName,
            'Setting': self.Setting,
            'SettingGroup': self.SettingGroup,
            'UserLicense': self.UserLicense
        }

    def to_table_entity(self) -> Dict[str, Any]:
        """Convert to Azure Table Storage entity - Updated for old monolithic format"""
        return {
            'PartitionKey': self.IntegrationId or 'unknown',
            'RowKey': f"{self.ProfileName or self.PermissionSetName or 'unknown'}-{self.CreatedAt.strftime('%Y%m%d%H%M%S%f') if self.CreatedAt else ''}",
            'OrgPolicyId': self.OrgPolicyId or '',
            'PolicyId': self.PolicyId or '',
            'ExecutionLogId': self.ExecutionLogId or '',
            'OrgValue': self.OrgValue or '',
            'OWASPCategory': self.OWASPCategory or '',
            'StandardValue': self.StandardValue or '',
            'IssueDescription': self.IssueDescription or '',
            'Recommendations': self.Recommendations or '',
            'Severity': self.Severity or '',
            'Weakness': self.Weakness or '',
            'IntegrationId': self.IntegrationId or '',
            'CreatedAt': self.CreatedAt.isoformat() if self.CreatedAt else '',
            'Type': self.Type or '',
            'ProfileName': self.ProfileName or '',
            'PermissionSetName': self.PermissionSetName or '',
            'Setting': self.Setting or '',
            'SettingGroup': self.SettingGroup or '',
            'UserLicense': self.UserLicense or ''
        }


# User Management Models (from original database_models.py)

@dataclass
class UserAccount:
    """Model for dbo.User_Account table"""
    UserId: Optional[int] = None  # PK, auto-increment
    FirstName: str = ""
    MiddleName: str = ""
    LastName: str = ""
    DoB: Optional[date] = None
    Email: str = ""
    Contact: Optional[str] = None
    State: str = ""
    Country: str = ""
    Organization: str = ""  # Added Organization field

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserAccount':
        """Create a UserAccount instance from a dictionary"""
        return cls(
            UserId=data.get('UserId'),
            FirstName=data.get('FirstName', ''),
            MiddleName=data.get('MiddleName', ''),
            LastName=data.get('LastName', ''),
            DoB=data.get('DoB'),
            Email=data.get('Email', ''),
            Contact=data.get('Contact'),
            State=data.get('State', ''),
            Country=data.get('Country', ''),
            Organization=data.get('Organization', '')
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'UserId': self.UserId,
            'FirstName': self.FirstName,
            'MiddleName': self.MiddleName,
            'LastName': self.LastName,
            'DoB': self.DoB,
            'Email': self.Email,
            'Contact': self.Contact,
            'State': self.State,
            'Country': self.Country,
            'Organization': self.Organization
        }

    def to_table_entity(self) -> Dict[str, Any]:
        """Convert to Azure Table Storage entity"""
        return {
            'PartitionKey': 'user_account',
            'RowKey': self.Email,
            'FirstName': self.FirstName,
            'MiddleName': self.MiddleName,
            'LastName': self.LastName,
            'DoB': self.DoB.isoformat() if self.DoB else '',
            'Email': self.Email,
            'Contact': str(self.Contact) if self.Contact else '',
            'State': self.State,
            'Country': self.Country,
            'Organization': self.Organization
        }


@dataclass
class HashingAlgorithm:
    """Model for dbo.HashingAlgorithm table"""
    HashAlgorithmId: Optional[int] = None  # PK, auto-increment
    AlgorithmName: str = ""

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HashingAlgorithm':
        """Create a HashingAlgorithm instance from a dictionary"""
        return cls(
            HashAlgorithmId=data.get('HashAlgorithmId'),
            AlgorithmName=data.get('AlgorithmName', '')
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'HashAlgorithmId': self.HashAlgorithmId,
            'AlgorithmName': self.AlgorithmName
        }

    def to_table_entity(self) -> Dict[str, Any]:
        """Convert to Azure Table Storage entity"""
        return {
            'PartitionKey': 'hashing_algorithm',
            'RowKey': str(self.HashAlgorithmId) if self.HashAlgorithmId else str(hash(self.AlgorithmName)),
            'AlgorithmName': self.AlgorithmName
        }


@dataclass
class UserLogin:
    """Model for dbo.App_User_Login table"""
    UserId: int  # PK, FK to User_Account
    Username: str
    PasswordHash: str
    PasswordSalt: str
    HashAlgorithmId: int  # FK to HashingAlgorithm

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserLogin':
        """Create a UserLogin instance from a dictionary"""
        return cls(
            UserId=data['UserId'],
            Username=data.get('Username', ''),
            PasswordHash=data.get('PasswordHash', ''),
            PasswordSalt=data.get('PasswordSalt', ''),
            HashAlgorithmId=data.get('HashAlgorithmId', 1)  # Default to algorithm ID 1
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'UserId': self.UserId,
            'Username': self.Username,
            'PasswordHash': self.PasswordHash,
            'PasswordSalt': self.PasswordSalt,
            'HashAlgorithmId': self.HashAlgorithmId
        }

    def to_table_entity(self) -> Dict[str, Any]:
        """Convert to Azure Table Storage entity"""
        return {
            'PartitionKey': 'user_login',
            'RowKey': self.Username,
            'UserId': self.UserId,
            'PasswordHash': self.PasswordHash,
            'PasswordSalt': self.PasswordSalt,
            'HashAlgorithmId': self.HashAlgorithmId
        }


