"""
Mock Service Bus Implementation for Local Testing

This module provides a mock Service Bus client that sends HTTP requests
directly to the SFDC service instead of using Azure Service Bus.
Perfect for offline local development and testing.
"""

import logging
import json
import requests
from typing import Dict, Any, Optional
import os

logger = logging.getLogger(__name__)

class MockServiceBusClient:
    """Mock Service Bus client for local testing"""
    
    def __init__(self, sfdc_service_url: str = None):
        """
        Initialize mock Service Bus client
        
        Args:
            sfdc_service_url: URL of the SFDC service (defaults to localhost:7071)
        """
        self.sfdc_service_url = sfdc_service_url or os.getenv('SFDC_SERVICE_URL', 'http://localhost:7071')
        logger.info(f"Mock Service Bus initialized with SFDC URL: {self.sfdc_service_url}")
        
    def send_message(self, topic_name: str, message_data: Dict[str, Any]) -> bool:
        """
        Send message directly to SFDC service HTTP endpoint

        Args:
            topic_name: Topic name (ignored in mock)
            message_data: Message data to send

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Mock Service Bus: Sending message to {self.sfdc_service_url}")
            logger.debug(f"Message data: {json.dumps(message_data, indent=2)}")

            # Send HTTP request to SFDC service mock endpoint
            response = requests.post(
                f"{self.sfdc_service_url}/api/servicebus/mock",
                json=message_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code == 200:
                logger.info(f"Mock Service Bus: Message sent successfully")
                return True
            else:
                logger.error(f"Mock Service Bus: Failed to send message - {response.status_code}: {response.text}")
                return False

        except requests.exceptions.ConnectionError:
            logger.warning(f"Mock Service Bus: SFDC service not available at {self.sfdc_service_url}")
            logger.info("Mock Service Bus: This is normal if SFDC service is not running")
            return False
        except requests.exceptions.Timeout:
            logger.error(f"Mock Service Bus: Timeout sending message to SFDC service")
            return False
        except Exception as e:
            logger.error(f"Mock Service Bus: Error sending message - {str(e)}")
            return False

    def send_task_message(self, task_data: Dict[str, Any], priority: str = "medium") -> bool:
        """
        Send task message to SFDC service via HTTP

        Args:
            task_data: Task data to send
            priority: Task priority (high, medium, low)

        Returns:
            bool: True if successful, False otherwise
        """
        task_id = task_data.get('task_id', 'unknown')
        priority = priority.lower()

        logger.info(f"Mock Service Bus: Sending task {task_id} to SFDC service with priority {priority}")

        try:
            # Send to the appropriate priority endpoint
            endpoint_url = f"{self.sfdc_service_url}/api/task/{priority}"

            response = requests.post(
                endpoint_url,
                json=task_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code == 200:
                logger.info(f"Successfully sent task {task_id} to SFDC service")
                return True
            else:
                logger.warning(f"SFDC service returned status {response.status_code} for task {task_id}")
                return True  # Return True anyway for local development

        except requests.exceptions.ConnectionError:
            logger.warning(f"Could not connect to SFDC service at {self.sfdc_service_url} for task {task_id}")
            logger.info("This is normal if SFDC service is not running")
            return True  # Return True anyway for local development
        except Exception as e:
            logger.warning(f"Could not send task {task_id} to SFDC service: {str(e)}")
            return True  # Return True anyway for local development

    def send_task_to_sfdc(self, task_data: Dict[str, Any], priority: str = "medium") -> bool:
        """
        Send task to SFDC service (alias for send_task_message for compatibility)

        Args:
            task_data: Task data to send
            priority: Task priority

        Returns:
            bool: True if successful, False otherwise
        """
        return self.send_task_message(task_data, priority)


class MockServiceBusMessage:
    """Mock Service Bus message for testing"""
    
    def __init__(self, message_data: Dict[str, Any]):
        self.message_data = message_data
        self._body = json.dumps(message_data).encode('utf-8')
        
    def get_body(self) -> bytes:
        """Get message body as bytes"""
        return self._body

def get_mock_service_bus_client() -> MockServiceBusClient:
    """Get mock Service Bus client for local testing"""
    return MockServiceBusClient()

# Mock Service Bus sender context manager
class MockServiceBusSender:
    """Mock Service Bus sender for context manager usage"""
    
    def __init__(self, client: MockServiceBusClient, topic_name: str):
        self.client = client
        self.topic_name = topic_name
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
        
    def send_messages(self, message):
        """Send a single message"""
        if hasattr(message, 'body'):
            # Handle ServiceBusMessage-like object
            message_data = json.loads(message.body)
        else:
            # Handle raw message data
            message_data = message
            
        return self.client.send_message(self.topic_name, message_data)

class MockServiceBusClientWithSender:
    """Mock Service Bus client that mimics Azure SDK interface"""
    
    def __init__(self):
        self.mock_client = get_mock_service_bus_client()
        
    def get_topic_sender(self, topic_name: str) -> MockServiceBusSender:
        """Get topic sender (mock)"""
        return MockServiceBusSender(self.mock_client, topic_name)

def create_mock_service_bus_message(data: Dict[str, Any]) -> MockServiceBusMessage:
    """Create a mock Service Bus message"""
    return MockServiceBusMessage(data)

# Test function for mock Service Bus
def test_mock_service_bus():
    """Test the mock Service Bus implementation"""
    print("Testing Mock Service Bus...")
    
    client = get_mock_service_bus_client()
    
    test_message = {
        "eventType": "TaskCreated",
        "eventId": "test-123",
        "timestamp": "2024-01-01T00:00:00Z",
        "data": {
            "task_id": "test-123",
            "task_type": "metadata_extraction",
            "org_id": "test-org",
            "user_id": "test-user",
            "priority": "high"
        }
    }
    
    success = client.send_message("atomsec-tasks", test_message)
    print(f"Mock Service Bus test result: {success}")
    
    return success

if __name__ == "__main__":
    test_mock_service_bus()
