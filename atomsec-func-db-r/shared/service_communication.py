"""
Service Communication Module

This module provides communication capabilities between microservices in the AtomSec architecture.
It handles HTTP-based communication between the DB service and SFDC service.

Features:
- HTTP client for service-to-service communication
- Authentication and authorization handling
- Circuit breaker pattern for resilience
- Request/response logging and monitoring
- Retry mechanisms with exponential backoff
- Environment-aware configuration

Best practices implemented:
- Clean separation of concerns
- Comprehensive error handling
- Proper logging and monitoring
- Type hints for better code clarity
- Environment-aware configuration
"""

import logging
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import os
import time

# Import shared modules
from shared.common import is_local_dev
from shared.azure_services import get_secret

# Configure module-level logger
logger = logging.getLogger(__name__)

# Service configuration
SFDC_SERVICE_BASE_URL = os.getenv("SFDC_SERVICE_BASE_URL", "https://atomsec-func-sfdc.azurewebsites.net")
LOCAL_SFDC_SERVICE_URL = "http://localhost:7072"  # Local development URL

# Request configuration
DEFAULT_TIMEOUT = 30  # seconds
MAX_RETRIES = 3
RETRY_DELAY = 1  # seconds
CIRCUIT_BREAKER_THRESHOLD = 5  # failures before opening circuit
CIRCUIT_BREAKER_TIMEOUT = 60  # seconds to wait before trying again


class CircuitBreakerState:
    """Circuit breaker state management"""
    
    def __init__(self):
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def record_success(self):
        """Record a successful request"""
        self.failure_count = 0
        self.state = "CLOSED"
        self.last_failure_time = None
    
    def record_failure(self):
        """Record a failed request"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= CIRCUIT_BREAKER_THRESHOLD:
            self.state = "OPEN"
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")
    
    def can_attempt_request(self) -> bool:
        """Check if a request can be attempted"""
        if self.state == "CLOSED":
            return True
        elif self.state == "OPEN":
            if self.last_failure_time and \
               datetime.now() - self.last_failure_time > timedelta(seconds=CIRCUIT_BREAKER_TIMEOUT):
                self.state = "HALF_OPEN"
                logger.info("Circuit breaker moved to HALF_OPEN state")
                return True
            return False
        elif self.state == "HALF_OPEN":
            return True
        
        return False


class ServiceCommunicationClient:
    """
    Client for communicating with other microservices
    
    This class provides a clean interface for making HTTP requests to other services
    with proper error handling, retries, and circuit breaker pattern.
    """
    
    def __init__(self):
        """Initialize the service communication client"""
        self.is_local = is_local_dev()
        self.sfdc_base_url = LOCAL_SFDC_SERVICE_URL if self.is_local else SFDC_SERVICE_BASE_URL
        self.circuit_breaker = CircuitBreakerState()
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            "Content-Type": "application/json",
            "User-Agent": "AtomSec-DB-Service/1.0"
        })
        
        logger.info(f"Service communication client initialized for {'local' if self.is_local else 'production'} environment")
        logger.info(f"SFDC service URL: {self.sfdc_base_url}")
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """
        Get authentication headers for service-to-service communication
        
        Returns:
            Dict[str, str]: Authentication headers
        """
        try:
            if self.is_local:
                # For local development, use a simple API key
                return {
                    "X-API-Key": "local-dev-key",
                    "X-Service": "atomsec-db-service"
                }
            else:
                # For production, use proper authentication
                # This could be JWT tokens, managed identity, or API keys from Key Vault
                api_key = get_secret("SFDC_SERVICE_API_KEY")
                if api_key:
                    return {
                        "X-API-Key": api_key,
                        "X-Service": "atomsec-db-service"
                    }
                else:
                    logger.warning("No API key found for SFDC service authentication")
                    return {}
        except Exception as e:
            logger.error(f"Error getting auth headers: {str(e)}")
            return {}
    
    def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Make an HTTP request with retry logic and circuit breaker
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint (without base URL)
            data: Request body data
            params: Query parameters
            timeout: Request timeout in seconds
        
        Returns:
            Dict[str, Any]: Response data if successful, None otherwise
        """
        if not self.circuit_breaker.can_attempt_request():
            logger.warning("Circuit breaker is OPEN, skipping request")
            return None
        
        url = f"{self.sfdc_base_url}/api/{endpoint.lstrip('/')}"
        
        if timeout is None:
            timeout = DEFAULT_TIMEOUT
        
        # Get authentication headers
        auth_headers = self._get_auth_headers()
        
        for attempt in range(MAX_RETRIES):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")
                
                response = self.session.request(
                    method=method,
                    url=url,
                    json=data,
                    params=params,
                    headers=auth_headers,
                    timeout=timeout
                )
                
                if response.status_code == 200:
                    self.circuit_breaker.record_success()
                    try:
                        return response.json()
                    except json.JSONDecodeError:
                        logger.warning(f"Invalid JSON response from {url}")
                        return {"success": True, "message": "Request successful"}
                
                elif response.status_code in [401, 403]:
                    logger.error(f"Authentication failed for {url}: {response.status_code}")
                    self.circuit_breaker.record_failure()
                    return None
                
                elif response.status_code >= 500:
                    logger.warning(f"Server error from {url}: {response.status_code}")
                    if attempt < MAX_RETRIES - 1:
                        time.sleep(RETRY_DELAY * (2 ** attempt))  # Exponential backoff
                        continue
                    else:
                        self.circuit_breaker.record_failure()
                        return None
                
                else:
                    logger.warning(f"Unexpected response from {url}: {response.status_code}")
                    try:
                        error_data = response.json()
                        return {"success": False, "error": error_data}
                    except json.JSONDecodeError:
                        return {"success": False, "error": f"HTTP {response.status_code}"}
                
            except requests.exceptions.Timeout:
                logger.warning(f"Timeout for {method} {url} (attempt {attempt + 1})")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY * (2 ** attempt))
                    continue
                else:
                    self.circuit_breaker.record_failure()
                    return None
            
            except requests.exceptions.ConnectionError:
                logger.warning(f"Connection error for {method} {url} (attempt {attempt + 1})")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY * (2 ** attempt))
                    continue
                else:
                    self.circuit_breaker.record_failure()
                    return None
            
            except Exception as e:
                logger.error(f"Unexpected error for {method} {url}: {str(e)}")
                self.circuit_breaker.record_failure()
                return None
        
        return None
    
    def send_task_to_sfdc(self, task_data: Dict[str, Any], priority: str = "medium") -> bool:
        """
        Send a task to the SFDC service for processing
        
        Args:
            task_data: Task data to send
            priority: Task priority level
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            endpoint = f"task/{priority}"
            
            logger.info(f"Sending task {task_data.get('task_id')} to SFDC service")
            
            response = self._make_request("POST", endpoint, data=task_data)
            
            if response and response.get("success"):
                logger.info(f"Task {task_data.get('task_id')} sent successfully to SFDC service")
                return True
            else:
                error = response.get("error") if response else "No response"
                logger.error(f"Failed to send task to SFDC service: {error}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending task to SFDC service: {str(e)}")
            return False
    
    def get_sfdc_health(self) -> Optional[Dict[str, Any]]:
        """
        Get health status from SFDC service
        
        Returns:
            Dict[str, Any]: Health status if successful, None otherwise
        """
        try:
            response = self._make_request("GET", "health")
            return response
        except Exception as e:
            logger.error(f"Error getting SFDC health: {str(e)}")
            return None
    
    def authenticate_sfdc_integration(self, integration_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Authenticate a Salesforce integration
        
        Args:
            integration_data: Integration authentication data
        
        Returns:
            Dict[str, Any]: Authentication result if successful, None otherwise
        """
        try:
            response = self._make_request("POST", "auth/sfdc", data=integration_data)
            return response
        except Exception as e:
            logger.error(f"Error authenticating SFDC integration: {str(e)}")
            return None


# Global service communication client instance
_service_client = None


def get_service_communication_client() -> ServiceCommunicationClient:
    """
    Get the global service communication client instance
    
    Returns:
        ServiceCommunicationClient: The client instance
    """
    global _service_client
    
    if _service_client is None:
        _service_client = ServiceCommunicationClient()
        logger.debug("Created global service communication client instance")
    
    return _service_client
