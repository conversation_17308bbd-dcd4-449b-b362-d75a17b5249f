# Azure Portal Guide: Getting Connection Strings

This guide shows you exactly where to find the connection strings and URLs needed for your database service deployment.

## Prerequisites

1. Access to Azure Portal
2. Appropriate permissions to view resources
3. Azure CLI installed (optional, for automated discovery)

## Method 1: Using Azure CLI (Recommended)

Run the discovery script to automatically find all resources:

```bash
cd atomsec-func-db-r
chmod +x scripts/find_azure_resources.sh
./scripts/find_azure_resources.sh
```

This will show you all storage accounts, service buses, and key vaults with their connection strings.

## Method 2: Manual Azure Portal Navigation

### 1. Azure Storage Connection String

#### Step-by-step:

1. **Open Azure Portal** → https://portal.azure.com
2. **Search for "Storage accounts"** in the search bar
3. **Click on your storage account** (likely named `statomsecdev`)
4. **In the left menu**, click **"Access keys"**
5. **Under "key1"**, click **"Show"** next to "Connection string"
6. **Copy the connection string**

#### Expected format:
```
DefaultEndpointsProtocol=https;AccountName=statomsecdev;AccountKey=YOUR_ACCOUNT_KEY;EndpointSuffix=core.windows.net
```

### 2. Azure Service Bus Connection String

#### Step-by-step:

1. **Open Azure Portal** → https://portal.azure.com
2. **Search for "Service Bus namespaces"** in the search bar
3. **Click on your Service Bus namespace** (likely named `sb-atomsec-dev`)
4. **In the left menu**, click **"Shared access policies"**
5. **Click on "RootManageSharedAccessKey"**
6. **Copy the "Primary Connection String"**

#### Expected format:
```
Endpoint=sb://sb-atomsec-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=YOUR_SHARED_ACCESS_KEY
```

### 3. Key Vault URL

#### Step-by-step:

1. **Open Azure Portal** → https://portal.azure.com
2. **Search for "Key vaults"** in the search bar
3. **Click on your Key Vault** (likely named `akv-atomsec-dev`)
4. **On the Overview page**, look for **"DNS Name"**
5. **Copy the DNS Name** (it should end with `.vault.azure.net`)

#### Expected format:
```
https://akv-atomsec-dev.vault.azure.net/
```

## Method 3: Resource Group Navigation

If you know your resource group names, you can navigate directly:

### For Storage Account:
1. Go to your resource group (likely `atomsec-dev-data`)
2. Find the storage account resource
3. Follow steps 4-6 from Method 2

### For Service Bus:
1. Go to your resource group (likely `atomsec-dev-backend`)
2. Find the Service Bus namespace resource
3. Follow steps 4-6 from Method 2

### For Key Vault:
1. Go to your resource group (likely `atomsec-dev-backend`)
2. Find the Key Vault resource
3. Follow steps 4-5 from Method 2

## Using the Values

Once you have the connection strings, you can:

### Option 1: Update the Pipeline Configuration

Replace the placeholder values in `pipeline-func-db-dev.yml`:

```yaml
# Replace these placeholder values:
AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=statomsecdev;AccountKey=***;EndpointSuffix=core.windows.net"
AZURE_SERVICE_BUS_CONNECTION_STRING="Endpoint=sb://sb-atomsec-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=***"
KEY_VAULT_URL="https://akv-atomsec-dev.vault.azure.net/"
```

### Option 2: Set Environment Variables

Set these as environment variables before running the fix script:

```bash
export AZURE_STORAGE_CONNECTION_STRING="your_storage_connection_string"
export AZURE_SERVICE_BUS_CONNECTION_STRING="your_service_bus_connection_string"
export KEY_VAULT_URL="https://akv-atomsec-dev.vault.azure.net/"
```

### Option 3: Use Azure Key Vault (Recommended for Production)

Store the connection strings in Key Vault and reference them:

```bash
# Get connection strings from Key Vault
STORAGE_CONNECTION_STRING=$(az keyvault secret show --vault-name akv-atomsec-dev --name StorageConnectionString --query value -o tsv)
SERVICE_BUS_CONNECTION_STRING=$(az keyvault secret show --vault-name akv-atomsec-dev --name ServiceBusConnectionString --query value -o tsv)
```

## Security Best Practices

1. **Never commit connection strings to source control**
2. **Use Azure Key Vault for production environments**
3. **Use Managed Identity when possible**
4. **Rotate access keys regularly**
5. **Use least privilege access**

## Troubleshooting

### If you can't find the resources:

1. **Check your subscription**: Make sure you're in the correct Azure subscription
2. **Check permissions**: Ensure you have Reader or Contributor access
3. **Check resource groups**: Look in different resource groups
4. **Use Azure CLI**: Run the discovery script to find all resources

### If connection strings are not working:

1. **Check the format**: Ensure the connection string is complete
2. **Check permissions**: Verify the access keys are valid
3. **Check network access**: Ensure the function app can reach the resources
4. **Check Key Vault access**: If using Key Vault, ensure the function app has access

## Next Steps

1. **Run the discovery script** to find your resources
2. **Copy the connection strings** to your pipeline configuration
3. **Test the connection** using the health endpoints
4. **Deploy the updated configuration** 