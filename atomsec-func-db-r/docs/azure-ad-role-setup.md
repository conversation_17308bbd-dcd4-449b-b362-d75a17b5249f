# Azure AD Role Assignment Setup Guide

This guide shows how to configure Azure AD app roles for proper permission management instead of hardcoded user lists.

## 1. Create App Roles in Azure AD

### Navigate to your App Registration
1. Go to Azure Portal → Azure Active Directory
2. Navigate to **App registrations**
3. Find your application (likely `AtomSec Function App` or similar)
4. Click on **App roles**

### Define App Roles
Click **Create app role** and add these roles:

#### Role 1: AtomSec Viewer
```json
{
  "displayName": "AtomSec Viewer",
  "description": "Can view integrations and security data",
  "value": "atomsec.viewer",
  "id": "generate-new-guid-1",
  "allowedMemberTypes": ["User"],
  "isEnabled": true
}
```

#### Role 2: AtomSec User  
```json
{
  "displayName": "AtomSec User",
  "description": "Can read and write integrations",
  "value": "atomsec.user", 
  "id": "generate-new-guid-2",
  "allowedMemberTypes": ["User"],
  "isEnabled": true
}
```

#### Role 3: AtomSec Admin
```json
{
  "displayName": "AtomSec Admin", 
  "description": "Full access including delete operations",
  "value": "atomsec.admin",
  "id": "generate-new-guid-3", 
  "allowedMemberTypes": ["User"],
  "isEnabled": true
}
```

#### Role 4: AtomSec System Admin
```json
{
  "displayName": "AtomSec System Admin",
  "description": "System administrator with all permissions", 
  "value": "atomsec.system_admin",
  "id": "generate-new-guid-4",
  "allowedMemberTypes": ["User"], 
  "isEnabled": true
}
```

## 2. Assign Users to Roles

### Method A: Through Enterprise Applications
1. Go to **Azure Active Directory** → **Enterprise applications**
2. Find your application
3. Click **Users and groups**
4. Click **Add user/group**
5. Select users and assign them appropriate roles

### Method B: Through App Registration
1. In your app registration → **Owners**
2. Add users who need admin access

## 3. Configure Token to Include Roles

### Update App Registration Manifest
1. Go to your app registration → **Manifest**
2. Ensure `groupMembershipClaims` is set to include roles:
```json
{
  "groupMembershipClaims": "ApplicationGroup",
  "optionalClaims": {
    "idToken": [
      {
        "name": "roles",
        "source": null,
        "essential": false,
        "additionalProperties": []
      }
    ],
    "accessToken": [
      {
        "name": "roles", 
        "source": null,
        "essential": false,
        "additionalProperties": []
      }
    ]
  }
}
```

## 4. Example Role Assignment Commands (PowerShell)

```powershell
# Connect to Azure AD
Connect-AzureAD

# Get your application
$app = Get-AzureADApplication -Filter "DisplayName eq 'AtomSec Function App'"

# Get the service principal
$servicePrincipal = Get-AzureADServicePrincipal -Filter "AppId eq '$($app.AppId)'"

# Get app roles
$appRoles = $servicePrincipal.AppRoles

# Assign user to admin role
$user = Get-AzureADUser -ObjectId "<EMAIL>"
$adminRole = $appRoles | Where-Object {$_.Value -eq "atomsec.admin"}

New-AzureADUserAppRoleAssignment -ObjectId $user.ObjectId -PrincipalId $user.ObjectId -ResourceId $servicePrincipal.ObjectId -Id $adminRole.Id
```

## 5. Verify Role Assignment

After assignment, user tokens will include:
```json
{
  "roles": ["atomsec.admin"],
  "email": "<EMAIL>",
  "name": "User Name"
}
```

## 6. Testing Role Assignment

Use this command to test roles in your JWT token:
```bash
# Decode your JWT token to verify roles are included
echo "YOUR_JWT_TOKEN" | cut -d. -f2 | base64 -d | jq .roles
```

## Next Steps

1. Create the app roles in Azure AD
2. Assign users to appropriate roles  
3. Update your application code to use Azure AD roles
4. Test with role-assigned users
