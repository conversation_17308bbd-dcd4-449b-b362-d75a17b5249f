# Azure AD Role Management for AtomSec

This guide helps you set up Azure AD app roles using Azure CLI for proper permission management.

## 🚀 Quick Setup

### Prerequisites
```bash
# 1. Install Azure CLI (if not already installed)
brew install azure-cli

# 2. Login to Azure
az login

# 3. Verify you're in the right subscription
az account show
```

### Step 1: Create App Roles
```bash
# Run the role creation script
./scripts/create-azure-ad-roles.sh
```

This script will:
- ✅ Find your AtomSec application automatically
- ✅ Create 4 app roles (viewer, user, admin, system_admin)  
- ✅ Configure tokens to include roles
- ✅ Set up service principal for assignments

### Step 2: Assign Yourself Admin Role
```bash
# Replace with your email
./scripts/assign-user-role.sh <EMAIL> atomsec.admin
```

### Step 3: Manage Roles (Optional)
```bash
# Interactive role management
./scripts/manage-roles.sh
```

## 🔑 Available Roles

| Role | Value | Permissions | Can Delete? |
|------|--------|-------------|-------------|
| **Viewer** | `atomsec.viewer` | Read only | ❌ No |
| **User** | `atomsec.user` | Read + Write | ❌ No |
| **Admin** | `atomsec.admin` | Read + Write + Delete | ✅ **Yes** |
| **System Admin** | `atomsec.system_admin` | Full access | ✅ **Yes** |

## 🧪 Testing

### Get User Token with Roles
After role assignment, when you get a user token, it will include:
```json
{
  "email": "<EMAIL>",
  "name": "Your Name", 
  "roles": ["atomsec.admin"],  // ← This enables delete operations!
  "tid": "your-tenant-id"
}
```

### Test Delete Operations
```bash
# Test the enhanced auth system
python3 scripts/test_azure_ad_roles.py

# Test with your actual user token
python3 scripts/test_enhanced_auth.py
```

### Verify Permissions
```python
# In your application, check permissions:
from shared.permissions import can_delete_integrations, get_user_role_summary

# With user token (not application token)
user_info = get_current_user(request)
can_delete = can_delete_integrations(user_info)
summary = get_user_role_summary(user_info)

print(f"Can delete: {can_delete}")
print(f"Role summary: {summary}")
```

## 🔧 Script Details

### create-azure-ad-roles.sh
- Creates 4 app roles in your Azure AD application
- Configures token claims to include roles
- Sets up service principal for role assignments
- **Run this once** to set up the roles

### assign-user-role.sh
- Assigns a specific role to a user
- Usage: `./assign-user-role.sh <EMAIL> atomsec.admin`
- **Run this for each user** you want to give permissions

### manage-roles.sh  
- Interactive script for role management
- List roles, assignments, assign/remove roles
- **Use this for ongoing management**

## 🎯 Common Use Cases

### Give Someone Delete Access
```bash
# Assign admin role for delete operations
./scripts/assign-user-role.sh <EMAIL> atomsec.admin
```

### Remove Someone's Access
```bash
# Use interactive management
./scripts/manage-roles.sh
# Choose option 4 to remove roles
```

### List All Current Assignments
```bash
# Use interactive management  
./scripts/manage-roles.sh
# Choose option 2 to list assignments
```

### Check What Someone Can Do
```bash
# Use interactive management
./scripts/manage-roles.sh
# Choose option 5 to check user capabilities
```

## 🚨 Important Notes

1. **Use User Tokens, Not Application Tokens**
   - Delete operations require user tokens with roles
   - Application tokens have different permissions

2. **Roles Take Effect Immediately**
   - But user needs fresh token to see new roles
   - Tell users to re-authenticate after role assignment

3. **Admin Role Required for Delete**
   - Only `atomsec.admin` and `atomsec.system_admin` can delete
   - Regular users get read/write access only

4. **Test Before Production**
   - Use the test scripts to verify permissions
   - Check tokens include the expected roles

## 🔍 Troubleshooting

### Script Can't Find Application
```bash
# List all applications to find yours
az ad app list --query "[].{displayName:displayName,appId:appId}" -o table

# Use the application ID directly in the script
# The script will prompt for manual input if auto-detection fails
```

### User Not Found Error
```bash
# Check if user exists in your tenant
az ad user show --id <EMAIL>

# Make sure you're in the right tenant
az account show
```

### Roles Not Appearing in Token
```bash
# Check if token configuration is correct
az ad app show --id YOUR_APP_ID --query "optionalClaims"

# Verify role assignment
az ad app role assignment list --assignee USER_EMAIL --resource-service-principal-id SERVICE_PRINCIPAL_ID
```

## 🎉 Success!

After setup, users with admin roles can:
- ✅ List integrations
- ✅ Create integrations  
- ✅ Update integrations
- ✅ **Delete integrations** (the main goal!)
- ✅ Access admin functions

No more hardcoded user lists - everything managed through Azure AD! 🎊
