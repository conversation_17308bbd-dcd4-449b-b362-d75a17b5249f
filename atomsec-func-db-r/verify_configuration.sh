#!/bin/bash

# Verify Function App Configuration
set -e

echo "🔍 Verifying Function App Configuration..."

# Configuration
RESOURCE_GROUP="atomsec-dev-data"
FUNCTION_APP_NAME="func-atomsec-dbconnect-dev02"

echo "📋 Checking Function App: $FUNCTION_APP_NAME"
echo "📋 Resource Group: $RESOURCE_GROUP"
echo ""

# Step 1: Check if function app exists
echo "✅ Step 1: Verifying Function App exists..."
FUNCTION_APP_STATUS=$(az functionapp show --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP --query "state" --output tsv 2>/dev/null || echo "NOT_FOUND")

if [ "$FUNCTION_APP_STATUS" = "NOT_FOUND" ]; then
    echo "❌ Function App '$FUNCTION_APP_NAME' not found in resource group '$RESOURCE_GROUP'"
    exit 1
else
    echo "✅ Function App exists and is in state: $FUNCTION_APP_STATUS"
fi

# Step 2: Get current application settings
echo ""
echo "✅ Step 2: Retrieving current application settings..."
CURRENT_SETTINGS=$(az functionapp config appsettings list --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP --output json)

# Step 3: Check required settings
echo ""
echo "✅ Step 3: Checking required configuration settings..."

# Function to check if a setting exists and has a value
check_setting() {
    local setting_name=$1
    local expected_value=$2
    local is_required=$3
    
    # Extract the value from current settings
    local actual_value=$(echo "$CURRENT_SETTINGS" | jq -r ".[] | select(.name == \"$setting_name\") | .value // \"NOT_SET\"")
    
    if [ "$actual_value" = "NOT_SET" ] || [ -z "$actual_value" ]; then
        if [ "$is_required" = "true" ]; then
            echo "❌ REQUIRED: $setting_name - NOT SET"
            return 1
        else
            echo "⚠️  OPTIONAL: $setting_name - NOT SET"
            return 0
        fi
    else
        if [ "$expected_value" = "*SECRET*" ]; then
            echo "✅ REQUIRED: $setting_name - SET (secret value)"
        elif [ "$expected_value" = "*STORAGE*" ]; then
            echo "✅ REQUIRED: $setting_name - SET (storage connection)"
        elif [ "$expected_value" = "*URL*" ]; then
            echo "✅ OPTIONAL: $setting_name - SET (URL value)"
        elif [ "$actual_value" = "$expected_value" ]; then
            echo "✅ REQUIRED: $setting_name - SET correctly"
        else
            echo "⚠️  REQUIRED: $setting_name - SET but value differs from expected"
            echo "   Expected: $expected_value"
            echo "   Actual: $actual_value"
        fi
        return 0
    fi
}

# Check required settings
echo "📋 Required Settings:"
MISSING_REQUIRED=0

check_setting "AZURE_AD_CLIENT_ID" "2d313c1a-d62d-492c-869e-cf8cb9258204" "true" || MISSING_REQUIRED=$((MISSING_REQUIRED + 1))
check_setting "AZURE_AD_TENANT_ID" "41b676db-bf6f-46ae-a354-a83a1362533f" "true" || MISSING_REQUIRED=$((MISSING_REQUIRED + 1))
check_setting "AZURE_AD_CLIENT_SECRET" "*SECRET*" "true" || MISSING_REQUIRED=$((MISSING_REQUIRED + 1))
check_setting "JWT_SECRET" "*SECRET*" "true" || MISSING_REQUIRED=$((MISSING_REQUIRED + 1))
check_setting "ENVIRONMENT" "production" "true" || MISSING_REQUIRED=$((MISSING_REQUIRED + 1))
check_setting "IS_LOCAL_DEV" "false" "true" || MISSING_REQUIRED=$((MISSING_REQUIRED + 1))
check_setting "FUNCTIONS_WORKER_RUNTIME" "python" "true" || MISSING_REQUIRED=$((MISSING_REQUIRED + 1))
check_setting "AzureWebJobsStorage" "*STORAGE*" "true" || MISSING_REQUIRED=$((MISSING_REQUIRED + 1))

# Check optional settings
echo ""
echo "📋 Optional Settings:"
check_setting "KEY_VAULT_NAME" "akv-atomsec-dev" "false"
check_setting "AZURE_STORAGE_CONNECTION_STRING" "*STORAGE*" "false"
check_setting "SFDC_SERVICE_URL" "*URL*" "false"

# Step 4: Check Key Vault access
echo ""
echo "✅ Step 4: Verifying Key Vault access..."
KEY_VAULT_SECRETS=("jwt-secret" "azure-ad-client-id" "azure-ad-tenant-id" "azure-ad-client-secret")

for secret in "${KEY_VAULT_SECRETS[@]}"; do
    if az keyvault secret show --vault-name akv-atomsec-dev --name "$secret" --query value --output tsv >/dev/null 2>&1; then
        echo "✅ Key Vault secret '$secret' - ACCESSIBLE"
    else
        echo "❌ Key Vault secret '$secret' - NOT ACCESSIBLE"
    fi
done

# Step 5: Check function app health
echo ""
echo "✅ Step 5: Checking function app health..."
HEALTH_URL="https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/health"
HEALTH_RESPONSE=$(curl -s -w "%{http_code}" "$HEALTH_URL" -o /tmp/health_response 2>/dev/null || echo "000")

if [ "$HEALTH_RESPONSE" = "200" ]; then
    echo "✅ Function App is healthy (HTTP 200)"
    echo "   Health URL: $HEALTH_URL"
else
    echo "⚠️  Function App health check failed (HTTP $HEALTH_RESPONSE)"
    echo "   Health URL: $HEALTH_URL"
fi

# Step 6: Check info endpoint
echo ""
echo "✅ Step 6: Checking info endpoint..."
INFO_URL="https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/info"
INFO_RESPONSE=$(curl -s -w "%{http_code}" "$INFO_URL" -o /tmp/info_response 2>/dev/null || echo "000")

if [ "$INFO_RESPONSE" = "200" ]; then
    echo "✅ Info endpoint is working (HTTP 200)"
    echo "   Info URL: $INFO_URL"
else
    echo "⚠️  Info endpoint check failed (HTTP $INFO_RESPONSE)"
    echo "   Info URL: $INFO_URL"
fi

# Summary
echo ""
echo "📊 Configuration Verification Summary:"
echo "====================================="

if [ $MISSING_REQUIRED -eq 0 ]; then
    echo "✅ All required settings are configured"
else
    echo "❌ Missing $MISSING_REQUIRED required settings"
fi

echo ""
echo "🔗 Function App URLs:"
echo "   Health: https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/health"
echo "   Info: https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/info"
echo "   Integrations: https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/integrations"
echo ""
echo "🌐 Azure Portal: https://portal.azure.com/#@atomsec.ai/resource/subscriptions/35518353-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Web/sites/$FUNCTION_APP_NAME" 