{"name": "vg-atomsec-db-qa", "description": "Variable group for AtomSec DB Function App - QA Environment (Deployment Slot)", "_TODO": ["1. Update AZURE_STORAGE_CONNECTION_STRING with actual storage account key from Azure Portal", "2. Update AZURE_TABLE_STORAGE_CONNECTION_STRING with actual storage account key from Azure Portal", "3. Update AZURE_SERVICE_BUS_CONNECTION_STRING with actual service bus key from Azure Portal", "4. ✅ QA deployment slot created successfully", "5. ✅ QA slot URLs verified: https://func-atomsec-dbconnect-dev02-qa.azurewebsites.net (HTTP 200)", "6. Test QA deployment pipeline with updated connection strings"], "variables": {"ENVIRONMENT": {"value": "qa", "description": "Environment name"}, "AZURE_SUBSCRIPTION": {"value": "sc-atomsec-dev-data", "description": "Azure subscription service connection name"}, "SUBSCRIPTION_ID": {"value": "********-3fc5-49c1-91cd-3ab90df8d78d", "description": "Azure subscription ID"}, "RESOURCE_GROUP": {"value": "atomsec-dev-data", "description": "Azure resource group name (same as dev)"}, "FUNCTION_APP_NAME": {"value": "func-atomsec-dbconnect-dev02", "description": "Azure Function App name (same as dev)"}, "DEPLOYMENT_SLOT": {"value": "qa", "description": "Deployment slot name for QA"}, "FUNCTION_APP_URL": {"value": "https://func-atomsec-dbconnect-dev02-qa.azurewebsites.net", "description": "Function App URL with QA slot"}, "FUNCTION_APP_URL_SCM": {"value": "https://func-atomsec-dbconnect-dev02-qa.scm.azurewebsites.net", "description": "Function App SCM URL with QA slot"}, "KEY_VAULT_NAME": {"value": "akv-atomsec-dev", "description": "Azure Key Vault name (same as dev)"}, "KEY_VAULT_URL": {"value": "https://akv-atomsec-dev.vault.azure.net/", "description": "Azure Key Vault URL (same as dev)"}, "IS_LOCAL_DEV": {"value": "false", "description": "Is local development environment"}, "LOG_LEVEL": {"value": "INFO", "description": "Logging level"}, "AZURE_STORAGE_CONNECTION_STRING": {"value": "DefaultEndpointsProtocol=https;AccountName=statomsecdbconnectdev02;AccountKey=[UPDATE_LATER];EndpointSuffix=core.windows.net", "description": "Azure Storage connection string (update later)", "isSecret": true}, "AZURE_TABLE_STORAGE_CONNECTION_STRING": {"value": "DefaultEndpointsProtocol=https;AccountName=statomsecdbconnectdev02;AccountKey=[UPDATE_LATER];EndpointSuffix=core.windows.net", "description": "Azure Table Storage connection string (update later)", "isSecret": true}, "AZURE_SERVICE_BUS_CONNECTION_STRING": {"value": "Endpoint=sb://atomsec-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=[UPDATE_LATER]", "description": "Azure Service Bus connection string (update later)", "isSecret": true}, "FRONTEND_URL": {"value": "https://app-atomsec-qa01.azurewebsites.net", "description": "Frontend application URL for QA"}}}