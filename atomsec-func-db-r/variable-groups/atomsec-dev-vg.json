{"name": "vg-atomsec-db-dev", "description": "Variable group for AtomSec DB Function App - Development Environment", "variables": {"ENVIRONMENT": {"value": "development", "description": "Environment name"}, "AZURE_SUBSCRIPTION": {"value": "sc-atomsec-dev-data", "description": "Azure subscription service connection name"}, "SUBSCRIPTION_ID": {"value": "35518353-3fc5-49c1-91cd-3ab90df8d78d", "description": "Azure subscription ID"}, "RESOURCE_GROUP": {"value": "atomsec-dev-data", "description": "Azure resource group name"}, "FUNCTION_APP_NAME": {"value": "func-atomsec-dbconnect-dev02", "description": "Azure Function App name"}, "FUNCTION_APP_URL": {"value": "https://func-atomsec-dbconnect-dev02.azurewebsites.net", "description": "Function App URL"}, "FUNCTION_APP_URL_SCM": {"value": "https://func-atomsec-dbconnect-dev02.scm.azurewebsites.net", "description": "Function App SCM URL"}, "KEY_VAULT_NAME": {"value": "akv-atomsec-dev", "description": "Azure Key Vault name"}, "KEY_VAULT_URL": {"value": "https://akv-atomsec-dev.vault.azure.net/", "description": "Azure Key Vault URL"}, "IS_LOCAL_DEV": {"value": "false", "description": "Is local development environment"}, "LOG_LEVEL": {"value": "INFO", "description": "Logging level"}, "AZURE_STORAGE_CONNECTION_STRING": {"value": "DefaultEndpointsProtocol=https;AccountName=statomsecdbconnectdev02;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "description": "Azure Storage connection string", "isSecret": true}, "AZURE_TABLE_STORAGE_CONNECTION_STRING": {"value": "DefaultEndpointsProtocol=https;AccountName=statomsecdbconnectdev02;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "description": "Azure Table Storage connection string", "isSecret": true}, "AZURE_SERVICE_BUS_CONNECTION_STRING": {"value": "Endpoint=sb://atomsec.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ubVwUkj4yj5IszQsD1IygjI7ox062MSKs+ASbL/bIU0=", "description": "Azure Service Bus connection string", "isSecret": true}, "FRONTEND_URL": {"value": "https://app-atomsec-dev01.azurewebsites.net", "description": "Frontend application URL"}}}