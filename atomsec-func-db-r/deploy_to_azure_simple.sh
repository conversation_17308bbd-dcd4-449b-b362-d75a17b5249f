#!/bin/bash

# Deploy AtomSec Database Function to Azure (Using Existing Resources)
set -e

echo "🚀 Starting deployment of AtomSec Database Function to Azure..."

# Configuration - Updated to use new function app with additional memory
RESOURCE_GROUP="atomsec-dev-data"
FUNCTION_APP_NAME="func-atomsec-dbconnect-dev02"
STORAGE_ACCOUNT="statomsecdbconnectdev02"

# Get secrets from Azure Key Vault
echo "🔐 Retrieving secrets from Azure Key Vault..."

# Get JWT secret
JWT_SECRET=$(az keyvault secret show --vault-name akv-atomsec-dev --name jwt-secret --query value --output tsv)
if [ -z "$JWT_SECRET" ]; then
    echo "❌ Failed to retrieve JWT secret from Key Vault"
    exit 1
fi
echo "✅ JWT secret retrieved successfully"

# Get Azure AD Client ID
AZURE_AD_CLIENT_ID=$(az keyvault secret show --vault-name akv-atomsec-dev --name azure-ad-client-id --query value --output tsv)
if [ -z "$AZURE_AD_CLIENT_ID" ]; then
    echo "❌ Failed to retrieve Azure AD Client ID from Key Vault"
    exit 1
fi
echo "✅ Azure AD Client ID retrieved successfully"

# Get Azure AD Tenant ID
AZURE_AD_TENANT_ID=$(az keyvault secret show --vault-name akv-atomsec-dev --name azure-ad-tenant-id --query value --output tsv)
if [ -z "$AZURE_AD_TENANT_ID" ]; then
    echo "❌ Failed to retrieve Azure AD Tenant ID from Key Vault"
    exit 1
fi
echo "✅ Azure AD Tenant ID retrieved successfully"

# Get Azure AD Client Secret
AZURE_AD_CLIENT_SECRET=$(az keyvault secret show --vault-name akv-atomsec-dev --name azure-ad-client-secret --query value --output tsv)
if [ -z "$AZURE_AD_CLIENT_SECRET" ]; then
    echo "❌ Failed to retrieve Azure AD Client Secret from Key Vault"
    exit 1
fi
echo "✅ Azure AD Client Secret retrieved successfully"

echo "📋 Configuration:"
echo "  Resource Group: $RESOURCE_GROUP"
echo "  Function App Name: $FUNCTION_APP_NAME"
echo "  Storage Account: $STORAGE_ACCOUNT"

# Step 1: Configure application settings
echo "⚙️  Configuring application settings..."
az functionapp config appsettings set \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --settings \
    AZURE_AD_CLIENT_ID="$AZURE_AD_CLIENT_ID" \
    AZURE_AD_TENANT_ID="$AZURE_AD_TENANT_ID" \
    AZURE_AD_CLIENT_SECRET="$AZURE_AD_CLIENT_SECRET" \
    ENVIRONMENT="production" \
    IS_LOCAL_DEV="false" \
    JWT_SECRET="$JWT_SECRET" \
    FUNCTIONS_WORKER_RUNTIME="python" \
  --output none

echo "✅ Application settings configured successfully!"

# Step 2: Deploy the function
echo "📤 Deploying function code..."
func azure functionapp publish $FUNCTION_APP_NAME --python

# Step 3: Test the deployment
echo "🧪 Testing deployment..."
sleep 30  # Wait for deployment to complete

echo "✅ Testing info endpoint..."
curl -s "https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/info" | python3 -m json.tool

echo "✅ Testing integrations endpoint..."
curl -s "https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/integrations" | python3 -m json.tool

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📊 Function App URL: https://$FUNCTION_APP_NAME.azurewebsites.net"
echo "🔗 API Base URL: https://$FUNCTION_APP_NAME.azurewebsites.net/api/db"
echo "📋 Integrations Endpoint: https://$FUNCTION_APP_NAME.azurewebsites.net/api/db/integrations"
echo ""
echo "🌐 View in Azure Portal: https://portal.azure.com/#@atomsec.ai/resource/subscriptions/35518353-3fc5-49c1-91cd-3ab90df8d78d/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Web/sites/$FUNCTION_APP_NAME"
echo ""
echo "📝 Next steps:"
echo "1. Go to Azure Portal and navigate to your Function App"
echo "2. Click on 'Functions' in the left menu"
echo "3. You should now see all endpoints including integrations"
echo "4. Update your frontend to use the Azure-hosted endpoints" 