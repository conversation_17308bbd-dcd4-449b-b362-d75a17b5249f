# AtomSec Database Function App

This is a dedicated Azure Function App that provides centralized database operations for the AtomSec application. It implements a clean separation of concerns by isolating all database CRUD operations into a separate microservice.

## Architecture

The database function app follows a layered architecture:

```
atomsec-func-db/
├── api/                    # HTTP endpoints layer
├── repositories/           # Data access layer (Repository pattern)
├── shared/                 # Shared utilities and models
└── function_app.py        # Main entry point
```

## Features

- **Centralized Database Access**: All database operations are handled through this service
- **Repository Pattern**: Clean separation between business logic and data access
- **Multi-Database Support**: Supports both Azure Table Storage (local dev) and SQL Database (production)
- **RESTful API**: Clean REST endpoints for all database operations
- **Error Handling**: Comprehensive error handling and logging

## API Endpoints

### Base URL
- Local: `http://localhost:7072/v1`
- Production: `https://apim-atomsec-dev.azure-api.net/v1` (via Azure API Management)
- Direct Function App: `https://func-atomsec-dbconnect-dev.azurewebsites.net/v1`

### Available Endpoints

#### Health & Info
- `GET /v1/health` - Health check endpoint
- `GET /v1/info` - Service information

#### User Management
- `GET /api/db/users` - List all users
- `GET /api/db/users/{user_id}` - Get user by ID
- `GET /api/db/users/email/{email}` - Get user by email
- `POST /api/db/users` - Create new user
- `PUT /api/db/users/{user_id}` - Update user
- `DELETE /api/db/users/{user_id}` - Delete user (soft delete)
- `POST /api/db/users/{user_id}/login` - Create user login credentials
- `POST /api/db/users/login/verify` - Verify login credentials

#### Organization Management (Coming Soon)
- `GET /api/db/organizations` - List organizations
- `GET /api/db/organizations/{org_id}` - Get organization
- `POST /api/db/organizations` - Create organization
- `PUT /api/db/organizations/{org_id}` - Update organization
- `DELETE /api/db/organizations/{org_id}` - Delete organization

#### Integration Management (Coming Soon)
- `GET /api/db/integrations` - List integrations
- `GET /api/db/integrations/{integration_id}` - Get integration
- `POST /api/db/integrations` - Create integration
- `PUT /api/db/integrations/{integration_id}` - Update integration
- `DELETE /api/db/integrations/{integration_id}` - Delete integration

#### Security Data (Coming Soon)
- `GET /api/db/security/health-checks` - Get health check data
- `GET /api/db/security/profiles` - Get profile permissions
- `GET /api/db/security/risks` - Get security risks

#### Task Management (Coming Soon)
- `GET /api/db/tasks` - List tasks
- `GET /api/db/tasks/{task_id}` - Get task status
- `POST /api/db/tasks` - Create task
- `PUT /api/db/tasks/{task_id}` - Update task status

## Local Development

### Prerequisites
- Python 3.9+
- Azure Functions Core Tools
- Azurite (for local Azure Storage emulation)

### Setup

1. Clone the repository:
```bash
cd atomsec-func-db
```

2. Create a virtual environment:
```bash
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Start Azurite (in a separate terminal):
```bash
azurite --silent --location c:\azurite --debug c:\azurite\debug.log
```

5. Run the function app:
```bash
func start
```

The function app will be available at `http://localhost:7072/api/db`

## Configuration

### Environment Variables

Update `local.settings.json` for local development:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "python",
    "AZURE_STORAGE_CONNECTION_STRING": "<connection-string>",
    "AZURE_TENANT_ID": "<tenant-id>",
    "AZURE_CLIENT_ID": "<client-id>",
    "AZURE_CLIENT_SECRET": "<client-secret>",
    "KEY_VAULT_NAME": "akv-atomsec-dev",
    "ENVIRONMENT": "local"
  }
}
```

## Database Schema

### Table Storage (Local Development)

#### UserAccount Table
- PartitionKey: "user_account"
- RowKey: email (lowercase)
- Fields: UserId, Name, Email, Phone, AccountId, CreatedAt, IsActive

#### UserLogin Table
- PartitionKey: "user_login"
- RowKey: username (lowercase)
- Fields: UserId, Username, PasswordHash, Salt, HashAlgorithmId, CreatedAt, LastLogin, IsActive

### SQL Database (Production)

#### App_User Table
```sql
CREATE TABLE App_User (
    UserId INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(255),
    Email NVARCHAR(255) UNIQUE NOT NULL,
    Phone NVARCHAR(50),
    AccountId INT,
    CreatedAt DATETIME DEFAULT GETDATE(),
    IsActive BIT DEFAULT 1
);
```

#### App_User_Login Table
```sql
CREATE TABLE App_User_Login (
    UserId INT PRIMARY KEY,
    Username NVARCHAR(255) UNIQUE NOT NULL,
    PasswordHash NVARCHAR(500) NOT NULL,
    Salt NVARCHAR(100) NOT NULL,
    HashAlgorithmId INT DEFAULT 1,
    CreatedAt DATETIME DEFAULT GETDATE(),
    LastLogin DATETIME,
    IsActive BIT DEFAULT 1,
    FOREIGN KEY (UserId) REFERENCES App_User(UserId)
);
```

## Integration with Main Function App

To integrate this database service with the main AtomSec function app:

1. Update the main function app to call this service instead of direct database access
2. Use HTTP clients to make requests to the database service endpoints
3. Handle authentication/authorization in the main function app

Example integration:

```python
import requests

# In the main function app
def get_user_by_email(email: str):
    response = requests.get(f"{DB_SERVICE_URL}/users/email/{email}")
    if response.status_code == 200:
        return response.json()["data"]
    return None
```

## Deployment

### Azure Deployment

1. Create a new Function App in Azure
2. Configure application settings with production values
3. Deploy using Azure Functions Core Tools:

```bash
func azure functionapp publish <function-app-name>
```

### CI/CD Pipeline

Create a pipeline file (e.g., `azure-pipelines.yml`) for automated deployment.

## Testing

Run tests using pytest:

```bash
pytest tests/
```

## Next Steps

1. **Complete Repository Implementations**: Implement the remaining repository classes for organizations, integrations, security, and tasks
2. **Add Authentication**: Implement proper authentication between services
3. **Add Caching**: Implement caching for frequently accessed data
4. **Add Monitoring**: Set up Application Insights for monitoring
5. **Add API Documentation**: Generate OpenAPI/Swagger documentation
6. **Performance Optimization**: Implement connection pooling and query optimization

## Contributing

1. Create a feature branch
2. Make your changes
3. Write/update tests
4. Submit a pull request

## License

[Your License Here] 