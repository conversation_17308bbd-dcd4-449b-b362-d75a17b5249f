#!/usr/bin/env python3
"""
Test script to check for import errors in the function app
"""

import sys
import traceback

def test_import(module_name):
    """Test importing a module and report any errors"""
    try:
        __import__(module_name)
        print(f"✅ {module_name} - OK")
        return True
    except Exception as e:
        print(f"❌ {module_name} - ERROR: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """Test all critical imports"""
    print("Testing Azure Functions imports...")
    
    # Test basic Azure Functions
    test_import("azure.functions")
    
    # Test shared modules
    print("\nTesting shared modules...")
    shared_modules = [
        "shared.cors_middleware",
        "shared.data_access", 
        "shared.azure_services",
        "shared.service_bus_client"
    ]
    
    for module in shared_modules:
        test_import(module)
    
    # Test API endpoints
    print("\nTesting API endpoint modules...")
    api_modules = [
        "api.auth_endpoints",
        "api.user_endpoints", 
        "api.account_endpoints",
        "api.cors_handler"
    ]
    
    for module in api_modules:
        test_import(module)
    
    # Test main function app
    print("\nTesting main function app...")
    test_import("function_app")

if __name__ == "__main__":
    main()
