"""
SFDC Proxy Endpoints

This module provides proxy endpoints that forward requests to the SFDC service.
This centralizes all frontend calls through the DB service while keeping business logic in SFDC service.

Endpoints:
- POST /integration/scan/{id} - Trigger integration scan
- GET /task-status - Get task status
- POST /tasks/cancel - Cancel task
- POST /tasks/schedule - Schedule task
- GET /health-score - Get health score
- GET /health-risks - Get health risks
- GET /profiles - Get profiles
- GET /permission-sets - Get permission sets
- GET /scan/accounts - Get scan accounts
- GET /scan/history - Get scan history
- GET /sfdc/health - Get SFDC service health
- POST /sfdc/info - Get SFDC service info
"""

import logging
import json
import azure.functions as func
from datetime import datetime
from shared.sfdc_service_client import get_sfdc_client
from shared.cors_middleware import add_cors_headers

logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

def _forward_auth_headers(req: func.HttpRequest) -> dict:
    """Extract and forward authentication headers"""
    headers = {}
    
    # Forward Authorization header
    auth_header = req.headers.get('Authorization')
    if auth_header:
        headers['Authorization'] = auth_header
    
    # Forward other relevant headers
    for header_name in ['X-User-ID', 'X-User-Email', 'X-Org-ID']:
        header_value = req.headers.get(header_name)
        if header_value:
            headers[header_name] = header_value
    
    return headers

def _create_error_response(message: str, status_code: int = 500) -> func.HttpResponse:
    """Create standardized error response"""
    response = func.HttpResponse(
        json.dumps({
            "success": False,
            "error": message
        }),
        mimetype="application/json",
        status_code=status_code
    )
    return response

# Integration scan endpoint
@bp.route(route="integration/scan/{integration_id}", methods=["POST", "OPTIONS"])
def scan_integration_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for integration scanning"""
    logger.info('Processing integration scan proxy request...')
    
    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)
    
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            response = _create_error_response("Integration ID is required", 400)
            return add_cors_headers(req, response)
        
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        
        result = sfdc_client.scan_integration(integration_id, headers)
        
        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)
        
        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']
        
        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)
        
    except Exception as e:
        logger.error(f"Error in scan_integration_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

# Task management endpoints
@bp.route(route="task-status", methods=["GET", "OPTIONS"])
def task_status_api(req: func.HttpRequest) -> func.HttpResponse:
    """
    Task status API endpoint - Direct implementation in DB service
    
    This function handles getting task status for an integration:
    - Retrieves tasks for a specific integration from TaskStatus table
    - Filters by status and task_type
    - Limits the number of results
    """
    logger.info('Processing task status request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    # Get query parameters
    all_params = dict(req.params)
    logger.info(f"All request parameters: {all_params}")

    integration_id = req.params.get("integration_id")
    status = req.params.get("Status") or req.params.get("status")
    limit = req.params.get("limit", "10")
    task_type = req.params.get("taskType") or req.params.get("task_type") or req.params.get("TaskType")

    logger.info(f"Parsed parameters - integration_id: {integration_id}, status: {status}, limit: {limit}, task_type: {task_type}")

    try:
        limit = int(limit)
    except ValueError:
        limit = 10

    # Validate required parameters
    if not integration_id:
        return func.HttpResponse(
            json.dumps({"success": False, "error": "Missing required parameter: integration_id"}),
            mimetype="application/json",
            status_code=400
        )

    try:
        # Get repository and fetch tasks
        from shared.data_access import get_table_storage_repository
        repo = get_table_storage_repository("TaskStatus")
        
        # Get tasks from TaskStatus table - use task_ prefix for partition key
        tasks = repo.query_entities(filter_query=f"PartitionKey eq 'task_{integration_id}'")
        
        if not tasks:
            logger.warning(f"No tasks found for integration_id: {integration_id}")
            response_data = {
                "success": True,
                "data": [],
                "message": f"No tasks found for integration_id: {integration_id}",
                "timestamp": datetime.now().isoformat()
            }
            response = func.HttpResponse(
                json.dumps(response_data),
                mimetype="application/json",
                status_code=200
            )
            return add_cors_headers(req, response)

        logger.info(f"Found {len(tasks)} tasks for integration_id")

        # Normalize field names and filter out sensitive data
        normalized_tasks = []
        for task in tasks:
            # Only include essential, non-sensitive fields
            normalized_task = {
                "TaskType": task.get("task_type") or task.get("TaskType", ""),
                "Status": task.get("status") or task.get("Status", ""),
                "OrgId": task.get("org_id") or task.get("OrgId", ""),
                "TaskId": task.get("task_id") or task.get("TaskId", ""),
                "Priority": task.get("priority") or task.get("Priority", ""),
                "execution_log_id": task.get("execution_log_id") or task.get("ExecutionLogId", ""),
                "CreatedAt": task.get("created_at") or task.get("CreatedAt", ""),
                "Message": task.get("message") or task.get("Message", "")
            }
            normalized_tasks.append(normalized_task)

        tasks = normalized_tasks

        # Filter by status if provided
        if status:
            logger.info(f"Filtering tasks by status: {status}")
            status_lower = status.lower()
            
            # Define common status variations
            completed_variations = ["complete", "completed", "done", "finished"]
            pending_variations = ["pending", "in progress", "running", "in-progress"]
            failed_variations = ["failed", "error", "failure", "failed"]

            # Determine target status variations to check
            target_status_variations = []
            if status_lower in completed_variations:
                target_status_variations = completed_variations
            elif status_lower in pending_variations:
                target_status_variations = pending_variations
            elif status_lower in failed_variations:
                target_status_variations = failed_variations
            else:
                target_status_variations = [status_lower]

            # Filter tasks by status
            filtered_tasks = []
            for task in tasks:
                task_status = task.get("Status", "")
                task_status_lower = task_status.lower()
                
                if any(variation in task_status_lower for variation in target_status_variations):
                    filtered_tasks.append(task)

            tasks = filtered_tasks
            logger.info(f"After status filtering: {len(tasks)} tasks")

        # Filter by task_type if provided
        if task_type:
            logger.info(f"Filtering tasks by task_type: {task_type}")
            task_type_lower = task_type.lower()
            
            filtered_tasks = []
            for task in tasks:
                task_task_type = task.get("TaskType", "")
                task_type_lower_value = task_task_type.lower()
                
                if task_type_lower_value == task_type_lower:
                    filtered_tasks.append(task)

            tasks = filtered_tasks
            logger.info(f"After task_type filtering: {len(tasks)} tasks")

        # Sort by CreatedAt (newest first)
        tasks = sorted(tasks, key=lambda x: x.get("CreatedAt", ""), reverse=True)

        # Limit the number of results
        tasks = tasks[:limit]

        logger.info(f"Returning {len(tasks)} tasks after applying limit of {limit}")

        # Return tasks
        response_data = {
            "success": True,
            "data": tasks,
            "timestamp": datetime.now().isoformat()
        }

        response = func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )

        return add_cors_headers(req, response)
        
    except Exception as e:
        error_message = f"Error getting task status: {str(e)}"
        logger.error(error_message)

        response = func.HttpResponse(
            json.dumps({"success": False, "error": error_message}),
            mimetype="application/json",
            status_code=500
        )

        return add_cors_headers(req, response)

@bp.route(route="tasks/cancel", methods=["POST", "OPTIONS"])
def cancel_task_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for canceling tasks"""
    logger.info('Processing cancel task proxy request...')
    
    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)
    
    try:
        # Get request body
        try:
            body = req.get_json()
        except ValueError:
            body = None
        
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        
        result = sfdc_client.cancel_task(body, headers)
        
        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)
        
        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']
        
        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)
        
    except Exception as e:
        logger.error(f"Error in cancel_task_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="tasks/schedule", methods=["POST", "OPTIONS"])
def schedule_task_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for scheduling tasks"""
    logger.info('Processing schedule task proxy request...')
    
    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)
    
    try:
        # Get request body
        try:
            body = req.get_json()
        except ValueError:
            body = None
        
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        
        result = sfdc_client.schedule_task(body, headers)
        
        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)
        
        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']
        
        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)
        
    except Exception as e:
        logger.error(f"Error in schedule_task_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

# Security analysis endpoints
@bp.route(route="health-score", methods=["GET", "OPTIONS"])
def health_score_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for health score"""
    logger.info('Processing health score proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_health_score(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in health_score_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="health-risks", methods=["GET", "OPTIONS"])
def health_risks_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for health risks"""
    logger.info('Processing health risks proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_health_risks(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in health_risks_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="profiles", methods=["GET", "OPTIONS"])
def profiles_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for profiles"""
    logger.info('Processing profiles proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_profiles(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in profiles_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="permission-sets", methods=["GET", "OPTIONS"])
def permission_sets_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for permission sets"""
    logger.info('Processing permission sets proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_permission_sets(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in permission_sets_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

# Scan results endpoints
@bp.route(route="scan/accounts", methods=["GET", "OPTIONS"])
def scan_accounts_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for scan accounts"""
    logger.info('Processing scan accounts proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_scan_accounts(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in scan_accounts_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="scan/history", methods=["GET", "OPTIONS"])
def scan_history_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for scan history"""
    logger.info('Processing scan history proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_scan_history(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in scan_history_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

# System endpoints
@bp.route(route="sfdc/health", methods=["GET", "OPTIONS"])
def sfdc_health_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for SFDC service health"""
    logger.info('Processing SFDC health proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)

        result = sfdc_client.get_health(headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in sfdc_health_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="sfdc/info", methods=["POST", "OPTIONS"])
def sfdc_info_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for SFDC service info"""
    logger.info('Processing SFDC info proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)

        result = sfdc_client.get_info(headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in sfdc_info_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

# Security scanning endpoints
@bp.route(route="security/scan", methods=["POST", "OPTIONS"])
def security_scan_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for security scanning"""
    logger.info('Processing security scan proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)

        # Get request body
        try:
            request_body = req.get_json()
        except ValueError:
            request_body = {}

        result = sfdc_client.perform_security_scan(request_body, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in security_scan_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="security/health-check", methods=["GET", "OPTIONS"])
def security_health_check_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for security health checks"""
    logger.info('Processing security health check proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)

        # Get query parameters
        params = dict(req.params)

        result = sfdc_client.get_security_health_check(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in security_health_check_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

# Note: integration/connect endpoint is handled by connect_integration function in integration_endpoints.py
# Removed duplicate integration_connect_proxy to avoid route conflicts



@bp.route(route="integration/{integration_id}/sync", methods=["POST", "OPTIONS"])
def integration_sync_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for integration sync"""
    logger.info('Processing integration sync proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            response = _create_error_response("Integration ID is required", 400)
            return add_cors_headers(req, response)

        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)

        # Get request body
        try:
            request_body = req.get_json()
        except ValueError:
            request_body = {}

        result = sfdc_client.sync_integration(integration_id, request_body, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in integration_sync_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)
