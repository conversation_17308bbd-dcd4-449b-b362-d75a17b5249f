"""
General Endpoints

This module provides general endpoints for common operations.
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any, Optional
from azure.functions import Blueprint

from shared.azure_services import is_local_dev, get_current_user_id
from shared.data_access import get_table_storage_repository, get_sql_database_repository, create_default_policies_and_rules_for_integration

logger = logging.getLogger(__name__)
bp = Blueprint()

@bp.route(route="stats", methods=["GET"])
def get_stats(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get database statistics
    
    Returns:
        JSON response with database statistics
    """
    try:
        # Get current user ID from request headers
        user_id = get_current_user_id(req)
        if not user_id:
            return func.HttpResponse(
                json.dumps({"error": "User not authenticated"}),
                mimetype="application/json",
                status_code=401
            )
        
        stats = {
            "users": 0,
            "accounts": 0,
            "organizations": 0,
            "integrations": 0,
            "tasks": 0,
            "security_checks": 0,
            "policies": 0
        }
        
        # Get repositories
        if is_local_dev():
            user_repo = get_table_storage_repository("App_User")
            account_repo = get_table_storage_repository("App_Account")
            org_repo = get_table_storage_repository("App_Organization")
            integration_repo = get_table_storage_repository("App_Integration")
            task_repo = get_table_storage_repository("App_Task")
            security_repo = get_table_storage_repository("App_SecurityHealthCheck")
            policy_repo = get_table_storage_repository("App_Policy")
        else:
            user_repo = get_sql_database_repository("App_User")
            account_repo = get_sql_database_repository("App_Account")
            org_repo = get_sql_database_repository("App_Organization")
            integration_repo = get_sql_database_repository("App_Integration")
            task_repo = get_sql_database_repository("App_Task")
            security_repo = get_sql_database_repository("App_SecurityHealthCheck")
            policy_repo = get_sql_database_repository("App_Policy")
        
        # Count records in each table
        try:
            if user_repo:
                stats["users"] = len(user_repo.get_all())
        except Exception as e:
            logger.warning(f"Error counting users: {str(e)}")
        
        try:
            if account_repo:
                stats["accounts"] = len(account_repo.get_all())
        except Exception as e:
            logger.warning(f"Error counting accounts: {str(e)}")
        
        try:
            if org_repo:
                stats["organizations"] = len(org_repo.get_all())
        except Exception as e:
            logger.warning(f"Error counting organizations: {str(e)}")
        
        try:
            if integration_repo:
                stats["integrations"] = len(integration_repo.get_all())
        except Exception as e:
            logger.warning(f"Error counting integrations: {str(e)}")
        
        try:
            if task_repo:
                stats["tasks"] = len(task_repo.get_all())
        except Exception as e:
            logger.warning(f"Error counting tasks: {str(e)}")
        
        try:
            if security_repo:
                stats["security_checks"] = len(security_repo.get_all())
        except Exception as e:
            logger.warning(f"Error counting security checks: {str(e)}")
        
        try:
            if policy_repo:
                stats["policies"] = len(policy_repo.get_all())
        except Exception as e:
            logger.warning(f"Error counting policies: {str(e)}")
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": stats
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error getting stats: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to get stats: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="backup", methods=["POST"])
def create_backup(req: func.HttpRequest) -> func.HttpResponse:
    """
    Create database backup
    
    Returns:
        JSON response with backup status
    """
    try:
        # Get current user ID from request headers
        user_id = get_current_user_id(req)
        if not user_id:
            return func.HttpResponse(
                json.dumps({"error": "User not authenticated"}),
                mimetype="application/json",
                status_code=401
            )
        
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON in request body"}),
                mimetype="application/json",
                status_code=400
            )
        
        backup_type = request_data.get("type", "full")
        include_data = request_data.get("include_data", True)
        
        # Validate backup type
        valid_types = ["full", "incremental", "differential"]
        if backup_type not in valid_types:
            return func.HttpResponse(
                json.dumps({"error": f"Invalid backup type: {backup_type}"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Create backup (simplified implementation)
        backup_id = f"backup_{backup_type}_{user_repo.get_current_timestamp()}"
        
        # Publish backup creation event
        from shared.service_bus_client import get_service_bus_client
        service_bus_client = get_service_bus_client()
        if service_bus_client:
            service_bus_client.publish_event(
                event_type="DatabaseBackupCreated",
                event_data={
                    "backup_id": backup_id,
                    "backup_type": backup_type,
                    "include_data": include_data,
                    "created_by": user_id
                },
                user_id=user_id
            )
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": {
                    "backup_id": backup_id,
                    "backup_type": backup_type,
                    "include_data": include_data,
                    "created_by": user_id,
                    "status": "initiated"
                },
                "message": "Backup creation initiated"
            }),
            mimetype="application/json",
            status_code=202
        )
        
    except Exception as e:
        logger.error(f"Error creating backup: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to create backup: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

# cleanup_old_data function removed