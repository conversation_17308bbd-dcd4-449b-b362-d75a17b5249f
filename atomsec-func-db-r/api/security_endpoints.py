"""
Security Data Management Endpoints

This module provides security-related database operations for the AtomSec application.
Moved from atomsec-func-sfdc to centralize database operations.

Endpoints:
- GET /api/security/health-check/{org_id} - Get health check data
- POST /api/security/health-check - Store health check data
- GET /api/security/profiles/{org_id} - Get profile permissions
- POST /api/security/profiles - Store profile permissions
- GET /api/security/permission-sets/{org_id} - Get permission set data
- POST /api/security/permission-sets - Store permission set data
- GET /api/security/overview/{org_id} - Get security overview
"""

import logging
import json
import azure.functions as func
from datetime import datetime
from typing import Dict, List, Any, Optional

from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.database_models_new import HealthCheck, ProfilePermission, Overview, ExecutionLog, ProfileAssignmentCount, PoliciesResult

logger = logging.getLogger(__name__)

# Global repository instances (lazy initialized)
_health_check_table_repo = None
_health_check_sql_repo = None
_profile_permission_table_repo = None
_profile_permission_sql_repo = None
_overview_table_repo = None
_overview_sql_repo = None
_policies_result_table_repo = None
_policies_result_sql_repo = None
_profile_assignment_count_table_repo = None
_profile_assignment_count_sql_repo = None
_pmd_scans_table_repo = None
_pmd_scans_sql_repo = None


def get_health_check_table_repo() -> Optional[TableStorageRepository]:
    """Get health check table repository for local development"""
    global _health_check_table_repo
    if _health_check_table_repo is None:
        try:
            _health_check_table_repo = TableStorageRepository(table_name="HealthCheck")
            logger.info("Initialized health check table repository")
        except Exception as e:
            logger.error(f"Failed to initialize health check table repository: {str(e)}")
            _health_check_table_repo = None
    return _health_check_table_repo


def get_health_check_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get health check SQL repository for production"""
    global _health_check_sql_repo
    if _health_check_sql_repo is None and not is_local_dev():
        try:
            _health_check_sql_repo = SqlDatabaseRepository(table_name="App_HealthCheck")
            logger.info("Initialized health check SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize health check SQL repository: {str(e)}")
            _health_check_sql_repo = None
    return _health_check_sql_repo


def get_profile_permission_table_repo() -> Optional[TableStorageRepository]:
    """Get profile permission table repository for local development"""
    global _profile_permission_table_repo
    if _profile_permission_table_repo is None:
        try:
            _profile_permission_table_repo = TableStorageRepository(table_name="ProfilePermission")
            logger.info("Initialized profile permission table repository")
        except Exception as e:
            logger.error(f"Failed to initialize profile permission table repository: {str(e)}")
            _profile_permission_table_repo = None
    return _profile_permission_table_repo


def get_profile_permission_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get profile permission SQL repository for production"""
    global _profile_permission_sql_repo
    if _profile_permission_sql_repo is None and not is_local_dev():
        try:
            _profile_permission_sql_repo = SqlDatabaseRepository(table_name="App_ProfilePermissions")
            logger.info("Initialized profile permission SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize profile permission SQL repository: {str(e)}")
            _profile_permission_sql_repo = None
    return _profile_permission_sql_repo


def get_overview_table_repo() -> Optional[TableStorageRepository]:
    """Get overview table repository for local development"""
    global _overview_table_repo
    if _overview_table_repo is None:
        try:
            _overview_table_repo = TableStorageRepository(table_name="Overview")
            logger.info("Initialized overview table repository")
        except Exception as e:
            logger.error(f"Failed to initialize overview table repository: {str(e)}")
            _overview_table_repo = None
    return _overview_table_repo


def get_overview_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get overview SQL repository for production"""
    global _overview_sql_repo
    if _overview_sql_repo is None and not is_local_dev():
        try:
            _overview_sql_repo = SqlDatabaseRepository(table_name="App_Overview")
            logger.info("Initialized overview SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize overview SQL repository: {str(e)}")
            _overview_sql_repo = None
    return _overview_sql_repo


def get_policies_result_table_repo() -> Optional[TableStorageRepository]:
    """Get policies result table repository for local development"""
    global _policies_result_table_repo
    if _policies_result_table_repo is None:
        try:
            _policies_result_table_repo = TableStorageRepository(table_name="PoliciesResult")
            logger.info("Initialized policies result table repository")
        except Exception as e:
            logger.error(f"Failed to initialize policies result table repository: {str(e)}")
            _policies_result_table_repo = None
    return _policies_result_table_repo


def get_policies_result_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get policies result SQL repository for production"""
    global _policies_result_sql_repo
    if _policies_result_sql_repo is None and not is_local_dev():
        try:
            _policies_result_sql_repo = SqlDatabaseRepository(table_name="App_PoliciesResult")
            logger.info("Initialized policies result SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize policies result SQL repository: {str(e)}")
            _policies_result_sql_repo = None
    return _policies_result_sql_repo


def get_profile_assignment_count_table_repo() -> Optional[TableStorageRepository]:
    """Get profile assignment count table repository for local development"""
    global _profile_assignment_count_table_repo
    if _profile_assignment_count_table_repo is None:
        try:
            _profile_assignment_count_table_repo = TableStorageRepository(table_name="ProfileAssignmentCount")
            logger.info("Initialized profile assignment count table repository")
        except Exception as e:
            logger.error(f"Failed to initialize profile assignment count table repository: {str(e)}")
            _profile_assignment_count_table_repo = None
    return _profile_assignment_count_table_repo


def get_profile_assignment_count_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get profile assignment count SQL repository for production"""
    global _profile_assignment_count_sql_repo
    if _profile_assignment_count_sql_repo is None and not is_local_dev():
        try:
            _profile_assignment_count_sql_repo = SqlDatabaseRepository(table_name="App_ProfileAssignmentCount")
            logger.info("Initialized profile assignment count SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize profile assignment count SQL repository: {str(e)}")
            _profile_assignment_count_sql_repo = None
    return _profile_assignment_count_sql_repo


def get_pmd_scans_table_repo() -> Optional[TableStorageRepository]:
    """Get PMD scans table repository for local development"""
    global _pmd_scans_table_repo
    if _pmd_scans_table_repo is None:
        try:
            _pmd_scans_table_repo = TableStorageRepository(table_name="PMDScans")
            logger.info("Initialized PMD scans table repository")
        except Exception as e:
            logger.error(f"Failed to initialize PMD scans table repository: {str(e)}")
            _pmd_scans_table_repo = None
    return _pmd_scans_table_repo


def get_pmd_scans_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get PMD scans SQL repository for production"""
    global _pmd_scans_sql_repo
    if _pmd_scans_sql_repo is None and not is_local_dev():
        try:
            _pmd_scans_sql_repo = SqlDatabaseRepository(table_name="App_PMDScans")
            logger.info("Initialized PMD scans SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize PMD scans SQL repository: {str(e)}")
            _pmd_scans_sql_repo = None
    return _pmd_scans_sql_repo


def store_security_health_check_risks(org_id: str, execution_log_id: str, risks_data: List[Dict[str, Any]]) -> bool:
    """
    Store security health check risks

    Args:
        org_id: Organization ID
        execution_log_id: Execution log ID
        risks_data: List of risk data dictionaries

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_health_check_table_repo()
            if not repo:
                logger.error("Health check table repository not available")
                return False

            for risk_data in risks_data:
                health_check = HealthCheck(
                    OrgId=str(org_id),
                    ExecutionLogId=str(execution_log_id),  # Keep as string for both UUID and integer cases
                    RiskType=risk_data.get('risk_type', ''),
                    Setting=risk_data.get('setting', ''),
                    SettingGroup=risk_data.get('setting_group', ''),
                    OrgValue=risk_data.get('org_value'),
                    StandardValue=risk_data.get('standard_value'),
                    LastUpdated=datetime.now()
                )

                entity = health_check.to_table_entity()
                if not repo.insert_entity(entity):
                    logger.error(f"Failed to insert health check risk: {risk_data}")
                    return False

            logger.info(f"Stored {len(risks_data)} health check risks for org {org_id}")
            return True

        else:
            # Use SQL Database for production
            repo = get_health_check_sql_repo()
            if not repo:
                logger.error("Health check SQL repository not available")
                return False

            # Batch insert for better performance
            query = """
            INSERT INTO App_HealthCheck (OrgId, ExecutionLogId, RiskType, Setting, SettingGroup,
                                       OrgValue, StandardValue, LastUpdated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """

            for risk_data in risks_data:
                params = (
                    str(org_id),
                    str(execution_log_id),  # Store as string for consistency
                    risk_data.get('risk_type', ''),
                    risk_data.get('setting', ''),
                    risk_data.get('setting_group', ''),
                    risk_data.get('org_value'),
                    risk_data.get('standard_value'),
                    datetime.now()
                )

                if not repo.execute_non_query(query, params):
                    logger.error(f"Failed to insert health check risk: {params}")
                    return False

            logger.info(f"Stored {len(risks_data)} health check risks for org {org_id}")
            return True

    except Exception as e:
        logger.error(f"Error storing security health check risks: {str(e)}")
        return False


def store_profile_data(org_id: int, execution_log_id: int, profile_data: List[Dict[str, Any]]) -> bool:
    """
    Store profile permission data

    Args:
        org_id: Organization ID
        execution_log_id: Execution log ID
        profile_data: List of profile permission data dictionaries

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_profile_permission_table_repo()
            if not repo:
                logger.error("Profile permission table repository not available")
                return False

            for profile in profile_data:
                profile_permission = ProfilePermission(
                    OrgId=org_id,
                    ExecutionLogId=execution_log_id,
                    SalesforceId=profile.get('salesforce_id', ''),
                    ProfileName=profile.get('profile_name', ''),
                    ObjectName=profile.get('object_name', ''),
                    Read=profile.get('read', False),
                    Create=profile.get('create', False),
                    Edit=profile.get('edit', False),
                    Delete=profile.get('delete', False),
                    ViewAllRecords=profile.get('view_all_records', False),
                    ModifyAllRecords=profile.get('modify_all_records', False),
                    SecurityRisk=profile.get('security_risk'),
                    LastUpdated=datetime.now()
                )

                entity = profile_permission.to_table_entity()
                if not repo.insert_entity(entity):
                    logger.error(f"Failed to insert profile permission: {profile}")
                    return False

            logger.info(f"Stored {len(profile_data)} profile permissions for org {org_id}")
            return True

        else:
            # Use SQL Database for production
            repo = get_profile_permission_sql_repo()
            if not repo:
                logger.error("Profile permission SQL repository not available")
                return False

            # Batch insert for better performance
            query = """
            INSERT INTO App_ProfilePermissions (OrgId, ExecutionLogId, SalesforceId, ProfileName,
                                              ObjectName, [Read], [Create], Edit, [Delete],
                                              ViewAllRecords, ModifyAllRecords, SecurityRisk, LastUpdated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            for profile in profile_data:
                params = (
                    org_id,
                    execution_log_id,
                    profile.get('salesforce_id', ''),
                    profile.get('profile_name', ''),
                    profile.get('object_name', ''),
                    profile.get('read', False),
                    profile.get('create', False),
                    profile.get('edit', False),
                    profile.get('delete', False),
                    profile.get('view_all_records', False),
                    profile.get('modify_all_records', False),
                    profile.get('security_risk'),
                    datetime.now()
                )

                if not repo.execute_non_query(query, params):
                    logger.error(f"Failed to insert profile permission: {profile}")
                    return False

            logger.info(f"Stored {len(profile_data)} profile permissions for org {org_id}")
            return True

    except Exception as e:
        logger.error(f"Error storing profile data: {str(e)}")
        return False


def get_security_health_check_data(org_id: str, risk_type: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get security health check data for an organization

    Args:
        org_id: Organization ID
        risk_type: Optional filter by risk type

    Returns:
        List of health check dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_health_check_table_repo()
            if not repo:
                logger.error("Health check table repository not available")
                return []

            filter_query = f"PartitionKey eq '{org_id}'"
            if risk_type:
                filter_query += f" and RiskType eq '{risk_type}'"

            entities = list(repo.query_entities(filter_query))

            health_checks = []
            for entity in entities:
                health_checks.append({
                    'id': entity.get('Id'),
                    'org_id': entity.get('OrgId'),
                    'execution_log_id': entity.get('ExecutionLogId'),
                    'risk_type': entity.get('RiskType'),
                    'setting': entity.get('Setting'),
                    'setting_group': entity.get('SettingGroup'),
                    'org_value': entity.get('OrgValue'),
                    'standard_value': entity.get('StandardValue'),
                    'last_updated': entity.get('LastUpdated')
                })

            return health_checks

        else:
            # Use SQL Database for production
            repo = get_health_check_sql_repo()
            if not repo:
                logger.error("Health check SQL repository not available")
                return []

            # Build query with filters
            query = """
            SELECT Id, OrgId, ExecutionLogId, RiskType, Setting, SettingGroup,
                   OrgValue, StandardValue, LastUpdated
            FROM App_HealthCheck WHERE OrgId = ?
            """
            params = [org_id]

            if risk_type:
                query += " AND RiskType = ?"
                params.append(risk_type)

            query += " ORDER BY LastUpdated DESC"

            results = repo.execute_query(query, tuple(params))

            health_checks = []
            for row in results:
                health_checks.append({
                    'id': row[0],
                    'org_id': row[1],
                    'execution_log_id': row[2],
                    'risk_type': row[3],
                    'setting': row[4],
                    'setting_group': row[5],
                    'org_value': row[6],
                    'standard_value': row[7],
                    'last_updated': row[8].isoformat() if row[8] else None
                })

            return health_checks

    except Exception as e:
        logger.error(f"Error getting security health check data: {str(e)}")
        return []


def get_profile_permissions_data(org_id: int, profile_name: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get profile permissions data for an organization

    Args:
        org_id: Organization ID
        profile_name: Optional filter by profile name

    Returns:
        List of profile permission dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_profile_permission_table_repo()
            if not repo:
                logger.error("Profile permission table repository not available")
                return []

            filter_query = f"PartitionKey eq 'profile_permission' and OrgId eq {org_id}"
            if profile_name:
                filter_query += f" and ProfileName eq '{profile_name}'"

            entities = list(repo.query_entities(filter_query))

            permissions = []
            for entity in entities:
                permissions.append({
                    'id': entity.get('Id'),
                    'org_id': entity.get('OrgId'),
                    'execution_log_id': entity.get('ExecutionLogId'),
                    'salesforce_id': entity.get('SalesforceId'),
                    'profile_name': entity.get('ProfileName'),
                    'object_name': entity.get('ObjectName'),
                    'read': entity.get('Read', False),
                    'create': entity.get('Create', False),
                    'edit': entity.get('Edit', False),
                    'delete': entity.get('Delete', False),
                    'view_all_records': entity.get('ViewAllRecords', False),
                    'modify_all_records': entity.get('ModifyAllRecords', False),
                    'security_risk': entity.get('SecurityRisk'),
                    'last_updated': entity.get('LastUpdated')
                })

            return permissions

        else:
            # Use SQL Database for production
            repo = get_profile_permission_sql_repo()
            if not repo:
                logger.error("Profile permission SQL repository not available")
                return []

            # Build query with filters
            query = """
            SELECT Id, OrgId, ExecutionLogId, SalesforceId, ProfileName, ObjectName,
                   [Read], [Create], Edit, [Delete], ViewAllRecords, ModifyAllRecords,
                   SecurityRisk, LastUpdated
            FROM App_ProfilePermissions WHERE OrgId = ?
            """
            params = [org_id]

            if profile_name:
                query += " AND ProfileName = ?"
                params.append(profile_name)

            query += " ORDER BY ProfileName, ObjectName"

            results = repo.execute_query(query, tuple(params))

            permissions = []
            for row in results:
                permissions.append({
                    'id': row[0],
                    'org_id': row[1],
                    'execution_log_id': row[2],
                    'salesforce_id': row[3],
                    'profile_name': row[4],
                    'object_name': row[5],
                    'read': row[6],
                    'create': row[7],
                    'edit': row[8],
                    'delete': row[9],
                    'view_all_records': row[10],
                    'modify_all_records': row[11],
                    'security_risk': row[12],
                    'last_updated': row[13].isoformat() if row[13] else None
                })

            return permissions

    except Exception as e:
        logger.error(f"Error getting profile permissions data: {str(e)}")
        return []


def get_security_overview_data(org_id: int) -> Optional[Dict[str, Any]]:
    """
    Get security overview data for an organization

    Args:
        org_id: Organization ID

    Returns:
        Overview data dictionary or None
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_overview_table_repo()
            if not repo:
                logger.error("Overview table repository not available")
                return None

            filter_query = f"PartitionKey eq 'overview' and OrgId eq {org_id}"
            entities = list(repo.query_entities(filter_query))

            if entities:
                # Get the most recent overview
                entity = max(entities, key=lambda x: x.get('LastUpdated', ''))
                return {
                    'id': entity.get('Id'),
                    'org_id': entity.get('OrgId'),
                    'execution_log_id': entity.get('ExecutionLogId'),
                    'health_score': entity.get('HealthScore'),
                    'total_profiles': entity.get('TotalProfiles'),
                    'total_permission_sets': entity.get('TotalPermissionSets'),
                    'total_risks': entity.get('TotalRisks'),
                    'high_risks': entity.get('HighRisks'),
                    'medium_risks': entity.get('MediumRisks'),
                    'low_risks': entity.get('LowRisks'),
                    'last_updated': entity.get('LastUpdated')
                }
            else:
                return None

        else:
            # Use SQL Database for production
            repo = get_overview_sql_repo()
            if not repo:
                logger.error("Overview SQL repository not available")
                return None

            query = """
            SELECT TOP 1 Id, OrgId, ExecutionLogId, HealthScore, TotalProfiles,
                   TotalPermissionSets, TotalRisks, HighRisks, MediumRisks, LowRisks, LastUpdated
            FROM App_Overview
            WHERE OrgId = ?
            ORDER BY LastUpdated DESC
            """

            results = repo.execute_query(query, (org_id,))

            if results:
                row = results[0]
                return {
                    'id': row[0],
                    'org_id': row[1],
                    'execution_log_id': row[2],
                    'health_score': row[3],
                    'total_profiles': row[4],
                    'total_permission_sets': row[5],
                    'total_risks': row[6],
                    'high_risks': row[7],
                    'medium_risks': row[8],
                    'low_risks': row[9],
                    'last_updated': row[10].isoformat() if row[10] else None
                }
            else:
                return None

    except Exception as e:
        logger.error(f"Error getting security overview data: {str(e)}")
        return None


def store_policies_result_data(org_id: str, execution_log_id: str, policies_data: List[Dict[str, Any]]) -> bool:
    """
    Store policies result data in old monolithic format:
    - One record per profile/permission set
    - All results stored as JSON in OrgValue field
    - Deterministic RowKeys based on profile/permission set name and timestamp
    - CRITICAL FIX: Special handling for HealthCheck records

    Args:
        org_id: Organization ID
        execution_log_id: Execution log ID
        policies_data: List of policies result data dictionaries

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_policies_result_table_repo()
            if not repo:
                logger.error("Policies result table repository not available")
                return False

            # CRITICAL FIX: Special handling for HealthCheck vs Profile/PermissionSet records
            if policies_data and policies_data[0].get('Type', '').strip() == 'HealthCheck':
                # For HealthCheck records, store each setting individually (not grouped)
                for policy_data in policies_data:
                    # Validate Type field - must not be empty
                    policy_type = policy_data.get('Type', '').strip()
                    if not policy_type:
                        logger.warning(f"Skipping policy data with empty Type field: {policy_data.get('Setting', 'unknown')}")
                        continue
                    
                    setting = policy_data.get('Setting', '').strip()
                    if not setting:
                        logger.warning(f"Skipping health check data with empty Setting field: {policy_data}")
                        continue
                    
                    # Create deterministic RowKey: HealthCheck_Setting-Timestamp
                    timestamp = datetime.now().strftime('%Y%m%d%H%M%S%f')[:-3]  # Remove microseconds, keep milliseconds
                    row_key = f"HealthCheck_{setting}-{timestamp}"
                    
                    # CRITICAL FIX: Check for existing health check records
                    try:
                        filter_query = f"PartitionKey eq '{org_id}' and TaskStatusId eq '{execution_log_id}' and Type eq 'HealthCheck' and Setting eq '{setting}'"
                        existing_entities = list(repo.query_entities(filter_query))
                        
                        if existing_entities:
                            logger.info(f"Health check record already exists for {setting} with execution_log_id {execution_log_id}. Skipping duplicate.")
                            continue
                    except Exception as check_error:
                        logger.warning(f"Error checking for existing health check record: {check_error}")
                        # Continue with insertion if check fails
                    
                    # Create entity with individual column values for health check
                    entity = {
                        "PartitionKey": str(org_id),
                        "RowKey": row_key,
                        "OrgValue": policy_data.get('OrgValue', ''),  # Store actual org value, not JSON
                        "OrgValue@type": "String",
                        "IntegrationId": str(org_id),
                        "IntegrationId@type": "String",
                        "TaskStatusId": execution_log_id,
                        "TaskStatusId@type": "String",
                        "CreatedAt": datetime.now().isoformat(),
                        "CreatedAt@type": "String",
                        "PermissionSetName": "",
                        "PermissionSetName@type": "String",
                        "ProfileName": "",
                        "ProfileName@type": "String",
                        "Type": "HealthCheck",
                        "Type@type": "String",
                        "OWASPCategory": policy_data.get('OWASPCategory', ''),
                        "OWASPCategory@type": "String",
                        "StandardValue": policy_data.get('StandardValue', ''),
                        "StandardValue@type": "String",
                        "IssueDescription": policy_data.get('IssueDescription', ''),
                        "IssueDescription@type": "String",
                        "Recommendations": policy_data.get('Recommendations', ''),
                        "Recommendations@type": "String",
                        "Severity": policy_data.get('Severity', ''),
                        "Severity@type": "String",
                        "Weakness": policy_data.get('Weakness', ''),
                        "Weakness@type": "String",
                        "Setting": setting,
                        "Setting@type": "String",
                        "SettingGroup": policy_data.get('SettingGroup', ''),
                        "SettingGroup@type": "String",
                        "UserLicense": "",
                        "UserLicense@type": "String"
                    }

                    if not repo.insert_entity(entity):
                        logger.error(f"Failed to insert health check result for {setting}")
                        return False
                
                logger.info(f"Stored {len(policies_data)} health check results (individual format) for org {org_id}")
                return True
            else:
                # For Profile/PermissionSet records, use the original grouped approach
                grouped_data = {}
                for policy_data in policies_data:
                    # Validate Type field - must not be empty
                    policy_type = policy_data.get('Type', '').strip()
                    if not policy_type:
                        logger.warning(f"Skipping policy data with empty Type field: {policy_data.get('Setting', 'unknown')}")
                        continue
                    
                    # Get profile or permission set name
                    profile_name = policy_data.get('ProfileName', '').strip()
                    permission_set_name = policy_data.get('PermissionSetName', '').strip()
                    
                    if not profile_name and not permission_set_name:
                        logger.warning(f"Skipping policy data with no profile or permission set name")
                        continue
                    
                    # Use profile name if available, otherwise permission set name
                    key_name = profile_name if profile_name else permission_set_name
                    
                    if key_name not in grouped_data:
                        grouped_data[key_name] = {
                            'ProfileName': profile_name,
                            'PermissionSetName': permission_set_name,
                            'Type': policy_type,
                            'results': []
                        }
                    
                    # Handle different task types
                    if policy_type == 'ProfilePermissions':
                        result_item = {
                            'SalesforceSetting': policy_data.get('Setting', ''),
                            'StandardValue': policy_data.get('StandardValue', ''),
                            'ProfileValue': policy_data.get('OrgValue', ''),
                            'Match': policy_data.get('Match', False),
                            'Description': policy_data.get('IssueDescription', ''),
                            'OWASP': policy_data.get('OWASPCategory', ''),
                            'RiskTypeBasedOnSeverity': policy_data.get('Severity', '')
                        }
                        grouped_data[key_name]['results'].append(result_item)
                    elif policy_type == 'PermissionSetPermissions':
                        result_item = {
                            'SalesforceSetting': policy_data.get('Setting', ''),
                            'StandardValue': policy_data.get('StandardValue', ''),
                            'PermissionSetValue-UserPermissions': policy_data.get('OrgValue', ''),
                            'Match': policy_data.get('Match', False),
                            'Description': policy_data.get('IssueDescription', ''),
                            'OWASP': policy_data.get('OWASPCategory', ''),
                            'RiskTypeBasedOnSeverity': policy_data.get('Severity', '')
                        }
                        grouped_data[key_name]['results'].append(result_item)
                    elif policy_type in ['DeviceActivationProfilePermissions', 'MFAProfilePermissions', 'MFAPermissionSetPermissions', 'LoginIPRangesProfilePermissions', 'SessionTimeoutProfilePermissions', 'PasswordPolicyProfilePermissions']:
                        # For new task types, the OrgValue contains a JSON string of the complete results array
                        # We should store this directly without any processing
                        org_value = policy_data.get('OrgValue', '')
                        if org_value:
                            # The OrgValue is already a JSON string - store it directly
                            # This bypasses the grouping logic since the data is pre-grouped
                            grouped_data[key_name]['results'] = org_value
                        else:
                            grouped_data[key_name]['results'] = '[]'
                    else:
                        # Fallback for unknown types
                        result_item = {
                            'SalesforceSetting': policy_data.get('Setting', ''),
                            'StandardValue': policy_data.get('StandardValue', ''),
                            'Match': policy_data.get('Match', False),
                            'Description': policy_data.get('IssueDescription', ''),
                            'OWASP': policy_data.get('OWASPCategory', ''),
                            'RiskTypeBasedOnSeverity': policy_data.get('Severity', '')
                        }
                        grouped_data[key_name]['results'].append(result_item)

            # Store one record per profile/permission set
            for key_name, group_data in grouped_data.items():
                # Create deterministic RowKey: ProfileName-Timestamp
                import hashlib
                timestamp = datetime.now().strftime('%Y%m%d%H%M%S%f')[:-3]  # Remove microseconds, keep milliseconds
                row_key = f"{key_name}-{timestamp}"
                
                # CRITICAL FIX: Check for existing records with same profile/permission set name and execution log ID
                # This prevents duplicates even when the same task runs multiple times
                try:
                    # Query for existing records with same profile/permission set name and execution log ID
                    filter_query = f"PartitionKey eq '{org_id}' and TaskStatusId eq '{execution_log_id}' and Type eq '{group_data['Type']}'"
                    
                    if group_data['ProfileName']:
                        filter_query += f" and ProfileName eq '{group_data['ProfileName']}'"
                    elif group_data['PermissionSetName']:
                        filter_query += f" and PermissionSetName eq '{group_data['PermissionSetName']}'"
                    
                    existing_entities = list(repo.query_entities(filter_query))
                    
                    if existing_entities:
                        logger.info(f"Policy record already exists for {key_name} with execution_log_id {execution_log_id}. Skipping duplicate.")
                        continue
                except Exception as check_error:
                    logger.warning(f"Error checking for existing record: {check_error}")
                    # Continue with insertion if check fails
                
                # Create entity in old monolithic format
                # Handle different storage formats for different task types
                if group_data['Type'] in ['DeviceActivationProfilePermissions', 'MFAProfilePermissions', 'MFAPermissionSetPermissions', 'LoginIPRangesProfilePermissions', 'SessionTimeoutProfilePermissions', 'PasswordPolicyProfilePermissions']:
                    # For new task types, results is already a JSON string
                    org_value = group_data['results']
                else:
                    # For old task types, results is a list that needs to be JSON encoded
                    org_value = json.dumps(group_data['results'])
                
                entity = {
                    "PartitionKey": str(org_id),
                    "RowKey": row_key,
                    "OrgValue": org_value,  # Use the appropriate format
                    "OrgValue@type": "String",
                    "IntegrationId": str(org_id),
                    "IntegrationId@type": "String",
                    "TaskStatusId": execution_log_id,
                    "TaskStatusId@type": "String",
                    "CreatedAt": datetime.now().isoformat(),
                    "CreatedAt@type": "String",
                    "PermissionSetName": group_data['PermissionSetName'],
                    "PermissionSetName@type": "String",
                    "ProfileName": group_data['ProfileName'],
                    "ProfileName@type": "String",
                    "Type": group_data['Type'],
                    "Type@type": "String",
                    "OWASPCategory": "",
                    "OWASPCategory@type": "String",
                    "StandardValue": "",
                    "StandardValue@type": "String",
                    "IssueDescription": "",
                    "IssueDescription@type": "String",
                    "Recommendations": "",
                    "Recommendations@type": "String",
                    "Severity": "",
                    "Severity@type": "String",
                    "Weakness": "",
                    "Weakness@type": "String",
                    "Setting": "",
                    "Setting@type": "String",
                    "SettingGroup": "",
                    "SettingGroup@type": "String",
                    "UserLicense": "",
                    "UserLicense@type": "String"
                }

                if not repo.insert_entity(entity):
                    logger.error(f"Failed to insert policies result for {key_name}")
                    return False

            logger.info(f"Stored {len(grouped_data)} policies results (monolithic format) for org {org_id}")
            return True

        else:
            # Use SQL Database for production
            repo = get_policies_result_sql_repo()
            if not repo:
                logger.error("Policies result SQL repository not available")
                return False

            # CRITICAL FIX: Special handling for HealthCheck vs Profile/PermissionSet records
            if policies_data and policies_data[0].get('Type', '').strip() == 'HealthCheck':
                # For HealthCheck records, store each setting individually (not grouped)
                for policy_data in policies_data:
                    # Validate Type field - must not be empty
                    policy_type = policy_data.get('Type', '').strip()
                    if not policy_type:
                        logger.warning(f"Skipping policy data with empty Type field: {policy_data.get('Setting', 'unknown')}")
                        continue
                    
                    setting = policy_data.get('Setting', '').strip()
                    if not setting:
                        logger.warning(f"Skipping health check data with empty Setting field: {policy_data}")
                        continue
                    
                    # CRITICAL FIX: Check for existing health check records
                    check_query = """
                    SELECT COUNT(*) FROM App_PoliciesResult 
                    WHERE OrgId = ? AND ExecutionLogId = ? AND Type = 'HealthCheck' AND Setting = ?
                    """
                    check_params = [org_id, execution_log_id, setting]
                    
                    try:
                        existing_count = repo.execute_scalar(check_query, tuple(check_params))
                        if existing_count and existing_count > 0:
                            logger.info(f"Health check record already exists for {setting} with execution_log_id {execution_log_id}. Skipping duplicate.")
                            continue
                    except Exception as check_error:
                        logger.warning(f"Error checking for existing health check record: {check_error}")
                        # Continue with insertion if check fails
                    
                    # Insert health check record with individual column values
                    query = """
                    INSERT INTO App_PoliciesResult (OrgId, ExecutionLogId, ProfileName, PermissionSetName, Type, OrgValue, 
                                                  OWASPCategory, StandardValue, IssueDescription, Recommendations, Severity,
                                                  Weakness, IntegrationId, TaskStatusId, CreatedAt, Setting, SettingGroup, UserLicense)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """

                    params = (
                        org_id,
                        execution_log_id,
                        "",  # ProfileName (empty for health check)
                        "",  # PermissionSetName (empty for health check)
                        "HealthCheck",
                        policy_data.get('OrgValue', ''),  # Store actual org value, not JSON
                        policy_data.get('OWASPCategory', ''),
                        policy_data.get('StandardValue', ''),
                        policy_data.get('IssueDescription', ''),
                        policy_data.get('Recommendations', ''),
                        policy_data.get('Severity', ''),
                        policy_data.get('Weakness', ''),
                        str(org_id),
                        execution_log_id,
                        datetime.now(),
                        setting,
                        policy_data.get('SettingGroup', ''),
                        ""   # UserLicense
                    )

                    if not repo.execute_non_query(query, params):
                        logger.error(f"Failed to insert health check result for {setting}")
                        return False
                
                logger.info(f"Stored {len(policies_data)} health check results (individual format) for org {org_id}")
                return True
            else:
                # For Profile/PermissionSet records, use the original grouped approach
                grouped_data = {}
                for policy_data in policies_data:
                    # Validate Type field - must not be empty
                    policy_type = policy_data.get('Type', '').strip()
                    if not policy_type:
                        logger.warning(f"Skipping policy data with empty Type field: {policy_data.get('Setting', 'unknown')}")
                        continue
                    
                    # Get profile or permission set name
                    profile_name = policy_data.get('ProfileName', '').strip()
                    permission_set_name = policy_data.get('PermissionSetName', '').strip()
                    
                    if not profile_name and not permission_set_name:
                        logger.warning(f"Skipping policy data with no profile or permission set name")
                        continue
                    
                    # Use profile name if available, otherwise permission set name
                    key_name = profile_name if profile_name else permission_set_name
                
                if key_name not in grouped_data:
                    grouped_data[key_name] = {
                        'ProfileName': profile_name,
                        'PermissionSetName': permission_set_name,
                        'Type': policy_type,
                        'results': []
                    }
                
                # Add individual result to the group
                grouped_data[key_name]['results'].append({
                    'SalesforceSetting': policy_data.get('Setting', ''),
                    'StandardValue': policy_data.get('StandardValue', ''),
                    'ProfileValue': policy_data.get('OrgValue', ''),
                    'PermissionSetValue-UserPermissions': policy_data.get('OrgValue', ''),
                    'Match': policy_data.get('Match', False),
                    'UserType': policy_data.get('UserType', 'blank'),
                    'Description': policy_data.get('IssueDescription', ''),
                    'OWASP': policy_data.get('OWASPCategory', ''),
                    'RiskTypeBasedOnSeverity': policy_data.get('Severity', '')
                })

            # Store one record per profile/permission set
            for key_name, group_data in grouped_data.items():
                # CRITICAL FIX: Check for existing records with same profile/permission set name and execution log ID
                # This prevents duplicates even when the same task runs multiple times
                check_query = """
                SELECT COUNT(*) FROM App_PoliciesResult 
                WHERE OrgId = ? AND ExecutionLogId = ? AND Type = ?
                """
                check_params = [org_id, execution_log_id, group_data['Type']]
                
                if group_data['ProfileName']:
                    check_query += " AND ProfileName = ?"
                    check_params.append(group_data['ProfileName'])
                elif group_data['PermissionSetName']:
                    check_query += " AND PermissionSetName = ?"
                    check_params.append(group_data['PermissionSetName'])
                
                try:
                    existing_count = repo.execute_scalar(check_query, tuple(check_params))
                    if existing_count and existing_count > 0:
                        logger.info(f"Policy record already exists for {key_name} with execution_log_id {execution_log_id}. Skipping duplicate.")
                        continue
                except Exception as check_error:
                    logger.warning(f"Error checking for existing record: {check_error}")
                    # Continue with insertion if check fails
                
                # Insert in old monolithic format
                query = """
                INSERT INTO App_PoliciesResult (OrgId, ExecutionLogId, ProfileName, PermissionSetName, Type, OrgValue, 
                                              OWASPCategory, StandardValue, IssueDescription, Recommendations, Severity,
                                              Weakness, IntegrationId, TaskStatusId, CreatedAt, Setting, SettingGroup, UserLicense)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                # Handle different storage formats for different task types
                if group_data['Type'] in ['DeviceActivationProfilePermissions', 'MFAProfilePermissions', 'MFAPermissionSetPermissions', 'LoginIPRangesProfilePermissions', 'SessionTimeoutProfilePermissions', 'PasswordPolicyProfilePermissions']:
                    # For new task types, results is already a JSON string
                    org_value = group_data['results']
                else:
                    # For old task types, results is a list that needs to be JSON encoded
                    org_value = json.dumps(group_data['results'])
                
                params = (
                    org_id,
                    execution_log_id,
                    group_data['ProfileName'],
                    group_data['PermissionSetName'],
                    group_data['Type'],
                    org_value,  # Use the appropriate format
                    '',  # OWASPCategory
                    '',  # StandardValue
                    '',  # IssueDescription
                    '',  # Recommendations
                    '',  # Severity
                    '',  # Weakness
                    str(org_id),
                    execution_log_id,
                    datetime.now(),
                    '',  # Setting
                    '',  # SettingGroup
                    ''   # UserLicense
                )

                if not repo.execute_non_query(query, params):
                    logger.error(f"Failed to insert policies result for {key_name}")
                    return False

            logger.info(f"Stored {len(grouped_data)} policies results (monolithic format) for org {org_id}")
            return True

    except Exception as e:
        logger.error(f"Error storing policies result data: {str(e)}")
        return False


def get_policies_result_data(org_id: str, execution_log_id: Optional[str] = None, policy_type: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get policies result data for an organization

    Args:
        org_id: Organization ID
        execution_log_id: Optional filter by execution log ID
        policy_type: Optional filter by policy type (e.g., 'HealthCheck', 'ProfilePermission')

    Returns:
        List of policies result dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_policies_result_table_repo()
            if not repo:
                logger.error("Policies result table repository not available")
                return []

            filter_query = f"PartitionKey eq '{org_id}'"
            if execution_log_id:
                filter_query += f" and TaskStatusId eq '{execution_log_id}'"
            if policy_type:
                filter_query += f" and Type eq '{policy_type}'"

            entities = list(repo.query_entities(filter_query))

            policies_results = []
            for entity in entities:
                policies_results.append({
                    'PolicyId': entity.get('PolicyId') or entity.get('Setting'),
                    'Setting': entity.get('Setting'),  # Add Setting field
                    'SettingGroup': entity.get('SettingGroup'),  # Add SettingGroup field
                    'OrgValue': entity.get('OrgValue'),
                    'StandardValue': entity.get('StandardValue'),
                    'Weakness': entity.get('Weakness'),
                    'OWASPCategory': entity.get('OWASPCategory'),
                    'Severity': entity.get('Severity'),
                    'Recommendations': entity.get('Recommendations'),
                    'IssueDescription': entity.get('IssueDescription'),
                    'CreatedAt': entity.get('CreatedAt'),
                    'ExecutionLogId': entity.get('ExecutionLogId') or entity.get('TaskStatusId'),
                    'IntegrationId': entity.get('IntegrationId'),
                    'Type': entity.get('Type'),
                    'ProfileName': entity.get('ProfileName'),
                    'PermissionSetName': entity.get('PermissionSetName')
                })

            # Sort by CreatedAt descending
            policies_results.sort(key=lambda x: x.get('CreatedAt', ''), reverse=True)
            return policies_results

        else:
            # Use SQL Database for production
            repo = get_policies_result_sql_repo()
            if not repo:
                logger.error("Policies result SQL repository not available")
                return []

            # Build query with filters
            query = """
            SELECT Setting, OrgValue, StandardValue, Weakness, OWASPCategory, Severity,
                   Recommendations, IssueDescription, CreatedAt, TaskStatusId, Type, SettingGroup
            FROM App_PoliciesResult WHERE OrgId = ?
            """
            params = [org_id]

            if execution_log_id:
                query += " AND TaskStatusId = ?"
                params.append(execution_log_id)
            if policy_type:
                query += " AND Type = ?"
                params.append(policy_type)

            query += " ORDER BY CreatedAt DESC"

            results = repo.execute_query(query, tuple(params))

            policies_results = []
            for row in results:
                policies_results.append({
                    'PolicyId': row[0],  # Setting field mapped to PolicyId
                    'Setting': row[0],  # Setting field
                    'SettingGroup': row[11],  # SettingGroup field
                    'OrgValue': row[1],
                    'StandardValue': row[2],
                    'Weakness': row[3],
                    'OWASPCategory': row[4],
                    'Severity': row[5],
                    'Recommendations': row[6],
                    'IssueDescription': row[7],
                    'CreatedAt': row[8].isoformat() if row[8] else None,
                    'ExecutionLogId': row[9],  # TaskStatusId mapped to ExecutionLogId
                    'Type': row[10]  # Add Type field
                })

            return policies_results

    except Exception as e:
        logger.error(f"Error getting policies result data: {str(e)}")
        return []


def store_profile_assignment_count_data(org_id: str, execution_log_id: str, assignment_data: List[Dict[str, Any]]) -> bool:
    """
    Store profile assignment count data

    Args:
        org_id: Organization ID
        execution_log_id: Execution log ID
        assignment_data: List of profile assignment count data dictionaries

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_profile_assignment_count_table_repo()
            if not repo:
                logger.error("Profile assignment count table repository not available")
                return False

            for assignment in assignment_data:
                # DEBUG: Log the assignment data being processed
                logger.info(f"DEBUG: Processing assignment data: {assignment}")
                
                profile_name = assignment.get('profile_name', '')  # Note: using 'profile_name' not 'ProfileName'
                permission_set_name = assignment.get('permission_set_name', '')  # Note: using 'permission_set_name' not 'PermissionSetName'
                assignment_count = assignment.get('assignment_count', 0)  # Note: using 'assignment_count' not 'AssignmentCount'
                
                logger.info(f"DEBUG: Extracted - ProfileName: '{profile_name}', PermissionSetName: '{permission_set_name}', AssignmentCount: {assignment_count}")
                
                # Generate deterministic RowKey without timestamp to prevent duplicates
                if permission_set_name:
                    # For permission set assignments: ProfileName-PermissionSetName
                    row_key = f"{profile_name}-{permission_set_name}"
                else:
                    # For profile assignments: ProfileName--
                    row_key = f"{profile_name}--"
                
                entity = {
                    "PartitionKey": str(org_id),
                    "RowKey": row_key,
                    "ExecutionLogId": execution_log_id,
                    "ProfileName": profile_name,
                    "PermissionSetName": permission_set_name,
                    "AssignmentCount": assignment_count,
                    "AssignedProfiles": assignment.get('AssignedProfiles', ''),
                    "Type": assignment.get('Type', ''),
                    "CreatedAt": datetime.now().isoformat()
                }
                
                logger.info(f"DEBUG: Created entity: {entity}")

                # Use insert_or_update_entity to handle both new records and updates
                if not repo.insert_or_update_entity(entity):
                    logger.error(f"Failed to insert/update profile assignment count: {assignment}")
                    return False

            logger.info(f"Stored {len(assignment_data)} profile assignment counts for org {org_id}")
            return True

        else:
            # Use SQL Database for production
            repo = get_profile_assignment_count_sql_repo()
            if not repo:
                logger.error("Profile assignment count SQL repository not available")
                return False

            # Use UPSERT (INSERT OR UPDATE) for better performance and to handle duplicates
            query = """
            MERGE INTO App_ProfileAssignmentCount AS target
            USING (VALUES (?, ?, ?, ?, ?, ?, ?)) AS source 
                (ExecutionLogId, ProfileName, PermissionSetName, AssignmentCount, AssignedProfiles, Type, CreatedAt)
            ON target.ProfileName = source.ProfileName 
                AND target.PermissionSetName = source.PermissionSetName
                AND target.ExecutionLogId = source.ExecutionLogId
            WHEN MATCHED THEN
                UPDATE SET 
                    AssignmentCount = source.AssignmentCount,
                    AssignedProfiles = source.AssignedProfiles,
                    Type = source.Type,
                    CreatedAt = source.CreatedAt
            WHEN NOT MATCHED THEN
                INSERT (ExecutionLogId, ProfileName, PermissionSetName, AssignmentCount, AssignedProfiles, Type, CreatedAt)
                VALUES (source.ExecutionLogId, source.ProfileName, source.PermissionSetName, 
                        source.AssignmentCount, source.AssignedProfiles, source.Type, source.CreatedAt);
            """

            for assignment in assignment_data:
                params = (
                    execution_log_id,
                    assignment.get('ProfileName', ''),
                    assignment.get('PermissionSetName', ''),
                    assignment.get('AssignmentCount', 0),
                    assignment.get('AssignedProfiles', ''),
                    assignment.get('Type', ''),
                    datetime.now()
                )

                if not repo.execute_non_query(query, params):
                    logger.error(f"Failed to insert/update profile assignment count: {params}")
                    return False

            logger.info(f"Stored {len(assignment_data)} profile assignment counts for org {org_id}")
            return True

    except Exception as e:
        logger.error(f"Error storing profile assignment count data: {str(e)}")
        return False


def get_profile_assignment_count_data(org_id: str, execution_log_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get profile assignment count data for an organization

    Args:
        org_id: Organization ID
        execution_log_id: Optional filter by execution log ID

    Returns:
        List of profile assignment count dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_profile_assignment_count_table_repo()
            if not repo:
                logger.error("Profile assignment count table repository not available")
                return []

            filter_query = f"PartitionKey eq '{org_id}'"
            if execution_log_id:
                filter_query += f" and ExecutionLogId eq '{execution_log_id}'"

            entities = list(repo.query_entities(filter_query))

            assignment_counts = []
            for entity in entities:
                assignment_counts.append({
                    'execution_log_id': entity.get('ExecutionLogId'),
                    'profile_name': entity.get('ProfileName'),
                    'permission_set_name': entity.get('PermissionSetName'),
                    'assignment_count': entity.get('AssignmentCount', 0),
                    'assigned_profiles': entity.get('AssignedProfiles'),
                    'type': entity.get('Type'),
                    'created_at': entity.get('CreatedAt')
                })

            # Sort by CreatedAt descending
            assignment_counts.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            return assignment_counts

        else:
            # Use SQL Database for production
            repo = get_profile_assignment_count_sql_repo()
            if not repo:
                logger.error("Profile assignment count SQL repository not available")
                return []

            # Build query with filters
            query = """
            SELECT ExecutionLogId, ProfileName, PermissionSetName, AssignmentCount,
                   AssignedProfiles, Type, CreatedAt
            FROM App_ProfileAssignmentCount WHERE 1=1
            """
            params = []

            if execution_log_id:
                query += " AND ExecutionLogId = ?"
                params.append(execution_log_id)

            query += " ORDER BY CreatedAt DESC"

            results = repo.execute_query(query, tuple(params))

            assignment_counts = []
            for row in results:
                assignment_counts.append({
                    'execution_log_id': row[0],
                    'profile_name': row[1],
                    'permission_set_name': row[2],
                    'assignment_count': row[3],
                    'assigned_profiles': row[4],
                    'type': row[5],
                    'created_at': row[6].isoformat() if row[6] else None
                })

            return assignment_counts

    except Exception as e:
        logger.error(f"Error getting profile assignment count data: {str(e)}")
        return []


# Create blueprint
bp = func.Blueprint()

@bp.route(route="security/health-checks", methods=["GET"])
def list_health_checks(req: func.HttpRequest) -> func.HttpResponse:
    """Get health check data with optional filtering"""
    try:
        # Get query parameters
        org_id = req.params.get('org_id')
        risk_type = req.params.get('risk_type')

        if org_id:
            health_checks = get_security_health_check_data(org_id, risk_type)
        else:
            health_checks = []

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": health_checks
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error getting health check data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/health-checks", methods=["POST"])
def store_health_checks(req: func.HttpRequest) -> func.HttpResponse:
    """Store health check data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        org_id = req_body.get('org_id')
        execution_log_id = req_body.get('execution_log_id')
        risks_data = req_body.get('risks_data', [])

        if not org_id or not execution_log_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id and execution_log_id are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Store health check data
        success = store_security_health_check_risks(org_id, execution_log_id, risks_data)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": f"Successfully stored {len(risks_data)} health check records"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store health check data"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing health check data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/policies-result", methods=["GET"])
def list_policies_results(req: func.HttpRequest) -> func.HttpResponse:
    """Get policies result data with optional filtering"""
    try:
        # Get query parameters
        org_id = req.params.get('org_id')
        execution_log_id = req.params.get('execution_log_id')
        policy_type = req.params.get('type')  # Add support for type filtering

        if org_id:
            policies_results = get_policies_result_data(org_id, execution_log_id, policy_type)
        else:
            policies_results = []

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": policies_results
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error getting policies result data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/policies-result", methods=["POST"])
def store_policies_results(req: func.HttpRequest) -> func.HttpResponse:
    """Store policies result data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        org_id = req_body.get('org_id')
        execution_log_id = req_body.get('execution_log_id')
        policies_data = req_body.get('policies_data', [])

        if not org_id or not execution_log_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id and execution_log_id are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        success = store_policies_result_data(org_id, execution_log_id, policies_data)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": f"Stored {len(policies_data)} policies results"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store policies result data"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing policies result data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/profile-assignment-counts", methods=["GET"])
def list_profile_assignment_counts(req: func.HttpRequest) -> func.HttpResponse:
    """Get profile assignment count data with optional filtering"""
    try:
        # Get query parameters
        org_id = req.params.get('org_id')
        execution_log_id = req.params.get('execution_log_id')

        if org_id:
            assignment_counts = get_profile_assignment_count_data(org_id, execution_log_id)
        else:
            assignment_counts = []

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": assignment_counts
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error getting profile assignment count data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/profile-assignment-counts", methods=["POST"])
def store_profile_assignment_counts(req: func.HttpRequest) -> func.HttpResponse:
    """Store profile assignment count data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        org_id = req_body.get('org_id')
        execution_log_id = req_body.get('execution_log_id')
        assignment_data = req_body.get('assignment_data', [])

        if not org_id or not execution_log_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id and execution_log_id are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        success = store_profile_assignment_count_data(org_id, execution_log_id, assignment_data)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": f"Stored {len(assignment_data)} profile assignment counts"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store profile assignment count data"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing profile assignment count data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/overview", methods=["POST"])
def store_overview(req: func.HttpRequest) -> func.HttpResponse:
    """Store overview data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        org_id = req_body.get('org_id')
        overview_data = req_body.get('overview_data')

        if not org_id or not overview_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id and overview_data are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Store overview data
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_overview_table_repo()
            if not repo:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Overview table repository not available"
                    }),
                    mimetype="application/json",
                    status_code=500
                )

            entity = {
                'PartitionKey': str(org_id),
                'RowKey': overview_data.get('execution_log_id', datetime.now().strftime('%Y%m%d%H%M%S')),
                'HealthScore': overview_data.get('health_score', 0),
                'TotalProfiles': overview_data.get('total_profiles', 0),
                'TotalPermissionSets': overview_data.get('total_permission_sets', 0),
                'TotalRisks': overview_data.get('total_risks', 0),
                'HighRisks': overview_data.get('high_risks', 0),
                'MediumRisks': overview_data.get('medium_risks', 0),
                'LowRisks': overview_data.get('low_risks', 0),
                'LastUpdated': overview_data.get('last_updated', datetime.now().isoformat())
            }

            success = repo.insert_entity(entity)
        else:
            # Use SQL Database for production
            repo = get_overview_sql_repo()
            if not repo:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Overview SQL repository not available"
                    }),
                    mimetype="application/json",
                    status_code=500
                )

            query = """
            INSERT INTO App_Overview (OrgId, ExecutionLogId, HealthScore, TotalProfiles, TotalPermissionSets,
                                    TotalRisks, HighRisks, MediumRisks, LowRisks, LastUpdated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                str(org_id),
                overview_data.get('execution_log_id'),
                overview_data.get('health_score', 0),
                overview_data.get('total_profiles', 0),
                overview_data.get('total_permission_sets', 0),
                overview_data.get('total_risks', 0),
                overview_data.get('high_risks', 0),
                overview_data.get('medium_risks', 0),
                overview_data.get('low_risks', 0),
                overview_data.get('last_updated', datetime.now().isoformat())
            )

            success = repo.execute_non_query(query, params)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Successfully stored overview data"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store overview data"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing overview data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/profiles", methods=["POST"])
def store_profiles(req: func.HttpRequest) -> func.HttpResponse:
    """Store profile data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        org_id = req_body.get('org_id')
        execution_log_id = req_body.get('execution_log_id')
        profile_data = req_body.get('profile_data')

        if not org_id or not execution_log_id or not profile_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id, execution_log_id, and profile_data are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Store profile data using the existing function
        success = store_profile_data(org_id, execution_log_id, profile_data)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": f"Successfully stored {len(profile_data)} profile records"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store profile data"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing profile data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/permission-sets", methods=["POST"])
def store_permission_sets(req: func.HttpRequest) -> func.HttpResponse:
    """Store permission sets data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        org_id = req_body.get('org_id')
        execution_log_id = req_body.get('execution_log_id')
        permission_sets_data = req_body.get('permission_sets_data')

        if not org_id or not execution_log_id or not permission_sets_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id, execution_log_id, and permission_sets_data are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Store permission sets data
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_profile_permission_table_repo()
            if not repo:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Profile permission table repository not available"
                    }),
                    mimetype="application/json",
                    status_code=500
                )

            for permission_set in permission_sets_data:
                entity = {
                    'PartitionKey': str(org_id),
                    'RowKey': f"{datetime.now().strftime('%Y%m%d%H%M%S%f')}-{permission_set.get('name', 'unknown')}",
                    'EntityType': 'PermissionSet',
                    'EntityId': permission_set.get('id', ''),
                    'Name': permission_set.get('name', ''),
                    'UserCount': permission_set.get('user_count', 0),
                    'LastUpdated': datetime.now().isoformat()
                }

                # Add permission fields
                for key, value in permission_set.items():
                    if key.startswith('permissions'):
                        perm_name = key[11:]  # Remove 'permissions' prefix
                        entity[f'Permission_{perm_name}'] = str(value)

                if not repo.insert_entity(entity):
                    logger.error(f"Failed to insert permission set: {permission_set}")

            success = True
        else:
            # Use SQL Database for production
            repo = get_profile_permission_sql_repo()
            if not repo:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Profile permission SQL repository not available"
                    }),
                    mimetype="application/json",
                    status_code=500
                )

            # For now, use the same table as profiles but with EntityType = 'PermissionSet'
            # In production, you might want a separate table
            query = """
            INSERT INTO App_ProfilePermissions (OrgId, ExecutionLogId, SalesforceId, ProfileName,
                                              EntityType, UserCount, LastUpdated)
            VALUES (?, ?, ?, ?, 'PermissionSet', ?, ?)
            """

            for permission_set in permission_sets_data:
                params = (
                    org_id,
                    execution_log_id,
                    permission_set.get('id', ''),
                    permission_set.get('name', ''),
                    permission_set.get('user_count', 0),
                    datetime.now()
                )

                if not repo.execute_non_query(query, params):
                    logger.error(f"Failed to insert permission set: {permission_set}")

            success = True

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": f"Successfully stored {len(permission_sets_data)} permission set records"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store permission sets data"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing permission sets data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="security/pmd-findings", methods=["POST"])
def store_pmd_finding(req: func.HttpRequest) -> func.HttpResponse:
    """Store PMD finding data"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        org_id = req_body.get('org_id')
        pmd_finding = req_body.get('pmd_finding')

        if not org_id or not pmd_finding:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id and pmd_finding are required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Store PMD finding data
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_pmd_scans_table_repo()
            if not repo:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "PMDScans table repository not available"
                    }),
                    mimetype="application/json",
                    status_code=500
                )

            # The pmd_finding already contains all the necessary fields
            success = repo.insert_entity(pmd_finding)
        else:
            # Use SQL Database for production
            repo = get_pmd_scans_sql_repo()
            if not repo:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "SQL repository not available"
                    }),
                    mimetype="application/json",
                    status_code=500
                )

            # Insert into App_PMDScans table
            query = """
            INSERT INTO App_PMDScans (OrgId, TaskId, ExecutionLogId, FileName, RuleName, Category,
                                    Priority, LineNumber, Description, WeaknessType, WeaknessDescription,
                                    BlobPrefix, CreatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                org_id,
                pmd_finding.get('TaskId'),
                pmd_finding.get('ExecutionLogId'),
                pmd_finding.get('FileName'),
                pmd_finding.get('RuleName'),
                pmd_finding.get('Category'),
                pmd_finding.get('Priority'),
                pmd_finding.get('LineNumber'),
                pmd_finding.get('Description'),
                pmd_finding.get('WeaknessType'),
                pmd_finding.get('WeaknessDescription'),
                pmd_finding.get('BlobPrefix'),
                pmd_finding.get('CreatedAt')
            )

            success = repo.execute_non_query(query, params)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Successfully stored PMD finding"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to store PMD finding"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error storing PMD finding: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="profiles-permissions/{org_id}", methods=["GET"])
def get_profiles_permissions(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get profiles and permissions data for an organization
    
    Args:
        org_id: Organization ID from route parameters
        execution_log_id: Execution log ID from query parameters (optional)
    
    Returns:
        JSON response with profiles and permissions data
    """
    logger.info('Processing profiles permissions request...')
    
    try:
        # Get org_id from route parameters
        org_id = req.route_params.get('org_id')
        if not org_id:
            return func.HttpResponse(
                json.dumps({"success": False, "error": "Organization ID is required"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Get execution_log_id and task_type from query parameters
        execution_log_id = req.params.get("execution_log_id")
        task_type = req.params.get("task_type")
        
        logger.info(f"Getting profiles and permissions for org_id: {org_id}, execution_log_id: {execution_log_id}, task_type: {task_type}")
        
        # Map task_type to policy_type for filtering
        policy_type = None
        if task_type:
            task_type_mapping = {
                'device_activation': 'DeviceActivationProfilePermissions',
                'login_ip_ranges': 'LoginIPRangesProfilePermissions',
                'mfa_enforcement': ['MFAProfilePermissions', 'MFAPermissionSetPermissions'],
                'password_policy': 'PasswordPolicyProfilePermissions',
                'session_timeout': 'SessionTimeoutProfilePermissions',
                'login_hours': 'LoginHoursProfilePermissions',
                'api_whitelisting': 'APIWhitelistingProfilePermissions'
            }
            policy_type = task_type_mapping.get(task_type)
        
        # Get policies result data with task type filtering
        if policy_type and isinstance(policy_type, list):
            # Handle multiple policy types (like MFA which has both profile and permission set policies)
            policies_data = []
            for pt in policy_type:
                policies_data.extend(get_policies_result_data(org_id, execution_log_id, pt))
        else:
            policies_data = get_policies_result_data(org_id, execution_log_id, policy_type)
        
        # Get assignment data directly from ProfileAssignmentCount table
        profile_assignments = []
        permission_set_assignments = []
        
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_profile_assignment_count_table_repo()
            if repo:
                filter_query = f"PartitionKey eq '{org_id}'"
                if execution_log_id:
                    filter_query += f" and ExecutionLogId eq '{execution_log_id}'"
                
                entities = list(repo.query_entities(filter_query))
                
                for entity in entities:
                    assignment_data = {
                        'execution_log_id': entity.get('ExecutionLogId'),
                        'profile_name': entity.get('ProfileName'),
                        'permission_set_name': entity.get('PermissionSetName'),
                        'assignment_count': entity.get('AssignmentCount', 0),
                        'assigned_profiles': entity.get('AssignedProfiles'),
                        'type': entity.get('Type'),
                        'created_at': entity.get('CreatedAt')
                    }
                    
                    # Separate profile assignments from permission set assignments
                    if entity.get('PermissionSetName'):
                        permission_set_assignments.append(assignment_data)
                    else:
                        profile_assignments.append(assignment_data)
        else:
            # Use SQL Database for production
            repo = get_profile_assignment_count_sql_repo()
            if repo:
                query = """
                SELECT ExecutionLogId, ProfileName, PermissionSetName, AssignmentCount,
                       AssignedProfiles, Type, CreatedAt
                FROM App_ProfileAssignmentCount WHERE IntegrationId = ?
                """
                params = [org_id]
                
                if execution_log_id:
                    query += " AND ExecutionLogId = ?"
                    params.append(execution_log_id)
                
                query += " ORDER BY CreatedAt DESC"
                results = repo.execute_query(query, tuple(params))
                
                for row in results:
                    assignment_data = {
                        'execution_log_id': row[0],
                        'profile_name': row[1],
                        'permission_set_name': row[2],
                        'assignment_count': row[3],
                        'assigned_profiles': row[4],
                        'type': row[5],
                        'created_at': row[6].isoformat() if row[6] else None
                    }
                    
                    # Separate profile assignments from permission set assignments
                    if row[2]:  # PermissionSetName
                        permission_set_assignments.append(assignment_data)
                    else:
                        profile_assignments.append(assignment_data)
        
        # Filter for profile and permission set related policies
        profile_policies = []
        permission_set_policies = []
        
        for policy in policies_data:
            policy_type = policy.get('Type', '')
            
            # Include all profile and permission set related policies, including task-specific ones
            if (policy_type == 'ProfilePermissions' or 
                policy_type == 'PermissionSetPermissions' or
                policy_type.endswith('ProfilePermissions') or
                policy_type.endswith('PermissionSetPermissions')):
                if policy_type == 'PermissionSetPermissions' or policy_type.endswith('PermissionSetPermissions'):
                    permission_set_policies.append(policy)
                else:
                    profile_policies.append(policy)
        
        # Collect all unique permission setting names for dynamic columns
        all_settings = set()
        for policy in profile_policies + permission_set_policies:
            org_value = policy.get('OrgValue', '{}')
            if isinstance(org_value, str):
                try:
                    settings = json.loads(org_value)
                    if isinstance(settings, list):
                        for item in settings:
                            if isinstance(item, dict):
                                all_settings.update(item.keys())
                    elif isinstance(settings, dict):
                        all_settings.update(settings.keys())
                except json.JSONDecodeError:
                    continue
            elif isinstance(org_value, dict):
                all_settings.update(org_value.keys())
        
        response_data = {
            "success": True,
            "status": "completed",
            "settings": sorted(list(all_settings)),
            "policies": profile_policies + permission_set_policies,
            "profileAssignments": profile_assignments,
            "permissionSetAssignments": permission_set_assignments,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"Returning profiles and permissions data with {len(profile_policies)} profile policies, {len(permission_set_policies)} permission set policies")
        
        return func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        error_message = f"Error getting profiles and permissions for org {org_id}: {str(e)}"
        logger.error(error_message)
        
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "status": "error",
                "message": error_message
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="security/pmd-findings/{org_id}", methods=["GET"])
def get_pmd_findings_by_org(req: func.HttpRequest) -> func.HttpResponse:
    """Get PMD findings for an organization"""
    try:
        org_id = req.route_params.get('org_id')
        if not org_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Get PMD findings data
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_pmd_scans_table_repo()
            if not repo:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "PMDScans table repository not available"
                    }),
                    mimetype="application/json",
                    status_code=500
                )

            # Query for PMD findings
            filter_query = f"PartitionKey eq '{org_id}'"
            entities = repo.query_entities(filter_query)

            findings = []
            if entities:
                for entity in entities:
                    findings.append({
                        "id": entity.get("RowKey"),
                        "task_id": entity.get("TaskId"),
                        "execution_log_id": entity.get("ExecutionLogId"),
                        "file_name": entity.get("FileName"),
                        "file_path": entity.get("FilePath"),
                        "rule_name": entity.get("RuleName"),
                        "issue_category": entity.get("IssueCategory"),
                        "issue_description": entity.get("IssueDescription"),
                        "issue_priority": entity.get("IssuePriority"),
                        "severity": entity.get("Severity"),
                        "status": entity.get("Status"),
                        "line_number": entity.get("LineNumber"),
                        "package": entity.get("Package"),
                        "issue_type": entity.get("IssueType"),
                        "pmd_rule_set": entity.get("PMDRuleSet"),
                        "pmd_problem": entity.get("PMDProblem"),
                        "weakness_type": entity.get("WeaknessType"),
                        "weakness_type_description": entity.get("WeaknessTypeDescription"),
                        "language": entity.get("Language"),
                        "tool": entity.get("Tool"),
                        "scan_type": entity.get("ScanType"),
                        "policy_name": entity.get("PolicyName"),
                        "blob_prefix": entity.get("BlobPrefix"),
                        "created_at": entity.get("CreatedAt")
                    })
        else:
            # Use SQL Database for production
            repo = get_pmd_scans_sql_repo()
            if not repo:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "PMDScans SQL repository not available"
                    }),
                    mimetype="application/json",
                    status_code=500
                )

            # Query for PMD findings
            query = """
            SELECT TaskId, ExecutionLogId, FileName, FilePath, RuleName, IssueCategory, 
                   IssueDescription, IssuePriority, Severity, Status, LineNumber, Package,
                   IssueType, PMDRuleSet, PMDProblem, WeaknessType, WeaknessTypeDescription,
                   Language, Tool, ScanType, PolicyName, BlobPrefix, CreatedAt
            FROM App_PMDScans
            WHERE OrgId = ?
            ORDER BY CreatedAt DESC
            """

            results = repo.execute_query(query, (org_id,))
            findings = []
            if results:
                for row in results:
                    findings.append({
                        "task_id": row[0],
                        "execution_log_id": row[1],
                        "file_name": row[2],
                        "file_path": row[3],
                        "rule_name": row[4],
                        "issue_category": row[5],
                        "issue_description": row[6],
                        "issue_priority": row[7],
                        "severity": row[8],
                        "status": row[9],
                        "line_number": row[10],
                        "package": row[11],
                        "issue_type": row[12],
                        "pmd_rule_set": row[13],
                        "pmd_problem": row[14],
                        "weakness_type": row[15],
                        "weakness_type_description": row[16],
                        "language": row[17],
                        "tool": row[18],
                        "scan_type": row[19],
                        "policy_name": row[20],
                        "blob_prefix": row[21],
                        "created_at": row[22]
                    })

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": findings,
                "count": len(findings)
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting PMD findings: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )