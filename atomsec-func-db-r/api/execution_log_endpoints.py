"""
Execution Log API Endpoints

This module provides RESTful API endpoints for managing execution logs.
Execution logs track the progress and status of long-running operations.
"""

import logging
import json
import azure.functions as func
from azure.functions import Blueprint
from typing import Dict, Any, Optional, List
from datetime import datetime

from shared.execution_log_service import get_execution_log_service

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create Azure Functions Blueprint for execution log endpoints
bp = Blueprint()

@bp.route(route="execution-logs", methods=["POST"])
def create_execution_log(req: func.HttpRequest) -> func.HttpResponse:
    """Create a new execution log"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Extract required fields
        org_id = req_body.get('org_id')
        execution_type = req_body.get('execution_type')
        user_id = req_body.get('user_id')
        priority = req_body.get('priority', 'Medium')
        status = req_body.get('status', 'Pending')

        # Validate required fields
        if not org_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        if not execution_type:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "execution_type is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        if not user_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "user_id is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Get execution log service
        execution_service = get_execution_log_service()
        if not execution_service:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Execution log service not available"
                }),
                mimetype="application/json",
                status_code=500
            )

        # Convert user_id to int if it's numeric, otherwise use 1 as default
        user_id_int = int(user_id) if str(user_id).isdigit() else 1

        # Create execution log
        execution_log_id = execution_service.create_execution_log(
            org_id=org_id,
            execution_type=execution_type,
            user_id=user_id_int,
            priority=priority,
            status=status
        )

        if execution_log_id:
            logger.info(f"Created execution log {execution_log_id} for org {org_id}")
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {
                        "execution_log_id": execution_log_id,
                        "org_id": org_id,
                        "execution_type": execution_type,
                        "user_id": user_id_int,
                        "priority": priority,
                        "status": status
                    }
                }),
                mimetype="application/json",
                status_code=201
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create execution log"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error creating execution log: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="execution-logs/get", methods=["GET"])
def get_execution_log(req: func.HttpRequest) -> func.HttpResponse:
    """Get an execution log by ID"""
    try:
        # Get execution_log_id from query parameters
        execution_log_id = req.params.get('execution_log_id')
        
        if not execution_log_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "execution_log_id parameter is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Get execution log service
        execution_service = get_execution_log_service()
        if not execution_service:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Execution log service not available"
                }),
                mimetype="application/json",
                status_code=500
            )

        # Get execution log
        execution_log = execution_service.get_execution_log(execution_log_id)

        if execution_log:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": execution_log
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Execution log not found"
                }),
                mimetype="application/json",
                status_code=404
            )

    except Exception as e:
        logger.error(f"Error getting execution log {execution_log_id}: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="execution-logs/update", methods=["PUT"])
def update_execution_log(req: func.HttpRequest) -> func.HttpResponse:
    """Update an execution log"""
    try:
        # Get execution_log_id from query parameters
        execution_log_id = req.params.get('execution_log_id')
        
        if not execution_log_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "execution_log_id parameter is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Get execution log service
        execution_service = get_execution_log_service()
        if not execution_service:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Execution log service not available"
                }),
                mimetype="application/json",
                status_code=500
            )

        # Extract update fields
        status = req_body.get('status')
        end_time = req_body.get('end_time')

        if status:
            # Update execution log status
            success = execution_service.update_execution_log_status(
                execution_log_id=execution_log_id,
                status=status,
                end_time=datetime.fromisoformat(end_time) if end_time else None
            )

            if success:
                return func.HttpResponse(
                    json.dumps({
                        "success": True,
                        "message": "Execution log updated successfully"
                    }),
                    mimetype="application/json",
                    status_code=200
                )
            else:
                return func.HttpResponse(
                    json.dumps({
                        "success": False,
                        "error": "Failed to update execution log"
                    }),
                    mimetype="application/json",
                    status_code=500
                )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "No valid update fields provided"
                }),
                mimetype="application/json",
                status_code=400
            )

    except Exception as e:
        logger.error(f"Error updating execution log {execution_log_id}: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="execution-logs", methods=["GET"])
def list_execution_logs(req: func.HttpRequest) -> func.HttpResponse:
    """List execution logs with optional filtering"""
    try:
        # Get query parameters
        org_id = req.params.get('org_id')
        limit = int(req.params.get('limit', 50))
        status_filter = req.params.get('status')

        if not org_id:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "org_id parameter is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Get execution log service
        execution_service = get_execution_log_service()
        if not execution_service:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Execution log service not available"
                }),
                mimetype="application/json",
                status_code=500
            )

        # Get execution logs
        execution_logs = execution_service.get_execution_logs_by_org(
            org_id=org_id,
            limit=limit,
            status_filter=status_filter
        )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": execution_logs
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error listing execution logs: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        ) 