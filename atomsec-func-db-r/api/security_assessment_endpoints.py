import azure.functions as func
import json
import logging
import asyncio
import aiohttp
import ssl
import socket
from urllib.parse import urlparse
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import re

logger = logging.getLogger(__name__)

# Security assessment configuration
ASSESSMENT_CONFIG = {
    'max_scan_duration': 300,  # 5 minutes
    'max_requests_per_minute': 60,
    'timeout': 30,  # seconds
    'user_agent': 'AtomSec-SecurityAssessment/1.0'
}

# Common security headers to check
SECURITY_HEADERS = [
    'X-Frame-Options',
    'X-Content-Type-Options',
    'X-XSS-Protection',
    'Strict-Transport-Security',
    'Content-Security-Policy',
    'Referrer-Policy',
    'Permissions-Policy',
    'Cross-Origin-Embedder-Policy',
    'Cross-Origin-Opener-Policy',
    'Cross-Origin-Resource-Policy'
]

# Common vulnerabilities to check
VULNERABILITY_PATTERNS = [
    {
        'name': 'SQL Injection',
        'pattern': r"('|--|;|/\*|\*/|xp_|sp_|@@|char\(|nchar\(|varchar\(|nvarchar\(|alter\s+table|create\s+table|drop\s+table|exec\s*\()",
        'severity': 'high'
    },
    {
        'name': 'XSS (Cross-Site Scripting)',
        'pattern': r"(<script|javascript:|vbscript:|onload=|onerror=|onclick=|<iframe|<object|<embed)",
        'severity': 'high'
    },
    {
        'name': 'Path Traversal',
        'pattern': r"(\.\./|\.\.\\|%2e%2e%2f|%2e%2e%5c)",
        'severity': 'medium'
    },
    {
        'name': 'Command Injection',
        'pattern': r"(\||&|;|`|\$\(|%0a|%0d)",
        'severity': 'high'
    }
]

def validate_target_url(url: str) -> Dict[str, Any]:
    """Validate target URL for security assessment"""
    try:
        parsed = urlparse(url)
        
        # Check protocol
        if parsed.scheme not in ['http', 'https']:
            return {'valid': False, 'error': 'Invalid protocol. Only HTTP and HTTPS are allowed.'}
        
        # Check if it's a valid domain
        if not parsed.netloc or '.' not in parsed.netloc:
            return {'valid': False, 'error': 'Invalid domain name.'}
        
        # Check for localhost or private IPs (forbidden in production)
        if parsed.netloc in ['localhost', '127.0.0.1'] or parsed.netloc.startswith('192.168.') or parsed.netloc.startswith('10.'):
            return {'valid': False, 'error': 'Localhost and private IP addresses are not allowed for security assessments.'}
        
        return {'valid': True, 'parsed_url': parsed}
    
    except Exception as e:
        return {'valid': False, 'error': f'URL validation failed: {str(e)}'}

def validate_authorization(auth_token: str) -> Dict[str, Any]:
    """Validate authorization token"""
    if not auth_token or len(auth_token) < 10:
        return {'valid': False, 'error': 'Valid authorization token is required'}
    
    # In a real implementation, you would validate the token against your auth system
    # For now, we'll just check if it exists and has minimum length
    return {'valid': True}

async def check_security_headers(url: str, session: aiohttp.ClientSession) -> List[Dict[str, Any]]:
    """Check security headers configuration"""
    issues = []
    
    try:
        async with session.get(url, timeout=aiohttp.ClientTimeout(total=ASSESSMENT_CONFIG['timeout'])) as response:
            headers = response.headers
            
            # Check for missing security headers
            for header in SECURITY_HEADERS:
                if header not in headers:
                    issues.append({
                        'title': f'Missing Security Header: {header}',
                        'description': f'The {header} security header is not configured.',
                        'severity': 'medium',
                        'recommendation': f'Configure the {header} header to improve security.',
                        'type': 'missing_header'
                    })
            
            # Check specific header values
            if 'X-Frame-Options' in headers:
                xfo_value = headers['X-Frame-Options'].lower()
                if xfo_value not in ['deny', 'sameorigin']:
                    issues.append({
                        'title': 'Weak X-Frame-Options Configuration',
                        'description': f'X-Frame-Options is set to "{xfo_value}" which may allow clickjacking attacks.',
                        'severity': 'medium',
                        'recommendation': 'Set X-Frame-Options to "DENY" or "SAMEORIGIN".',
                        'type': 'weak_header'
                    })
            
            if 'Strict-Transport-Security' in headers:
                hsts_value = headers['Strict-Transport-Security']
                if 'max-age=' not in hsts_value:
                    issues.append({
                        'title': 'Incomplete HSTS Configuration',
                        'description': 'Strict-Transport-Security header is missing max-age directive.',
                        'severity': 'medium',
                        'recommendation': 'Include max-age directive in HSTS header (e.g., "max-age=31536000").',
                        'type': 'weak_header'
                    })
    
    except Exception as e:
        issues.append({
            'title': 'Failed to Check Security Headers',
            'description': f'Unable to retrieve security headers: {str(e)}',
            'severity': 'low',
            'recommendation': 'Verify the target URL is accessible and try again.',
            'type': 'connection_error'
        })
    
    return issues

async def check_ssl_tls_configuration(url: str) -> List[Dict[str, Any]]:
    """Check SSL/TLS configuration"""
    issues = []
    
    try:
        parsed = urlparse(url)
        if parsed.scheme != 'https':
            issues.append({
                'title': 'No SSL/TLS Encryption',
                'description': 'The target URL does not use HTTPS encryption.',
                'severity': 'high',
                'recommendation': 'Enable HTTPS with a valid SSL certificate.',
                'type': 'no_ssl'
            })
            return issues
        
        # Check SSL certificate
        hostname = parsed.netloc
        port = parsed.port or 443
        
        context = ssl.create_default_context()
        with socket.create_connection((hostname, port), timeout=ASSESSMENT_CONFIG['timeout']) as sock:
            with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                cert = ssock.getpeercert()
                
                # Check certificate expiration
                not_after = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                if not_after < datetime.now():
                    issues.append({
                        'title': 'Expired SSL Certificate',
                        'description': f'SSL certificate expired on {not_after.strftime("%Y-%m-%d")}.',
                        'severity': 'high',
                        'recommendation': 'Renew the SSL certificate immediately.',
                        'type': 'expired_cert'
                    })
                
                # Check certificate validity period
                not_before = datetime.strptime(cert['notBefore'], '%b %d %H:%M:%S %Y %Z')
                if not_after - not_before > timedelta(days=365):
                    issues.append({
                        'title': 'Long Certificate Validity Period',
                        'description': 'SSL certificate has a validity period longer than 1 year.',
                        'severity': 'low',
                        'recommendation': 'Consider using shorter certificate validity periods for better security.',
                        'type': 'long_validity'
                    })
                
                # Check TLS version
                version = ssock.version()
                if version < 'TLSv1.2':
                    issues.append({
                        'title': 'Weak TLS Version',
                        'description': f'Server is using {version}, which is considered insecure.',
                        'severity': 'high',
                        'recommendation': 'Upgrade to TLS 1.2 or higher.',
                        'type': 'weak_tls'
                    })
    
    except Exception as e:
        issues.append({
            'title': 'SSL/TLS Configuration Check Failed',
            'description': f'Unable to check SSL/TLS configuration: {str(e)}',
            'severity': 'medium',
            'recommendation': 'Verify the SSL certificate is properly configured.',
            'type': 'ssl_error'
        })
    
    return issues

async def check_vulnerabilities(url: str, session: aiohttp.ClientSession) -> List[Dict[str, Any]]:
    """Check for common vulnerabilities"""
    issues = []
    
    # Test parameters for common vulnerabilities
    test_params = {
        'id': "1' OR '1'='1",
        'search': '<script>alert(1)</script>',
        'file': '../../../etc/passwd',
        'cmd': 'ls; cat /etc/passwd'
    }
    
    for param_name, test_value in test_params.items():
        try:
            test_url = f"{url}?{param_name}={test_value}"
            async with session.get(test_url, timeout=aiohttp.ClientTimeout(total=ASSESSMENT_CONFIG['timeout'])) as response:
                content = await response.text()
                
                # Check for error messages that might indicate vulnerabilities
                error_patterns = [
                    r'sql.*error|mysql.*error|oracle.*error|sqlserver.*error',
                    r'stack.*trace|exception.*details',
                    r'warning.*mysql|warning.*sql',
                    r'error.*syntax|syntax.*error'
                ]
                
                for pattern in error_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        issues.append({
                            'title': f'Potential {param_name.upper()} Parameter Vulnerability',
                            'description': f'Error messages in response suggest potential vulnerability in {param_name} parameter.',
                            'severity': 'medium',
                            'recommendation': f'Implement proper input validation and error handling for {param_name} parameter.',
                            'type': 'parameter_vulnerability'
                        })
                        break
        
        except Exception as e:
            # Continue with other tests even if one fails
            continue
    
    return issues

async def check_api_security(url: str, session: aiohttp.ClientSession) -> List[Dict[str, Any]]:
    """Check API security endpoints"""
    issues = []
    
    # Common API endpoints to check
    api_endpoints = [
        '/api/users',
        '/api/admin',
        '/api/config',
        '/api/health',
        '/api/status',
        '/api/debug',
        '/api/test'
    ]
    
    for endpoint in api_endpoints:
        try:
            test_url = f"{url.rstrip('/')}{endpoint}"
            async with session.get(test_url, timeout=aiohttp.ClientTimeout(total=ASSESSMENT_CONFIG['timeout'])) as response:
                if response.status == 200:
                    issues.append({
                        'title': f'Potentially Exposed API Endpoint: {endpoint}',
                        'description': f'The API endpoint {endpoint} is accessible without authentication.',
                        'severity': 'high',
                        'recommendation': f'Secure the {endpoint} endpoint with proper authentication and authorization.',
                        'type': 'exposed_api'
                    })
        
        except Exception:
            # Continue with other endpoints
            continue
    
    return issues

async def perform_security_assessment(target_url: str, scan_type: str, auth_token: str) -> Dict[str, Any]:
    """Perform security assessment based on scan type"""
    start_time = datetime.now()
    issues = []
    
    # Create session with custom headers
    headers = {
        'User-Agent': ASSESSMENT_CONFIG['user_agent'],
        'Authorization': f'Bearer {auth_token}'
    }
    
    timeout = aiohttp.ClientTimeout(total=ASSESSMENT_CONFIG['timeout'])
    connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
    
    async with aiohttp.ClientSession(headers=headers, timeout=timeout, connector=connector) as session:
        try:
            if scan_type in ['vulnerability', 'all']:
                vuln_issues = await check_vulnerabilities(target_url, session)
                issues.extend(vuln_issues)
            
            if scan_type in ['headers', 'all']:
                header_issues = await check_security_headers(target_url, session)
                issues.extend(header_issues)
            
            if scan_type in ['ssl', 'all']:
                ssl_issues = await check_ssl_tls_configuration(target_url)
                issues.extend(ssl_issues)
            
            if scan_type in ['api', 'all']:
                api_issues = await check_api_security(target_url, session)
                issues.extend(api_issues)
        
        except Exception as e:
            issues.append({
                'title': 'Assessment Failed',
                'description': f'Security assessment failed: {str(e)}',
                'severity': 'low',
                'recommendation': 'Check target URL accessibility and try again.',
                'type': 'assessment_error'
            })
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    return {
        'targetUrl': target_url,
        'scanType': scan_type,
        'timestamp': start_time.isoformat(),
        'duration': f"{duration:.1f} seconds",
        'issues': issues,
        'summary': {
            'total_issues': len(issues),
            'critical': len([i for i in issues if i['severity'] == 'critical']),
            'high': len([i for i in issues if i['severity'] == 'high']),
            'medium': len([i for i in issues if i['severity'] == 'medium']),
            'low': len([i for i in issues if i['severity'] == 'low'])
        }
    }

# Azure Functions endpoints
bp = func.Blueprint()

@bp.route(route="security-assessment/initiate", methods=["POST"])
def initiate_security_assessment(req: func.HttpRequest) -> func.HttpResponse:
    """Initiate a security assessment"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )
        
        target_url = req_body.get('targetUrl')
        auth_token = req_body.get('authorizationToken')
        scan_type = req_body.get('scanType', 'vulnerability')
        rate_limit = req_body.get('rateLimit', 1000)
        
        # Validate inputs
        url_validation = validate_target_url(target_url)
        if not url_validation['valid']:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": url_validation['error']
                }),
                mimetype="application/json",
                status_code=400
            )
        
        auth_validation = validate_authorization(auth_token)
        if not auth_validation['valid']:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": auth_validation['error']
                }),
                mimetype="application/json",
                status_code=400
            )
        
        # Generate assessment ID
        assessment_id = f"assessment_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(target_url) % 10000}"
        
        # Store assessment request (in production, use Azure Table Storage or Database)
        # For now, we'll return the assessment ID and perform the assessment immediately
        
        # Perform assessment asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            results = loop.run_until_complete(perform_security_assessment(target_url, scan_type, auth_token))
            
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "assessmentId": assessment_id,
                    "status": "completed",
                    "results": results
                }),
                mimetype="application/json",
                status_code=200
            )
        
        finally:
            loop.close()
    
    except Exception as e:
        logger.error(f"Error initiating security assessment: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to initiate assessment: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="security-assessment/{assessment_id}/status", methods=["GET"])
def get_assessment_status(req: func.HttpRequest) -> func.HttpResponse:
    """Get assessment status and results"""
    try:
        assessment_id = req.route_params.get('assessment_id')
        
        # In a real implementation, you would retrieve the assessment status from storage
        # For now, we'll return a mock response
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "assessmentId": assessment_id,
                "status": "completed",
                "message": "Assessment completed successfully"
            }),
            mimetype="application/json",
            status_code=200
        )
    
    except Exception as e:
        logger.error(f"Error getting assessment status: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Failed to get assessment status: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        ) 