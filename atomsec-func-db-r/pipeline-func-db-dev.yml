# Azure DevOps Pipeline for AtomSec DB Function App (dev) - OPTIMIZED
# Reference: pipeline-func-sfdc-dev.yml structure

trigger:
- dev
- dev-db

pool:
  vmImage: ubuntu-latest

variables:
- group: vg-atomsec-db-dev  # Variable group for dev environment
- name: PIP_CACHE_DIR
  value: '$(Pipeline.Workspace)/.pip-cache'

stages:
- stage: Build
  displayName: 'Build and Package'
  jobs:
  - job: Build
    displayName: 'Build Function App'
    steps:
    - task: UsePythonVersion@0
      inputs:
        versionSpec: '3.12'
      displayName: 'Set up Python 3.12'

    # Cache pip dependencies
    - task: Cache@2
      inputs:
        key: 'pip | "$(Agent.OS)" | requirements.txt'
        restoreKeys: |
          pip | "$(Agent.OS)"
        path: $(PIP_CACHE_DIR)
      displayName: 'Cache pip dependencies'

    # Parallel setup of Azure CLI and Functions Tools
    - task: AzureCLI@2
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo 'Azure CLI version:'
          az --version
          echo 'Installing Azure Functions Core Tools...'
          npm install -g azure-functions-core-tools@4 --unsafe-perm true
      displayName: 'Setup Azure CLI and Functions Tools'

    # Optimized dependency installation with caching
    - script: |
        python --version
        python -m pip install --upgrade pip setuptools wheel

        # Set pip cache directory
        export PIP_CACHE_DIR=$(PIP_CACHE_DIR)
        
        echo 'Installing system dependencies for Python packages...'
        sudo apt-get update
        sudo apt-get install -y build-essential python3-dev pkg-config

        echo 'Installing ODBC driver for SQL Server on the build agent...'
        sudo apt-get install -y curl gnupg2
        
        # Remove any existing Microsoft repository entries to avoid conflicts
        sudo rm -f /etc/apt/sources.list.d/mssql-release.list
        sudo rm -f /etc/apt/sources.list.d/microsoft-prod.list
        
        # Add Microsoft repository properly
        curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | sudo gpg --dearmor -o /usr/share/keyrings/microsoft-archive-keyring.gpg
        echo "deb [arch=amd64,arm64,armhf signed-by=/usr/share/keyrings/microsoft-archive-keyring.gpg] https://packages.microsoft.com/ubuntu/$(lsb_release -rs)/prod $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/microsoft-prod.list
        
        sudo apt-get update
        sudo ACCEPT_EULA=Y apt-get install -y msodbcsql18
        sudo ACCEPT_EULA=Y apt-get install -y unixodbc-dev

        echo 'ODBC driver installation complete.'

        echo 'Installing Python dependencies with caching...'
        
        # Install dependencies with pip cache
        pip install --cache-dir $(PIP_CACHE_DIR) --upgrade setuptools>=68.0.0 wheel>=0.40.0 build>=1.0.0
        
        # Install all dependencies at once (more efficient)
        pip install --cache-dir $(PIP_CACHE_DIR) -r requirements.txt -t .
      displayName: 'Install ODBC driver and Python dependencies (Cached)'

    # Critical verification step
    - script: |
        echo 'Verifying package structure...'
        ls -la
        
        # Critical check: Ensure function_app.py exists and imports correctly
        if [ ! -f "function_app.py" ]; then
          echo "❌ ERROR: function_app.py not found!"
          exit 1
        fi
        
        # Test import - this must succeed for deployment to work
        python -c "import function_app; print('✅ function_app.py imports successfully')"
        if [ $? -ne 0 ]; then
          echo "❌ ERROR: function_app.py import failed - deployment will fail!"
          exit 1
        fi
        
        echo "✅ Package verification completed successfully"
      displayName: 'Critical Package Verification'

    # Archive with optimized settings
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
        replaceExistingArchive: true
        verbose: false
      displayName: 'Archive DB Function App'

    # Publish artifacts
    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).zip'
        ArtifactName: 'drop'
        publishLocation: 'Container'
      displayName: 'Publish DB Service Artifacts'

- stage: Deploy
  displayName: 'Deploy and Configure'
  dependsOn: Build
  jobs:
  - job: Deploy
    displayName: 'Deploy to Azure'
    steps:
    # Download artifacts
    - download: current
      artifact: drop

    # Deploy function app
    - task: AzureFunctionApp@2
      inputs:
        connectedServiceNameARM: '$(AZURE_SUBSCRIPTION)'
        appType: 'functionAppLinux'
        appName: '$(FUNCTION_APP_NAME)'
        package: '$(Pipeline.Workspace)/drop/build-$(Build.BuildNumber).zip'
        runtimeStack: 'PYTHON|3.12'
        deploymentMethod: 'zipDeploy'
        resourceGroupName: '$(RESOURCE_GROUP)'
      displayName: 'Deploy DB Function App to $(ENVIRONMENT)'

    # Configure function app settings
    - task: AzureCLI@2
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo '🔐 Configuring Function App with Key Vault References...'
          
          # Set core Azure Functions settings
          echo "Setting core Azure Functions settings..."
          if ! az functionapp config appsettings set \
            --name $(FUNCTION_APP_NAME) \
            --resource-group $(RESOURCE_GROUP) \
            --settings \
              FUNCTIONS_WORKER_RUNTIME=python \
              FUNCTIONS_EXTENSION_VERSION=~4 \
              WEBSITE_RUN_FROM_PACKAGE=1 \
              SCM_DO_BUILD_DURING_DEPLOYMENT=false \
              AzureWebJobsFeatureFlags=EnableWorkerIndexing \
              WEBSITE_ENABLE_SYNC_UPDATE_SITE=true \
              WEBSITE_WEBSOCKETS_ENABLED=false \
              WEBSITE_HTTPLOGGING_RETENTION_DAYS=7 \
              WEBSITE_ENABLE_APP_SERVICE_STORAGE=false; then
            echo "❌ ERROR: Failed to set core Azure Functions settings"
            exit 1
          fi
          echo "✅ Core settings configured successfully"
          
          # Set application-specific settings with Key Vault references
          echo "Setting application-specific settings with Key Vault references..."
          if ! az functionapp config appsettings set \
            --name $(FUNCTION_APP_NAME) \
            --resource-group $(RESOURCE_GROUP) \
            --settings \
              AZURE_AD_CLIENT_ID="@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/azure-ad-client-id/)" \
              AZURE_AD_TENANT_ID="@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/azure-ad-tenant-id/)" \
              AZURE_AD_CLIENT_SECRET="@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/azure-ad-client-secret/)" \
              AZURE_AD_ADDITIONAL_CLIENT_IDS="82e79715-7451-4680-bd1c-53453bfd45ea" \
              JWT_SECRET="@Microsoft.KeyVault(SecretUri=https://$(KEY_VAULT_NAME).vault.azure.net/secrets/jwt-secret/)" \
              ENVIRONMENT="$(ENVIRONMENT)" \
              IS_LOCAL_DEV="$(IS_LOCAL_DEV)" \
              AZURE_STORAGE_CONNECTION_STRING="$(AZURE_STORAGE_CONNECTION_STRING)" \
              AZURE_TABLE_STORAGE_CONNECTION_STRING="$(AZURE_TABLE_STORAGE_CONNECTION_STRING)" \
              AZURE_SERVICE_BUS_CONNECTION_STRING="$(AZURE_SERVICE_BUS_CONNECTION_STRING)" \
              KEY_VAULT_URL="$(KEY_VAULT_URL)" \
              KEY_VAULT_NAME="$(KEY_VAULT_NAME)" \
              LOG_LEVEL="$(LOG_LEVEL)"; then
            echo "❌ ERROR: Failed to set application settings"
            exit 1
          fi
          echo "✅ Application settings configured successfully"
          
          # Enable Always On for better performance
          echo "Enabling Always On..."
          if ! az functionapp config set \
            --name $(FUNCTION_APP_NAME) \
            --resource-group $(RESOURCE_GROUP) \
            --always-on true; then
            echo "❌ ERROR: Failed to enable Always On"
            exit 1
          fi
          echo "✅ Always On enabled successfully"
          
          echo '✅ Function app configuration completed successfully'
      displayName: 'Configure Function App Settings'

    # Critical deployment verification
    - task: AzureCLI@2
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo 'Verifying $(ENVIRONMENT) deployment...'
          
          # Wait for deployment to be ready
          echo "Waiting for deployment to stabilize..."
          sleep 45
          
          # Check function app status first
          echo "Checking function app state..."
          APP_STATE=$(az functionapp show --name $(FUNCTION_APP_NAME) --resource-group $(RESOURCE_GROUP) --query "state" -o tsv)
          if [ "$APP_STATE" != "Running" ]; then
            echo "❌ ERROR: Function app is not running. Current state: $APP_STATE"
            exit 1
          fi
          echo "✅ Function app is running"
          
          # Critical health check
          HEALTH_URL="$(FUNCTION_APP_URL)/api/db/health"
          echo "Testing health endpoint: $HEALTH_URL"
          
          # Retry health check up to 3 times
          for i in {1..3}; do
            echo "Health check attempt $i/3..."
            HEALTH_RESPONSE=$(curl -s -w "%{http_code}" "$HEALTH_URL" -o /tmp/health_response 2>/dev/null || echo "000")
            
            if [ "$HEALTH_RESPONSE" = "200" ]; then
              echo "✅ Health endpoint is working (HTTP 200)"
              break
            elif [ $i -eq 3 ]; then
              echo "❌ ERROR: Health endpoint failed after 3 attempts (HTTP $HEALTH_RESPONSE)"
              echo "Response body:"
              cat /tmp/health_response 2>/dev/null || echo "No response body"
              exit 1
            else
              echo "⚠️  Health check attempt $i failed (HTTP $HEALTH_RESPONSE), retrying in 10s..."
              sleep 10
            fi
          done
          
          echo '✅ $(ENVIRONMENT) deployment verification completed successfully'
      displayName: 'Critical Deployment Verification'

    # Critical configuration verification
    - task: AzureCLI@2
      inputs:
        azureSubscription: '$(AZURE_SUBSCRIPTION)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo '🔍 Critical Configuration Verification...'
          
          # Get current application settings
          CURRENT_SETTINGS=$(az functionapp config appsettings list --name $(FUNCTION_APP_NAME) --resource-group $(RESOURCE_GROUP) --output json)
          
          # Critical verification of required settings
          echo 'Verifying critical configuration settings...'
          
          MISSING_CRITICAL=0
          
          # Function to check if a critical setting exists
          check_critical_setting() {
            local setting_name=$1
            local actual_value=$(echo "$CURRENT_SETTINGS" | jq -r ".[] | select(.name == \"$setting_name\") | .value // \"NOT_SET\"")
            
            if [ "$actual_value" != "NOT_SET" ] && [ -n "$actual_value" ]; then
              echo "✅ $setting_name - SET"
              return 0
            else
              echo "❌ CRITICAL ERROR: $setting_name - NOT SET"
              return 1
            fi
          }
          
          # Check critical settings that must exist for the app to function
          check_critical_setting "FUNCTIONS_WORKER_RUNTIME" || MISSING_CRITICAL=$((MISSING_CRITICAL + 1))
          check_critical_setting "AZURE_AD_CLIENT_ID" || MISSING_CRITICAL=$((MISSING_CRITICAL + 1))
          check_critical_setting "ENVIRONMENT" || MISSING_CRITICAL=$((MISSING_CRITICAL + 1))
          check_critical_setting "JWT_SECRET" || MISSING_CRITICAL=$((MISSING_CRITICAL + 1))
          
          # Check optional but important settings
          echo ''
          echo '📋 Additional Authentication Settings:'
          check_critical_setting "AZURE_AD_ADDITIONAL_CLIENT_IDS" || echo "ℹ️  Note: AZURE_AD_ADDITIONAL_CLIENT_IDS not set (this is optional)"
          
          # Fail if any critical settings are missing
          if [ $MISSING_CRITICAL -gt 0 ]; then
            echo "❌ ERROR: $MISSING_CRITICAL critical settings are missing!"
            echo "Deployment cannot be considered successful with missing critical configuration."
            exit 1
          fi
          
          echo ''
          echo '✅ All critical configuration settings verified successfully'
          echo ''
          echo '🔗 Function App URLs:'
          echo "   Health: $(FUNCTION_APP_URL)/api/db/health"
          echo "   Info: $(FUNCTION_APP_URL)/api/db/info"
          
          echo ''
          echo '🌐 Azure Portal: https://portal.azure.com/#@atomsec.ai/resource/subscriptions/$(SUBSCRIPTION_ID)/resourceGroups/$(RESOURCE_GROUP)/providers/Microsoft.Web/sites/$(FUNCTION_APP_NAME)'
          echo ''
          echo '🎉 Deployment completed successfully with all verifications passed!'
      displayName: 'Critical Configuration Verification' 