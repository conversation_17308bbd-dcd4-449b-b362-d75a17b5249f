"""
User Repository Module

This module provides repository classes for user-related data access operations.
Refactored for the dedicated database function app.
"""

import logging
import secrets
import hashlib
from datetime import datetime, date
from typing import Dict, List, Any, Optional, Tuple, Union

from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.database_models import User<PERSON><PERSON><PERSON>, UserLogin, Role, UserRole
from shared.database_models_new import User as User<PERSON><PERSON>, Account, Role as <PERSON>New, UserRole as UserRoleNew

logger = logging.getLogger(__name__)


def generate_salt() -> str:
    """Generate a random salt for password hashing"""
    return secrets.token_hex(16)


def hash_password(password: str, salt: str, algorithm_id: int = 1) -> str:
    """
    Hash a password using the specified algorithm

    Args:
        password: Password to hash
        salt: Salt to use for hashing
        algorithm_id: Hashing algorithm ID (1=SHA256, 2=PBKDF2, 3=Bcrypt)

    Returns:
        str: Hashed password
    """
    if algorithm_id == 1:
        # SHA256 with salt
        return hashlib.sha256((password + salt).encode()).hexdigest()
    elif algorithm_id == 2:
        # PBKDF2 (more secure)
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.backends import default_backend
        import base64

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt.encode(),
            iterations=100000,
            backend=default_backend()
        )
        return base64.b64encode(kdf.derive(password.encode())).decode()
    elif algorithm_id == 3:
        # Bcrypt (most secure)
        import bcrypt
        return bcrypt.hashpw(password.encode(), salt.encode()).decode()
    else:
        # Default to SHA256
        return hashlib.sha256((password + salt).encode()).hexdigest()


def verify_password(password: str, hashed_password: str, salt: str, algorithm_id: int = 1) -> bool:
    """
    Verify a password against a hashed password

    Args:
        password: Password to verify
        hashed_password: Hashed password to compare against
        salt: Salt used for hashing
        algorithm_id: Hashing algorithm ID

    Returns:
        bool: True if password matches, False otherwise
    """
    return hash_password(password, salt, algorithm_id) == hashed_password


class UserRepository:
    """Repository for user-related database operations"""

    def __init__(self):
        """Initialize the user repository"""
        self.is_local = is_local_dev()
        self._table_repo = None
        self._sql_repo = None
        self._user_login_table_repo = None
        self._user_login_sql_repo = None
        self._role_table_repo = None
        self._role_sql_repo = None

    def _get_table_repo(self) -> Optional[TableStorageRepository]:
        """Get or create table storage repository"""
        if self._table_repo is None:
            try:
                self._table_repo = TableStorageRepository(table_name="UserAccount")
                logger.info("Initialized user account table repository")
            except Exception as e:
                logger.error(f"Failed to initialize user account table repository: {str(e)}")
        return self._table_repo

    def _get_sql_repo(self) -> Optional[SqlDatabaseRepository]:
        """Get or create SQL database repository"""
        if self._sql_repo is None and not self.is_local:
            try:
                self._sql_repo = SqlDatabaseRepository(table_name="App_User")
                logger.info("Initialized user account SQL repository")
            except Exception as e:
                logger.error(f"Failed to initialize user account SQL repository: {str(e)}")
        return self._sql_repo

    def _get_user_login_table_repo(self) -> Optional[TableStorageRepository]:
        """Get or create user login table repository"""
        if self._user_login_table_repo is None:
            try:
                self._user_login_table_repo = TableStorageRepository(table_name="UserLogin")
                logger.info("Initialized user login table repository")
            except Exception as e:
                logger.error(f"Failed to initialize user login table repository: {str(e)}")
        return self._user_login_table_repo

    def _get_user_login_sql_repo(self) -> Optional[SqlDatabaseRepository]:
        """Get or create user login SQL repository"""
        if self._user_login_sql_repo is None and not self.is_local:
            try:
                self._user_login_sql_repo = SqlDatabaseRepository(table_name="App_User_Login")
                logger.info("Initialized user login SQL repository")
            except Exception as e:
                logger.error(f"Failed to initialize user login SQL repository: {str(e)}")
        return self._user_login_sql_repo

    def create_user(self, user_data: Dict[str, Any]) -> Optional[int]:
        """
        Create a new user

        Args:
            user_data: Dictionary containing user information

        Returns:
            User ID if successful, None otherwise
        """
        try:
            if self.is_local:
                # Use Table Storage for local development
                repo = self._get_table_repo()
                if not repo:
                    return None

                # Check if user already exists
                email = user_data.get('email', '').lower()
                entities = repo.query_entities(f"PartitionKey eq 'user_account' and RowKey eq '{email}'")
                if entities:
                    logger.warning(f"User already exists: {email}")
                    return None

                # Generate user ID
                import random
                user_id = random.randint(1000, 9999)

                # Create entity
                entity = {
                    'PartitionKey': 'user_account',
                    'RowKey': email,
                    'UserId': user_id,
                    'Name': user_data.get('name', ''),
                    'Email': user_data.get('email', ''),
                    'Phone': user_data.get('phone', ''),
                    'AccountId': user_data.get('account_id', 0),
                    'CreatedAt': datetime.now().isoformat(),
                    'IsActive': True
                }

                if repo.insert_entity(entity):
                    logger.info(f"Created user: {email} with ID {user_id}")
                    return user_id

            else:
                # Use SQL Database for production
                repo = self._get_sql_repo()
                if not repo:
                    return None

                # Check if user already exists
                email = user_data.get('email', '').lower()
                query = "SELECT UserId FROM App_User WHERE LOWER(Email) = ?"
                results = repo.execute_query(query, (email,))

                if results:
                    logger.warning(f"User already exists: {email}")
                    return results[0][0]

                # Insert user
                query = """
                INSERT INTO App_User (Name, Email, Phone, AccountId, CreatedAt, IsActive)
                VALUES (?, ?, ?, ?, ?, ?);
                SELECT SCOPE_IDENTITY();
                """
                params = (
                    user_data.get('name', ''),
                    user_data.get('email', ''),
                    user_data.get('phone'),
                    user_data.get('account_id', 0),
                    datetime.now(),
                    True
                )

                results = repo.execute_query(query, params)
                if results:
                    user_id = int(results[0][0])
                    logger.info(f"Created user: {email} with ID {user_id}")
                    return user_id

        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")

        return None

    def create_user_with_password(
        self,
        email: str,
        password: str,
        first_name: str = "",
        middle_name: str = "",
        last_name: str = "",
        dob: str = "",
        contact: str = "",
        state: str = "",
        country: str = "",
        organization: str = "",
        algorithm_id: int = 1
    ) -> Optional[int]:
        """
        Create a new user with password (account and login)

        Args:
            email: User email
            password: Plain text password
            first_name: User first name
            middle_name: User middle name
            last_name: User last name
            dob: Date of birth
            contact: Contact information
            state: State
            country: Country
            organization: User organization
            algorithm_id: Hashing algorithm ID

        Returns:
            int: User ID if successful, None otherwise
        """
        try:
            # Create user account first
            full_name = f"{first_name} {middle_name} {last_name}".replace("  ", " ").strip()

            if self.is_local:
                # Use Table Storage for local development
                repo = self._get_table_repo()
                if not repo:
                    return None

                # Check if user already exists
                normalized_email = email.lower()
                entities = repo.query_entities(f"PartitionKey eq 'user_account' and RowKey eq '{normalized_email}'")
                if entities:
                    logger.warning(f"User already exists: {email}")
                    return None

                # Generate user ID
                import random
                user_id = random.randint(1000, 9999)

                # Create user account entity
                entity = {
                    'PartitionKey': 'user_account',
                    'RowKey': normalized_email,
                    'UserId': user_id,
                    'FirstName': first_name,
                    'MiddleName': middle_name,
                    'LastName': last_name,
                    'DoB': dob,
                    'Email': email,
                    'Contact': contact,
                    'State': state,
                    'Country': country,
                    'Organization': organization,
                    'CreatedAt': datetime.now().isoformat(),
                    'IsActive': True
                }

                if not repo.insert_entity(entity):
                    logger.error(f"Failed to create user account: {email}")
                    return None

                # Create user login
                salt = generate_salt()
                password_hash = hash_password(password, salt, algorithm_id)

                if not self.create_user_login(user_id, email, password_hash, salt):
                    logger.error(f"Failed to create user login: {email}")
                    # TODO: Consider rolling back user account creation
                    return None

                logger.info(f"Created user with password: {email} with ID {user_id}")
                return user_id

            else:
                # Use SQL Database for production
                repo = self._get_sql_repo()
                if not repo:
                    return None

                # Check if user already exists
                normalized_email = email.lower()
                query = "SELECT UserId FROM App_User WHERE LOWER(Email) = ?"
                results = repo.execute_query(query, (normalized_email,))

                if results:
                    logger.warning(f"User already exists: {email}")
                    return results[0][0]

                # Insert user account
                query = """
                INSERT INTO App_User (Name, Email, Phone, AccountId, CreatedAt, IsActive)
                VALUES (?, ?, ?, ?, ?, ?);
                SELECT SCOPE_IDENTITY();
                """
                params = (
                    full_name,
                    email,
                    contact,
                    0,  # Default account ID
                    datetime.now(),
                    True
                )

                results = repo.execute_query(query, params)
                if not results:
                    logger.error(f"Failed to create user account: {email}")
                    return None

                user_id = int(results[0][0])

                # Create user login
                salt = generate_salt()
                password_hash = hash_password(password, salt, algorithm_id)

                if not self.create_user_login(user_id, email, password_hash, salt):
                    logger.error(f"Failed to create user login: {email}")
                    # TODO: Consider rolling back user account creation
                    return None

                logger.info(f"Created user with password: {email} with ID {user_id}")
                return user_id

        except Exception as e:
            logger.error(f"Error creating user with password: {str(e)}")

        return None

    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """
        Get user by email

        Args:
            email: User email

        Returns:
            User data dictionary or None
        """
        try:
            normalized_email = email.lower() if email else ""

            if self.is_local:
                repo = self._get_table_repo()
                if not repo:
                    return None
                # Use direct filter by PartitionKey and RowKey
                entities = repo.query_entities(f"PartitionKey eq 'user_account' and RowKey eq '{normalized_email}'")
                if entities:
                    entity = entities[0]
                    return {
                        'user_id': entity.get('UserId'),
                        'name': f"{entity.get('FirstName', '')} {entity.get('MiddleName', '')} {entity.get('LastName', '')}".strip(),
                        'first_name': entity.get('FirstName', ''),
                        'middle_name': entity.get('MiddleName', ''),
                        'last_name': entity.get('LastName', ''),
                        'dob': entity.get('DoB', ''),
                        'email': entity.get('Email', ''),
                        'phone': entity.get('Contact', ''),
                        'job_title': entity.get('JobTitle', ''),
                        'state': entity.get('State', ''),
                        'country': entity.get('Country', ''),
                        'last_login': entity.get('LastLogin', ''),
                        'organization': entity.get('Organization', ''),
                        'last_updated': entity.get('LastUpdated', ''),
                        'created_at': entity.get('CreatedAt'),
                        'is_active': entity.get('IsActive', True)
                    }
            else:
                repo = self._get_sql_repo()
                if not repo:
                    return None

                query = """
                SELECT UserId, Name, Email, Phone, AccountId, CreatedAt, IsActive
                FROM App_User WHERE LOWER(Email) = ?
                """
                results = repo.execute_query(query, (normalized_email,))
                if results:
                    row = results[0]
                    return {
                        'user_id': row[0],
                        'name': row[1],
                        'email': row[2],
                        'phone': row[3],
                        'account_id': row[4],
                        'created_at': row[5],
                        'is_active': row[6]
                    }
        except Exception as e:
            logger.error(f"Error getting user by email: {str(e)}")

        return None

    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get user by ID

        Args:
            user_id: User ID

        Returns:
            User data dictionary or None
        """
        try:
            if self.is_local:
                repo = self._get_table_repo()
                if not repo:
                    return None

                entities = repo.query_entities(f"UserId eq {user_id}")

                if entities:
                    entity = entities[0]
                    return {
                        'user_id': entity.get('UserId'),
                        'name': entity.get('Name', ''),
                        'email': entity.get('Email', ''),
                        'phone': entity.get('Phone'),
                        'account_id': entity.get('AccountId', 0),
                        'created_at': entity.get('CreatedAt'),
                        'is_active': entity.get('IsActive', True)
                    }

            else:
                repo = self._get_sql_repo()
                if not repo:
                    return None

                query = """
                SELECT UserId, Name, Email, Phone, AccountId, CreatedAt, IsActive
                FROM App_User WHERE UserId = ?
                """
                results = repo.execute_query(query, (user_id,))

                if results:
                    row = results[0]
                    return {
                        'user_id': row[0],
                        'name': row[1],
                        'email': row[2],
                        'phone': row[3],
                        'account_id': row[4],
                        'created_at': row[5].isoformat() if row[5] else None,
                        'is_active': row[6]
                    }

        except Exception as e:
            logger.error(f"Error getting user by ID: {str(e)}")

        return None

    def update_user(self, user_id: int, user_data: Dict[str, Any]) -> bool:
        """
        Update user information

        Args:
            user_id: User ID
            user_data: Dictionary containing fields to update

        Returns:
            True if successful, False otherwise
        """
        try:
            if self.is_local:
                repo = self._get_table_repo()
                if not repo:
                    return False

                # Get existing user
                entities = repo.query_entities(f"UserId eq {user_id}")
                if not entities:
                    logger.warning(f"User not found: {user_id}")
                    return False

                entity = entities[0]

                # Update fields - map to table storage field names
                field_mapping = {
                    'name': 'Name',
                    'first_name': 'FirstName',
                    'middle_name': 'MiddleName',
                    'last_name': 'LastName',
                    'phone': 'Contact',
                    'job_title': 'JobTitle',
                    'organization': 'Organization',
                    'state': 'State',
                    'country': 'Country',
                    'is_active': 'IsActive'
                }

                for key, value in user_data.items():
                    if key in field_mapping:
                        entity[field_mapping[key]] = value

                # Update timestamp
                entity['LastUpdated'] = datetime.now().isoformat()

                return repo.update_entity(entity)

            else:
                repo = self._get_sql_repo()
                if not repo:
                    return False

                # Build update query - map to SQL column names
                field_mapping = {
                    'name': 'Name',
                    'first_name': 'FirstName',
                    'middle_name': 'MiddleName',
                    'last_name': 'LastName',
                    'phone': 'Phone',
                    'job_title': 'JobTitle',
                    'organization': 'Organization',
                    'state': 'State',
                    'country': 'Country',
                    'is_active': 'IsActive'
                }

                update_fields = []
                params = []

                for key, value in user_data.items():
                    if key in field_mapping:
                        update_fields.append(f"{field_mapping[key]} = ?")
                        params.append(value)

                if not update_fields:
                    return True  # Nothing to update

                # Add LastUpdated timestamp
                update_fields.append("LastUpdated = ?")
                params.append(datetime.now())

                params.append(user_id)
                query = f"UPDATE App_User SET {', '.join(update_fields)} WHERE UserId = ?"

                return repo.execute_non_query(query, tuple(params))

        except Exception as e:
            logger.error(f"Error updating user: {str(e)}")

        return False

    def delete_user(self, user_id: int) -> bool:
        """
        Delete a user (soft delete by setting IsActive = False)

        Args:
            user_id: User ID

        Returns:
            True if successful, False otherwise
        """
        return self.update_user(user_id, {'is_active': False})

    def list_users(self, account_id: Optional[int] = None, is_active: Optional[bool] = None) -> List[Dict[str, Any]]:
        """
        List users with optional filtering

        Args:
            account_id: Filter by account ID
            is_active: Filter by active status

        Returns:
            List of user dictionaries
        """
        try:
            if self.is_local:
                repo = self._get_table_repo()
                if not repo:
                    return []

                filter_query = "PartitionKey eq 'user_account'"
                entities = repo.query_entities(filter_query)

                users = []
                for entity in entities:
                    # Apply filters
                    if account_id is not None and entity.get('AccountId') != account_id:
                        continue
                    if is_active is not None and entity.get('IsActive', True) != is_active:
                        continue

                    users.append({
                        'user_id': entity.get('UserId'),
                        'name': entity.get('Name', ''),
                        'email': entity.get('Email', ''),
                        'phone': entity.get('Phone'),
                        'account_id': entity.get('AccountId', 0),
                        'created_at': entity.get('CreatedAt'),
                        'is_active': entity.get('IsActive', True)
                    })

                return users

            else:
                repo = self._get_sql_repo()
                if not repo:
                    return []

                # Build query with filters
                query = "SELECT UserId, Name, Email, Phone, AccountId, CreatedAt, IsActive FROM App_User WHERE 1=1"
                params = []

                if account_id is not None:
                    query += " AND AccountId = ?"
                    params.append(account_id)

                if is_active is not None:
                    query += " AND IsActive = ?"
                    params.append(is_active)

                results = repo.execute_query(query, tuple(params))

                users = []
                for row in results:
                    users.append({
                        'user_id': row[0],
                        'name': row[1],
                        'email': row[2],
                        'phone': row[3],
                        'account_id': row[4],
                        'created_at': row[5].isoformat() if row[5] else None,
                        'is_active': row[6]
                    })

                return users

        except Exception as e:
            logger.error(f"Error listing users: {str(e)}")

        return []

    def create_user_login(self, user_id: int, username: str, password_hash: str, salt: str) -> bool:
        """
        Create user login credentials

        Args:
            user_id: User ID
            username: Username (usually email)
            password_hash: Hashed password
            salt: Salt used for hashing

        Returns:
            True if successful, False otherwise
        """
        try:
            if self.is_local:
                repo = self._get_user_login_table_repo()
                if not repo:
                    return False

                entity = {
                    'PartitionKey': 'user_login',
                    'RowKey': username.lower(),
                    'UserId': user_id,
                    'Username': username,
                    'PasswordHash': password_hash,
                    'PasswordSalt': salt,  # Use PasswordSalt for consistency with existing records
                    'HashAlgorithmId': 1,  # SHA256 by default
                    'CreatedAt': datetime.now().isoformat(),
                    'LastLogin': None,
                    'IsActive': True
                }

                return repo.insert_entity(entity)

            else:
                repo = self._get_user_login_sql_repo()
                if not repo:
                    return False

                query = """
                INSERT INTO App_User_Login (UserId, Username, PasswordHash, PasswordSalt, HashAlgorithmId, CreatedAt, IsActive)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                params = (user_id, username, password_hash, salt, 1, datetime.now(), True)

                return repo.execute_non_query(query, params)

        except Exception as e:
            logger.error(f"Error creating user login: {str(e)}")

        return False

    def verify_user_login(self, username: str, password: str) -> Optional[int]:
        """
        Verify user login credentials

        Args:
            username: Username
            password: Plain text password to verify

        Returns:
            User ID if credentials are valid, None otherwise
        """
        try:
            if self.is_local:
                repo = self._get_user_login_table_repo()
                if not repo:
                    return None

                entities = repo.query_entities(f"RowKey eq '{username.lower()}'")

                if entities:
                    entity = entities[0]
                    if entity.get('IsActive', True):
                        stored_hash = entity.get('PasswordHash')
                        # Handle both field names for backward compatibility
                        salt = entity.get('Salt') or entity.get('PasswordSalt')
                        algorithm_id = entity.get('HashAlgorithmId', 1)

                        # Verify password using stored salt and algorithm
                        if salt and verify_password(password, stored_hash, salt, algorithm_id):
                            # Update last login
                            entity['LastLogin'] = datetime.now().isoformat()
                            repo.update_entity(entity)
                            return entity.get('UserId')

            else:
                repo = self._get_user_login_sql_repo()
                if not repo:
                    return None

                query = """
                SELECT UserId, PasswordHash, PasswordSalt, HashAlgorithmId FROM App_User_Login
                WHERE Username = ? AND IsActive = 1
                """
                results = repo.execute_query(query, (username,))

                if results:
                    user_id, stored_hash, salt, algorithm_id = results[0]

                    # Verify password using stored salt and algorithm
                    if verify_password(password, stored_hash, salt, algorithm_id):
                        # Update last login
                        update_query = "UPDATE App_User_Login SET LastLogin = ? WHERE UserId = ?"
                        repo.execute_non_query(update_query, (datetime.now(), user_id))

                        return user_id

        except Exception as e:
            logger.error(f"Error verifying user login: {str(e)}")

        return None