"""
Base Repository for database operations

This module provides a base repository class that handles common database operations
using the DB service client for the new microservices architecture.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from shared.db_service_client import get_db_client

logger = logging.getLogger(__name__)


class BaseRepository:
    """
    Base repository class providing common database operations
    
    This class serves as a foundation for all repository classes in the new architecture,
    providing standardized database access through the DB service client.
    """
    
    def __init__(self):
        """Initialize the base repository with DB service client"""
        self.db_client = get_db_client()
        if not self.db_client:
            logger.warning("DB service client not available")
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Execute a SQL query and return results
        
        Args:
            query: SQL query string
            params: Optional parameters for the query
            
        Returns:
            List of dictionaries representing query results
        """
        try:
            if not self.db_client:
                logger.error("DB service client not available")
                return []
            
            result = self.db_client.execute_query(query, params or {})
            return result if result else []
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            return []
    
    def execute_non_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> bool:
        """
        Execute a non-query SQL statement (INSERT, UPDATE, DELETE)
        
        Args:
            query: SQL query string
            params: Optional parameters for the query
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.db_client:
                logger.error("DB service client not available")
                return False
            
            result = self.db_client.execute_non_query(query, params or {})
            return result
        except Exception as e:
            logger.error(f"Error executing non-query: {str(e)}")
            return False
    
    def execute_scalar(self, query: str, params: Optional[Dict[str, Any]] = None) -> Optional[Any]:
        """
        Execute a query and return a single scalar value
        
        Args:
            query: SQL query string
            params: Optional parameters for the query
            
        Returns:
            Single scalar value or None
        """
        try:
            if not self.db_client:
                logger.error("DB service client not available")
                return None
            
            result = self.db_client.execute_scalar(query, params or {})
            return result
        except Exception as e:
            logger.error(f"Error executing scalar query: {str(e)}")
            return None
    
    def begin_transaction(self) -> bool:
        """
        Begin a database transaction
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.db_client:
                logger.error("DB service client not available")
                return False
            
            return self.db_client.begin_transaction()
        except Exception as e:
            logger.error(f"Error beginning transaction: {str(e)}")
            return False
    
    def commit_transaction(self) -> bool:
        """
        Commit the current transaction
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.db_client:
                logger.error("DB service client not available")
                return False
            
            return self.db_client.commit_transaction()
        except Exception as e:
            logger.error(f"Error committing transaction: {str(e)}")
            return False
    
    def rollback_transaction(self) -> bool:
        """
        Rollback the current transaction
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.db_client:
                logger.error("DB service client not available")
                return False
            
            return self.db_client.rollback_transaction()
        except Exception as e:
            logger.error(f"Error rolling back transaction: {str(e)}")
            return False
    
    def test_connection(self) -> bool:
        """
        Test database connectivity
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            if not self.db_client:
                logger.error("DB service client not available")
                return False
            
            # Try a simple query to test connection
            result = self.db_client.execute_scalar("SELECT 1")
            return result is not None
        except Exception as e:
            logger.error(f"Database connection test failed: {str(e)}")
            return False
    
    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        """
        Get schema information for a table
        
        Args:
            table_name: Name of the table
            
        Returns:
            List of column information dictionaries
        """
        try:
            if not self.db_client:
                logger.error("DB service client not available")
                return []
            
            query = """
                SELECT 
                    COLUMN_NAME,
                    DATA_TYPE,
                    IS_NULLABLE,
                    COLUMN_DEFAULT,
                    CHARACTER_MAXIMUM_LENGTH
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = @table_name
                ORDER BY ORDINAL_POSITION
            """
            
            result = self.db_client.execute_query(query, {"table_name": table_name})
            return result if result else []
        except Exception as e:
            logger.error(f"Error getting table schema: {str(e)}")
            return []
    
    def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists
        
        Args:
            table_name: Name of the table to check
            
        Returns:
            True if table exists, False otherwise
        """
        try:
            if not self.db_client:
                logger.error("DB service client not available")
                return False
            
            query = """
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = @table_name
            """
            
            result = self.db_client.execute_scalar(query, {"table_name": table_name})
            return result > 0 if result is not None else False
        except Exception as e:
            logger.error(f"Error checking if table exists: {str(e)}")
            return False
    
    def get_last_insert_id(self) -> Optional[int]:
        """
        Get the last inserted ID (for auto-increment columns)
        
        Returns:
            Last inserted ID or None
        """
        try:
            if not self.db_client:
                logger.error("DB service client not available")
                return None
            
            result = self.db_client.execute_scalar("SELECT SCOPE_IDENTITY()")
            return int(result) if result is not None else None
        except Exception as e:
            logger.error(f"Error getting last insert ID: {str(e)}")
            return None
    
    def format_datetime(self, dt: Union[datetime, str]) -> str:
        """
        Format datetime for database storage
        
        Args:
            dt: Datetime object or string
            
        Returns:
            Formatted datetime string
        """
        if isinstance(dt, str):
            return dt
        elif isinstance(dt, datetime):
            return dt.isoformat()
        else:
            return datetime.now().isoformat()
    
    def sanitize_string(self, value: str) -> str:
        """
        Sanitize string values for database storage
        
        Args:
            value: String value to sanitize
            
        Returns:
            Sanitized string
        """
        if not value:
            return ""
        
        # Remove or escape potentially dangerous characters
        # This is a basic implementation - consider using parameterized queries instead
        return str(value).replace("'", "''").replace(";", "").strip()
    
    def log_operation(self, operation: str, details: Optional[Dict[str, Any]] = None):
        """
        Log database operations for debugging and monitoring
        
        Args:
            operation: Description of the operation
            details: Optional details about the operation
        """
        log_message = f"DB Operation: {operation}"
        if details:
            log_message += f" - Details: {details}"
        
        logger.info(log_message) 