"""
AtomSec Database Function App

This is a dedicated Azure Function App for handling all database operations.
It provides a clean API for CRUD operations on various entities.

Features:
- Centralized database access layer
- Repository pattern implementation
- Support for both Azure Table Storage (local dev) and SQL Database (production)
- RESTful API endpoints for all database operations
- Event publishing to Service Bus for async operations
"""

import logging
import json
import os
import azure.functions as func
from typing import Dict, Any
from datetime import datetime

# Load local settings if running locally
def load_local_settings():
    """Load local.settings.json if it exists"""
    try:
        if os.path.exists('local.settings.json'):
            with open('local.settings.json', 'r') as f:
                settings = json.load(f)
            
            # Set environment variables from local.settings.json
            if 'Values' in settings:
                for key, value in settings['Values'].items():
                    os.environ[key] = value
                print(f"Loaded {len(settings['Values'])} environment variables from local.settings.json")
            return True
    except Exception as e:
        print(f"Could not load local.settings.json: {e}")
    return False

# Load local settings at startup
load_local_settings()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import API endpoints with error handling
blueprints = []

try:
    from api.user_endpoints import bp as user_bp
    blueprints.append(('user_bp', user_bp))
except Exception as e:
    logger.error(f"Failed to import user_endpoints: {e}")

try:
    from api.account_endpoints import bp as account_bp
    blueprints.append(('account_bp', account_bp))
except Exception as e:
    logger.error(f"Failed to import account_endpoints: {e}")

try:
    from api.organization_endpoints import bp as organization_bp
    blueprints.append(('organization_bp', organization_bp))
except Exception as e:
    logger.error(f"Failed to import organization_endpoints: {e}")

try:
    from api.integration_endpoints import bp as integration_bp
    blueprints.append(('integration_bp', integration_bp))
except Exception as e:
    logger.error(f"Failed to import integration_endpoints: {e}")

try:
    from api.security_endpoints import bp as security_bp
    blueprints.append(('security_bp', security_bp))
except Exception as e:
    logger.error(f"Failed to import security_endpoints: {e}")

try:
    from api.task_endpoints import bp as task_bp
    blueprints.append(('task_bp', task_bp))
except Exception as e:
    logger.error(f"Failed to import task_endpoints: {e}")

try:
    from api.auth_endpoints import bp as auth_bp
    blueprints.append(('auth_bp', auth_bp))
except Exception as e:
    logger.error(f"Failed to import auth_endpoints: {e}")

try:
    from api.policy_endpoints import bp as policy_bp
    blueprints.append(('policy_bp', policy_bp))
except Exception as e:
    logger.error(f"Failed to import policy_endpoints: {e}")

try:
    from api.security_assessment_endpoints import bp as security_assessment_bp
    blueprints.append(('security_assessment_bp', security_assessment_bp))
except Exception as e:
    logger.error(f"Failed to import security_assessment_endpoints: {e}")

try:
    from api.table_endpoints import bp as table_bp
    blueprints.append(('table_bp', table_bp))
except Exception as e:
    logger.error(f"Failed to import table_endpoints: {e}")

try:
    from api.execution_log_endpoints import bp as execution_log_bp
    blueprints.append(('execution_log_bp', execution_log_bp))
except Exception as e:
    logger.error(f"Failed to import execution_log_endpoints: {e}")

try:
    from api.cors_handler import bp as cors_bp
    blueprints.append(('cors_bp', cors_bp))
except Exception as e:
    logger.error(f"Failed to import cors_handler: {e}")

try:
    from api.sfdc_proxy_endpoints import bp as sfdc_proxy_bp
    blueprints.append(('sfdc_proxy_bp', sfdc_proxy_bp))
except Exception as e:
    logger.error(f"Failed to import sfdc_proxy_endpoints: {e}")

try:
    from api.user_profile_endpoints import bp as user_profile_bp
    blueprints.append(('user_profile_bp', user_profile_bp))
except Exception as e:
    logger.error(f"Failed to import user_profile_endpoints: {e}")

try:
    from api.key_vault_endpoints import bp as key_vault_bp
    blueprints.append(('key_vault_bp', key_vault_bp))
except Exception as e:
    logger.error(f"Failed to import key_vault_endpoints: {e}")

try:
    from api.general_endpoints import bp as general_bp
    blueprints.append(('general_bp', general_bp))
except Exception as e:
    logger.error(f"Failed to import general_endpoints: {e}")

try:
    from api.pmd_endpoints import bp as pmd_bp
    blueprints.append(('pmd_bp', pmd_bp))
except Exception as e:
    logger.error(f"Failed to import pmd_endpoints: {e}")

try:
    from api.task_processor_endpoints import bp as task_processor_bp
    blueprints.append(('task_processor_bp', task_processor_bp))
except Exception as e:
    logger.error(f"Failed to import task_processor_endpoints: {e}")

try:
    from api.scan_endpoints import bp as scan_bp
    blueprints.append(('scan_bp', scan_bp))
except Exception as e:
    logger.error(f"Failed to import scan_endpoints: {e}")

# Create the Function App
app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

# Register all successfully imported blueprints
registered_count = 0
for name, blueprint in blueprints:
    try:
        app.register_functions(blueprint)
        logger.info(f"Successfully registered {name}")
        registered_count += 1
    except Exception as e:
        logger.error(f"Error registering {name}: {str(e)}")

logger.info(f"Successfully registered {registered_count} out of {len(blueprints)} database API blueprints")

# Health check endpoint
@app.route(route="health", methods=["GET"])
def health_check(req: func.HttpRequest) -> func.HttpResponse:
    """
    Health check endpoint for the database function app

    Returns:
        JSON response with health status
    """
    try:
        # Test database connectivity
        from shared.data_access import get_table_storage_repository
        from shared.azure_services import is_local_dev
        from shared.cors_middleware import add_cors_headers

        health_status = {
            "status": "healthy",
            "service": "atomsec-func-db",
            "environment": "local" if is_local_dev() else "production",
            "checks": {}
        }

        # Test Table Storage connectivity
        try:
            test_repo = get_table_storage_repository("HealthCheck")
            if test_repo:
                health_status["checks"]["table_storage"] = "connected"
            else:
                health_status["checks"]["table_storage"] = "failed"
                health_status["status"] = "degraded"
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Table storage health check failed: {error_msg}")
            
            # Check if it's a connection issue
            if "Connection refused" in error_msg or "Failed to establish" in error_msg:
                health_status["checks"]["table_storage"] = "connection_failed"
                health_status["status"] = "degraded"
                health_status["table_storage_error"] = "Unable to connect to Azure Table Storage. Check connection string and network connectivity."
            else:
                health_status["checks"]["table_storage"] = f"error: {error_msg}"
                health_status["status"] = "degraded"

        # Test SQL Database connectivity (only in production)
        if not is_local_dev():
            try:
                from shared.data_access import get_sql_database_repository
                test_sql_repo = get_sql_database_repository("App_Account")
                if test_sql_repo:
                    health_status["checks"]["sql_database"] = "connected"
                else:
                    health_status["checks"]["sql_database"] = "failed"
                    health_status["status"] = "degraded"
            except Exception as e:
                health_status["checks"]["sql_database"] = f"error: {str(e)}"
                health_status["status"] = "degraded"

        # Test Service Bus connectivity
        try:
            from shared.service_bus_client import get_service_bus_client
            sb_client = get_service_bus_client()
            if sb_client:
                health_status["checks"]["service_bus"] = "connected"
            else:
                health_status["checks"]["service_bus"] = "failed"
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["checks"]["service_bus"] = f"error: {str(e)}"
            health_status["status"] = "degraded"

        # Determine status code based on health
        if health_status["status"] == "healthy":
            status_code = 200
        elif health_status["status"] == "degraded":
            status_code = 200  # Return 200 for degraded but still functional
        else:
            status_code = 503

        response = func.HttpResponse(
            json.dumps(health_status),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        response = func.HttpResponse(
            json.dumps({
                "status": "unhealthy",
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=503
        )
        return add_cors_headers(req, response)

# Info endpoint
@app.route(route="info", methods=["GET"])
def info(req: func.HttpRequest) -> func.HttpResponse:
    """
    Information endpoint for the database function app

    Returns:
        JSON response with service information
    """
    info_data = {
        "service": "atomsec-func-db",
        "version": "1.0.0",
        "description": "AtomSec Database Service - Centralized database operations",
        "endpoints": {
            "users": {
                "base": "/api/users",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "accounts": {
                "base": "/api/accounts",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "organizations": {
                "base": "/api/organizations",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "integrations": {
                "base": "/api/integrations",
                "operations": ["GET", "POST", "PUT", "DELETE"],
                "sub_endpoints": ["/overview", "/health-check", "/profiles", "/credentials", "/pmd-issues"]
            },
            "security": {
                "base": "/api/security",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/health-checks", "/profiles", "/overview"]
            },
            "tasks": {
                "base": "/api/tasks",
                "operations": ["GET", "POST", "PUT"],
                "sub_endpoints": ["/status", "/results"]
            },
            "auth": {
                "base": "/api/auth",
                "operations": ["GET"],
                "sub_endpoints": ["/azure/login", "/azure/callback", "/azure/me"]
            },
            "policies": {
                "base": "/api/policies",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/rules", "/policy-rule-settings", "/policy-rule-settings/enabled-tasks"]
            },
            "user_profile": {
                "base": "/api/user",
                "operations": ["GET", "PUT"],
                "sub_endpoints": ["/profile", "/password"]
            },
            "key_vault": {
                "base": "/api/key-vault",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/secrets", "/create", "/access-policy", "/client-credentials"]
            },
            "sfdc_proxy": {
                "base": "/v1",
                "description": "Proxy endpoints to SFDC service",
                "operations": ["GET", "POST"],
                "sub_endpoints": [
                    "/integration/scan/{id}",
                    "/task-status",
                    "/tasks/cancel",
                    "/tasks/schedule",
                    "/health-score",
                    "/health-risks",
                    "/profiles",
                    "/permission-sets",
                    "/scan/accounts",
                    "/scan/history",
                    "/sfdc/health",
                    "/sfdc/info"
                ]
            }
        },
        "features": [
            "Dual storage support (Azure Table Storage + SQL Database)",
            "Environment-aware (local dev + production)",
            "Repository pattern implementation",
            "Comprehensive error handling",
            "RESTful API design",
            "Service Bus event publishing",
            "Event-driven architecture"
        ]
    }

    return func.HttpResponse(
        json.dumps(info_data),
        mimetype="application/json",
        status_code=200
    )

# Diagnostic endpoint
@app.route(route="diagnostic", methods=["GET"])
def diagnostic(req: func.HttpRequest) -> func.HttpResponse:
    """
    Diagnostic endpoint for troubleshooting configuration and connection issues

    Returns:
        JSON response with diagnostic information
    """
    try:
        from shared.config import validate_storage_configuration
        from shared.azure_services import is_local_dev
        
        diagnostic_info = {
            "timestamp": datetime.now().isoformat(),
            "environment": "local" if is_local_dev() else "production",
            "storage_config": validate_storage_configuration(),
            "azure_environment_vars": {
                "WEBSITE_SITE_NAME": os.environ.get("WEBSITE_SITE_NAME"),
                "WEBSITE_INSTANCE_ID": os.environ.get("WEBSITE_INSTANCE_ID"),
                "FUNCTIONS_WORKER_RUNTIME": os.environ.get("FUNCTIONS_WORKER_RUNTIME"),
                "FUNCTIONS_WORKER_RUNTIME_VERSION": os.environ.get("FUNCTIONS_WORKER_RUNTIME_VERSION"),
                "IS_LOCAL_DEV": os.environ.get("IS_LOCAL_DEV"),
                "USE_LOCAL_STORAGE": os.environ.get("USE_LOCAL_STORAGE"),
                "WEBSITE_HOSTNAME": os.environ.get("WEBSITE_HOSTNAME")
            }
        }
        
        return func.HttpResponse(
            json.dumps(diagnostic_info, indent=2),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Diagnostic endpoint failed: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }),
            mimetype="application/json",
            status_code=500
        )

def handle_error(e: Exception, operation: str) -> func.HttpResponse:
    """
    Standard error handler for database operations

    Args:
        e: Exception that occurred
        operation: Description of the operation that failed

    Returns:
        func.HttpResponse: Error response
    """
    logger.error(f"Error in {operation}: {str(e)}")
    return func.HttpResponse(
        json.dumps({
            "success": False,
            "error": f"Database operation failed: {str(e)}"
        }),
        mimetype="application/json",
        status_code=500
    )

logger.info("AtomSec Database Function App initialized successfully")