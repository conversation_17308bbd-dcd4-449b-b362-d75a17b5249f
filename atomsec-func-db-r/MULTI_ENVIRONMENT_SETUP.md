# Multi-Environment Deployment Setup Guide

This guide explains how to set up multi-environment deployments for AtomSec using Azure DevOps Variable Groups.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   DEV Pipeline  │    │   QA Pipeline   │    │  PROD Pipeline  │
│                 │    │                 │    │                 │
│ Variable Group: │    │ Variable Group: │    │ Variable Group: │
│ atomsec-dev-vg  │    │ atomsec-qa-vg   │    │ atomsec-prod-vg │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  DEV Function   │    │  QA Function    │    │ PROD Function   │
│ func-atomsec-   │    │ func-atomsec-   │    │ func-atomsec-   │
│ dbconnect-dev   │    │ dbconnect-qa    │    │ dbconnect-prod  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   DEV Frontend  │    │   QA Frontend   │    │  PROD Frontend  │
│ app-atomsec-    │    │ app-atomsec-    │    │ core.atomsec.ai │
│ dev01.azure-    │    │ qa01.azure-     │    │                 │
│ websites.net    │    │ websites.net    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Environment URLs

| Environment | Frontend URL | Function App | Purpose |
|-------------|--------------|--------------|---------|
| **DEV** | `https://app-atomsec-dev01.azurewebsites.net/` | `func-atomsec-dbconnect-dev` | Development & Testing |
| **QA** | `https://app-atomsec-qa01.azurewebsites.net/` | `func-atomsec-dbconnect-qa` | Quality Assurance |
| **PROD** | `https://core.atomsec.ai` | `func-atomsec-dbconnect-prod` | Production |

## 🚀 Setup Steps

### Step 1: Create Azure DevOps Variable Groups

#### 1.1 Create Dev Variable Group
```bash
# Navigate to Azure DevOps
# Go to: Library → Variable Groups → New Variable Group

# Group Name: atomsec-dev-vg
# Description: Variable group for AtomSec DB Function App - Development Environment
```

**Variables to add:**
```json
{
  "ENVIRONMENT": "development",
  "AZURE_SUBSCRIPTION": "sc-atomsec-dev-data",
  "SUBSCRIPTION_ID": "35518353-3fc5-49c1-91cd-3ab90df8d78d",
  "RESOURCE_GROUP": "atomsec-dev-data",
  "FUNCTION_APP_NAME": "func-atomsec-dbconnect-dev",
  "FUNCTION_APP_URL": "https://func-atomsec-dbconnect-dev.azurewebsites.net",
  "FUNCTION_APP_URL_SCM": "https://func-atomsec-dbconnect-dev.scm.azurewebsites.net",
  "KEY_VAULT_NAME": "akv-atomsec-dev",
  "KEY_VAULT_URL": "https://akv-atomsec-dev.vault.azure.net/",
  "IS_LOCAL_DEV": "false",
  "LOG_LEVEL": "INFO",
  "FRONTEND_URL": "https://app-atomsec-dev01.azurewebsites.net"
}
```

#### 1.2 Create QA Variable Group
```bash
# Group Name: atomsec-qa-vg
# Description: Variable group for AtomSec DB Function App - QA Environment
```

**Variables to add:**
```json
{
  "ENVIRONMENT": "qa",
  "AZURE_SUBSCRIPTION": "sc-atomsec-qa-data",
  "SUBSCRIPTION_ID": "35518353-3fc5-49c1-91cd-3ab90df8d78d",
  "RESOURCE_GROUP": "atomsec-qa-data",
  "FUNCTION_APP_NAME": "func-atomsec-dbconnect-qa",
  "FUNCTION_APP_URL": "https://func-atomsec-dbconnect-qa.azurewebsites.net",
  "FUNCTION_APP_URL_SCM": "https://func-atomsec-dbconnect-qa.scm.azurewebsites.net",
  "KEY_VAULT_NAME": "akv-atomsec-qa",
  "KEY_VAULT_URL": "https://akv-atomsec-qa.vault.azure.net/",
  "IS_LOCAL_DEV": "false",
  "LOG_LEVEL": "INFO",
  "FRONTEND_URL": "https://app-atomsec-qa01.azurewebsites.net"
}
```

#### 1.3 Create Production Variable Group
```bash
# Group Name: atomsec-prod-vg
# Description: Variable group for AtomSec DB Function App - Production Environment
```

**Variables to add:**
```json
{
  "ENVIRONMENT": "production",
  "AZURE_SUBSCRIPTION": "sc-atomsec-prod-data",
  "SUBSCRIPTION_ID": "35518353-3fc5-49c1-91cd-3ab90df8d78d",
  "RESOURCE_GROUP": "atomsec-prod-data",
  "FUNCTION_APP_NAME": "func-atomsec-dbconnect-prod",
  "FUNCTION_APP_URL": "https://func-atomsec-dbconnect-prod.azurewebsites.net",
  "FUNCTION_APP_URL_SCM": "https://func-atomsec-dbconnect-prod.scm.azurewebsites.net",
  "KEY_VAULT_NAME": "akv-atomsec-prod",
  "KEY_VAULT_URL": "https://akv-atomsec-prod.vault.azure.net/",
  "IS_LOCAL_DEV": "false",
  "LOG_LEVEL": "WARNING",
  "FRONTEND_URL": "https://core.atomsec.ai"
}
```

### Step 2: Create Azure Resources

#### 2.1 Create Resource Groups
```bash
# Dev Resource Group
az group create --name atomsec-dev-data --location eastus

# QA Resource Group  
az group create --name atomsec-qa-data --location eastus

# Production Resource Group
az group create --name atomsec-prod-data --location eastus
```

#### 2.2 Create Key Vaults
```bash
# Dev Key Vault
az keyvault create --name akv-atomsec-dev --resource-group atomsec-dev-data --location eastus

# QA Key Vault
az keyvault create --name akv-atomsec-qa --resource-group atomsec-qa-data --location eastus

# Production Key Vault
az keyvault create --name akv-atomsec-prod --resource-group atomsec-prod-data --location eastus
```

#### 2.3 Create Function Apps
```bash
# Dev Function App
az functionapp create --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --storage-account statomsecfuncsfdcdev --consumption-plan-location eastus --runtime python --runtime-version 3.12

# QA Function App
az functionapp create --name func-atomsec-dbconnect-qa --resource-group atomsec-qa-data --storage-account statomsecfuncsfdcqa --consumption-plan-location eastus --runtime python --runtime-version 3.12

# Production Function App
az functionapp create --name func-atomsec-dbconnect-prod --resource-group atomsec-prod-data --storage-account statomsecfuncsfdcprod --consumption-plan-location eastus --runtime python --runtime-version 3.12
```

### Step 3: Configure Key Vault Secrets

#### 3.1 Add Secrets to Each Key Vault
```bash
# For each environment (dev, qa, prod), add these secrets:
az keyvault secret set --vault-name akv-atomsec-dev --name jwt-secret --value "YOUR_JWT_SECRET"
az keyvault secret set --vault-name akv-atomsec-dev --name azure-ad-client-id --value "YOUR_CLIENT_ID"
az keyvault secret set --vault-name akv-atomsec-dev --name azure-ad-tenant-id --value "YOUR_TENANT_ID"
az keyvault secret set --vault-name akv-atomsec-dev --name azure-ad-client-secret --value "YOUR_CLIENT_SECRET"
```

### Step 4: Create Azure DevOps Pipelines

#### 4.1 Dev Pipeline
- **File**: `pipeline-func-db-dev.yml`
- **Trigger**: `dev`, `dev-db`
- **Variable Group**: `atomsec-dev-vg`

#### 4.2 QA Pipeline
- **File**: `pipeline-func-db-qa.yml`
- **Trigger**: `qa`, `qa-db`
- **Variable Group**: `atomsec-qa-vg`

#### 4.3 Production Pipeline
- **File**: `pipeline-func-db-prod.yml`
- **Trigger**: `main`, `prod`
- **Variable Group**: `atomsec-prod-vg`

### Step 5: Configure Service Connections

#### 5.1 Create Service Connections
```bash
# In Azure DevOps, go to: Project Settings → Service Connections → New Service Connection

# Dev Service Connection
Name: sc-atomsec-dev-data
Scope: Subscription
Subscription: Atomsec Dev

# QA Service Connection  
Name: sc-atomsec-qa-data
Scope: Subscription
Subscription: Atomsec Dev

# Production Service Connection
Name: sc-atomsec-prod-data
Scope: Subscription
Subscription: Atomsec Dev
```

## 🔄 Deployment Flow

### Development Workflow
1. **Developer pushes to `dev` branch**
2. **Dev pipeline triggers automatically**
3. **Deploys to**: `func-atomsec-dbconnect-dev`
4. **Frontend**: `https://app-atomsec-dev01.azurewebsites.net/`

### QA Workflow
1. **QA team pushes to `qa` branch**
2. **QA pipeline triggers automatically**
3. **Deploys to**: `func-atomsec-dbconnect-qa`
4. **Frontend**: `https://app-atomsec-qa01.azurewebsites.net/`

### Production Workflow
1. **Release manager merges to `main`**
2. **Production pipeline triggers automatically**
3. **Deploys to**: `func-atomsec-dbconnect-prod`
4. **Frontend**: `https://core.atomsec.ai`

## 🔐 Security Features

### Key Vault Integration
- ✅ **Secure secret management** - All secrets stored in Key Vault
- ✅ **Environment-specific secrets** - Each environment has its own Key Vault
- ✅ **No hardcoded secrets** - Pipeline retrieves secrets dynamically

### Environment Isolation
- ✅ **Separate resource groups** - Complete isolation between environments
- ✅ **Separate function apps** - No shared resources
- ✅ **Separate Key Vaults** - Environment-specific secrets

## 📊 Monitoring & Verification

### Built-in Verification
Each pipeline includes:
- ✅ **Configuration verification** - Checks all required settings
- ✅ **Endpoint testing** - Tests health and info endpoints
- ✅ **Secret validation** - Verifies Key Vault access
- ✅ **Deployment status** - Monitors deployment success

### Manual Verification
```bash
# Test any environment
curl https://func-atomsec-dbconnect-dev.azurewebsites.net/api/db/health
curl https://func-atomsec-dbconnect-qa.azurewebsites.net/api/db/health
curl https://func-atomsec-dbconnect-prod.azurewebsites.net/api/db/health
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Variable Group Not Found
```bash
# Solution: Ensure variable group exists and is linked to pipeline
# Go to: Pipeline → Edit → Variables → Link Variable Group
```

#### 2. Key Vault Access Denied
```bash
# Solution: Grant access to service principal
az keyvault set-policy --name akv-atomsec-dev --spn YOUR_SERVICE_PRINCIPAL_ID --secret-permissions get list
```

#### 3. Function App Not Found
```bash
# Solution: Create function app first
az functionapp create --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --storage-account statomsecfuncsfdcdev --consumption-plan-location eastus --runtime python --runtime-version 3.12
```

## 📝 Next Steps

1. **Create the Variable Groups** in Azure DevOps
2. **Set up the Azure resources** (Resource Groups, Key Vaults, Function Apps)
3. **Configure the secrets** in each Key Vault
4. **Create the pipelines** in Azure DevOps
5. **Test the deployment flow** from dev → qa → prod

## 🎯 Benefits

- ✅ **Environment isolation** - Complete separation between dev/qa/prod
- ✅ **Secure deployments** - All secrets managed via Key Vault
- ✅ **Automated verification** - Built-in configuration and endpoint testing
- ✅ **Scalable architecture** - Easy to add new environments
- ✅ **Developer-friendly** - Clear deployment flow and URLs 