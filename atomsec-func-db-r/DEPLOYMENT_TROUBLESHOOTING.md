# Database Service Deployment Troubleshooting Guide

This guide addresses the specific issues encountered in the database service pipeline deployment.

## Issues Identified

### 1. Azure CLI Command Deprecation
**Error:** `ERROR: 'list' is misspelled or not recognized by the system.`

**Root Cause:** The `az webapp deployment list` command is deprecated in newer Azure CLI versions.

**Solution:** Use `az functionapp deployment list` instead.

### 2. SCM/Kudu Access Issues
**Error:** 
```
ERROR: Failed to connect to 'https://func-atomsec-dbconnect-dev.scm.azurewebsites.net/dump' with status code '404'
ERROR: Failed to connect to 'https://func-atomsec-dbconnect-dev.scm.azurewebsites.net/logstream' with status code '404'
```

**Root Cause:** SCM (Kudu) endpoints are not accessible or not properly configured.

**Solutions:**
1. Check if SCM is enabled in the function app configuration
2. Verify network access and firewall rules
3. Use alternative logging methods

### 3. Function App Configuration Issues
**Issues:**
- `alwaysOn: false` - Causes cold starts
- Missing application settings
- Incomplete CORS configuration

## Immediate Fixes

### 1. Update Pipeline Commands

Replace deprecated commands in the pipeline:

```bash
# Old (deprecated)
az webapp deployment list --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data

# New (recommended)
az functionapp deployment list --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data
```

### 2. Configure Function App Settings

Add these essential settings to your function app:

```bash
# Core Azure Functions settings
az functionapp config appsettings set \
  --name func-atomsec-dbconnect-dev \
  --resource-group atomsec-dev-data \
  --settings \
    FUNCTIONS_WORKER_RUNTIME=python \
    FUNCTIONS_EXTENSION_VERSION=~4 \
    WEBSITE_RUN_FROM_PACKAGE=1 \
    SCM_DO_BUILD_DURING_DEPLOYMENT=false \
    AzureWebJobsFeatureFlags=EnableWorkerIndexing \
    WEBSITE_ENABLE_SYNC_UPDATE_SITE=true \
    WEBSITE_WEBSOCKETS_ENABLED=false \
    WEBSITE_HTTPLOGGING_RETENTION_DAYS=7 \
    WEBSITE_ENABLE_APP_SERVICE_STORAGE=false

# Application-specific settings
az functionapp config appsettings set \
  --name func-atomsec-dbconnect-dev \
  --resource-group atomsec-dev-data \
  --settings \
    AZURE_STORAGE_CONNECTION_STRING="your_storage_connection_string" \
    AZURE_TABLE_STORAGE_CONNECTION_STRING="your_table_storage_connection_string" \
    AZURE_SERVICE_BUS_CONNECTION_STRING="your_service_bus_connection_string" \
    KEY_VAULT_URL="https://akv-atomsec-dev.vault.azure.net/" \
    ENVIRONMENT="development" \
    LOG_LEVEL="INFO"

# Enable Always On
az functionapp config set \
  --name func-atomsec-dbconnect-dev \
  --resource-group atomsec-dev-data \
  --always-on true
```

### 3. Alternative Logging Methods

Since SCM logs are not accessible, use these alternatives:

```bash
# Get function app logs
az functionapp log download --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --log-file deployment-logs.zip

# Get live logs with timeout
timeout 30s az functionapp log tail --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data

# Check Application Insights logs (if configured)
az monitor app-insights query --app "func-atomsec-dbconnect-dev" --analytics-query "traces | where timestamp > ago(1h) | order by timestamp desc"
```

## Health Check Endpoints

Test these endpoints to verify deployment:

```bash
# Health check with route prefix
curl -v "https://func-atomsec-dbconnect-dev.azurewebsites.net/api/db/health"

# Health check without route prefix
curl -v "https://func-atomsec-dbconnect-dev.azurewebsites.net/api/health"

# Info endpoint
curl -v "https://func-atomsec-dbconnect-dev.azurewebsites.net/api/db/info"
```

## Network and Security Configuration

### 1. CORS Configuration
Update `host.json` to include proper CORS settings:

```json
{
  "extensions": {
    "http": {
      "routePrefix": "api/db",
      "cors": {
        "allowedOrigins": [
          "http://localhost:3000",
          "https://app-atomsec-dev01.azurewebsites.net",
          "https://apim-atomsec-dev.azure-api.net"
        ],
        "allowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allowedHeaders": [
          "Content-Type",
          "Authorization",
          "X-Requested-With",
          "Origin",
          "Accept"
        ],
        "allowCredentials": true
      }
    }
  }
}
```

### 2. IP Security Restrictions
If needed, configure IP restrictions:

```bash
# Allow all access (for development)
az functionapp config access-restriction add \
  --name func-atomsec-dbconnect-dev \
  --resource-group atomsec-dev-data \
  --rule-name "Allow all" \
  --action Allow \
  --ip-address "Any"
```

## Monitoring and Diagnostics

### 1. Enable Application Insights
```bash
# Enable Application Insights
az monitor app-insights component create \
  --app "func-atomsec-dbconnect-dev" \
  --location "East US" \
  --resource-group atomsec-dev-data \
  --application-type web
```

### 2. Configure Logging
```bash
# Enable detailed logging
az functionapp config appsettings set \
  --name func-atomsec-dbconnect-dev \
  --resource-group atomsec-dev-data \
  --settings \
    WEBSITE_HTTPLOGGING_RETENTION_DAYS=7 \
    WEBSITE_ENABLE_APP_SERVICE_STORAGE=false \
    LOG_LEVEL=INFO
```

## Troubleshooting Commands

### 1. Check Function App Status
```bash
# Check function app state
az functionapp show --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data --query "state"

# Check function app configuration
az functionapp config show --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data
```

### 2. Check Deployment Status
```bash
# List deployments
az functionapp deployment list --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data

# Check deployment slots
az functionapp deployment slot list --name func-atomsec-dbconnect-dev --resource-group atomsec-dev-data
```

### 3. Test Connectivity
```bash
# Test SCM accessibility
curl -I "https://func-atomsec-dbconnect-dev.scm.azurewebsites.net/"

# Test function app health
curl -s "https://func-atomsec-dbconnect-dev.azurewebsites.net/api/db/health"
```

## Common Error Codes and Solutions

| Error | Cause | Solution |
|-------|-------|----------|
| 404 on SCM endpoints | SCM not enabled or network issues | Enable SCM, check network rules |
| Azure CLI command not found | Deprecated commands | Use modern Azure CLI commands |
| Cold starts | Always On disabled | Enable Always On setting |
| CORS errors | Missing CORS configuration | Update host.json with proper CORS settings |
| Authentication errors | Missing app settings | Configure Azure AD and Key Vault settings |

## Next Steps

1. **Update the pipeline** with the fixes provided above
2. **Configure function app settings** using the commands in this guide
3. **Test the deployment** using the health check endpoints
4. **Monitor the logs** using Application Insights or alternative methods
5. **Verify connectivity** between frontend and database service

## Support

If issues persist after implementing these fixes:

1. Check Azure Function App logs in the Azure portal
2. Review Application Insights for detailed error information
3. Verify network connectivity and firewall rules
4. Test individual endpoints to isolate the problem 