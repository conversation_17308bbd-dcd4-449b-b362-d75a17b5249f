# Azure DevOps Variable Groups Management Guide

This guide shows you how to view, create, and manage Variable Groups in Azure DevOps.

## 🔍 **How to View Variable Groups**

### **Method 1: Azure DevOps Web Interface**

1. **Navigate to your Azure DevOps project**
2. **Go to**: `Pipelines` → `Library` → `Variable groups`
3. **You should see your variable groups**:
   - `atomsec-dev-vg`
   - `atomsec-qa-vg`
   - `atomsec-prod-vg`

### **Method 2: Azure CLI with DevOps Extension**

```bash
# Install Azure DevOps extension (if not already installed)
az extension add --name azure-devops

# Configure Azure DevOps CLI
az devops configure --defaults organization=https://dev.azure.com/YOUR_ORG project=YOUR_PROJECT

# List all variable groups
az pipelines variable-group list --organization https://dev.azure.com/YOUR_ORG --project YOUR_PROJECT

# Get details of a specific variable group
az pipelines variable-group show --id VARIABLE_GROUP_ID --organization https://dev.azure.com/YOUR_ORG --project YOUR_PROJECT
```

## 🛠️ **How to Create Variable Groups**

### **Method 1: Using Azure DevOps Web Interface**

1. **Go to**: `Pipelines` → `Library` → `Variable groups`
2. **Click**: `+ Variable group`
3. **Fill in details**:
   - **Name**: `atomsec-dev-vg`
   - **Description**: `Variable group for AtomSec DB Function App - Development Environment`
   - **Variables**: Add the variables from the JSON files

### **Method 2: Using Azure CLI**

```bash
# Create variable group from JSON file
az pipelines variable-group create \
  --name "atomsec-dev-vg" \
  --description "Variable group for AtomSec DB Function App - Development Environment" \
  --variables ENVIRONMENT=development AZURE_SUBSCRIPTION=sc-atomsec-dev-data \
  --organization https://dev.azure.com/YOUR_ORG \
  --project YOUR_PROJECT
```

## 📋 **Variable Groups Configuration**

### **Dev Environment Variables**
```json
{
  "ENVIRONMENT": "development",
  "AZURE_SUBSCRIPTION": "sc-atomsec-dev-data",
  "SUBSCRIPTION_ID": "35518353-3fc5-49c1-91cd-3ab90df8d78d",
  "RESOURCE_GROUP": "atomsec-dev-data",
  "FUNCTION_APP_NAME": "func-atomsec-dbconnect-dev",
  "FUNCTION_APP_URL": "https://func-atomsec-dbconnect-dev.azurewebsites.net",
  "FUNCTION_APP_URL_SCM": "https://func-atomsec-dbconnect-dev.scm.azurewebsites.net",
  "KEY_VAULT_NAME": "akv-atomsec-dev",
  "KEY_VAULT_URL": "https://akv-atomsec-dev.vault.azure.net/",
  "IS_LOCAL_DEV": "false",
  "LOG_LEVEL": "INFO",
  "FRONTEND_URL": "https://app-atomsec-dev01.azurewebsites.net"
}
```

### **QA Environment Variables**
```json
{
  "ENVIRONMENT": "qa",
  "AZURE_SUBSCRIPTION": "sc-atomsec-qa-data",
  "SUBSCRIPTION_ID": "35518353-3fc5-49c1-91cd-3ab90df8d78d",
  "RESOURCE_GROUP": "atomsec-qa-data",
  "FUNCTION_APP_NAME": "func-atomsec-dbconnect-qa",
  "FUNCTION_APP_URL": "https://func-atomsec-dbconnect-qa.azurewebsites.net",
  "FUNCTION_APP_URL_SCM": "https://func-atomsec-dbconnect-qa.scm.azurewebsites.net",
  "KEY_VAULT_NAME": "akv-atomsec-qa",
  "KEY_VAULT_URL": "https://akv-atomsec-qa.vault.azure.net/",
  "IS_LOCAL_DEV": "false",
  "LOG_LEVEL": "INFO",
  "FRONTEND_URL": "https://app-atomsec-qa01.azurewebsites.net"
}
```

### **Production Environment Variables**
```json
{
  "ENVIRONMENT": "production",
  "AZURE_SUBSCRIPTION": "sc-atomsec-prod-data",
  "SUBSCRIPTION_ID": "35518353-3fc5-49c1-91cd-3ab90df8d78d",
  "RESOURCE_GROUP": "atomsec-prod-data",
  "FUNCTION_APP_NAME": "func-atomsec-dbconnect-prod",
  "FUNCTION_APP_URL": "https://func-atomsec-dbconnect-prod.azurewebsites.net",
  "FUNCTION_APP_URL_SCM": "https://func-atomsec-dbconnect-prod.scm.azurewebsites.net",
  "KEY_VAULT_NAME": "akv-atomsec-prod",
  "KEY_VAULT_URL": "https://akv-atomsec-prod.vault.azure.net/",
  "IS_LOCAL_DEV": "false",
  "LOG_LEVEL": "WARNING",
  "FRONTEND_URL": "https://core.atomsec.ai"
}
```

## 🔧 **Azure CLI Commands for Verification**

### **1. Check if Variable Groups Exist**
```bash
# List all variable groups
az pipelines variable-group list --organization https://dev.azure.com/YOUR_ORG --project YOUR_PROJECT --output table

# Get specific variable group details
az pipelines variable-group show --id VARIABLE_GROUP_ID --organization https://dev.azure.com/YOUR_ORG --project YOUR_PROJECT --output json
```

### **2. Verify Azure Resources**
```bash
# Check if resource groups exist
az group list --output table

# Check if function apps exist
az functionapp list --output table

# Check if Key Vaults exist
az keyvault list --output table
```

### **3. Verify Key Vault Secrets**
```bash
# List secrets in Key Vault
az keyvault secret list --vault-name akv-atomsec-dev --output table

# Check if specific secrets exist
az keyvault secret show --vault-name akv-atomsec-dev --name jwt-secret --query value --output tsv
```

## 🚨 **Troubleshooting**

### **Issue 1: Variable Group Not Found**
```bash
# Solution: Create the variable group
az pipelines variable-group create \
  --name "atomsec-dev-vg" \
  --description "Variable group for AtomSec DB Function App - Development Environment" \
  --variables ENVIRONMENT=development \
  --organization https://dev.azure.com/YOUR_ORG \
  --project YOUR_PROJECT
```

### **Issue 2: Pipeline Can't Access Variable Group**
```bash
# Solution: Link variable group to pipeline
# Go to: Pipeline → Edit → Variables → Link Variable Group
# Select the variable group and save
```

### **Issue 3: Key Vault Access Denied**
```bash
# Solution: Grant access to service principal
az keyvault set-policy \
  --name akv-atomsec-dev \
  --spn YOUR_SERVICE_PRINCIPAL_ID \
  --secret-permissions get list
```

## 📊 **Verification Checklist**

### **✅ Variable Groups**
- [ ] `atomsec-dev-vg` exists
- [ ] `atomsec-qa-vg` exists  
- [ ] `atomsec-prod-vg` exists
- [ ] All variables are set correctly
- [ ] Variable groups are linked to pipelines

### **✅ Azure Resources**
- [ ] Resource groups exist (`atomsec-dev-data`, `atomsec-qa-data`, `atomsec-prod-data`)
- [ ] Function apps exist (`func-atomsec-dbconnect-dev`, `func-atomsec-dbconnect-qa`, `func-atomsec-dbconnect-prod`)
- [ ] Key Vaults exist (`akv-atomsec-dev`, `akv-atomsec-qa`, `akv-atomsec-prod`)

### **✅ Key Vault Secrets**
- [ ] `jwt-secret` exists in all Key Vaults
- [ ] `azure-ad-client-id` exists in all Key Vaults
- [ ] `azure-ad-tenant-id` exists in all Key Vaults
- [ ] `azure-ad-client-secret` exists in all Key Vaults

### **✅ Service Connections**
- [ ] `sc-atomsec-dev-data` exists
- [ ] `sc-atomsec-qa-data` exists
- [ ] `sc-atomsec-prod-data` exists

## 🎯 **Quick Verification Commands**

```bash
# 1. Check Variable Groups
az pipelines variable-group list --output table

# 2. Check Azure Resources
az group list --output table
az functionapp list --output table
az keyvault list --output table

# 3. Check Key Vault Secrets
az keyvault secret list --vault-name akv-atomsec-dev --output table

# 4. Test Key Vault Access
az keyvault secret show --vault-name akv-atomsec-dev --name jwt-secret --query value --output tsv
```

## 📝 **Next Steps**

1. **Create Variable Groups** using the web interface or CLI
2. **Configure all variables** as shown in the JSON examples
3. **Link Variable Groups** to your pipelines
4. **Verify Azure resources** exist and are accessible
5. **Test the deployment** with a small change 