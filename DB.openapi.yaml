openapi: 3.0.1
info:
  title: DB
  description: Import from "func-atomsec-dbconnect-dev" Function App
  version: v1
servers:
  - url: https://apim-atomsec-dev.azure-api.net/db/v1
paths:
  '/tasks/{task_id}':
    get:
      summary: get_task_endpoint
      operationId: get-get-task-endpoint
      parameters:
        - name: task_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  /auth/azure/callback:
    get:
      summary: azure_callback
      operationId: get-azure-callback
      responses:
        '200':
          description: ''
  '/integrations/{integration_id}/pmd-issues':
    get:
      summary: get_integration_pmd_issues
      operationId: get-get-integration-pmd-issues
      parameters:
        - name: integration_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  '/integrations/{integration_id}/profiles':
    get:
      summary: get_integration_profiles
      operationId: get-get-integration-profiles
      parameters:
        - name: integration_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  /users/login/verify:
    post:
      summary: verify_login
      operationId: post-verify-login
      responses:
        '200':
          description: ''
  /health:
    get:
      summary: health_check
      operationId: get-health-check
      responses:
        '200':
          description: ''
  /integration/test-connection:
    post:
      summary: test_connection
      operationId: post-test-connection
      responses:
        '200':
          description: ''
  '/users/{user_id}':
    put:
      summary: update_user
      operationId: put-update-user
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
    get:
      summary: get_user
      operationId: get-get-user
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
    delete:
      summary: delete_user
      operationId: delete-delete-user
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  /auth/azure/login:
    get:
      summary: azure_login
      operationId: get-azure-login
      responses:
        '200':
          description: ''
  /user/profile:
    put:
      summary: update_user_profile
      operationId: put-update-user-profile
      responses:
        '200':
          description: ''
    get:
      summary: get_user_profile
      operationId: get-get-user-profile
      responses:
        '200':
          description: ''
  /security/health-checks:
    post:
      summary: store_health_checks
      operationId: post-store-health-checks
      responses:
        '200':
          description: ''
    get:
      summary: list_health_checks
      operationId: get-list-health-checks
      responses:
        '200':
          description: ''
  '/integrations/{integration_id}/overview':
    get:
      summary: get_integration_overview
      operationId: get-get-integration-overview
      parameters:
        - name: integration_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  /accounts:
    get:
      summary: list_accounts
      operationId: get-list-accounts
      responses:
        '200':
          description: ''
    post:
      summary: create_account_endpoint
      operationId: post-create-account-endpoint
      responses:
        '200':
          description: ''
  '/integrations/{integration_id}':
    put:
      summary: update_integration
      operationId: put-update-integration
      parameters:
        - name: integration_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
    get:
      summary: get_integration
      operationId: get-get-integration
      parameters:
        - name: integration_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
    delete:
      summary: delete_integration
      operationId: delete-delete-integration
      parameters:
        - name: integration_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  '/integrations/{integration_id}/health-check':
    get:
      summary: get_integration_health_check
      operationId: get-get-integration-health-check
      parameters:
        - name: integration_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  /info:
    get:
      summary: info
      operationId: get-info
      responses:
        '200':
          description: ''
  '/tasks/{task_id}/status':
    put:
      summary: update_task_status_endpoint
      operationId: put-update-task-status-endpoint
      parameters:
        - name: task_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  '/integrations/{integration_id}/credentials':
    get:
      summary: get_integration_credentials_endpoint
      operationId: get-get-integration-credentials-endpoint
      parameters:
        - name: integration_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  '/accounts/{account_id}':
    put:
      summary: update_account_endpoint
      operationId: put-update-account-endpoint
      parameters:
        - name: account_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: ''
    get:
      summary: get_account_endpoint
      operationId: get-get-account-endpoint
      parameters:
        - name: account_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: ''
    delete:
      summary: delete_account_endpoint
      operationId: delete-delete-account-endpoint
      parameters:
        - name: account_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: ''
  /security/policies-result:
    post:
      summary: store_policies_results
      operationId: post-store-policies-results
      responses:
        '200':
          description: ''
    get:
      summary: list_policies_results
      operationId: get-list-policies-results
      responses:
        '200':
          description: ''
  /integrations:
    post:
      summary: create_integration
      operationId: post-create-integration
      responses:
        '200':
          description: ''
    get:
      summary: list_integrations
      operationId: get-list-integrations
      responses:
        '200':
          description: ''
  /integration/connect:
    post:
      summary: connect_integration
      operationId: post-connect-integration
      responses:
        '200':
          description: ''
  /security/profile-assignment-counts:
    post:
      summary: store_profile_assignment_counts
      operationId: post-store-profile-assignment-counts
      responses:
        '200':
          description: ''
    get:
      summary: list_profile_assignment_counts
      operationId: get-list-profile-assignment-counts
      responses:
        '200':
          description: ''
  /auth/azure/me:
    get:
      summary: azure_me
      operationId: get-azure-me
      responses:
        '200':
          description: ''
  /tasks:
    get:
      summary: list_tasks
      operationId: get-list-tasks
      responses:
        '200':
          description: ''
    post:
      summary: create_task_endpoint
      operationId: post-create-task-endpoint
      responses:
        '200':
          description: ''
  '/users/email/{email}':
    get:
      summary: get_user_by_email
      operationId: get-get-user-by-email
      parameters:
        - name: email
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  /auth/signup:
    post:
      summary: signup
      operationId: post-signup
      responses:
        '200':
          description: ''
  /auth/login:
    post:
      summary: login
      operationId: post-login
      responses:
        '200':
          description: ''
  /users:
    get:
      summary: list_users
      operationId: get-list-users
      responses:
        '200':
          description: ''
    post:
      summary: create_user
      operationId: post-create-user
      responses:
        '200':
          description: ''
  '/integration/scan/{integration_id}':
    post:
      summary: scan_integration
      operationId: post-scan-integration
      parameters:
        - name: integration_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  '/users/{user_id}/login':
    post:
      summary: create_user_login
      operationId: post-create-user-login
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: ''
      responses:
        '200':
          description: ''
  /organizations:
    get:
      summary: list_organizations
      operationId: get-list-organizations
      responses:
        '200':
          description: ''
components:
  securitySchemes:
    apiKeyHeader:
      type: apiKey
      name: Ocp-Apim-Subscription-Key
      in: header
    apiKeyQuery:
      type: apiKey
      name: subscription-key
      in: query
security:
  - apiKeyHeader: [ ]
  - apiKeyQuery: [ ]