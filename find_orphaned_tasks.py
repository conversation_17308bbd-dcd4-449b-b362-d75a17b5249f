#!/usr/bin/env python3
"""
Script to find orphaned tasks (tasks with no parent) in Azure DevOps
"""

import json
import subprocess
import sys

def run_az_command(command):
    """Run Azure CLI command and return JSON output"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            print(f"Error running command: {result.stderr}")
            return None
    except json.JSONDecodeError:
        print(f"Error parsing JSO<PERSON> from command: {command}")
        return None

def get_all_tasks():
    """Get all tasks from the project"""
    command = 'az boards query --wiql "SELECT [System.Id], [System.Title], [System.State], [System.AssignedTo] FROM WorkItems WHERE [System.WorkItemType] = \'Task\' AND [System.State] <> \'Removed\'" --organization "https://dev.azure.com/AtomSec" --project "atomsec_app" --output json'
    return run_az_command(command)

def check_task_relations(task_id):
    """Check if a task has any parent relations"""
    command = f'az boards work-item show --id {task_id} --organization "https://dev.azure.com/AtomSec" --expand links --output json'
    result = run_az_command(command)
    
    if result and 'relations' in result:
        return result['relations'] is None or len(result['relations']) == 0
    return True  # Assume orphaned if we can't determine

def main():
    print("🔍 Finding orphaned tasks in atomsec_app project...")
    print("=" * 60)
    
    # Get all tasks
    tasks_data = get_all_tasks()
    if not tasks_data:
        print("❌ Failed to retrieve tasks")
        return
    
    # The response is a direct array, not wrapped in 'workItems'
    tasks = tasks_data
    print(f"📊 Found {len(tasks)} total tasks")
    
    orphaned_tasks = []
    
    # Check each task for parent relations
    for i, task in enumerate(tasks, 1):
        task_id = task['fields']['System.Id']
        task_title = task['fields']['System.Title']
        task_state = task['fields']['System.State']
        assigned_to = task['fields'].get('System.AssignedTo', {}).get('displayName', 'Unassigned')
        
        print(f"Checking task {i}/{len(tasks)}: {task_id} - {task_title[:50]}...")
        
        if check_task_relations(task_id):
            orphaned_tasks.append({
                'id': task_id,
                'title': task_title,
                'state': task_state,
                'assigned_to': assigned_to
            })
    
    # Display results
    print("\n" + "=" * 60)
    print(f"🎯 ORPHANED TASKS FOUND: {len(orphaned_tasks)}")
    print("=" * 60)
    
    if orphaned_tasks:
        print(f"{'ID':<6} {'State':<12} {'Assigned To':<20} {'Title'}")
        print("-" * 80)
        for task in orphaned_tasks:
            print(f"{task['id']:<6} {task['state']:<12} {task['assigned_to']:<20} {task['title']}")
        
        print(f"\n📋 Summary:")
        print(f"   • Total tasks: {len(tasks)}")
        print(f"   • Orphaned tasks: {len(orphaned_tasks)}")
        print(f"   • Percentage orphaned: {(len(orphaned_tasks)/len(tasks)*100):.1f}%")
        
        # Group by state
        states = {}
        for task in orphaned_tasks:
            state = task['state']
            states[state] = states.get(state, 0) + 1
        
        print(f"\n📊 Orphaned tasks by state:")
        for state, count in states.items():
            print(f"   • {state}: {count}")
            
    else:
        print("✅ No orphaned tasks found! All tasks have parent relationships.")
    
    print("\n💡 Next steps:")
    print("   1. Review each orphaned task to determine if it needs a parent")
    print("   2. Link orphaned tasks to appropriate user stories or features")
    print("   3. Consider if some tasks should remain standalone")
    print("   4. Set up regular reviews to prevent future orphaned tasks")

if __name__ == "__main__":
    main() 