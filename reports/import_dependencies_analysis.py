#!/usr/bin/env python3
"""
Import Dependencies Analysis Script
Analyzes import statements across both SFDC and DB services to document current dependencies
before folder structure reorganization.
"""

import os
import re
import json
from pathlib import Path
from collections import defaultdict, Counter
import ast

class ImportAnalyzer:
    def __init__(self):
        self.import_map = defaultdict(list)
        self.file_imports = {}
        self.circular_imports = []
        self.external_imports = Counter()
        self.internal_imports = Counter()
        self.problematic_imports = []
        
    def analyze_file(self, file_path):
        """Analyze imports in a single Python file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST to get imports
            try:
                tree = ast.parse(content)
                imports = self.extract_imports_from_ast(tree)
            except SyntaxError:
                # Fallback to regex if AST parsing fails
                imports = self.extract_imports_from_regex(content)
            
            self.file_imports[str(file_path)] = imports
            
            # Categorize imports
            for imp in imports:
                if self.is_internal_import(imp, file_path):
                    self.internal_imports[imp] += 1
                else:
                    self.external_imports[imp] += 1
                    
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            
    def extract_imports_from_ast(self, tree):
        """Extract imports using AST parsing"""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ''
                for alias in node.names:
                    if alias.name == '*':
                        imports.append(f"{module}.*")
                    else:
                        imports.append(f"{module}.{alias.name}" if module else alias.name)
                        
        return imports
    
    def extract_imports_from_regex(self, content):
        """Fallback regex-based import extraction"""
        imports = []
        
        # Match import statements
        import_patterns = [
            r'^import\s+([^\s#]+)',
            r'^from\s+([^\s]+)\s+import\s+([^#\n]+)',
        ]
        
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('import ') or line.startswith('from '):
                for pattern in import_patterns:
                    match = re.match(pattern, line)
                    if match:
                        if 'from' in pattern:
                            module, items = match.groups()
                            if '*' in items:
                                imports.append(f"{module}.*")
                            else:
                                for item in items.split(','):
                                    item = item.strip()
                                    imports.append(f"{module}.{item}")
                        else:
                            imports.append(match.group(1))
                        break
                        
        return imports
    
    def is_internal_import(self, import_name, file_path):
        """Determine if import is internal to the project"""
        # Check if import starts with known internal modules
        internal_prefixes = [
            'api', 'shared', 'blueprints', 'task_processor', 'pmd_components',
            'repositories', 'routers', 'src', 'tests', 'scripts', 'config'
        ]
        
        # Also check relative imports
        if import_name.startswith('.'):
            return True
            
        for prefix in internal_prefixes:
            if import_name.startswith(prefix):
                return True
                
        return False
    
    def find_python_files(self, directory):
        """Find all Python files in directory"""
        python_files = []
        for root, dirs, files in os.walk(directory):
            # Skip certain directories
            skip_dirs = {'.git', '__pycache__', '.pytest_cache', 'node_modules', '.venv', 'venv'}
            dirs[:] = [d for d in dirs if d not in skip_dirs]
            
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)
                    
        return python_files
    
    def analyze_service(self, service_path):
        """Analyze all Python files in a service"""
        print(f"Analyzing service: {service_path}")
        python_files = self.find_python_files(service_path)
        
        for file_path in python_files:
            self.analyze_file(file_path)
            
        return len(python_files)
    
    def detect_circular_imports(self):
        """Detect potential circular import issues"""
        # This is a simplified detection - would need more sophisticated analysis for real circular imports
        for file_path, imports in self.file_imports.items():
            file_module = self.path_to_module(file_path)
            for imp in imports:
                if self.is_internal_import(imp, file_path):
                    # Check if the imported module might import back
                    potential_circular = self.check_potential_circular(file_module, imp)
                    if potential_circular:
                        self.circular_imports.append((file_path, imp))
    
    def path_to_module(self, file_path):
        """Convert file path to module name"""
        # Simplified conversion
        path = Path(file_path)
        if 'atomsec-func-sfdc' in str(path):
            relative = path.relative_to(path.parts[path.parts.index('atomsec-func-sfdc')])
        elif 'atomsec-func-db-r' in str(path):
            relative = path.relative_to(path.parts[path.parts.index('atomsec-func-db-r')])
        else:
            return str(path)
            
        return str(relative).replace('/', '.').replace('.py', '')
    
    def check_potential_circular(self, file_module, import_name):
        """Check if there might be a circular import"""
        # Simplified check
        return import_name in file_module or file_module in import_name
    
    def generate_report(self):
        """Generate comprehensive import analysis report"""
        report = {
            'summary': {
                'total_files_analyzed': len(self.file_imports),
                'total_internal_imports': sum(self.internal_imports.values()),
                'total_external_imports': sum(self.external_imports.values()),
                'unique_internal_imports': len(self.internal_imports),
                'unique_external_imports': len(self.external_imports)
            },
            'external_dependencies': dict(self.external_imports.most_common(20)),
            'internal_dependencies': dict(self.internal_imports.most_common(20)),
            'file_imports': self.file_imports,
            'potential_issues': {
                'circular_imports': self.circular_imports,
                'problematic_imports': self.problematic_imports
            }
        }
        
        return report
    
    def analyze_import_patterns(self):
        """Analyze common import patterns"""
        patterns = {
            'relative_imports': 0,
            'absolute_imports': 0,
            'wildcard_imports': 0,
            'deep_imports': 0
        }
        
        for imports in self.file_imports.values():
            for imp in imports:
                if imp.startswith('.'):
                    patterns['relative_imports'] += 1
                else:
                    patterns['absolute_imports'] += 1
                    
                if '.*' in imp or imp.endswith('*'):
                    patterns['wildcard_imports'] += 1
                    
                if imp.count('.') > 3:
                    patterns['deep_imports'] += 1
                    
        return patterns

def main():
    analyzer = ImportAnalyzer()
    
    # Analyze both services
    services = [
        'atomsec-func-sfdc',
        'atomsec-func-db-r'
    ]
    
    total_files = 0
    for service in services:
        if os.path.exists(service):
            files_count = analyzer.analyze_service(service)
            total_files += files_count
            print(f"Analyzed {files_count} Python files in {service}")
        else:
            print(f"Service directory {service} not found")
    
    # Detect potential issues
    analyzer.detect_circular_imports()
    
    # Generate report
    report = analyzer.generate_report()
    patterns = analyzer.analyze_import_patterns()
    report['import_patterns'] = patterns
    
    # Save detailed report
    with open('import_dependencies_detailed.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # Generate human-readable summary
    generate_summary_report(report, total_files)
    
    print(f"\nAnalysis complete! Analyzed {total_files} Python files.")
    print("Reports generated:")
    print("- import_dependencies_detailed.json (detailed data)")
    print("- import_dependencies_summary.md (human-readable)")

def generate_summary_report(report, total_files):
    """Generate human-readable summary report"""
    
    summary_content = f"""# Import Dependencies Analysis Report

## Summary

- **Total Python files analyzed:** {total_files}
- **Total internal imports:** {report['summary']['total_internal_imports']}
- **Total external imports:** {report['summary']['total_external_imports']}
- **Unique internal modules:** {report['summary']['unique_internal_imports']}
- **Unique external packages:** {report['summary']['unique_external_imports']}

## Import Patterns

- **Relative imports:** {report['import_patterns']['relative_imports']}
- **Absolute imports:** {report['import_patterns']['absolute_imports']}
- **Wildcard imports:** {report['import_patterns']['wildcard_imports']}
- **Deep imports (>3 levels):** {report['import_patterns']['deep_imports']}

## Top External Dependencies

"""
    
    for package, count in list(report['external_dependencies'].items())[:10]:
        summary_content += f"- **{package}:** {count} imports\n"
    
    summary_content += "\n## Top Internal Dependencies\n\n"
    
    for module, count in list(report['internal_dependencies'].items())[:10]:
        summary_content += f"- **{module}:** {count} imports\n"
    
    if report['potential_issues']['circular_imports']:
        summary_content += "\n## Potential Circular Imports\n\n"
        for file_path, import_name in report['potential_issues']['circular_imports'][:10]:
            summary_content += f"- **{file_path}** imports **{import_name}**\n"
    
    summary_content += """
## Files That Will Need Import Updates

Based on the planned folder structure changes, the following types of files will need import statement updates:

### Test Files to be Moved
"""
    
    test_files = [f for f in report['file_imports'].keys() if 'test_' in f and not '/tests/' in f]
    for test_file in test_files[:10]:
        summary_content += f"- {test_file}\n"
    
    summary_content += "\n### Utility Scripts to be Moved\n"
    
    script_files = [f for f in report['file_imports'].keys() if any(script in f for script in [
        'check_credentials.py', 'check_policies_result.py', 'create_policies_result.py',
        'create_queues.py', 'setup_local_dev.py', 'simple_test.py', 'task_management.py'
    ])]
    
    for script_file in script_files:
        summary_content += f"- {script_file}\n"
    
    summary_content += """
## Recommendations for Folder Restructuring

1. **Preserve Core Structure:** Most imports are already well-organized in api/, shared/, etc.
2. **Update Test Imports:** Test files being moved will need import path updates
3. **Script Import Updates:** Utility scripts moved to scripts/ will need import updates
4. **Minimal Core Changes:** Main application structure can remain unchanged

## Risk Assessment

- **Low Risk:** Core application imports are well-structured
- **Medium Risk:** Test file imports will need updates
- **Low Risk:** Utility script imports are isolated and easy to update
"""
    
    with open('import_dependencies_summary.md', 'w') as f:
        f.write(summary_content)

if __name__ == "__main__":
    main()