# Import Dependencies Analysis Report

## Summary

- **Total Python files analyzed:** 341
- **Total internal imports:** 1458
- **Total external imports:** 3228
- **Unique internal modules:** 480
- **Unique external packages:** 227

## Import Patterns

- **Relative imports:** 0
- **Absolute imports:** 4686
- **Wildcard imports:** 0
- **Deep imports (>3 levels):** 47

## Top External Dependencies

- **json:** 247 imports
- **logging:** 233 imports
- **datetime.datetime:** 231 imports
- **typing.Any:** 201 imports
- **typing.Dict:** 200 imports
- **typing.Optional:** 183 imports
- **typing.List:** 158 imports
- **os:** 124 imports
- **azure.functions:** 94 imports
- **datetime.timedelta:** 92 imports

## Top Internal Dependencies

- **src.shared.azure_services.is_local_dev:** 55 imports
- **src.shared.cors_middleware.handle_cors_preflight:** 48 imports
- **src.shared.common.is_local_dev:** 46 imports
- **src.shared.data_access.TableStorageRepository:** 38 imports
- **src.shared.azure_services.get_secret:** 34 imports
- **src.shared.data_access.get_table_storage_repository:** 33 imports
- **src.shared.db_service_client.get_db_client:** 32 imports
- **src.shared.data_access.SqlDatabaseRepository:** 32 imports
- **src.shared.config.get_storage_connection_string:** 22 imports
- **src.shared.auth_utils.get_current_user:** 20 imports

## Potential Circular Imports

- **atomsec-func-sfdc/src/shared/auth.py** imports **src.shared.auth_utils.get_jwt_secret**
- **atomsec-func-sfdc/src/shared/auth.py** imports **src.shared.auth_utils.get_jwt_algorithm**
- **atomsec-func-sfdc/src/shared/auth.py** imports **src.shared.auth_utils.decode_token**
- **atomsec-func-sfdc/src/shared/auth.py** imports **src.shared.auth_utils.get_token_from_header**
- **atomsec-func-sfdc/src/shared/auth.py** imports **src.shared.auth_utils.get_current_user**
- **atomsec-func-sfdc/src/shared/auth.py** imports **src.shared.auth_utils.require_auth**

## Files That Will Need Import Updates

Based on the planned folder structure changes, the following types of files will need import statement updates:

### Test Files to be Moved
- atomsec-func-sfdc/test_local_startup.py
- atomsec-func-sfdc/test_queue_message_processor.py
- atomsec-func-sfdc/test_account_management.py
- atomsec-func-sfdc/test_endpoints.py
- atomsec-func-sfdc/test_integration.py
- atomsec-func-sfdc/test_api.py
- atomsec-func-sfdc/test_enhanced_error_handling.py
- atomsec-func-sfdc/test_queue_processing.py
- atomsec-func-sfdc/src/pmd_components/test_pmd_components.py
- atomsec-func-db-r/test_enhanced_queue_manager.py

### Utility Scripts to be Moved
- atomsec-func-sfdc/check_policies_result.py
- atomsec-func-sfdc/setup_local_dev.py
- atomsec-func-sfdc/task_management.py
- atomsec-func-sfdc/simple_test.py
- atomsec-func-sfdc/check_credentials.py
- atomsec-func-sfdc/create_policies_result.py
- atomsec-func-sfdc/create_queues.py

## Recommendations for Folder Restructuring

1. **Preserve Core Structure:** Most imports are already well-organized in api/, shared/, etc.
2. **Update Test Imports:** Test files being moved will need import path updates
3. **Script Import Updates:** Utility scripts moved to scripts/ will need import updates
4. **Minimal Core Changes:** Main application structure can remain unchanged

## Risk Assessment

- **Low Risk:** Core application imports are well-structured
- **Medium Risk:** Test file imports will need updates
- **Low Risk:** Utility script imports are isolated and easy to update
