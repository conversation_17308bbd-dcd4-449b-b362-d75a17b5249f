#!/usr/bin/env python3
"""
File Categorization Audit Script
Categorizes files in both SFDC and DB services by type
"""

import os
import re
from pathlib import Path
from collections import defaultdict
import json

def categorize_file(file_path):
    """Categorize a file based on its path and name"""
    path = Path(file_path)
    name = path.name
    parent_dir = path.parent.name
    full_path = str(path)
    
    # File type categories
    categories = []
    
    # Source code files
    if name.endswith('.py') and not name.startswith('test_') and not name.startswith('example_'):
        if 'src/' in full_path or 'api/' in full_path or 'shared/' in full_path or 'blueprints/' in full_path:
            categories.append('source')
        elif 'scripts/' in full_path:
            categories.append('script')
        elif name == 'function_app.py' or name == 'conftest.py':
            categories.append('source')
        elif parent_dir in ['AccountsApiFunction', 'DirectAccountsFunction', 'DirectRolesFunction', 
                           'IntegrationFunction', 'RolesApiFunction', 'RolesManagementFunction', 
                           'WrapperFunction', 'home']:
            categories.append('deprecated_function')
        else:
            categories.append('source')
    
    # Test files
    elif name.startswith('test_') or 'tests/' in full_path:
        categories.append('test')
        if full_path.count('/') <= 2:  # Root level test files
            categories.append('misplaced_test')
    
    # Example files
    elif name.startswith('example_') or 'examples/' in full_path:
        categories.append('example')
        if full_path.count('/') <= 2:  # Root level example files
            categories.append('misplaced_example')
    
    # Configuration files
    elif (name.endswith('.json') and any(x in name for x in ['config', 'settings', 'local', 'production', 'common'])) or \
         'config/' in full_path or name in ['host.json', 'local.settings.json']:
        categories.append('config')
    
    # Documentation files
    elif name.endswith('.md') or 'docs/' in full_path:
        categories.append('documentation')
        if full_path.count('/') <= 2 and name.endswith('.md'):  # Root level docs
            categories.append('misplaced_documentation')
    
    # Temporary/cache files
    elif any(x in full_path for x in ['__pycache__', 'cache', 'azurite', '__blobstorage__']) or \
         name.endswith('.log') or 'c:azurite' in full_path:
        categories.append('temporary')
    
    # Deprecated/disabled files
    elif '.disabled' in name or '.old' in name or '.backup' in name:
        categories.append('deprecated')
    
    # Result/report files
    elif name.endswith('_results.json') or name.endswith('_report.json') or \
         'results' in name.lower() or 'report' in name.lower():
        categories.append('result_file')
    
    # Infrastructure files
    elif name.endswith('.bicep') or name.endswith('.bicepparam') or 'infrastructure/' in full_path:
        categories.append('infrastructure')
    
    # Pipeline files
    elif name.endswith('.yml') and ('pipeline' in name or 'template' in name):
        categories.append('pipeline')
    
    # Requirements/dependencies
    elif name in ['requirements.txt', 'package.json', 'package-lock.json']:
        categories.append('dependencies')
    
    # Other specific files
    elif name in ['.gitignore', '.funcignore', 'Procfile', 'web.config', 'setup.py']:
        categories.append('project_config')
    
    # Default category
    if not categories:
        categories.append('other')
    
    return categories

def scan_service(service_path):
    """Scan a service directory and categorize all files"""
    results = defaultdict(list)
    
    for root, dirs, files in os.walk(service_path):
        # Skip certain directories
        skip_dirs = ['.git', '.venv', 'node_modules', '.pytest_cache']
        dirs[:] = [d for d in dirs if d not in skip_dirs]
        
        for file in files:
            file_path = os.path.join(root, file)
            relative_path = os.path.relpath(file_path, service_path)
            categories = categorize_file(relative_path)
            
            for category in categories:
                results[category].append(relative_path)
    
    return results

def main():
    services = {
        'SFDC': 'atomsec-func-sfdc',
        'DB': 'atomsec-func-db-r'
    }
    
    all_results = {}
    
    for service_name, service_path in services.items():
        if os.path.exists(service_path):
            print(f"\n=== Scanning {service_name} Service ({service_path}) ===")
            results = scan_service(service_path)
            all_results[service_name] = results
            
            # Print summary
            for category, files in sorted(results.items()):
                print(f"\n{category.upper().replace('_', ' ')} ({len(files)} files):")
                for file in sorted(files)[:10]:  # Show first 10 files
                    print(f"  - {file}")
                if len(files) > 10:
                    print(f"  ... and {len(files) - 10} more files")
        else:
            print(f"Service path {service_path} not found")
    
    # Save detailed results to JSON
    with open('file_categorization_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\nDetailed results saved to file_categorization_results.json")
    
    # Generate summary report
    generate_summary_report(all_results)

def generate_summary_report(all_results):
    """Generate a summary report of file categorization"""
    
    report = []
    report.append("# File Categorization Summary Report")
    report.append("")
    
    for service_name, results in all_results.items():
        report.append(f"## {service_name} Service")
        report.append("")
        
        total_files = sum(len(files) for files in results.values())
        report.append(f"**Total Files:** {total_files}")
        report.append("")
        
        # Category breakdown
        report.append("### File Categories:")
        for category, files in sorted(results.items()):
            report.append(f"- **{category.replace('_', ' ').title()}:** {len(files)} files")
        
        report.append("")
        
        # Problem areas
        problem_categories = ['misplaced_test', 'misplaced_example', 'misplaced_documentation', 
                            'deprecated', 'temporary', 'deprecated_function']
        
        problems = {cat: files for cat, files in results.items() if cat in problem_categories and files}
        
        if problems:
            report.append("### Issues Found:")
            for category, files in problems.items():
                report.append(f"- **{category.replace('_', ' ').title()}:** {len(files)} files need attention")
                for file in files[:5]:  # Show first 5 examples
                    report.append(f"  - {file}")
                if len(files) > 5:
                    report.append(f"  - ... and {len(files) - 5} more")
        
        report.append("")
    
    # Write summary report
    with open('file_categorization_summary.md', 'w') as f:
        f.write('\n'.join(report))
    
    print("Summary report saved to file_categorization_summary.md")

if __name__ == "__main__":
    main()