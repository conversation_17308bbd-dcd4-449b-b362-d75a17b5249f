{"summary": {"total_files_analyzed": 341, "total_internal_imports": 1458, "total_external_imports": 3228, "unique_internal_imports": 480, "unique_external_imports": 227}, "external_dependencies": {"json": 247, "logging": 233, "datetime.datetime": 231, "typing.Any": 201, "typing.Dict": 200, "typing.Optional": 183, "typing.List": 158, "os": 124, "azure.functions": 94, "datetime.timedelta": 92, "uuid": 83, "traceback": 74, "time": 71, "sys": 56, "typing.Tuple": 50, "asyncio": 41, "unittest.mock.patch": 39, "dataclasses.dataclass": 38, "typing.Union": 36, "unittest.mock.MagicMock": 35}, "internal_dependencies": {"src.shared.azure_services.is_local_dev": 55, "src.shared.cors_middleware.handle_cors_preflight": 48, "src.shared.common.is_local_dev": 46, "src.shared.data_access.TableStorageRepository": 38, "src.shared.azure_services.get_secret": 34, "src.shared.data_access.get_table_storage_repository": 33, "src.shared.db_service_client.get_db_client": 32, "src.shared.data_access.SqlDatabaseRepository": 32, "src.shared.config.get_storage_connection_string": 22, "src.shared.auth_utils.get_current_user": 20, "src.shared.task_status_service.get_task_status_service": 18, "src.shared.utils.create_json_response": 17, "src.shared.data_access.BlobStorageRepository": 16, "src.shared.utils.handle_exception": 14, "src.shared.salesforce_utils.execute_salesforce_query": 14, "src.shared.data_access.get_sql_database_repository": 14, "src.shared.background_processor.BackgroundProcessor": 13, "src.shared.queue_message_processor.QueueMessageProcessor": 13, "src.shared.config.get_jwt_config": 13, "src.shared.config.is_local_dev": 12}, "file_imports": {"atomsec-func-sfdc/test_local_startup.py": ["sys", "os", "logging", "src.shared.service_bus_client.get_service_bus_task_client", "src.shared.mock_service_bus.get_mock_service_bus_client", "function_app", "traceback", "traceback"], "atomsec-func-sfdc/run_fixed_router_updated.py": ["logging", "fastapi.FastAPI", "fastapi.middleware.cors.CORSMiddleware", "pydantic.BaseModel", "pydantic.Field", "typing.Optional", "typing.Any", "datetime.datetime", "routers.integration_router.router"], "atomsec-func-sfdc/service_bus_processor.py": ["logging", "json", "azure.functions", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.background_processor.BackgroundProcessor", "src.shared.background_processor.TASK_TYPE_METADATA_EXTRACTION", "src.shared.background_processor.TASK_TYPE_HEALTH_CHECK", "src.shared.background_processor.TASK_TYPE_OVERVIEW", "src.shared.background_processor.TASK_TYPE_PROFILES", "src.shared.background_processor.TASK_TYPE_SFDC_AUTHENTICATE", "src.shared.background_processor.TASK_STATUS_RUNNING", "src.shared.background_processor.TASK_STATUS_FAILED", "src.shared.utils.create_json_response", "src.shared.cors_middleware.cors_middleware", "task_processor.process_metadata_extraction_task", "task_processor.process_health_check_task", "task_processor.process_overview_task", "task_processor.process_profiles_task", "task_processor.process_sfdc_authenticate_task", "os"], "atomsec-func-sfdc/conftest.py": ["os", "sys", "logging"], "atomsec-func-sfdc/test_queue_message_processor.py": ["json", "uuid", "datetime.datetime", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "src.shared.queue_message_processor.QueueMessageProcessor", "src.shared.queue_message_processor.get_queue_message_processor"], "atomsec-func-sfdc/check_policies_result.py": ["os", "sys", "logging", "azure.data.tables.TableServiceClient"], "atomsec-func-sfdc/test_account_management.py": ["os", "sys", "json", "logging", "requests", "datetime.datetime", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_table_storage_repository", "random", "datetime.datetime", "src.shared.data_access.get_table_storage_repository"], "atomsec-func-sfdc/test_endpoints.py": ["requests", "json", "sys", "time"], "atomsec-func-sfdc/__init__.py": [], "atomsec-func-sfdc/akv_test.py": ["azure.identity.DefaultAzureCredential", "azure.keyvault.secrets.SecretClient"], "atomsec-func-sfdc/function_app.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "src.api.user_endpoints.bp", "src.api.account_endpoints.bp", "src.api.organization_endpoints.bp", "src.api.integration_endpoints.bp", "src.api.security_endpoints.bp", "src.api.task_endpoints.bp", "src.api.enhanced_task_endpoints.bp", "src.api.auth_endpoints.bp", "src.api.policy_endpoints.bp", "src.api.cors_handler.bp", "src.api.user_profile_endpoints.bp", "src.api.key_vault_endpoints.bp", "src.api.pmd_endpoints.bp", "service_bus_processor.bp", "src.shared.security_middleware.internal_service_endpoint", "src.shared.security_middleware.internal_service_endpoint", "src.shared.security_middleware.internal_service_endpoint", "src.shared.db_service_client.get_db_client", "src.shared.azure_services.is_local_dev", "task_processor.process_sfdc_authenticate_task", "task_processor.process_metadata_extraction_task", "task_processor.process_health_check_task", "task_processor.process_profiles_task", "task_processor.process_overview_task", "task_processor.process_mfa_enforcement_task", "task_processor.process_device_activation_task", "task_processor.process_login_ip_ranges_task", "task_processor.process_login_hours_task", "task_processor.process_session_timeout_task", "task_processor.process_api_whitelisting_task", "task_processor.process_password_policy_task", "task_processor.process_pmd_task", "task_processor.check_task_dependencies_and_wait", "task_processor.TASK_TYPE_MAPPING", "src.shared.background_processor.BackgroundProcessor", "src.shared.background_processor.TASK_TYPE_SFDC_AUTHENTICATE", "src.shared.background_processor.TASK_TYPE_METADATA_EXTRACTION", "src.shared.background_processor.TASK_TYPE_HEALTH_CHECK", "src.shared.background_processor.TASK_TYPE_PROFILES", "src.shared.background_processor.TASK_TYPE_OVERVIEW", "src.shared.background_processor.TASK_TYPE_MFA_ENFORCEMENT", "src.shared.background_processor.TASK_TYPE_DEVICE_ACTIVATION", "src.shared.background_processor.TASK_TYPE_LOGIN_IP_RANGES", "src.shared.background_processor.TASK_TYPE_LOGIN_HOURS", "src.shared.background_processor.TASK_TYPE_SESSION_TIMEOUT", "src.shared.background_processor.TASK_TYPE_API_WHITELISTING", "src.shared.background_processor.TASK_TYPE_PASSWORD_POLICY", "src.shared.background_processor.TASK_TYPE_PMD_APEX_SECURITY", "src.shared.background_processor.TASK_STATUS_FAILED", "uuid", "src.shared.azure_services.is_local_dev", "src.shared.db_service_client.get_db_client", "src.shared.service_bus_client.get_service_bus_client", "src.shared.azure_services.get_key_vault_client", "src.shared.sfdc_service_client.get_sfdc_client", "src.shared.enhanced_parameter_validator.validate_sfdc_request", "uuid", "uuid", "traceback", "task_processor.process_sfdc_authenticate_task", "task_processor.process_metadata_extraction_task", "task_processor.process_health_check_task", "task_processor.process_profiles_task", "task_processor.process_profiles_permission_sets_task", "task_processor.process_permission_sets_task", "task_processor.process_overview_task", "task_processor.process_data_export_task", "task_processor.process_report_generation_task", "task_processor.process_scheduled_scan_task", "task_processor.process_notification_task", "task_processor.process_mfa_enforcement_task", "task_processor.process_device_activation_task", "task_processor.process_login_ip_ranges_task", "task_processor.process_login_hours_task", "task_processor.process_session_timeout_task", "task_processor.process_api_whitelisting_task", "task_processor.process_password_policy_task", "task_processor.process_pmd_task", "task_processor.check_task_dependencies_and_wait", "task_processor.TASK_TYPE_MAPPING", "src.shared.background_processor.BackgroundProcessor", "src.shared.background_processor.TASK_STATUS_FAILED", "uuid", "uuid"], "atomsec-func-sfdc/setup_local_dev.py": ["os", "subprocess", "sys", "time", "json", "platform", "shutil", "socket"], "atomsec-func-sfdc/function_app_production.py": ["azure.functions", "logging", "json", "src.shared.background_processor.BackgroundProcessor", "src.shared.constants.TASK_STATUS_RUNNING", "src.shared.constants.TASK_STATUS_COMPLETED", "src.shared.constants.TASK_STATUS_FAILED", "src.shared.constants.TASK_TYPE_HEALTH_CHECK", "src.shared.constants.TASK_TYPE_METADATA_EXTRACTION", "src.shared.constants.TASK_TYPE_NOTIFICATION", "src.shared.constants.TASK_TYPE_SFDC_AUTHENTICATE", "src.shared.constants.TASK_TYPE_PROFILES_PERMISSION_SETS", "task_processor.process_health_check_task", "task_processor.process_metadata_extraction_task", "task_processor.process_notification_task", "task_processor.process_sfdc_authenticate_task", "task_processor.process_profiles_permission_sets_task", "task_processor.main"], "atomsec-func-sfdc/setup.py": ["setuptools.setup", "setuptools.find_packages"], "atomsec-func-sfdc/task_management.py": ["logging", "json", "azure.functions", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "src.shared.background_processor.BackgroundProcessor", "src.shared.utils.create_json_response", "src.shared.cors_middleware.cors_middleware", "src.shared.auth.get_current_user"], "atomsec-func-sfdc/simple_test.py": ["json", "uuid", "datetime.datetime", "traceback"], "atomsec-func-sfdc/account_management_asgi.py": ["logging", "fastapi.FastAPI", "fastapi.HTTPException", "fastapi.status", "fastapi.Request", "fastapi.middleware.cors.CORSMiddleware", "pydantic.BaseModel", "pydantic.Field", "typing.Optional", "typing.Any", "typing.List", "typing.Dict", "datetime.datetime", "datetime.timezone", "random", "src.shared.config.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.utils.create_json_response", "src.shared.auth_utils.get_token_from_header", "src.shared.auth_utils.decode_token", "src.shared.user_repository.is_user_admin", "src.shared.user_repository.get_user_account_by_email", "src.shared.user_repository.is_user_admin", "src.shared.user_repository.is_user_admin", "uuid", "src.shared.auth_utils.decode_token", "src.shared.db_service_client.get_db_client", "src.shared.auth_utils.decode_token", "src.shared.auth_utils.decode_token"], "atomsec-func-sfdc/check_credentials.py": ["azure.data.tables.TableServiceClient", "src.shared.config.get_storage_connection_string"], "atomsec-func-sfdc/test_integration.py": ["sys", "uuid", "json", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "blueprints.security_analysis.load_best_practices", "blueprints.security_analysis.load_best_practices"], "atomsec-func-sfdc/test_api.py": ["requests", "json", "logging"], "atomsec-func-sfdc/test_enhanced_error_handling.py": ["pytest", "time", "datetime.datetime", "src.shared.error_handler.get_enhanced_error_handler", "src.shared.error_handler.get_error_response_builder", "src.shared.error_handler.ErrorResponse", "src.shared.error_handler.ErrorCategory", "src.shared.error_handler.RetryConfig", "src.shared.error_handler.RetryStrategy", "src.shared.task_sequence_failure_handler.get_task_sequence_failure_handler", "src.shared.task_sequence_failure_handler.TaskSequenceState", "src.shared.task_sequence_failure_handler.TaskState", "src.shared.task_sequence_failure_handler.FailureStrategy", "traceback"], "atomsec-func-sfdc/create_policies_result.py": ["os", "sys", "logging", "signal", "datetime.datetime", "azure.data.tables.TableServiceClient"], "atomsec-func-sfdc/create_queues.py": ["os", "sys", "logging", "azure.storage.queue.QueueServiceClient", "azure.storage.queue.QueueClient", "json"], "atomsec-func-sfdc/test_queue_processing.py": ["os", "json", "time", "uuid", "datetime.datetime", "azure.storage.queue.QueueServiceClient"], "atomsec-func-sfdc/routers/__init__.py": [], "atomsec-func-sfdc/routers/integration_router.py": ["logging", "json", "datetime.datetime", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Union", "fastapi.APIRouter", "fastapi.Depends", "fastapi.HTTPException", "fastapi.Query", "fastapi.Path", "fastapi.Request", "fastapi.responses.JSONResponse", "pydantic.BaseModel", "pydantic.Field", "src.shared.config.is_local_dev", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.shared.auth_utils.require_auth", "src.shared.auth_utils.get_current_user", "src.shared.db_service_client.get_db_client", "src.shared.background_processor.BackgroundProcessor", "src.shared.background_processor.TASK_TYPE_OVERVIEW", "src.shared.background_processor.TASK_TYPE_HEALTH_CHECK", "src.shared.background_processor.TASK_TYPE_PROFILES", "traceback", "traceback", "traceback", "traceback"], "atomsec-func-sfdc/IntegrationFunction/__init__.py": ["azure.functions", "logging", "json", "re", "function_app.app", "blueprints.integration.test_connection", "blueprints.integration.connect_integration"], "atomsec-func-sfdc/home/<USER>": ["azure.functions", "logging", "function_app.app"], "atomsec-func-sfdc/DirectAccountsFunction/__init__.py": ["logging", "azure.functions", "json", "datetime.datetime", "datetime.timezone"], "atomsec-func-sfdc/assets_project_maintenance/ConvertsProfilePermissionMetadataFromCSVtoXML.py": ["csv", "xml.etree.ElementTree", "xml.dom.minidom", "<PERSON><PERSON><PERSON><PERSON>", "sys", "os"], "atomsec-func-sfdc/RolesApiFunction/__init__.py": ["logging", "azure.functions", "json", "datetime.datetime", "datetime.timezone"], "atomsec-func-sfdc/tests/test_requirements_verification.py": ["test_helpers", "json", "uuid", "datetime.datetime", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "src.shared.queue_message_processor.QueueMessageProcessor", "src.shared.queue_message_processor.QueueMessageProcessor", "src.shared.queue_message_processor.QueueMessageProcessor", "src.shared.queue_message_processor.QueueMessageProcessor", "traceback"], "atomsec-func-sfdc/tests/conftest.py": ["os", "sys", "pytest", "asyncio", "logging", "unittest.mock.patch", "logging", "tests.mock_table_storage.MockTableStorageRepository", "tests.mock_table_storage.reset_tables", "shared.data_access"], "atomsec-func-sfdc/tests/test_queue_message_processor.py": [], "atomsec-func-sfdc/tests/test_user_repository.py": ["unittest", "os", "sys", "datetime.date", "unittest.mock.patch", "unittest.mock.MagicMock", "src.shared.user_repository.create_user", "src.shared.user_repository.get_user_account_by_email", "src.shared.user_repository.authenticate_user", "src.shared.user_repository.hash_password", "src.shared.user_repository.verify_password", "src.shared.user_repository.generate_salt"], "atomsec-func-sfdc/tests/test_poison_queue_functionality.py": ["test_helpers", "json", "uuid", "time", "datetime.datetime", "azure.storage.queue.QueueClient", "unittest.mock.patch", "src.shared.queue_message_processor.QueueMessageProcessor", "traceback", "traceback"], "atomsec-func-sfdc/tests/mock_table_storage.py": ["logging", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "datetime.datetime"], "atomsec-func-sfdc/tests/test_auth.py": ["unittest", "json", "secrets", "unittest.mock.patch", "unittest.mock.MagicMock", "blueprints.auth.bp", "blueprints.auth.refresh_token", "blueprints.auth.get_user", "src.shared.user_repository.hash_password", "src.shared.user_repository.hash_password", "azure.functions", "tests.mock_azure_functions.HttpRequest", "tests.mock_azure_functions.HttpResponse", "tests.mock_azure_functions.Blueprint", "tests.mock_azure_functions.FunctionApp", "src.shared.user_repository.create_user", "uuid"], "atomsec-func-sfdc/tests/test_deployment_verification.py": ["unittest", "requests", "os"], "atomsec-func-sfdc/tests/test_queue_integration.py": ["unittest", "json", "uuid", "logging", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "typing.Dict", "typing.Any", "typing.List", "src.shared.task_coordination_service.get_task_coordination_service", "src.shared.task_coordination_service.TaskCoordinationService", "src.shared.execution_context_manager.get_execution_context_manager", "src.shared.execution_context_manager.ExecutionContextManager", "src.shared.task_status_service.get_task_status_service", "src.shared.task_status_service.TaskStatusService", "src.shared.queue_manager.get_queue_manager", "src.shared.queue_manager.QueueManager", "src.shared.error_handler.get_task_error_handler", "src.shared.error_handler.get_queue_error_handler", "src.shared.monitoring.get_task_monitor", "src.shared.monitoring.get_performance_monitor", "src.shared.db_service_client.get_db_client", "src.shared.db_service_client.DatabaseServiceClient", "threading", "time", "json"], "atomsec-func-sfdc/tests/test_fastapi.py": ["pytest", "fastapi.testclient.TestClient", "app.app"], "atomsec-func-sfdc/tests/__init__.py": [], "atomsec-func-sfdc/tests/mock_azure_functions.py": ["json"], "atomsec-func-sfdc/tests/test_dummy.py": [], "atomsec-func-sfdc/tests/test_auth_fixed.py": ["unittest", "json", "secrets", "unittest.mock.patch", "unittest.mock.MagicMock", "blueprints.auth.bp", "blueprints.auth.refresh_token", "blueprints.auth.get_user", "src.shared.user_repository.hash_password", "src.shared.user_repository.hash_password", "azure.functions", "tests.mock_azure_functions.HttpRequest", "tests.mock_azure_functions.HttpResponse", "tests.mock_azure_functions.Blueprint", "tests.mock_azure_functions.FunctionApp", "src.shared.user_repository.create_user", "uuid"], "atomsec-func-sfdc/tests/test_poison_queue_client.py": ["test_helpers", "json", "azure.storage.queue.QueueClient", "unittest.mock.patch", "traceback", "src.shared.queue_message_processor.QueueMessageProcessor", "src.shared.queue_message_processor.POISON_QUEUES", "traceback"], "atomsec-func-sfdc/tests/test_deployment_simple.py": ["os", "sys", "pytest", "importlib.util", "pathlib.Path", "json", "sys"], "atomsec-func-sfdc/tests/test_fastapi_integration.py": ["pytest", "requests", "json", "tests.make_requests.make_request", "random"], "atomsec-func-sfdc/tests/test_my_work.py": ["tests.make_requests.make_request", "pytest"], "atomsec-func-sfdc/tests/test_azurite_connection.py": ["json", "uuid", "datetime.datetime", "azure.storage.queue.QueueServiceClient", "azure.storage.queue.QueueClient", "azure.core.exceptions.ResourceExistsError", "azure.core.exceptions.ResourceNotFoundError", "sys", "os", "unittest.mock.patch", "traceback", "src.shared.queue_message_processor.QueueMessageProcessor", "traceback"], "atomsec-func-sfdc/tests/test_simple_queue_processor.py": ["json", "uuid", "datetime.datetime", "unittest.mock.Mock", "unittest.mock.patch", "src.shared.queue_message_processor.QueueMessageProcessor", "src.shared.queue_message_processor.get_queue_message_processor", "traceback"], "atomsec-func-sfdc/tests/test_comprehensive_queue_processor.py": ["json", "uuid", "datetime.datetime", "unittest.mock.Mock", "unittest.mock.patch", "src.shared.queue_message_processor.QueueMessageProcessor", "traceback", "src.shared.queue_message_processor.QueueMessageProcessor"], "atomsec-func-sfdc/tests/test_helpers.py": ["sys", "os"], "atomsec-func-sfdc/tests/test_WrapperFunction.py": ["tests.make_requests.make_request", "pytest"], "atomsec-func-sfdc/tests/run_unit_tests.py": ["os", "sys", "subprocess", "<PERSON><PERSON><PERSON><PERSON>", "json", "pathlib.Path"], "atomsec-func-sfdc/tests/make_requests.py": ["requests", "os", "json"], "atomsec-func-sfdc/tests/test_poison_queue_scenario.py": ["test_helpers", "json", "uuid", "time", "datetime.datetime", "azure.storage.queue.QueueClient", "unittest.mock.patch", "unittest.mock.Mock", "src.shared.queue_message_processor.QueueMessageProcessor", "src.shared.queue_message_processor.MAX_RETRY_COUNT", "traceback"], "atomsec-func-sfdc/tests/unit/test_monitoring_dashboards.py": ["pytest", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "src.shared.monitoring_dashboards.MonitoringDashboardService", "src.shared.monitoring_dashboards.DashboardConfig", "src.shared.monitoring_dashboards.MetricDefinition", "src.shared.monitoring_dashboards.AlertRule", "src.shared.monitoring_dashboards.DashboardCreationError"], "atomsec-func-sfdc/tests/unit/test_performance_optimizer.py": ["pytest", "asyncio", "time", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.AsyncMock", "unittest.mock.MagicMock", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "src.shared.performance_optimizer.PerformanceOptimizer", "src.shared.performance_optimizer.ConnectionPoolManager", "src.shared.performance_optimizer.CacheManager", "src.shared.performance_optimizer.get_performance_optimizer", "src.shared.performance_optimizer.PerformanceMonitor", "src.shared.performance_optimizer.ResourceManager"], "atomsec-func-sfdc/tests/unit/test_monitoring_service.py": ["pytest", "json", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "logging", "src.shared.monitoring.StructuredLogger", "src.shared.monitoring.MetricsCollector", "src.shared.monitoring.PerformanceMonitor", "src.shared.monitoring.HealthCheckService", "src.shared.monitoring.get_monitoring_service", "src.shared.monitoring.MonitoringService", "time", "time", "time"], "atomsec-func-sfdc/tests/unit/test_task_sequence_configuration.py": ["pytest", "unittest.mock.patch", "unittest.mock.Mock", "src.shared.task_sequence_configuration.TaskSequenceConfiguration", "src.shared.task_sequence_configuration.TaskSequenceValidator", "tests.unit.mocks.mock_database.MockDatabaseConnection"], "atomsec-func-sfdc/tests/unit/__init__.py": [], "atomsec-func-sfdc/tests/unit/test_feature_flag_service.py": ["pytest", "json", "unittest.mock.patch", "unittest.mock.Mock", "unittest.mock.mock_open", "src.shared.feature_flag_service.FeatureFlagService", "tests.unit.mocks.mock_key_vault.MockKeyVaultService"], "atomsec-func-sfdc/tests/unit/test_enhanced_configuration_manager.py": ["pytest", "os", "unittest.mock.patch", "unittest.mock.Mock", "unittest.mock.MagicMock", "src.shared.enhanced_configuration_manager.EnhancedConfigurationManager", "tests.unit.mocks.mock_key_vault.MockKeyVaultService", "tests.unit.mocks.mock_key_vault.MockAzureKeyVaultClient"], "atomsec-func-sfdc/tests/unit/test_security_middleware.py": ["pytest", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.AsyncMock", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "jwt", "src.shared.security_middleware.SecurityMiddleware", "src.shared.security_middleware.SecurityConfig", "src.shared.security_middleware.RateLimitExceeded", "src.shared.security_middleware.AuthenticationError"], "atomsec-func-sfdc/tests/unit/test_enhanced_auth_service.py": ["pytest", "jwt", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "json", "src.shared.enhanced_auth_service.EnhancedAuthService", "src.shared.enhanced_auth_service.get_enhanced_auth_service", "src.shared.enhanced_auth_service.AuthenticationError", "src.shared.enhanced_auth_service.RateLimitExceededError", "src.shared.enhanced_auth_service.SalesforceAuthenticationError", "src.shared.enhanced_auth_service.TokenValidationResult"], "atomsec-func-sfdc/tests/unit/test_enhanced_parameter_validator.py": ["pytest", "unittest.mock.Mock", "unittest.mock.patch", "json", "datetime.datetime", "src.shared.enhanced_parameter_validator.EnhancedParameterValidator", "src.shared.enhanced_parameter_validator.ValidationResult", "src.shared.enhanced_parameter_validator.ValidationError"], "atomsec-func-sfdc/tests/unit/test_error_handler.py": ["pytest", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "enum.Enum", "src.shared.error_handler.ErrorHandler", "src.shared.error_handler.CircuitBreaker", "src.shared.error_handler.RetryConfig", "src.shared.error_handler.ErrorCategory", "src.shared.error_handler.CircuitBreakerState", "src.shared.error_handler.ErrorResponse", "src.shared.error_handler.CircuitBreakerOpenError"], "atomsec-func-sfdc/tests/unit/mocks/mock_performance_optimizer.py": ["unittest.mock.Mock", "unittest.mock.MagicMock", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "time", "asyncio", "fnmatch"], "atomsec-func-sfdc/tests/unit/mocks/mock_service_bus.py": ["logging", "typing.Dict", "typing.List", "typing.Optional", "typing.Any", "unittest.mock.Mock", "datetime.datetime", "json"], "atomsec-func-sfdc/tests/unit/mocks/mock_salesforce_client.py": ["unittest.mock.Mock", "unittest.mock.MagicMock", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>"], "atomsec-func-sfdc/tests/unit/mocks/__init__.py": [], "atomsec-func-sfdc/tests/unit/mocks/mock_database.py": ["logging", "typing.Dict", "typing.List", "typing.Optional", "typing.Any", "unittest.mock.Mock", "datetime.datetime"], "atomsec-func-sfdc/tests/unit/mocks/mock_key_vault.py": ["logging", "typing.Dict", "typing.Optional", "typing.Any", "unittest.mock.Mock"], "atomsec-func-sfdc/tests/unit/mocks/mock_azure_services.py": ["unittest.mock.Mock", "unittest.mock.MagicMock", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>"], "atomsec-func-sfdc/tests/security/run_security_tests.py": ["os", "sys", "subprocess", "json", "time", "pathlib.Path", "datetime.datetime", "<PERSON><PERSON><PERSON><PERSON>", "re", "re"], "atomsec-func-sfdc/tests/security/test_authentication_security.py": ["pytest", "jwt", "json", "time", "unittest.mock.Mock", "unittest.mock.patch", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "azure.functions", "src.shared.enhanced_auth_service.EnhancedAuthService", "src.shared.enhanced_auth_service.AuthenticationError", "src.shared.enhanced_auth_service.RateLimitExceededError", "src.shared.security_middleware.SecurityMiddleware", "src.shared.security_middleware.get_security_middleware", "uuid"], "atomsec-func-sfdc/tests/security/test_injection_attacks.py": ["pytest", "json", "unittest.mock.Mock", "unittest.mock.patch", "azure.functions", "src.shared.enhanced_parameter_validator.get_enhanced_parameter_validator", "src.shared.enhanced_parameter_validator.ParameterValidationError", "src.shared.enhanced_parameter_validator.SecurityValidationError"], "atomsec-func-sfdc/tests/integration/validate_service_integration_points.py": ["os", "sys", "json", "asyncio", "logging", "aiohttp", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "typing.<PERSON>", "dataclasses.dataclass", "pathlib.Path", "time"], "atomsec-func-sfdc/tests/integration/test_enhanced_integration_simple.py": ["asyncio", "sys", "os", "uuid", "datetime.datetime", "src.shared.enhanced_configuration_manager.EnhancedConfigurationManager", "src.shared.enhanced_auth_service.EnhancedAuthService", "src.shared.enhanced_parameter_validator.EnhancedParameterValidator", "src.shared.execution_log_coordination_service.ExecutionLogCoordinationService", "src.shared.task_sequence_configuration.TaskSequenceConfiguration", "src.shared.feature_flag_service.FeatureFlagService", "src.shared.monitoring_dashboards.MonitoringDashboardsService", "src.shared.error_handler.EnhancedErrorHandler", "src.shared.execution_log_coordination_service.ExecutionLogCoordinationService", "src.shared.enhanced_parameter_validator.EnhancedParameterValidator", "src.shared.task_sequence_configuration.TaskSequenceConfiguration", "src.shared.enhanced_configuration_manager.EnhancedConfigurationManager", "src.shared.feature_flag_service.FeatureFlagService", "src.shared.error_handler.EnhancedErrorHandler", "traceback", "traceback"], "atomsec-func-sfdc/tests/integration/validate_monitoring_alerting.py": ["sys", "os", "json", "asyncio", "time", "pathlib.Path", "typing.Dict", "typing.Any", "typing.List", "typing.<PERSON>", "typing.Optional", "src.shared.monitoring_dashboards.MonitoringDashboardsService", "src.shared.monitoring_dashboards.MonitoringDashboardsService"], "atomsec-func-sfdc/tests/integration/test_deployment_integration.py": ["pytest", "requests", "json", "time", "os", "typing.Dict", "typing.Any", "typing.Optional", "urllib.parse.urljoin", "concurrent.futures", "concurrent.futures", "statistics"], "atomsec-func-sfdc/tests/integration/__init__.py": [], "atomsec-func-sfdc/tests/integration/validate_integration_configuration.py": ["os", "sys", "json", "logging", "re", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "pathlib.Path"], "atomsec-func-sfdc/tests/integration/test_execution_log_coordination.py": ["pytest", "asyncio", "uuid", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.AsyncMock", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "json", "src.shared.execution_log_coordination_service.ExecutionLogCoordinationService", "src.shared.execution_log_coordination_service.get_execution_log_coordination_service", "src.shared.execution_log_coordination_service.ExecutionLogError", "src.shared.task_sequence_configuration.get_task_sequence_configuration", "asyncio"], "atomsec-func-sfdc/tests/integration/validate_enhanced_integration.py": ["sys", "os", "json", "asyncio", "pathlib.Path", "typing.Dict", "typing.Any", "typing.List", "typing.<PERSON>", "api.enhanced_task_endpoints.enhanced_task_endpoints"], "atomsec-func-sfdc/tests/integration/test_sequential_task_processing.py": ["pytest", "asyncio", "json", "uuid", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.AsyncMock", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "azure.functions", "src.shared.execution_log_coordination_service.ExecutionLogCoordinationService", "src.shared.execution_log_coordination_service.get_execution_log_coordination_service", "src.shared.task_sequence_configuration.TaskSequenceConfiguration", "src.shared.task_sequence_configuration.get_task_sequence_configuration", "src.shared.enhanced_parameter_validator.get_enhanced_parameter_validator", "src.shared.enhanced_parameter_validator.ParameterValidationError"], "atomsec-func-sfdc/tests/integration/validate_deployment_pipeline.py": ["sys", "os", "json", "yaml", "subprocess", "pathlib.Path", "typing.Dict", "typing.Any", "typing.List", "typing.<PERSON>", "typing.Optional"], "atomsec-func-sfdc/tests/integration/test_enhanced_components_integration.py": ["pytest", "asyncio", "json", "uuid", "time", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.AsyncMock", "unittest.mock.MagicMock", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "azure.functions", "typing.Dict", "typing.Any", "typing.List", "src.shared.enhanced_auth_service.get_enhanced_auth_service", "src.shared.enhanced_configuration_manager.get_enhanced_configuration_manager", "src.shared.enhanced_parameter_validator.get_enhanced_parameter_validator", "src.shared.execution_log_coordination_service.get_execution_log_coordination_service", "src.shared.task_sequence_configuration.get_task_sequence_configuration", "src.shared.feature_flag_service.get_feature_flag_service", "src.shared.monitoring_dashboards.get_monitoring_dashboards_service", "src.shared.error_handler.get_enhanced_error_handler", "src.shared.task_sequence_failure_handler.get_task_sequence_failure_handler", "src.shared.security_middleware.get_security_middleware", "src.shared.performance_optimization_framework.get_performance_optimizer", "src.shared.advanced_caching_service.get_advanced_caching_service", "src.shared.auto_scaling_service.get_auto_scaling_service", "api.enhanced_task_endpoints.enhanced_task_endpoints", "api.task_endpoints.task_endpoints"], "atomsec-func-sfdc/tests/architecture/validate_microservices_compliance.py": ["os", "sys", "json", "logging", "importlib.util", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "pathlib.Path", "re"], "atomsec-func-sfdc/tests/fixtures/__init__.py": [], "atomsec-func-sfdc/tests/performance/locustfile.py": ["json", "random", "time", "uuid", "locust.HttpUser", "locust.task", "locust.between", "locust.events"], "atomsec-func-sfdc/tests/performance/test_load_testing.py": ["pytest", "asyncio", "time", "statistics", "concurrent.futures", "unittest.mock.Mock", "unittest.mock.patch", "json", "uuid", "typing.List", "typing.Dict", "typing.Any", "azure.functions", "src.shared.performance_optimizer.get_performance_optimizer", "src.shared.monitoring.get_monitoring_service", "psutil", "psutil"], "atomsec-func-sfdc/tests/performance/comprehensive_load_test.py": ["locust.HttpUser", "locust.task", "locust.between", "locust.events", "json", "random", "time", "logging"], "atomsec-func-sfdc/AccountsApiFunction/__init__.py": ["logging", "azure.functions", "json", "datetime.datetime", "datetime.timezone", "random"], "atomsec-func-sfdc/DirectRolesFunction/__init__.py": ["logging", "azure.functions", "json", "datetime.datetime", "datetime.timezone"], "atomsec-func-sfdc/examples/salesforce_client_example.py": ["logging", "os", "sys", "json", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.salesforce_client.SalesforceClient", "src.shared.salesforce_utils.get_salesforce_client", "src.shared.salesforce_utils.execute_salesforce_query", "src.shared.salesforce_utils.execute_salesforce_tooling_query", "src.shared.salesforce_utils.get_salesforce_access_token", "src.shared.salesforce_utils.test_salesforce_connection"], "atomsec-func-sfdc/examples/performance_optimization_example.py": ["time", "logging", "typing.Dict", "typing.Any", "typing.List", "src.shared.advanced_caching_service.get_cache_manager", "src.shared.advanced_caching_service.cache_decorator", "src.shared.auto_scaling_service.get_auto_scaling_service", "src.shared.auto_scaling_service.ScalingTrigger", "src.shared.performance_optimization_framework.get_performance_framework", "src.shared.cache_performance_analytics.get_cache_analytics", "src.shared.cache_performance_analytics.MetricType", "json"], "atomsec-func-sfdc/examples/configuration_management_example.py": ["os", "logging", "datetime.datetime", "typing.Dict", "typing.Any", "src.shared.enhanced_configuration_manager.get_configuration_manager", "src.shared.enhanced_configuration_manager.get_config", "src.shared.enhanced_configuration_manager.get_typed_config", "src.shared.feature_flag_service.get_feature_flag_service", "src.shared.feature_flag_service.is_feature_enabled", "src.shared.feature_flag_service.UserContext", "src.shared.task_sequence_configuration.get_task_sequence_configuration", "src.shared.task_sequence_configuration.TaskType", "src.shared.execution_log_coordination_service.get_execution_log_coordination_service", "traceback"], "atomsec-func-sfdc/scripts/smoke_tests.py": ["sys", "json", "time", "urllib.request", "urllib.error", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "dataclasses.dataclass", "datetime.datetime"], "atomsec-func-sfdc/scripts/queue_management_utility.py": ["json", "uuid", "<PERSON><PERSON><PERSON><PERSON>", "datetime.datetime", "azure.storage.queue.QueueServiceClient", "azure.storage.queue.QueueClient", "azure.core.exceptions.ResourceNotFoundError"], "atomsec-func-sfdc/scripts/deploy_queue_based_processing.py": ["os", "sys", "json", "<PERSON><PERSON><PERSON><PERSON>", "logging", "subprocess", "datetime.datetime", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "requests", "src.shared.queue_manager.PRIORITY_QUEUES", "src.shared.queue_manager.POISON_QUEUES", "src.shared.db_service_client.get_db_client", "src.shared.queue_manager.get_queue_manager"], "atomsec-func-sfdc/scripts/clear_poison_queue.py": ["os", "json", "azure.storage.queue.QueueServiceClient"], "atomsec-func-sfdc/scripts/generate_certificate.py": ["os", "cryptography.x509", "cryptography.x509.oid.NameOID", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.primitives.asymmetric.rsa", "cryptography.hazmat.primitives.serialization.Encoding", "cryptography.hazmat.primitives.serialization.PrivateFormat", "cryptography.hazmat.primitives.serialization.NoEncryption", "cryptography.hazmat.primitives.serialization.pkcs12", "cryptography.hazmat.primitives.serialization.BestAvailableEncryption", "datetime"], "atomsec-func-sfdc/scripts/generate_function_json.py": ["os", "json", "re", "sys"], "atomsec-func-sfdc/scripts/canary_deployment.py": ["sys", "json", "time", "subprocess", "os", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "datetime.datetime", "<PERSON><PERSON><PERSON><PERSON>", "urllib.request", "urllib.error"], "atomsec-func-sfdc/scripts/demo_queue_processor_with_azurite.py": ["json", "uuid", "time", "datetime.datetime", "azure.storage.queue.QueueClient", "unittest.mock.patch", "src.shared.queue_message_processor.QueueMessageProcessor", "traceback", "traceback"], "atomsec-func-sfdc/scripts/rollback_deployment.py": ["sys", "json", "time", "subprocess", "os", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "datetime.datetime", "<PERSON><PERSON><PERSON><PERSON>", "urllib.request"], "atomsec-func-sfdc/scripts/check_queues.py": ["os", "azure.storage.queue.QueueServiceClient"], "atomsec-func-sfdc/scripts/check_azurite.py": ["os", "sys", "logging", "azure.data.tables.TableServiceClient"], "atomsec-func-sfdc/scripts/verify_fastapi_deployment.py": ["<PERSON><PERSON><PERSON><PERSON>", "requests", "sys", "time", "logging", "xml.etree.ElementTree", "datetime.datetime"], "atomsec-func-sfdc/scripts/blue_green_deployment.py": ["sys", "json", "time", "subprocess", "os", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "datetime.datetime", "<PERSON><PERSON><PERSON><PERSON>", "urllib.request", "urllib.error"], "atomsec-func-sfdc/scripts/setup_service_bus.py": ["os", "sys", "logging", "azure.servicebus.management.ServiceBusAdministrationClient", "azure.servicebus.management.CreateTopicOptions", "azure.servicebus.management.CreateSubscriptionOptions", "azure.servicebus.management.SqlRuleFilter", "datetime.<PERSON><PERSON><PERSON>", "datetime.<PERSON><PERSON><PERSON>"], "atomsec-func-sfdc/scripts/deployment_monitoring.py": ["sys", "json", "time", "subprocess", "os", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "urllib.request", "urllib.error", "threading", "queue", "statistics"], "atomsec-func-sfdc/scripts/deployment_health_check.py": ["sys", "time", "urllib.request", "urllib.error", "json", "os", "subprocess", "threading", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "concurrent.futures", "<PERSON><PERSON><PERSON><PERSON>", "logging", "traceback"], "atomsec-func-sfdc/scripts/init_database.py": ["os", "sys", "logging", "datetime.datetime", "src.shared.azure_services.is_local_dev", "src.shared.data_access.SqlDatabaseRepository", "src.shared.data_access.TableStorageRepository", "src.shared.user_repository.get_user_account_table_repo", "src.shared.user_repository.get_user_login_table_repo", "src.shared.user_repository.get_hashing_algorithm_table_repo", "src.shared.user_repository.get_user_role_table_repo", "src.shared.user_repository.ensure_default_hashing_algorithms"], "atomsec-func-sfdc/scripts/verify_deployment.py": ["os", "sys", "<PERSON><PERSON><PERSON><PERSON>", "subprocess"], "atomsec-func-sfdc/scripts/init_policy_rule_setting.py": ["os", "datetime.datetime", "src.shared.db_service_client.get_db_client"], "atomsec-func-sfdc/scripts/init_policy_and_rule.py": ["uuid", "datetime.datetime", "src.shared.db_service_client.get_db_client"], "atomsec-func-sfdc/scripts/verify_function_discovery.py": ["os", "sys", "json", "re", "subprocess", "pathlib.Path"], "atomsec-func-sfdc/scripts/deploy_monitoring_dashboards.py": ["json", "os", "sys", "<PERSON><PERSON><PERSON><PERSON>", "logging", "typing.Dict", "typing.Any", "typing.List", "azure.identity.DefaultAzureCredential", "azure.mgmt.applicationinsights.ApplicationInsightsManagementClient", "azure.mgmt.monitor.MonitorManagementClient", "uuid"], "atomsec-func-sfdc/RolesManagementFunction/__init__.py": ["azure.functions", "logging", "sys", "os", "account_management_asgi.app", "<PERSON><PERSON><PERSON>", "src.shared.asgi_middleware.AsgiMiddleware", "traceback"], "atomsec-func-sfdc/src/__init__.py": [], "atomsec-func-sfdc/src/blueprints/auth.py": ["logging", "azure.functions", "json", "secrets", "jwt", "os", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.config.is_local_dev", "src.shared.config.get_jwt_config", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.shared.cors_middleware.cors_middleware", "src.shared.user_repository.create_user", "src.shared.user_repository.get_user_account_by_email", "src.shared.user_repository.authenticate_user", "src.shared.user_repository.update_last_login", "src.shared.user_repository.get_user_account_table_repo", "src.shared.user_repository.get_user_login_table_repo", "src.shared.user_repository.get_user_account_sql_repo", "src.shared.user_repository.get_user_login_sql_repo", "src.shared.user_repository.hash_password", "src.shared.auth_utils.get_jwt_secret", "src.shared.auth_utils.get_jwt_algorithm"], "atomsec-func-sfdc/src/blueprints/azure_ad_auth.py": ["logging", "json", "os", "secrets", "requests", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "typing.Dict", "typing.Any", "typing.Optional", "urllib.parse", "azure.functions", "src.shared.azure_services.get_secret", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.shared.cors_middleware.cors_middleware", "src.shared.user_repository.get_user_account_by_email", "src.shared.user_repository.update_last_login", "src.blueprints.auth.create_access_token", "src.blueprints.auth.create_refresh_token", "src.blueprints.auth.store_refresh_token", "src.shared.config.get_azure_ad_config", "src.shared.config.is_local_dev", "src.shared.config.get_backend_url", "src.shared.config.is_local_dev", "src.shared.config.get_backend_url", "src.shared.config.get_frontend_url", "src.shared.auth_utils.decode_token", "src.shared.user_repository.get_user_roles", "src.shared.user_repository.is_user_admin", "src.shared.config.get_frontend_url"], "atomsec-func-sfdc/src/blueprints/organization.py": ["logging", "azure.functions", "json", "asyncio", "uuid", "datetime.datetime", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.<PERSON>", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.azure_services.is_local_dev", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.shared.utils.get_salesforce_access_token", "src.shared.utils.execute_salesforce_query", "src.shared.auth_utils.get_current_user", "src.shared.auth_utils.require_auth", "src.shared.cors_middleware.cors_middleware", "src.shared.db_service_client.get_db_client", "src.shared.background_processor.BackgroundProcessor", "src.shared.db_service_client.get_db_client", "src.shared.db_service_client.get_db_client", "src.shared.auth_service.authenticate_salesforce_integration", "src.shared.common.is_local_dev", "src.shared.db_service_client.get_db_client", "src.blueprints.security_analysis.fetch_security_health_check_risks", "src.blueprints.security_analysis.calculate_health_score", "src.blueprints.security_analysis.process_and_store_policies_results", "asyncio", "datetime.datetime", "src.blueprints.security_analysis.fetch_security_health_check_risks", "src.blueprints.security_analysis.calculate_health_score", "src.blueprints.security_analysis.process_and_store_policies_results", "asyncio", "concurrent.futures", "concurrent.futures", "threading"], "atomsec-func-sfdc/src/blueprints/profile.py": ["fastapi.APIRouter", "fastapi.HTTPException", "fastapi.Depends", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "logging"], "atomsec-func-sfdc/src/blueprints/security.py": ["fastapi.APIRouter", "fastapi.HTTPException", "fastapi.Depends", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "logging"], "atomsec-func-sfdc/src/blueprints/profile_metadata_fixed.py": ["logging", "azure.functions", "pandas", "asyncio", "aiohttp", "json", "datetime.datetime", "typing.List", "typing.<PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.BlobStorageRepository", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.utils.get_salesforce_access_token", "src.shared.utils.execute_salesforce_query", "src.shared.utils.handle_exception"], "atomsec-func-sfdc/src/blueprints/__init__.py": [], "atomsec-func-sfdc/src/blueprints/scan_metadata_fixed.py": ["json", "logging", "azure.functions", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.auth_utils.get_current_user", "src.shared.data_access.BlobStorageRepository", "src.shared.common.is_local_dev", "src.shared.background_processor.BackgroundProcessor", "src.shared.background_processor.TASK_TYPE_METADATA_EXTRACTION", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.blueprints.integration.get_integration_table_repo", "src.blueprints.integration.get_integration_sql_repo", "src.shared.azure_services.get_secret", "src.shared.salesforce_jwt_auth.test_jwt_bearer_flow", "azure.data.tables.TableServiceClient", "src.shared.config.get_storage_connection_string", "src.shared.salesforce_utils.test_salesforce_connection", "src.shared.salesforce_jwt_auth.test_jwt_bearer_flow", "src.shared.salesforce_utils.test_salesforce_connection"], "atomsec-func-sfdc/src/blueprints/cors_handler.py": ["logging", "azure.functions"], "atomsec-func-sfdc/src/blueprints/user_profile_blueprint.py": ["logging", "json", "azure.functions", "datetime.datetime", "src.shared.config.is_local_dev", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.shared.cors_middleware.cors_middleware", "src.shared.auth_utils.require_auth", "src.shared.auth_utils.get_current_user", "src.shared.user_repository.get_user_account_by_email", "src.shared.user_repository.get_user_account_by_id", "src.shared.user_repository.get_user_account_table_repo", "src.shared.user_repository.get_user_account_sql_repo", "src.shared.user_repository.get_user_login_by_username", "src.shared.user_repository.get_user_login_table_repo", "src.shared.user_repository.get_user_login_sql_repo", "src.shared.user_repository.hash_password", "src.shared.user_repository.verify_password", "src.shared.user_repository.generate_salt"], "atomsec-func-sfdc/src/blueprints/integration.py": ["json", "logging", "uuid", "azure.functions", "os", "xml.etree.ElementTree", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "datetime.datetime", "src.shared.azure_services.store_client_credentials", "src.shared.azure_services.get_secret", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.shared.auth_utils.get_current_user", "src.shared.auth_utils.require_auth", "src.shared.salesforce_utils.test_salesforce_connection", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.data_access.BlobStorageRepository", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.create_default_policies_and_rules_for_integration", "src.shared.common.is_local_dev", "src.shared.auth_service.authenticate_salesforce_integration", "src.shared.metadata_utils.get_salesforce_metadata", "src.shared.background_processor.BackgroundProcessor", "src.shared.background_processor.TASK_TYPE_SFDC_AUTHENTICATE", "src.shared.background_processor.TASK_PRIORITY_HIGH", "src.shared.user_repository.get_user_account_by_id", "src.shared.db_service_client.get_db_client", "traceback", "traceback", "traceback", "traceback", "traceback"], "atomsec-func-sfdc/src/blueprints/account_management.py": ["logging", "azure.functions", "json", "datetime.datetime", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "src.shared.config.is_local_dev", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.shared.cors_middleware.cors_middleware", "src.shared.auth_utils.require_auth", "src.shared.auth_utils.get_current_user", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.user_repository.create_user", "src.shared.user_repository.get_user_account_by_id", "src.shared.user_repository.get_user_account_table_repo", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository", "random", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_table_storage_repository"], "atomsec-func-sfdc/src/blueprints/key_vault_endpoints.py": ["json", "logging", "os", "azure.functions", "src.shared.azure_services.set_secret", "src.shared.azure_services.get_secret", "src.shared.azure_services.store_client_credentials", "src.shared.azure_services.create_key_vault_if_not_exists", "src.shared.azure_services.add_access_policy", "src.shared.azure_services.get_current_object_id", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.shared.cors_middleware.cors_middleware", "src.shared.auth_utils.require_auth"], "atomsec-func-sfdc/src/blueprints/integration_tabs.py": ["json", "logging", "azure.functions", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "datetime.datetime", "asyncio", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.shared.cors_middleware.cors_middleware", "src.shared.auth_utils.get_current_user", "src.shared.auth_utils.require_auth", "src.shared.salesforce_utils.test_salesforce_connection", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.common.is_local_dev", "src.shared.azure_services.get_secret", "src.shared.background_processor.BackgroundProcessor", "src.shared.background_processor.TASK_STATUS_PENDING", "src.shared.background_processor.TASK_STATUS_RUNNING", "src.shared.background_processor.TASK_STATUS_COMPLETED", "src.shared.background_processor.TASK_STATUS_FAILED", "src.shared.background_processor.TASK_TYPE_HEALTH_CHECK", "src.shared.background_processor.TASK_TYPE_PROFILES", "src.shared.background_processor.TASK_PRIORITY_HIGH", "src.shared.background_processor.TASK_PRIORITY_MEDIUM", "src.shared.background_processor.TASK_PRIORITY_LOW", "task_processor.get_integration_by_id", "src.blueprints.profile_system_permissions.fetch_profile_system_permissions", "src.blueprints.profile_system_permissions.fetch_permission_set_system_permissions", "src.blueprints.profile_system_permissions.save_profile_system_permissions", "src.blueprints.profile_system_permissions.save_permission_set_system_permissions", "src.blueprints.profile_system_permissions.get_profile_system_permissions", "src.blueprints.profile_system_permissions.get_permission_set_system_permissions", "traceback", "traceback", "azure.data.tables.TableServiceClient", "src.shared.config.get_storage_connection_string", "traceback", "traceback"], "atomsec-func-sfdc/src/blueprints/security_data.py": ["logging", "azure.functions", "pandas", "json", "asyncio", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "datetime.datetime", "src.shared.utils.create_json_response", "src.shared.salesforce_utils.get_salesforce_access_token", "src.shared.salesforce_utils.execute_salesforce_query", "src.shared.salesforce_utils.execute_salesforce_tooling_query", "src.shared.data_access.BlobStorageRepository", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.azure_services.is_local_dev", "src.shared.cors_middleware.cors_middleware"], "atomsec-func-sfdc/src/blueprints/health_blueprint.py": ["fastapi.APIRouter", "fastapi.HTTPException", "fastapi.Depends", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "logging"], "atomsec-func-sfdc/src/blueprints/scan.py": ["logging", "azure.functions", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.azure_services.is_local_dev", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.shared.auth_utils.get_current_user"], "atomsec-func-sfdc/src/blueprints/profiles_permissions.py": ["azure.functions", "json", "src.shared.data_access.TableStorageRepository", "datetime.datetime", "json"], "atomsec-func-sfdc/src/blueprints/profile_system_permissions.py": ["logging", "azure.functions", "aiohttp", "json", "datetime.datetime", "typing.List", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.utils.get_salesforce_access_token", "src.shared.utils.execute_salesforce_query", "src.shared.utils.handle_exception", "src.shared.utils.create_json_response"], "atomsec-func-sfdc/src/blueprints/utilities.py": ["fastapi.APIRouter", "fastapi.HTTPException", "fastapi.Depends", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "logging"], "atomsec-func-sfdc/src/blueprints/security_analysis.py": ["logging", "azure.functions", "json", "asyncio", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "datetime.datetime", "re", "os", "xml.etree.ElementTree", "uuid", "azure.data.tables.TableServiceClient", "azure.core.exceptions.ResourceExistsError", "src.shared.db_service_client.get_db_client", "src.shared.azure_services.is_local_dev", "src.shared.utils.create_json_response", "src.shared.utils.handle_exception", "src.shared.salesforce_utils.get_salesforce_access_token", "src.shared.salesforce_utils.execute_salesforce_query", "src.shared.salesforce_utils.execute_salesforce_tooling_query", "src.shared.auth_utils.get_current_user", "src.shared.cors_middleware.cors_middleware", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.TableStorageRepository", "uuid", "src.shared.db_service_client.get_db_client", "traceback", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>"], "atomsec-func-sfdc/src/blueprints/general.py": ["logging", "azure.functions", "json", "platform", "os", "datetime.datetime", "src.shared.azure_services.is_local_dev", "src.shared.api_utils.create_json_response", "src.shared.api_utils.handle_api_exception"], "atomsec-func-sfdc/src/blueprints/security_health_check_simplified.py": ["logging", "azure.functions", "pandas", "json", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "datetime.datetime", "src.shared.api_utils.create_json_response", "src.shared.api_utils.handle_api_exception", "src.shared.utils.get_salesforce_access_token", "src.shared.data_access.BlobStorageRepository", "requests", "urllib.parse"], "atomsec-func-sfdc/src/task_processor/__init__.py": ["json", "logging", "azure.functions", "requests", "urllib.parse", "traceback", "re", "xml.etree.ElementTree", "os", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "asyncio", "uuid", "src.shared.background_processor.BackgroundProcessor", "src.shared.background_processor.TASK_STATUS_PENDING", "src.shared.background_processor.TASK_STATUS_RUNNING", "src.shared.background_processor.TASK_STATUS_COMPLETED", "src.shared.background_processor.TASK_STATUS_FAILED", "src.shared.background_processor.TASK_STATUS_RETRY", "src.shared.background_processor.TASK_STATUS_CANCELLED", "src.shared.background_processor.TASK_TYPE_OVERVIEW", "src.shared.background_processor.TASK_TYPE_HEALTH_CHECK", "src.shared.background_processor.TASK_TYPE_PROFILES", "src.shared.background_processor.TASK_TYPE_PROFILES_PERMISSION_SETS", "src.shared.background_processor.TASK_TYPE_DATA_EXPORT", "src.shared.background_processor.TASK_TYPE_REPORT_GENERATION", "src.shared.background_processor.TASK_TYPE_SCHEDULED_SCAN", "src.shared.background_processor.TASK_TYPE_NOTIFICATION", "src.shared.background_processor.TASK_TYPE_METADATA_EXTRACTION", "src.shared.background_processor.TASK_TYPE_SFDC_AUTHENTICATE", "src.shared.background_processor.TASK_TYPE_PERMISSION_SETS", "src.shared.background_processor.TASK_TYPE_MFA_ENFORCEMENT", "src.shared.background_processor.TASK_TYPE_DEVICE_ACTIVATION", "src.shared.background_processor.TASK_TYPE_LOGIN_IP_RANGES", "src.shared.background_processor.TASK_TYPE_LOGIN_HOURS", "src.shared.background_processor.TASK_TYPE_SESSION_TIMEOUT", "src.shared.background_processor.TASK_TYPE_API_WHITELISTING", "src.shared.background_processor.TASK_TYPE_PASSWORD_POLICY", "src.shared.background_processor.TASK_TYPE_PMD_APEX_SECURITY", "src.shared.background_processor.TASK_PRIORITY_HIGH", "src.shared.background_processor.TASK_PRIORITY_MEDIUM", "src.shared.background_processor.TASK_PRIORITY_LOW", "src.shared.salesforce_utils.execute_salesforce_query", "src.shared.salesforce_utils.execute_salesforce_tooling_query", "src.shared.salesforce_utils.get_salesforce_access_token", "src.shared.salesforce_utils.test_salesforce_connection", "src.shared.salesforce_utils.get_salesforce_client", "src.shared.auth_service.authenticate_salesforce_integration", "src.shared.data_access.BlobStorageRepository", "src.shared.data_access.get_table_storage_repository", "src.shared.common.is_local_dev", "src.shared.azure_services.get_secret", "src.shared.metadata_extraction.extract_salesforce_metadata", "src.blueprints.security_analysis.fetch_security_health_check_risks", "src.blueprints.security_analysis.calculate_health_score", "src.blueprints.security_analysis.process_and_store_policies_results", "src.shared.db_service_client.get_db_client", "fastapi.APIRouter", "tasks.pmd_task.process_pmd_task", "tasks.password_policy.process_password_policy_task", "tasks.device_activation.process_device_activation_task", "tasks.session_timeout.process_session_timeout_task", "tasks.mfa_enforcement.process_mfa_enforcement_task", "tasks.login_ip_ranges.process_login_ip_ranges_task", "tasks.login_hours.process_login_hours_task", "tasks.api_whitelisting.process_api_whitelisting_task", "tasks.profiles_permission_sets.process_profiles_permission_sets_task", "tasks.pmd_task.process_pmd_task", "fuzzywuzzy.fuzz", "time", "traceback", "xml.etree.ElementTree", "urllib.parse", "xml.etree.ElementTree", "src.blueprints.profile_system_permissions.fetch_profile_system_permissions", "src.blueprints.profile_system_permissions.fetch_permission_set_system_permissions", "src.blueprints.profile_system_permissions.save_profile_system_permissions", "src.blueprints.profile_system_permissions.save_permission_set_system_permissions", "datetime.datetime", "asyncio", "concurrent.futures", "traceback", "traceback", "concurrent.futures", "traceback", "traceback", "traceback", "traceback", "traceback", "asyncio", "concurrent.futures", "traceback", "asyncio", "concurrent.futures", "traceback", "json", "src.shared.background_processor.BackgroundProcessor"], "atomsec-func-sfdc/src/task_processor/tasks/device_activation.py": ["os", "asyncio", "json", "logging", "datetime.datetime", "src.shared.data_access.BlobStorageRepository", "src.shared.db_service_client.get_db_client", "src.shared.salesforce_utils.execute_salesforce_query", "utils.load_best_practices_xml", "utils.normalize_profile_name", "utils.normalize_permission_name", "utils.normalize_value", "utils.parse_profile_permissions", "utils.get_active_profiles_and_permissionsets", "traceback"], "atomsec-func-sfdc/src/task_processor/tasks/password_policy.py": ["os", "asyncio", "json", "logging", "datetime.datetime", "xml.etree.ElementTree", "src.shared.data_access.BlobStorageRepository", "src.shared.db_service_client.get_db_client", "src.shared.salesforce_utils.execute_salesforce_query", "utils.load_best_practices_xml", "utils.normalize_profile_name", "utils.normalize_permission_name", "utils.normalize_value", "utils.extract_user_license_from_profile", "utils.get_active_profiles_and_permissionsets", "traceback"], "atomsec-func-sfdc/src/task_processor/tasks/pmd_task.py": ["logging", "typing.Dict", "typing.Any", "typing.Optional", "os", "tempfile", "src.shared.background_processor.BackgroundProcessor", "src.shared.db_service_client.get_db_client", "src.pmd_components.pmd_scanner.PMDScanner", "src.pmd_components.pmd_blob_handler.PMDBlobHandler", "src.pmd_components.pmd_results_processor.PMDResultsProcessor", "datetime.datetime"], "atomsec-func-sfdc/src/task_processor/tasks/mfa_enforcement.py": ["os", "asyncio", "json", "logging", "datetime.datetime", "src.shared.data_access.BlobStorageRepository", "src.shared.db_service_client.get_db_client", "src.shared.salesforce_utils.execute_salesforce_query", "utils.load_best_practices_xml", "utils.normalize_profile_name", "utils.normalize_permission_name", "utils.normalize_value", "utils.parse_profile_permissions", "utils.parse_permissionset_permissions", "utils.extract_user_license_from_profile", "traceback"], "atomsec-func-sfdc/src/task_processor/tasks/utils.py": ["xml.etree.ElementTree", "urllib.parse", "re", "logging", "src.shared.salesforce_utils.execute_salesforce_query", "asyncio"], "atomsec-func-sfdc/src/task_processor/tasks/login_hours.py": ["os", "asyncio", "json", "logging", "datetime.datetime", "xml.etree.ElementTree", "src.shared.data_access.BlobStorageRepository", "src.shared.db_service_client.get_db_client", "src.shared.salesforce_utils.execute_salesforce_query", "utils.normalize_profile_name", "utils.get_active_profiles_and_permissionsets", "traceback"], "atomsec-func-sfdc/src/task_processor/tasks/login_ip_ranges.py": ["os", "asyncio", "json", "logging", "datetime.datetime", "ipaddress", "src.shared.data_access.BlobStorageRepository", "src.shared.db_service_client.get_db_client", "src.shared.salesforce_utils.execute_salesforce_query", "utils.normalize_profile_name", "utils.get_active_profiles_and_permissionsets", "xml.etree.ElementTree", "traceback"], "atomsec-func-sfdc/src/task_processor/tasks/profiles_permission_sets.py": ["asyncio", "logging", "json", "datetime.datetime", "src.shared.data_access.BlobStorageRepository", "src.shared.data_access.get_policies_result_table_repo", "src.shared.data_access.get_table_storage_repository", "src.shared.salesforce_utils.execute_salesforce_query", "utils.load_best_practices_xml", "utils.normalize_profile_name", "utils.normalize_permission_name", "utils.normalize_value", "utils.parse_profile_permissions", "os", "permission_sets.process_permissionsets_in_blob"], "atomsec-func-sfdc/src/task_processor/tasks/api_whitelisting.py": ["os", "asyncio", "json", "logging", "datetime.datetime", "xml.etree.ElementTree", "src.shared.data_access.BlobStorageRepository", "src.shared.db_service_client.get_db_client", "src.shared.salesforce_utils.execute_salesforce_query", "utils.normalize_profile_name", "utils.get_active_profiles_and_permissionsets", "traceback"], "atomsec-func-sfdc/src/task_processor/tasks/session_timeout.py": ["os", "asyncio", "json", "logging", "datetime.datetime", "xml.etree.ElementTree", "src.shared.data_access.BlobStorageRepository", "src.shared.db_service_client.get_db_client", "src.shared.salesforce_utils.execute_salesforce_query", "utils.load_best_practices_xml", "utils.normalize_profile_name", "utils.normalize_permission_name", "utils.normalize_value", "utils.get_active_profiles_and_permissionsets", "traceback"], "atomsec-func-sfdc/src/repositories/base_repository.py": ["logging", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.<PERSON>", "src.shared.db_service_client.get_db_client"], "atomsec-func-sfdc/src/repositories/__init__.py": [], "atomsec-func-sfdc/src/repositories/pmd_repository.py": ["logging", "json", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "datetime.datetime", "base_repository.BaseRepository"], "atomsec-func-sfdc/src/repositories/user_repository.py": ["logging", "secrets", "<PERSON><PERSON><PERSON>", "datetime.datetime", "datetime.date", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "typing.<PERSON>", "typing.Union", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models.UserAccount", "src.shared.database_models.UserLogin", "src.shared.database_models.Role", "src.shared.database_models.UserRole", "src.shared.database_models_new.User", "src.shared.database_models_new.Account", "src.shared.database_models_new.Role", "src.shared.database_models_new.UserRole", "cryptography.hazmat.primitives.kdf.pbkdf2.PBKDF2HMAC", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.backends.default_backend", "base64", "bcrypt", "random", "random"], "atomsec-func-sfdc/src/shared/standalone_permission_set_profile_analysis.py": ["os", "json", "logging", "xml.etree.ElementTree", "re", "datetime.datetime", "typing.List", "typing.Dict", "typing.Any", "subprocess", "csv"], "atomsec-func-sfdc/src/shared/enhanced_auth_service.py": ["logging", "time", "<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "collections.defaultdict", "collections.deque", "threading", "src.shared.auth_utils.decode_token", "src.shared.auth_utils.get_current_user", "src.shared.auth_utils.get_user_id_from_request", "src.shared.auth_utils.get_user_from_request_or_default", "src.shared.salesforce_client.SalesforceClient", "src.shared.azure_services.get_secret"], "atomsec-func-sfdc/src/shared/data_protection_service.py": ["logging", "json", "<PERSON><PERSON><PERSON>", "secrets", "base64", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Set", "typing.<PERSON>", "typing.Union", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "threading", "re", "cryptography.fernet.Fernet", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.primitives.serialization", "cryptography.hazmat.primitives.kdf.pbkdf2.PBKDF2HMAC", "cryptography.hazmat.primitives.asymmetric.rsa", "cryptography.hazmat.primitives.asymmetric.padding", "cryptography.hazmat.backends.default_backend", "src.shared.azure_services.get_secret", "src.shared.monitoring.get_monitoring_service", "hmac"], "atomsec-func-sfdc/src/shared/auth.py": ["src.shared.auth_utils.get_jwt_secret", "src.shared.auth_utils.get_jwt_algorithm", "src.shared.auth_utils.decode_token", "src.shared.auth_utils.get_token_from_header", "src.shared.auth_utils.get_current_user", "src.shared.auth_utils.require_auth"], "atomsec-func-sfdc/src/shared/mock_service_bus.py": ["logging", "json", "requests", "typing.Dict", "typing.Any", "typing.Optional", "datetime.datetime", "function_app.process_service_bus_task_message"], "atomsec-func-sfdc/src/shared/auth_service.py": ["logging", "requests", "time", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "typing.Dict", "typing.Any", "typing.Optional", "typing.<PERSON>", "simple_salesforce.Salesforce", "simple_salesforce.SalesforceAuthenticationFailed", "jwt"], "atomsec-func-sfdc/src/shared/cors_middleware.py": ["logging", "azure.functions", "src.shared.azure_services.is_local_dev", "src.shared.azure_services.get_secret", "src.shared.azure_services.get_secret"], "atomsec-func-sfdc/src/shared/auto_scaling_service.py": ["logging", "time", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Callable", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "threading", "collections.defaultdict", "collections.deque", "statistics"], "atomsec-func-sfdc/src/shared/execution_context_manager.py": ["logging", "uuid", "json", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.task_status_service.get_task_status_service"], "atomsec-func-sfdc/src/shared/parameter_validator.py": ["json", "logging", "uuid", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "datetime.datetime", "ast", "re"], "atomsec-func-sfdc/src/shared/enhanced_parameter_validator.py": ["json", "logging", "re", "uuid", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "typing.Union", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "hmac", "base64", "src.shared.parameter_validator.ParameterValidator", "src.shared.azure_services.get_secret"], "atomsec-func-sfdc/src/shared/advanced_caching_service.py": ["logging", "json", "time", "asyncio", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Callable", "typing.Union", "typing.Set", "functools.wraps", "collections.defaultdict", "threading", "<PERSON><PERSON><PERSON>", "pickle", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "redis"], "atomsec-func-sfdc/src/shared/advanced_auth_service.py": ["logging", "time", "<PERSON><PERSON><PERSON>", "secrets", "json", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "typing.Set", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "collections.defaultdict", "collections.deque", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "threading", "re", "ipaddress", "src.shared.enhanced_auth_service.get_enhanced_auth_service", "src.shared.enhanced_auth_service.AuthenticationError", "src.shared.azure_services.get_secret", "src.shared.monitoring.get_monitoring_service"], "atomsec-func-sfdc/src/shared/config.py": ["os", "logging", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.common.is_local_dev", "src.shared.common.is_test_env", "src.shared.azure_services.get_secret", "src.shared.azure_services.get_secret", "src.shared.azure_services.get_secret", "src.shared.azure_services.get_secret", "src.shared.azure_services.get_secret", "src.shared.azure_services.get_secret", "traceback", "traceback"], "atomsec-func-sfdc/src/shared/task_coordination_service.py": ["logging", "uuid", "json", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "src.shared.azure_services.get_queue_client", "src.shared.azure_services.get_queue_client", "src.shared.config.get_storage_connection_string", "src.shared.common.is_local_dev", "src.shared.monitoring.get_task_monitor", "src.shared.monitoring.get_structured_logger", "src.shared.config.get_storage_connection_string", "src.shared.common.is_local_dev", "src.shared.monitoring.get_task_monitor", "src.shared.monitoring.get_structured_logger", "src.shared.task_status_service.get_task_status_service", "azure.storage.queue.QueueClient"], "atomsec-func-sfdc/src/shared/database_models.py": ["dataclasses.dataclass", "datetime.datetime", "datetime.date", "typing.Optional", "typing.List", "typing.Dict", "typing.Any"], "atomsec-func-sfdc/src/shared/cache_performance_analytics.py": ["logging", "time", "statistics", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.<PERSON>", "collections.defaultdict", "collections.deque", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "threading", "json"], "atomsec-func-sfdc/src/shared/feature_flag_service.py": ["os", "json", "logging", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "typing.Callable", "dataclasses.dataclass", "dataclasses.field", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "enum.Enum", "threading", "<PERSON><PERSON><PERSON>", "random", "src.shared.common.is_local_dev", "src.shared.common.is_test_env", "src.shared.azure_services.get_secret", "pathlib.Path"], "atomsec-func-sfdc/src/shared/function_configuration.py": ["logging", "os", "json", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "opencensus.ext.azure.log_exporter.AzureLogHandler", "opencensus.ext.azure.trace_exporter.AzureExporter", "opencensus.trace.samplers.ProbabilitySampler", "opencensus.trace.config_integration"], "atomsec-func-sfdc/src/shared/database_models_new.py": ["dataclasses.dataclass", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "json"], "atomsec-func-sfdc/src/shared/user_activity_monitor.py": ["logging", "json", "time", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Set", "typing.<PERSON>", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "collections.defaultdict", "collections.deque", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "threading", "statistics", "re", "src.shared.advanced_auth_service.get_advanced_auth_service", "src.shared.monitoring.get_monitoring_service"], "atomsec-func-sfdc/src/shared/service_bus_client.py": ["logging", "json", "os", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "azure.servicebus.ServiceBusClient", "azure.servicebus.ServiceBusMessage", "azure.servicebus.exceptions.ServiceBusError", "src.shared.common.is_local_dev", "logging", "src.shared.mock_service_bus.get_mock_service_bus_client", "azure.servicebus.management.ServiceBusAdministrationClient", "azure.servicebus.management.CreateSubscriptionOptions", "azure.servicebus.management.SqlRuleFilter"], "atomsec-func-sfdc/src/shared/salesforce_jwt_auth.py": ["jwt", "requests", "logging", "datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.<PERSON>", "simple_salesforce.Salesforce"], "atomsec-func-sfdc/src/shared/monitoring.py": ["logging", "json", "time", "uuid", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Callable", "functools.wraps", "threading", "collections.defaultdict", "collections.deque", "psutil", "os", "inspect", "traceback", "applicationinsights.TelemetryClient", "monitoring_dashboards.setup_monitoring_dashboards", "monitoring_dashboards.cleanup_monitoring"], "atomsec-func-sfdc/src/shared/azure_services.py": ["os", "logging", "uuid", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.common.is_local_dev", "src.shared.common.is_test_env", "azure.identity.DefaultAzureCredential", "azure.identity.AzureCliCredential", "azure.keyvault.secrets.SecretClient", "azure.mgmt.keyvault.KeyVaultManagementClient", "azure.mgmt.keyvault.models.VaultCreateOrUpdateParameters", "azure.mgmt.keyvault.models.VaultProperties", "azure.mgmt.keyvault.models.Sku", "azure.mgmt.keyvault.models.SkuName", "azure.mgmt.keyvault.models.AccessPolicyEntry", "azure.mgmt.keyvault.models.Permissions", "azure.mgmt.keyvault.models.SecretPermissions", "azure.mgmt.keyvault.models.KeyPermissions", "azure.mgmt.keyvault.models.CertificatePermissions", "azure.mgmt.keyvault.models.StoragePermissions", "azure.storage.blob.BlobServiceClient", "azure.storage.queue.QueueServiceClient", "azure.data.tables.TableServiceClient", "unittest.mock.MagicMock", "src.shared.config.get_sql_connection_string", "src.shared.config.get_key_vault_url", "unittest.mock.MagicMock", "unittest.mock.MagicMock", "unittest.mock.MagicMock", "unittest.mock.MagicMock", "subprocess", "json", "subprocess", "json", "src.shared.config.get_storage_connection_string", "src.shared.config.get_storage_connection_string", "src.shared.config.get_storage_connection_string", "azure.data.tables.TableServiceClient", "src.shared.config.get_storage_connection_string", "azure.data.tables.TableServiceClient", "src.shared.config.get_storage_connection_string", "azure.data.tables.TableServiceClient", "src.shared.config.get_storage_connection_string", "azure.data.tables.TableServiceClient", "azure.data.tables.TableClient", "src.shared.config.get_storage_connection_string"], "atomsec-func-sfdc/src/shared/performance_optimization_framework.py": ["logging", "time", "psutil", "threading", "functools", "traceback", "gc", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Callable", "typing.<PERSON>", "dataclasses.dataclass", "dataclasses.asdict", "collections.defaultdict", "collections.deque", "enum.Enum", "statistics", "weakref", "sys", "os"], "atomsec-func-sfdc/src/shared/security.py": ["logging", "json", "<PERSON><PERSON><PERSON>", "hmac", "secrets", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "cryptography.fernet.Fernet", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.primitives.kdf.pbkdf2.PBKDF2HMAC", "base64", "os"], "atomsec-func-sfdc/src/shared/salesforce_client.py": ["logging", "urllib.parse", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "typing.Union", "json", "datetime", "simple_salesforce.Salesforce", "simple_salesforce.SalesforceLogin", "simple_salesforce.SFType", "simple_salesforce.exceptions.SalesforceAuthenticationFailed", "simple_salesforce.exceptions.SalesforceError", "src.shared.azure_services.get_secret", "requests", "src.shared.salesforce_jwt_auth.test_jwt_bearer_flow"], "atomsec-func-sfdc/src/shared/auth_utils.py": ["logging", "jwt", "typing.Dict", "typing.Any", "typing.Optional", "azure.functions", "fastapi.Request", "src.shared.config.get_jwt_config", "src.shared.user_repository.get_user_account_by_email", "functools", "src.shared.common.is_local_dev", "azure.functions", "json"], "atomsec-func-sfdc/src/shared/session_management.py": ["logging", "json", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "dataclasses.dataclass", "dataclasses.asdict", "azure.functions", "fastapi.Request", "fastapi.HTTPException", "fastapi.status", "src.shared.advanced_auth_service.get_advanced_auth_service", "src.shared.advanced_auth_service.AuthenticationError", "src.shared.monitoring.get_monitoring_service", "functools"], "atomsec-func-sfdc/src/shared/__init__.py": [], "atomsec-func-sfdc/src/shared/enhanced_configuration_manager.py": ["os", "json", "logging", "typing.Dict", "typing.Any", "typing.Optional", "typing.Union", "typing.List", "typing.Type", "typing.TypeVar", "typing.Generic", "dataclasses.dataclass", "dataclasses.field", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "enum.Enum", "threading", "pathlib.Path", "src.shared.common.is_local_dev", "src.shared.common.is_test_env", "src.shared.azure_services.get_secret", "src.shared.azure_services.get_keyvault_client"], "atomsec-func-sfdc/src/shared/queue_message_processor.py": ["logging", "json", "uuid", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "azure.storage.queue.QueueClient", "azure.core.exceptions.ResourceNotFoundError", "src.shared.azure_services.get_queue_client", "src.shared.task_status_service.get_task_status_service", "src.shared.task_status_service.TaskStatusService", "src.shared.execution_context_manager.get_execution_context_manager", "src.shared.execution_context_manager.ExecutionContextManager", "src.shared.common.is_local_dev", "src.shared.config.get_storage_connection_string", "src.shared.parameter_validation_service.get_parameter_validation_service"], "atomsec-func-sfdc/src/shared/db_service_client.py": ["logging", "json", "requests", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "os", "src.shared.config.get_db_service_config", "time", "random", "base64", "base64"], "atomsec-func-sfdc/src/shared/task_lookup_service.py": ["logging", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "src.shared.task_status_service.get_task_status_service", "src.shared.task_status_service.TaskStatusService"], "atomsec-func-sfdc/src/shared/sfdc_service_client.py": ["logging", "requests", "json", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "src.shared.config.is_local_dev", "os"], "atomsec-func-sfdc/src/shared/task_sequence_configuration.py": ["os", "json", "logging", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "typing.Callable", "typing.Set", "dataclasses.dataclass", "dataclasses.field", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "enum.Enum", "re", "uuid", "src.shared.common.is_local_dev", "src.shared.common.is_test_env"], "atomsec-func-sfdc/src/shared/internal_service_security.py": ["logging", "<PERSON><PERSON><PERSON>", "hmac", "time", "json", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "azure.functions", "src.shared.azure_services.get_secret", "src.shared.azure_services.is_local_dev"], "atomsec-func-sfdc/src/shared/intrusion_detection_system.py": ["logging", "json", "time", "re", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Set", "typing.<PERSON>", "typing.Pattern", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "collections.defaultdict", "collections.deque", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "threading", "<PERSON><PERSON><PERSON>", "ipaddress", "statistics.mean", "statistics.stdev", "src.shared.security_monitoring_service.get_security_monitoring_service", "src.shared.security_monitoring_service.ThreatLevel", "src.shared.security_monitoring_service.AlertType", "src.shared.user_activity_monitor.get_user_activity_monitor", "src.shared.monitoring.get_monitoring_service", "threading"], "atomsec-func-sfdc/src/shared/asgi_middleware.py": ["logging", "azure.functions", "urllib.parse.urlparse", "urllib.parse.parse_qs", "re", "traceback"], "atomsec-func-sfdc/src/shared/task_sequence_coordinator.py": ["logging", "json", "time", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "collections.defaultdict", "threading", "asyncio", "concurrent.futures.ThreadPoolExecutor", "src.shared.parameter_validator.get_parameter_validator", "src.shared.task_status_service.get_task_status_service"], "atomsec-func-sfdc/src/shared/data_access.py": ["json", "logging", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "typing.Union", "src.shared.azure_services.get_blob_client", "src.shared.azure_services.get_table_client", "src.shared.azure_services.get_queue_client", "src.shared.azure_services.get_credential", "src.shared.azure_services.is_local_dev", "pyodbc", "src.shared.db_service_client.get_db_client", "traceback"], "atomsec-func-sfdc/src/shared/security_monitoring_service.py": ["logging", "json", "time", "<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Set", "typing.<PERSON>", "typing.Callable", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "collections.defaultdict", "collections.deque", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "threading", "re", "ipaddress", "statistics.mean", "statistics.stdev", "src.shared.advanced_auth_service.get_advanced_auth_service", "src.shared.user_activity_monitor.get_user_activity_monitor", "src.shared.user_activity_monitor.ActivityType", "src.shared.monitoring.get_monitoring_service", "threading"], "atomsec-func-sfdc/src/shared/security_middleware.py": ["logging", "json", "time", "typing.Dict", "typing.Any", "typing.Optional", "typing.Callable", "typing.List", "datetime.datetime", "functools.wraps", "azure.functions", "src.shared.enhanced_parameter_validator.get_enhanced_parameter_validator", "src.shared.enhanced_parameter_validator.ParameterValidationError", "src.shared.enhanced_parameter_validator.SecurityValidationError", "src.shared.enhanced_auth_service.get_enhanced_auth_service", "src.shared.enhanced_auth_service.AuthenticationError", "src.shared.enhanced_auth_service.RateLimitExceededError", "src.shared.enhanced_auth_service.SalesforceAuthenticationError", "uuid", "src.shared.internal_service_security.validate_internal_service_call", "src.shared.internal_service_security.add_internal_response_headers", "src.shared.internal_service_security.InternalServiceSecurityError"], "atomsec-func-sfdc/src/shared/event_publisher.py": ["logging", "json", "os", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "azure.servicebus.ServiceBusClient", "azure.servicebus.ServiceBusMessage", "src.shared.azure_services.is_local_dev", "src.shared.azure_services.is_local_dev"], "atomsec-func-sfdc/src/shared/common.py": ["os", "logging"], "atomsec-func-sfdc/src/shared/metadata_utils.py": ["logging", "requests", "json", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>"], "atomsec-func-sfdc/src/shared/utils.py": ["logging", "json", "os", "requests", "asyncio", "aiohttp", "typing.Dict", "typing.Any", "typing.<PERSON>", "typing.List", "typing.Optional", "typing.Union", "datetime.datetime", "re", "azure.functions", "src.shared.salesforce_utils.execute_salesforce_query", "src.shared.salesforce_utils.execute_salesforce_tooling_query", "src.shared.salesforce_utils.get_salesforce_access_token", "src.shared.salesforce_utils.test_salesforce_connection"], "atomsec-func-sfdc/src/shared/service_communication.py": ["logging", "json", "asyncio", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "dataclasses.dataclass", "enum.Enum", "aiohttp", "azure.functions", "src.shared.azure_services.is_local_dev", "src.shared.azure_services.get_secret"], "atomsec-func-sfdc/src/shared/execution_log_coordination_service.py": ["os", "json", "logging", "uuid", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "typing.Set", "dataclasses.dataclass", "dataclasses.field", "dataclasses.asdict", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "enum.Enum", "threading", "<PERSON><PERSON><PERSON>", "src.shared.common.is_local_dev", "src.shared.common.is_test_env", "src.shared.task_sequence_configuration.TaskType", "src.shared.task_sequence_configuration.TaskStatus", "src.shared.task_sequence_configuration.ValidationResult", "src.shared.task_sequence_configuration.ValidationSeverity"], "atomsec-func-sfdc/src/shared/gdpr_compliance_middleware.py": ["logging", "json", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Callable", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "dataclasses.dataclass", "azure.functions", "functools.wraps", "src.shared.data_protection_service.get_data_protection_service", "src.shared.data_protection_service.DataCategory", "src.shared.data_protection_service.ProcessingPurpose", "src.shared.data_protection_service.DataClassification", "src.shared.monitoring.get_monitoring_service"], "atomsec-func-sfdc/src/shared/background_processor.py": ["json", "logging", "ast", "azure.storage.queue.QueueClient", "azure.core.exceptions.ResourceExistsError", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "uuid", "src.shared.config.get_storage_connection_string", "src.shared.common.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.data_access.get_policy_table_repo", "src.shared.data_access.get_rule_table_repo", "src.shared.db_service_client.get_db_client", "src.shared.service_bus_client.get_service_bus_task_client", "json", "src.shared.db_service_client.get_db_client", "traceback", "traceback", "traceback", "unittest.mock.MagicMock", "traceback", "traceback", "src.shared.service_bus_client.get_service_bus_task_client", "re"], "atomsec-func-sfdc/src/shared/standalone_profile_best_practice_analysis.py": ["os", "json", "logging", "xml.etree.ElementTree", "re", "csv", "subprocess", "typing.List", "typing.Dict", "typing.Any"], "atomsec-func-sfdc/src/shared/metadata_extraction.py": ["logging", "json", "io", "zipfile", "base64", "xml.dom.minidom", "xml.etree.ElementTree", "requests", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "typing.BinaryIO", "re", "os", "threading", "azure.storage.blob.BlobClient", "uuid", "lxml.etree", "src.shared.data_access.BlobStorageRepository", "src.shared.common.is_local_dev", "base64", "io", "zipfile", "time", "re", "traceback", "xml.etree.ElementTree"], "atomsec-func-sfdc/src/shared/user_repository.py": ["os", "logging", "<PERSON><PERSON><PERSON>", "secrets", "base64", "datetime.datetime", "datetime.date", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "typing.<PERSON>", "typing.Union", "src.shared.azure_services.is_local_dev", "src.shared.db_service_client.get_db_client", "src.shared.database_models.UserAccount", "src.shared.database_models.UserLogin", "src.shared.database_models.HashingAlgorithm", "src.shared.database_models.Role", "src.shared.database_models.UserRole", "<PERSON><PERSON><PERSON>", "cryptography.hazmat.primitives.kdf.pbkdf2.PBKDF2HMAC", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.backends.default_backend", "base64", "bcrypt"], "atomsec-func-sfdc/src/shared/error_handler.py": ["logging", "time", "json", "os", "sys", "threading", "uuid", "typing.Dict", "typing.Any", "typing.Optional", "typing.Callable", "typing.List", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "functools.wraps", "traceback", "random", "random", "psutil", "signal"], "atomsec-func-sfdc/src/shared/task_performance_monitor.py": ["logging", "time", "functools", "typing.Dict", "typing.Any", "typing.Optional", "typing.Callable", "datetime.datetime", "threading", "src.shared.task_sequence_coordinator.get_task_sequence_coordinator"], "atomsec-func-sfdc/src/shared/performance_optimizer.py": ["logging", "json", "time", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Callable", "typing.Union", "functools.lru_cache", "threading", "collections.defaultdict", "psutil", "requests", "requests.adapters.HTTPAdapter", "urllib3.util.retry.Retry", "weakref", "pyodbc", "redis"], "atomsec-func-sfdc/src/shared/monitoring_dashboards.py": ["json", "os", "logging", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Callable", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "threading", "time", "monitoring.get_performance_monitor", "monitoring.get_task_sequence_monitor", "monitoring.get_metrics_collector"], "atomsec-func-sfdc/src/shared/enhanced_salesforce_credential_manager.py": ["logging", "json", "<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.<PERSON>", "typing.List", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "threading", "src.shared.azure_services.get_secret", "src.shared.azure_services.set_secret", "src.shared.azure_services.get_key_vault_client", "src.shared.azure_services.is_local_dev", "src.shared.salesforce_client.SalesforceClient"], "atomsec-func-sfdc/src/shared/parameter_validation_service.py": ["json", "logging", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "datetime.datetime", "datetime.date", "ast", "re"], "atomsec-func-sfdc/src/shared/api_utils.py": ["json", "logging", "azure.functions", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Union", "datetime.datetime"], "atomsec-func-sfdc/src/shared/task_status_service.py": ["logging", "uuid", "json", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.Task", "src.shared.common.is_local_dev", "src.shared.parameter_validator.get_parameter_validator", "src.shared.parameter_validation_service.get_parameter_validation_service", "time", "random", "ast", "re"], "atomsec-func-sfdc/src/shared/queue_manager.py": ["logging", "json", "uuid", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "src.shared.azure_services.is_local_dev", "src.shared.config.get_storage_connection_string", "src.shared.common.is_local_dev", "src.shared.error_handler.get_queue_error_handler", "src.shared.error_handler.retry_with_error_handler", "azure.storage.queue.QueueClient", "azure.storage.queue.QueueServiceClient", "azure.core.exceptions.ResourceNotFoundError", "azure.core.exceptions.HttpResponseError"], "atomsec-func-sfdc/src/shared/task_sequence_failure_handler.py": ["logging", "json", "uuid", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "typing.Callable", "enum.Enum", "dataclasses.dataclass", "src.shared.error_handler.get_enhanced_error_handler", "src.shared.error_handler.EnhancedRetryPolicy", "src.shared.error_handler.RetryConfig", "src.shared.error_handler.RetryStrategy", "src.shared.error_handler.ErrorCategory"], "atomsec-func-sfdc/src/shared/salesforce_utils.py": ["logging", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "typing.Union", "json", "datetime", "src.shared.salesforce_client.SalesforceClient", "src.shared.azure_services.get_secret", "src.shared.salesforce_jwt_auth.test_jwt_bearer_flow"], "atomsec-func-sfdc/src/api/auth_endpoints.py": ["logging", "json", "os", "secrets", "requests", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "typing.Dict", "typing.Any", "typing.Optional", "urllib.parse", "azure.functions", "src.shared.cors_middleware.add_cors_headers", "jwt", "src.shared.config.get_jwt_config", "src.shared.cors_middleware.handle_cors_preflight", "jwt", "src.shared.config.get_jwt_config", "jwt", "src.shared.config.get_jwt_config"], "atomsec-func-sfdc/src/api/task_endpoints.py": ["logging", "json", "uuid", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.Task", "src.shared.event_publisher.publish_task_event", "src.shared.auth_utils.get_user_from_request_or_default", "src.shared.task_status_service.get_task_status_service", "ast", "src.shared.service_bus_client.get_service_bus_task_client", "src.shared.queue_manager.get_queue_manager"], "atomsec-func-sfdc/src/api/enhanced_task_endpoints.py": ["logging", "json", "uuid", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.security_middleware.secure_endpoint", "src.shared.security_middleware.validate_sequential_task_request", "src.shared.enhanced_parameter_validator.validate_sfdc_request", "src.shared.enhanced_parameter_validator.validate_sequential_task", "src.shared.enhanced_parameter_validator.ParameterValidationError", "src.shared.enhanced_parameter_validator.SecurityValidationError", "src.shared.enhanced_auth_service.authenticate_salesforce_api", "src.shared.enhanced_auth_service.SalesforceAuthenticationError", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.Task", "src.shared.event_publisher.publish_task_event", "src.shared.auth_utils.get_user_from_request_or_default", "src.shared.security_middleware.internal_service_endpoint", "api.task_endpoints.create_task", "api.task_endpoints.create_task", "src.shared.task_sequence_coordinator.get_task_sequence_coordinator", "src.shared.task_sequence_coordinator.get_task_sequence_coordinator", "src.shared.task_sequence_coordinator.get_task_sequence_coordinator", "api.task_endpoints.create_task", "src.shared.enhanced_parameter_validator.get_enhanced_parameter_validator", "src.shared.enhanced_auth_service.get_enhanced_auth_service", "src.shared.internal_service_security.get_internal_service_security", "src.shared.enhanced_salesforce_credential_manager.get_salesforce_credential_manager", "src.shared.enhanced_salesforce_credential_manager.get_salesforce_credential_manager", "src.shared.enhanced_salesforce_credential_manager.get_salesforce_credential_manager", "src.shared.enhanced_salesforce_credential_manager.get_salesforce_credential_manager", "src.shared.enhanced_parameter_validator.get_enhanced_parameter_validator", "src.shared.enhanced_auth_service.get_enhanced_auth_service", "src.shared.internal_service_security.get_internal_service_security", "src.shared.enhanced_salesforce_credential_manager.get_salesforce_credential_manager"], "atomsec-func-sfdc/src/api/integration_endpoints.py": ["logging", "json", "uuid", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.Organization", "src.shared.database_models_new.Credentials", "src.shared.auth_utils.get_user_from_request_or_default", "src.shared.auth_utils.get_current_user", "datetime.datetime", "datetime.datetime", "datetime.datetime", "datetime.datetime", "src.shared.azure_services.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.azure_services.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.azure_services.is_local_dev", "src.shared.data_access.get_table_storage_repository", "repositories.pmd_repository.PMDRepository", "task_endpoints.create_task", "security_endpoints.get_security_health_check_data", "simple_salesforce.Salesforce", "traceback", "requests"], "atomsec-func-sfdc/src/api/security_endpoints.py": ["logging", "json", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.HealthCheck", "src.shared.database_models_new.ProfilePermission", "src.shared.database_models_new.Overview", "src.shared.database_models_new.ExecutionLog", "src.shared.database_models_new.ProfileAssignmentCount", "src.shared.database_models_new.PoliciesResult"], "atomsec-func-sfdc/src/api/organization_endpoints.py": ["logging", "json", "uuid", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.Organization"], "atomsec-func-sfdc/src/api/__init__.py": [], "atomsec-func-sfdc/src/api/cors_handler.py": ["logging", "azure.functions", "src.shared.cors_middleware.handle_cors_preflight"], "atomsec-func-sfdc/src/api/pmd_endpoints.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "typing.List", "repositories.pmd_repository.PMDRepository"], "atomsec-func-sfdc/src/api/key_vault_endpoints.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "typing.Optional", "azure.functions.Blueprint", "src.shared.azure_services.is_local_dev", "src.shared.azure_services.get_current_user_id", "src.shared.azure_services.get_key_vault_client", "src.shared.service_bus_client.get_service_bus_client", "re", "secrets", "string"], "atomsec-func-sfdc/src/api/user_endpoints.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "src.shared.db_service_client.get_db_client", "secrets", "jwt", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "secrets", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "src.shared.config.get_jwt_config", "jwt", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "src.shared.config.get_jwt_config"], "atomsec-func-sfdc/src/api/account_endpoints.py": ["logging", "json", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.Account", "random"], "atomsec-func-sfdc/src/api/sfdc_proxy_endpoints.py": ["logging", "json", "azure.functions", "src.shared.sfdc_service_client.get_sfdc_client", "src.shared.cors_middleware.add_cors_headers", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight"], "atomsec-func-sfdc/src/api/user_profile_endpoints.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "typing.Optional", "azure.functions.Blueprint", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository", "src.shared.azure_services.is_local_dev", "src.shared.azure_services.get_current_user_id", "src.shared.service_bus_client.get_service_bus_client"], "atomsec-func-sfdc/src/api/policy_endpoints.py": ["logging", "json", "uuid", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.common.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository"], "atomsec-func-sfdc/src/pmd_components/pmd_rules_config.py": ["os", "logging", "typing.List", "typing.Dict", "typing.Any", "typing.Optional", "pathlib.Path", "xml.etree.ElementTree", "xml.etree.ElementTree"], "atomsec-func-sfdc/src/pmd_components/pmd_results_processor.py": ["logging", "json", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "datetime.datetime", "sys", "os", "src.shared.db_service_client.get_db_client"], "atomsec-func-sfdc/src/pmd_components/test_pmd_components.py": ["sys", "os", "logging", "src.pmd_components.pmd_rules_config.PMDRulesConfig", "src.pmd_components.pmd_scanner.PMDScanner", "src.pmd_components.pmd_blob_handler.PMDBlobHandler", "src.pmd_components.pmd_results_processor.PMDResultsProcessor", "src.task_processor.tasks.pmd_task.process_pmd_task"], "atomsec-func-sfdc/src/pmd_components/__init__.py": ["pmd_rules_config.PMDRulesConfig", "pmd_scanner.PMDScanner", "pmd_blob_handler.PMDBlobHandler", "pmd_results_processor.PMDResultsProcessor"], "atomsec-func-sfdc/src/pmd_components/pmd_scanner.py": ["logging", "os", "platform", "subprocess", "tempfile", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.<PERSON>", "datetime.datetime", "pmd_rules_config.PMDRulesConfig"], "atomsec-func-sfdc/src/pmd_components/pmd_blob_handler.py": ["logging", "os", "tempfile", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Iterator", "typing.<PERSON>", "datetime.datetime", "sys", "src.shared.data_access.BlobStorageRepository", "shutil"], "atomsec-func-db-r/test_enhanced_queue_manager.py": ["pytest", "unittest", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "json", "shared.enhanced_queue_manager.EnhancedQueueManager", "shared.enhanced_queue_manager.get_enhanced_queue_manager", "shared.enhanced_queue_manager.QueuePriority", "shared.enhanced_queue_manager.MessageState", "shared.enhanced_queue_manager.ScalingAction", "shared.enhanced_queue_manager.QueueMetrics", "shared.enhanced_queue_manager.MessageMetadata", "shared.enhanced_queue_manager.DeadLetterInfo"], "atomsec-func-db-r/test_enhanced_task_status_service.py": ["unittest", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "datetime.datetime", "json", "shared.task_status_service.TaskStatusService", "shared.task_status_service.TASK_STATUS_PENDING", "shared.task_status_service.TASK_STATUS_RUNNING", "shared.task_status_service.TASK_STATUS_COMPLETED", "shared.task_status_service.TASK_STATUS_FAILED", "shared.task_status_service.TASK_PRIORITY_MEDIUM"], "atomsec-func-db-r/test_enhanced_database_security.py": ["unittest", "threading", "time", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "shared.enhanced_repository_security.EnhancedSecurityRepository", "shared.enhanced_repository_security.SecureConnectionManager", "shared.enhanced_repository_security.QuerySecurityValidator", "shared.enhanced_repository_security.DatabaseAuditLogger", "shared.enhanced_repository_security.SecurityLevel", "shared.enhanced_repository_security.QueryType", "shared.enhanced_repository_security.SecurityValidationResult", "shared.advanced_connection_pool.AdvancedConnectionPool", "shared.advanced_connection_pool.DualStorageConnectionManager", "shared.advanced_connection_pool.StorageType", "shared.advanced_connection_pool.ConnectionState", "shared.advanced_connection_pool.PoolState", "shared.advanced_connection_pool.get_connection_manager", "shared.transaction_manager.DistributedTransactionManager", "shared.transaction_manager.TransactionOperation", "shared.transaction_manager.TransactionContext", "shared.transaction_manager.ConsistencyLevel", "shared.transaction_manager.ConflictResolutionStrategy", "shared.transaction_manager.get_transaction_manager", "shared.transaction_manager.transaction_scope", "logging"], "atomsec-func-db-r/function_app.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "api.user_endpoints.bp", "api.account_endpoints.bp", "api.organization_endpoints.bp", "api.integration_endpoints.bp", "api.security_endpoints.bp", "api.task_endpoints.bp", "api.auth_endpoints.bp", "api.policy_endpoints.bp", "api.cors_handler.bp", "api.sfdc_proxy_endpoints.bp", "api.user_profile_endpoints.bp", "api.key_vault_endpoints.bp", "api.general_endpoints.bp", "api.pmd_endpoints.bp", "api.task_processor_endpoints.bp", "api.scan_endpoints.bp", "api.task_hierarchy_endpoints.bp", "api.performance_endpoints.bp", "api.compliance_endpoints.bp", "src.shared.data_access.get_table_storage_repository", "src.shared.azure_services.is_local_dev", "src.shared.service_bus_client.get_service_bus_client", "src.shared.data_access.get_sql_database_repository"], "atomsec-func-db-r/test_task_coordination_service.py": ["pytest", "uuid", "datetime.datetime", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "typing.Dict", "typing.Any", "shared.task_coordination_service.TaskCoordinationService", "shared.task_coordination_service.get_task_coordination_service"], "atomsec-func-db-r/test_execution_context_manager.py": ["unittest", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "datetime.datetime", "json", "sys", "os", "shared.execution_context_manager.ExecutionContextManager", "shared.execution_context_manager.get_execution_context_manager", "shared.execution_context_manager.EXECUTION_STATUS_PENDING", "shared.execution_context_manager.EXECUTION_STATUS_RUNNING", "shared.execution_context_manager.EXECUTION_STATUS_COMPLETED", "shared.execution_context_manager.EXECUTION_STATUS_FAILED", "shared.execution_context_manager.EXECUTION_STATUS_CANCELLED"], "atomsec-func-db-r/run_integration_tests.py": ["os", "sys", "unittest", "<PERSON><PERSON><PERSON><PERSON>", "logging", "datetime.datetime"], "atomsec-func-db-r/test_enhanced_task_coordination_service.py": ["pytest", "unittest", "unittest.mock.Mock", "unittest.mock.patch", "unittest.mock.MagicMock", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "json", "shared.enhanced_task_coordination_service.EnhancedTaskCoordinationService", "shared.enhanced_task_coordination_service.get_enhanced_task_coordination_service", "shared.enhanced_task_coordination_service.DependencyType", "shared.enhanced_task_coordination_service.SchedulingStrategy", "shared.enhanced_task_coordination_service.ExecutionMode", "shared.enhanced_task_coordination_service.TaskDependency", "shared.enhanced_task_coordination_service.TaskSchedule", "shared.enhanced_task_coordination_service.ExecutionPlan"], "atomsec-func-db-r/tests/__init__.py": [], "atomsec-func-db-r/tests/unit/__init__.py": [], "atomsec-func-db-r/tests/integration/test_queue_based_processing.py": ["os", "sys", "uuid", "time", "unittest", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "unittest.mock.patch", "unittest.mock.MagicMock", "shared.queue_manager.get_queue_manager", "shared.queue_manager.QueueManager", "shared.task_coordination_service.TaskCoordinationService", "shared.task_status_service.get_task_status_service", "shared.task_status_service.TaskStatusService", "shared.execution_context_manager.ExecutionContextManager", "shared.common.is_local_dev"], "atomsec-func-db-r/tests/integration/test_task_status_aggregation.py": ["os", "sys", "uuid", "time", "unittest", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "unittest.mock.patch", "unittest.mock.MagicMock", "shared.task_status_service.get_task_status_service", "shared.task_status_service.TaskStatusService", "shared.execution_context_manager.ExecutionContextManager"], "atomsec-func-db-r/tests/integration/__init__.py": [], "atomsec-func-db-r/tests/integration/test_queue_message_processing.py": ["os", "sys", "uuid", "time", "unittest", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "unittest.mock.patch", "unittest.mock.MagicMock", "shared.queue_manager.get_queue_manager", "shared.queue_manager.QueueManager", "shared.queue_message_processor.QueueMessageProcessor"], "atomsec-func-db-r/tests/fixtures/__init__.py": [], "atomsec-func-db-r/scripts/migrate_database_schema.py": ["logging", "sys", "os", "datetime.datetime", "shared.data_access.SqlDatabaseRepository", "shared.data_access.TableStorageRepository", "shared.common.is_local_dev", "shared.azure_services.get_secret"], "atomsec-func-db-r/scripts/test_migration.py": ["requests", "json", "time", "uuid", "datetime.datetime", "typing.Dict", "typing.Any", "typing.List", "<PERSON><PERSON><PERSON><PERSON>", "logging"], "atomsec-func-db-r/scripts/migrate_to_queue_based_processing.py": ["os", "sys", "<PERSON><PERSON><PERSON><PERSON>", "logging", "json", "uuid", "datetime.datetime", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.<PERSON>", "shared.common.is_local_dev", "shared.data_access.SqlDatabaseRepository", "shared.data_access.TableStorageRepository", "shared.azure_services.get_secret", "shared.feature_flags.get_feature_flag_manager", "shared.feature_flags.FeatureFlagManager", "shared.queue_manager.get_queue_manager"], "atomsec-func-db-r/scripts/deploy_queue_infrastructure.py": ["os", "sys", "<PERSON><PERSON><PERSON><PERSON>", "logging", "json", "uuid", "datetime.datetime", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.<PERSON>", "shared.common.is_local_dev", "shared.azure_services.get_secret", "azure.storage.queue.QueueServiceClient", "azure.storage.queue.QueueServiceClient", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "azure.storage.queue.QueueSasPermissions"], "atomsec-func-db-r/scripts/setup_queues.py": ["logging", "sys", "os", "shared.queue_manager.get_queue_manager", "shared.queue_manager.PRIORITY_QUEUES", "shared.queue_manager.DEFAULT_QUEUE_NAME", "shared.common.is_local_dev"], "atomsec-func-db-r/src/__init__.py": [], "atomsec-func-db-r/src/repositories/base_repository.py": ["logging", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Union", "datetime.datetime", "src.shared.db_service_client.get_db_client"], "atomsec-func-db-r/src/repositories/__init__.py": [], "atomsec-func-db-r/src/repositories/pmd_repository.py": ["logging", "json", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "datetime.datetime", "base_repository.BaseRepository", "src.shared.data_access.is_local_dev", "src.shared.data_access.get_table_storage_repository"], "atomsec-func-db-r/src/repositories/user_repository.py": ["logging", "secrets", "<PERSON><PERSON><PERSON>", "datetime.datetime", "datetime.date", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "typing.<PERSON>", "typing.Union", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models.UserAccount", "src.shared.database_models.UserLogin", "src.shared.database_models.Role", "src.shared.database_models.UserRole", "src.shared.database_models_new.User", "src.shared.database_models_new.Account", "src.shared.database_models_new.Role", "src.shared.database_models_new.UserRole", "cryptography.hazmat.primitives.kdf.pbkdf2.PBKDF2HMAC", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.backends.default_backend", "base64", "bcrypt", "random", "random"], "atomsec-func-db-r/src/shared/mock_service_bus.py": ["logging", "json", "requests", "typing.Dict", "typing.Any", "typing.Optional", "os"], "atomsec-func-db-r/src/shared/api_gateway.py": ["logging", "json", "time", "<PERSON><PERSON><PERSON>", "gzip", "typing.Dict", "typing.Any", "typing.Optional", "typing.<PERSON>", "typing.List", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "azure.functions", "src.shared.config.is_local_dev", "os"], "atomsec-func-db-r/src/shared/cors_middleware.py": ["logging", "azure.functions", "src.shared.config.is_local_dev", "src.shared.azure_services.get_secret"], "atomsec-func-db-r/src/shared/advanced_connection_pool.py": ["logging", "threading", "time", "queue", "statistics", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Union", "typing.Callable", "dataclasses.dataclass", "dataclasses.field", "enum.Enum", "asyncio", "concurrent.futures.ThreadPoolExecutor", "concurrent.futures.Future", "weakref", "src.shared.azure_services.get_table_service_client", "src.shared.azure_services.get_credential", "pyodbc", "struct", "src.shared.common.is_local_dev", "random"], "atomsec-func-db-r/src/shared/execution_context_manager.py": ["logging", "uuid", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "src.shared.execution_log_service.get_execution_log_service", "src.shared.execution_log_service.ExecutionLogService", "src.shared.task_status_service.get_task_status_service", "src.shared.task_status_service.TaskStatusService", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "json", "json", "json", "json", "json", "json", "json"], "atomsec-func-db-r/src/shared/config.py": ["os", "logging", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.common.is_local_dev", "src.shared.common.is_test_env", "src.shared.azure_services.get_secret", "src.shared.azure_services.get_secret", "src.shared.azure_services.get_secret", "src.shared.azure_services.get_secret", "src.shared.azure_services.get_secret", "traceback", "traceback"], "atomsec-func-db-r/src/shared/secure_queue_manager.py": ["logging", "json", "os", "time", "base64", "uuid", "datetime.datetime", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Union", "typing.<PERSON>", "src.shared.queue_manager.QueueManager", "src.shared.queue_manager.get_queue_manager", "src.shared.common.is_local_dev", "src.shared.azure_services.get_secret", "cryptography.hazmat.primitives.ciphers.Cipher", "cryptography.hazmat.primitives.ciphers.algorithms", "cryptography.hazmat.primitives.ciphers.modes", "cryptography.hazmat.backends.default_backend", "cryptography.hazmat.primitives.ciphers.Cipher", "cryptography.hazmat.primitives.ciphers.algorithms", "cryptography.hazmat.primitives.ciphers.modes", "cryptography.hazmat.backends.default_backend"], "atomsec-func-db-r/src/shared/task_coordination_service.py": ["logging", "uuid", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "src.shared.execution_log_service.get_execution_log_service", "src.shared.execution_log_service.ExecutionLogService", "src.shared.task_status_service.get_task_status_service", "src.shared.task_status_service.TaskStatusService", "src.shared.queue_manager.get_queue_manager", "src.shared.queue_manager.QueueManager", "src.shared.secure_queue_manager.get_secure_queue_manager", "src.shared.task_orchestration_validator.get_task_orchestration_validator", "src.shared.audit_logger.log_task_creation", "src.shared.audit_logger.log_task_status_update"], "atomsec-func-db-r/src/shared/database_models.py": ["dataclasses.dataclass", "datetime.datetime", "datetime.date", "typing.Optional", "typing.List", "typing.Dict", "typing.Any"], "atomsec-func-db-r/src/shared/scan_initiation.py": ["logging", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "src.shared.background_processor.get_background_processor", "src.shared.background_processor.TASK_TYPE_OVERVIEW", "src.shared.background_processor.TASK_TYPE_HEALTH_CHECK", "src.shared.background_processor.TASK_TYPE_PROFILES", "src.shared.background_processor.TASK_TYPE_PROFILES_PERMISSION_SETS", "src.shared.background_processor.TASK_TYPE_PERMISSION_SETS", "src.shared.background_processor.TASK_TYPE_METADATA_EXTRACTION", "src.shared.background_processor.TASK_TYPE_PMD_APEX_SECURITY", "src.shared.background_processor.TASK_TYPE_SFDC_AUTHENTICATE", "src.shared.background_processor.TASK_PRIORITY_HIGH", "src.shared.background_processor.TASK_PRIORITY_MEDIUM", "src.shared.background_processor.TASK_PRIORITY_LOW", "src.shared.policy_management.get_policy_management_service", "src.shared.pmd_subtask_service.get_pmd_subtask_service", "src.shared.execution_log_service.get_execution_log_service", "src.shared.sfdc_service_client.get_sfdc_client", "traceback"], "atomsec-func-db-r/src/shared/request_processor.py": ["logging", "json", "time", "asyncio", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Callable", "typing.<PERSON>", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "dataclasses.dataclass", "dataclasses.field", "enum.Enum", "queue.PriorityQueue", "queue.Queue", "threading", "uuid", "concurrent.futures.ThreadPoolExecutor", "concurrent.futures.Future", "azure.functions", "re"], "atomsec-func-db-r/src/shared/database_models_new.py": ["dataclasses.dataclass", "datetime.datetime", "datetime.date", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "json"], "atomsec-func-db-r/src/shared/service_bus_client.py": ["logging", "json", "os", "typing.Dict", "typing.Any", "typing.Optional", "datetime.datetime", "azure.servicebus.ServiceBusClient", "azure.servicebus.ServiceBusMessage", "azure.servicebus.exceptions.ServiceBusError"], "atomsec-func-db-r/src/shared/azure_services.py": ["os", "logging", "uuid", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.common.is_local_dev", "src.shared.common.is_test_env", "azure.identity.DefaultAzureCredential", "azure.identity.AzureCliCredential", "azure.keyvault.secrets.SecretClient", "azure.mgmt.keyvault.KeyVaultManagementClient", "azure.mgmt.keyvault.models.VaultCreateOrUpdateParameters", "azure.mgmt.keyvault.models.VaultProperties", "azure.mgmt.keyvault.models.Sku", "azure.mgmt.keyvault.models.SkuName", "azure.mgmt.keyvault.models.AccessPolicyEntry", "azure.mgmt.keyvault.models.Permissions", "azure.mgmt.keyvault.models.SecretPermissions", "azure.mgmt.keyvault.models.KeyPermissions", "azure.mgmt.keyvault.models.CertificatePermissions", "azure.mgmt.keyvault.models.StoragePermissions", "azure.storage.blob.BlobServiceClient", "azure.storage.queue.QueueServiceClient", "azure.data.tables.TableServiceClient", "unittest.mock.MagicMock", "src.shared.config.get_sql_connection_string", "subprocess", "json", "src.shared.config.get_key_vault_url", "unittest.mock.MagicMock", "unittest.mock.MagicMock", "unittest.mock.MagicMock", "subprocess", "json", "src.shared.config.get_storage_connection_string", "src.shared.config.get_storage_connection_string", "src.shared.config.get_storage_connection_string", "azure.data.tables.TableServiceClient", "src.shared.config.get_storage_connection_string", "azure.data.tables.TableServiceClient", "src.shared.config.get_storage_connection_string", "azure.data.tables.TableServiceClient", "src.shared.config.get_storage_connection_string", "azure.data.tables.TableServiceClient", "azure.data.tables.TableClient", "src.shared.config.get_storage_connection_string"], "atomsec-func-db-r/src/shared/queue_performance_monitor.py": ["logging", "time", "threading", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.<PERSON>", "src.shared.queue_manager.get_queue_manager", "src.shared.queue_manager.QueueManager", "src.shared.queue_manager.PRIORITY_QUEUES", "src.shared.common.is_local_dev"], "atomsec-func-db-r/src/shared/auth_utils.py": ["logging", "jwt", "typing.Dict", "typing.Any", "typing.Optional", "azure.functions", "datetime.datetime", "datetime.timezone", "src.shared.config.get_jwt_config", "src.shared.azure_services.is_local_dev", "functools", "src.shared.azure_services.is_local_dev", "src.shared.rbac_service.rbac_service", "src.shared.rbac_service.rbac_service", "src.shared.rbac_service.AccessContext", "src.shared.rbac_service.ResourceType", "src.shared.rbac_service.PermissionType", "repositories.user_repository.UserRepository", "json"], "atomsec-func-db-r/src/shared/__init__.py": [], "atomsec-func-db-r/src/shared/feature_flags.py": ["logging", "json", "os", "time", "threading", "datetime.datetime", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Union", "typing.Set", "src.shared.common.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "random"], "atomsec-func-db-r/src/shared/mfa_service.py": ["logging", "secrets", "base64", "qrcode", "io", "smtplib", "os", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "dataclasses.dataclass", "dataclasses.field", "enum.Enum", "pyotp", "azure.functions"], "atomsec-func-db-r/src/shared/db_service_client.py": ["logging", "json", "requests", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "os", "src.shared.config.get_db_service_config", "base64", "base64", "time", "time", "time"], "atomsec-func-db-r/src/shared/sfdc_service_client.py": ["logging", "requests", "json", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "src.shared.config.is_local_dev", "os"], "atomsec-func-db-r/src/shared/execution_log_service.py": ["logging", "uuid", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository"], "atomsec-func-db-r/src/shared/enhanced_sfdc_proxy.py": ["logging", "time", "json", "<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "requests", "src.shared.config.is_local_dev", "src.shared.sfdc_service_client.SFDCServiceClient", "os", "random"], "atomsec-func-db-r/src/shared/data_access.py": ["json", "logging", "struct", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "typing.Union", "src.shared.azure_services.get_blob_client", "src.shared.azure_services.get_table_client", "src.shared.azure_services.get_queue_client", "src.shared.azure_services.get_credential", "src.shared.azure_services.is_local_dev", "os", "uuid", "yaml", "api.policy_endpoints.store_policy_data", "api.policy_endpoints.store_rule_data", "repositories.pmd_repository.PMDRepository", "pyodbc", "src.shared.azure_services.get_credential"], "atomsec-func-db-r/src/shared/event_publisher.py": ["logging", "json", "os", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "azure.servicebus.ServiceBusClient", "azure.servicebus.ServiceBusMessage", "src.shared.common.is_local_dev", "src.shared.common.is_local_dev", "mock_service_bus.get_mock_service_bus_client"], "atomsec-func-db-r/src/shared/audit_logger.py": ["logging", "json", "os", "time", "uuid", "<PERSON><PERSON><PERSON>", "hmac", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Union", "typing.Set", "src.shared.common.is_local_dev", "src.shared.azure_services.get_secret", "os", "base64", "os"], "atomsec-func-db-r/src/shared/common.py": ["os", "logging"], "atomsec-func-db-r/src/shared/policy_management.py": ["logging", "uuid", "json", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.common.is_local_dev"], "atomsec-func-db-r/src/shared/enhanced_queue_manager.py": [], "atomsec-func-db-r/src/shared/pmd_subtask_service.py": ["logging", "uuid", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.common.is_local_dev"], "atomsec-func-db-r/src/shared/service_communication.py": ["logging", "json", "requests", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "os", "time", "src.shared.common.is_local_dev", "src.shared.azure_services.get_secret"], "atomsec-func-db-r/src/shared/optimized_query_manager.py": ["logging", "time", "threading", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.<PERSON>", "typing.Union", "typing.Callable", "functools.wraps", "src.shared.data_access.SqlDatabaseRepository", "src.shared.data_access.TableStorageRepository", "src.shared.common.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository"], "atomsec-func-db-r/src/shared/api_middleware.py": ["logging", "json", "functools", "typing.Callable", "typing.Dict", "typing.Any", "typing.Optional", "azure.functions", "src.shared.api_gateway.get_api_gateway", "src.shared.api_gateway.RateLimitResult", "src.shared.cors_middleware.add_cors_headers", "re", "src.shared.api_versioning.with_api_versioning", "src.shared.request_processor.get_request_processor", "src.shared.request_processor.ProcessingRequest", "src.shared.request_processor.RequestMetadata", "src.shared.request_processor.RequestPriority", "src.shared.request_processor.RequestType", "uuid", "datetime.datetime"], "atomsec-func-db-r/src/shared/background_processor.py": ["logging", "json", "uuid", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "os", "traceback", "src.shared.azure_services.get_queue_client", "src.shared.azure_services.is_local_dev", "src.shared.task_status_service.get_task_status_service", "src.shared.execution_log_service.get_execution_log_service", "src.shared.service_bus_client.get_service_bus_client", "src.shared.mock_service_bus.MockServiceBusClient", "src.shared.service_communication.get_service_communication_client", "src.shared.data_access.TableStorageRepository", "ast", "re"], "atomsec-func-db-r/src/shared/enhanced_azure_ad_service.py": ["logging", "json", "secrets", "base64", "<PERSON><PERSON><PERSON>", "requests", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "dataclasses.dataclass", "enum.Enum", "azure.functions", "os"], "atomsec-func-db-r/src/shared/task_orchestration_validator.py": ["logging", "typing.Dict", "typing.Any", "typing.List"], "atomsec-func-db-r/src/shared/transaction_manager.py": ["logging", "threading", "time", "json", "uuid", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Union", "typing.Callable", "typing.<PERSON>", "dataclasses.dataclass", "dataclasses.field", "enum.Enum", "contextlib.contextmanager", "asyncio", "concurrent.futures.ThreadPoolExecutor", "concurrent.futures.Future", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository", "src.shared.common.is_local_dev", "src.shared.common.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository", "src.shared.common.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository", "src.shared.common.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository", "src.shared.common.is_local_dev"], "atomsec-func-db-r/src/shared/enhanced_repository_security.py": ["logging", "<PERSON><PERSON><PERSON>", "secrets", "re", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.List", "typing.Optional", "typing.Union", "typing.<PERSON>", "dataclasses.dataclass", "enum.Enum", "base64", "cryptography.fernet.Fernet", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.primitives.kdf.pbkdf2.PBKDF2HMAC", "src.shared.azure_services.get_secret_from_key_vault", "src.shared.azure_services.get_secret_from_key_vault", "src.shared.common.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository", "src.shared.common.is_local_dev", "src.shared.common.is_local_dev", "src.shared.common.is_local_dev", "os"], "atomsec-func-db-r/src/shared/enhanced_task_coordination_service.py": ["logging", "uuid", "asyncio", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "typing.Set", "typing.<PERSON>", "dataclasses.dataclass", "dataclasses.field", "enum.Enum", "json", "src.shared.task_coordination_service.TaskCoordinationService", "src.shared.task_coordination_service.get_task_coordination_service", "src.shared.execution_log_service.get_execution_log_service", "src.shared.execution_log_service.ExecutionLogService", "src.shared.task_status_service.get_task_status_service", "src.shared.task_status_service.TaskStatusService", "src.shared.queue_manager.get_queue_manager", "src.shared.queue_manager.QueueManager", "src.shared.secure_queue_manager.get_secure_queue_manager", "src.shared.audit_logger.log_task_creation", "src.shared.audit_logger.log_task_status_update", "src.shared.optimized_query_manager.get_optimized_query_manager"], "atomsec-func-db-r/src/shared/enhanced_jwt_service.py": ["logging", "jwt", "secrets", "<PERSON><PERSON><PERSON>", "json", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.<PERSON>", "dataclasses.dataclass", "dataclasses.asdict", "enum.Enum", "azure.functions", "src.shared.config.get_jwt_config", "repositories.user_repository.UserRepository"], "atomsec-func-db-r/src/shared/task_status_service.py": ["logging", "uuid", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "json", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.Task", "src.shared.common.is_local_dev", "src.shared.optimized_query_manager.get_optimized_query_manager", "src.shared.optimized_query_manager.query_metrics_decorator"], "atomsec-func-db-r/src/shared/api_versioning.py": ["logging", "json", "re", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Callable", "typing.<PERSON>", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "dataclasses.dataclass", "dataclasses.field", "enum.Enum", "azure.functions", "src.shared.cors_middleware.add_cors_headers"], "atomsec-func-db-r/src/shared/db_service.py": ["typing.Optional", "typing.List", "src.shared.database_models_new.ExecutionLog", "src.shared.azure_services.is_local_dev", "src.shared.data_access.get_sql_database_repository", "src.shared.data_access.get_table_storage_repository", "datetime.datetime"], "atomsec-func-db-r/src/shared/queue_manager.py": ["logging", "json", "base64", "uuid", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "os", "src.shared.azure_services.get_queue_client", "src.shared.azure_services.is_local_dev", "src.shared.common.is_local_dev"], "atomsec-func-db-r/src/shared/enhanced_execution_context_manager.py": ["logging", "uuid", "asyncio", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Union", "typing.Set", "typing.Callable", "dataclasses.dataclass", "dataclasses.field", "enum.Enum", "json", "collections.defaultdict", "collections.deque", "src.shared.execution_context_manager.ExecutionContextManager", "src.shared.execution_context_manager.get_execution_context_manager", "src.shared.execution_log_service.get_execution_log_service", "src.shared.execution_log_service.ExecutionLogService", "src.shared.task_status_service.get_task_status_service", "src.shared.task_status_service.TaskStatusService", "src.shared.audit_logger.log_task_creation", "src.shared.audit_logger.log_task_status_update"], "atomsec-func-db-r/src/shared/rbac_service.py": ["logging", "json", "datetime.datetime", "datetime.timezone", "typing.Dict", "typing.Any", "typing.Optional", "typing.List", "typing.Set", "typing.<PERSON>", "dataclasses.dataclass", "dataclasses.field", "enum.Enum", "azure.functions", "functools", "src.shared.auth_utils.get_current_user", "json", "json", "json"], "atomsec-func-db-r/src/api/general_endpoints.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "typing.Optional", "azure.functions.Blueprint", "src.shared.azure_services.is_local_dev", "src.shared.azure_services.get_current_user_id", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository", "src.shared.data_access.create_default_policies_and_rules_for_integration", "src.shared.service_bus_client.get_service_bus_client", "src.shared.service_bus_client.get_service_bus_client"], "atomsec-func-db-r/src/api/compliance_endpoints.py": ["logging", "json", "azure.functions", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.audit_logger.get_audit_logger", "src.shared.secure_queue_manager.get_secure_queue_manager", "src.shared.auth_utils.get_user_from_request_or_default"], "atomsec-func-db-r/src/api/auth_endpoints.py": ["logging", "json", "os", "secrets", "requests", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "typing.Dict", "typing.Any", "typing.Optional", "urllib.parse", "azure.functions", "src.shared.cors_middleware.add_cors_headers", "jwt", "src.shared.config.get_jwt_config", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.enhanced_azure_ad_service.enhanced_azure_ad_service", "src.shared.enhanced_azure_ad_service.AuthFlow", "src.shared.enhanced_azure_ad_service.enhanced_azure_ad_service", "src.shared.enhanced_azure_ad_service.ConditionalAccessResult", "src.shared.enhanced_jwt_service.enhanced_jwt_service", "src.shared.enhanced_jwt_service.enhanced_jwt_service", "src.shared.enhanced_jwt_service.enhanced_jwt_service", "src.shared.enhanced_jwt_service.enhanced_jwt_service", "src.shared.enhanced_jwt_service.enhanced_jwt_service", "src.shared.enhanced_azure_ad_service.enhanced_azure_ad_service", "src.shared.enhanced_azure_ad_service.enhanced_azure_ad_service", "src.shared.enhanced_jwt_service.enhanced_jwt_service", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.mfa_service.mfa_service", "src.shared.enhanced_jwt_service.enhanced_jwt_service", "repositories.user_repository.UserRepository", "jwt", "src.shared.config.get_jwt_config", "src.shared.enhanced_jwt_service.enhanced_jwt_service", "repositories.user_repository.UserRepository", "repositories.user_repository.UserRepository", "src.shared.enhanced_jwt_service.enhanced_jwt_service"], "atomsec-func-db-r/src/api/task_endpoints.py": ["logging", "json", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.Task", "src.shared.event_publisher.publish_task_event", "src.shared.auth_utils.get_user_from_request_or_default", "src.shared.task_status_service.get_task_status_service", "src.shared.background_processor.get_background_processor", "src.shared.task_status_service.get_task_status_service", "src.shared.task_status_service.get_task_status_service", "src.shared.task_status_service.get_task_status_service"], "atomsec-func-db-r/src/api/mfa_endpoints.py": ["logging", "json", "base64", "datetime.datetime", "datetime.timezone", "typing.Dict", "typing.Any", "typing.Optional", "azure.functions", "src.shared.cors_middleware.add_cors_headers", "src.shared.mfa_service.mfa_service", "src.shared.mfa_service.MFAMethod", "src.shared.auth_utils.require_auth", "src.shared.auth_utils.get_current_user", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight"], "atomsec-func-db-r/src/api/integration_endpoints.py": ["logging", "json", "uuid", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.data_access.create_default_policies_and_rules_for_integration", "src.shared.database_models_new.Organization", "src.shared.database_models_new.Credentials", "src.shared.auth_utils.get_user_from_request_or_default", "src.shared.auth_utils.get_current_user", "src.shared.execution_log_service.get_execution_log_service", "datetime.datetime", "datetime.datetime", "datetime.datetime", "datetime.datetime", "src.shared.azure_services.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.azure_services.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.azure_services.is_local_dev", "src.shared.data_access.get_table_storage_repository", "repositories.pmd_repository.PMDRepository", "src.shared.sfdc_service_client.get_sfdc_client", "src.shared.sfdc_service_client.get_sfdc_client", "src.shared.sfdc_service_client.get_sfdc_client", "repositories.pmd_repository.PMDRepository", "security_endpoints.get_security_health_check_data", "repositories.pmd_repository.PMDRepository", "src.shared.auth_utils.get_current_user", "traceback", "src.shared.auth_utils.get_current_user", "traceback"], "atomsec-func-db-r/src/api/security_endpoints.py": ["logging", "json", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.HealthCheck", "src.shared.database_models_new.ProfilePermission", "src.shared.database_models_new.Overview", "src.shared.database_models_new.ExecutionLog", "src.shared.database_models_new.ProfileAssignmentCount", "src.shared.database_models_new.PoliciesResult"], "atomsec-func-db-r/src/api/organization_endpoints.py": ["logging", "json", "uuid", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.Organization"], "atomsec-func-db-r/src/api/__init__.py": [], "atomsec-func-db-r/src/api/cors_handler.py": ["logging", "azure.functions", "src.shared.cors_middleware.handle_cors_preflight"], "atomsec-func-db-r/src/api/performance_endpoints.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.queue_performance_monitor.get_queue_performance_monitor", "src.shared.queue_performance_monitor.start_queue_monitoring", "src.shared.queue_performance_monitor.stop_queue_monitoring", "src.shared.queue_performance_monitor.get_performance_metrics", "src.shared.optimized_query_manager.get_optimized_query_manager", "src.shared.auth_utils.get_user_from_request_or_default"], "atomsec-func-db-r/src/api/scan_endpoints.py": ["logging", "json", "azure.functions", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.scan_initiation.get_scan_initiation_service", "src.shared.background_processor.TASK_TYPE_OVERVIEW", "src.shared.background_processor.TASK_TYPE_HEALTH_CHECK", "src.shared.background_processor.TASK_TYPE_PROFILES", "src.shared.background_processor.TASK_TYPE_PMD_APEX_SECURITY", "src.shared.background_processor.TASK_PRIORITY_HIGH", "src.shared.background_processor.TASK_PRIORITY_MEDIUM", "src.shared.background_processor.TASK_PRIORITY_LOW", "src.shared.execution_log_service.get_execution_log_service", "src.shared.cors_middleware.add_cors_headers"], "atomsec-func-db-r/src/api/pmd_endpoints.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "typing.List", "repositories.pmd_repository.PMDRepository"], "atomsec-func-db-r/src/api/task_processor_endpoints.py": ["logging", "json", "azure.functions", "datetime.datetime", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.background_processor.get_background_processor", "src.shared.background_processor.TASK_STATUS_PENDING", "src.shared.background_processor.TASK_STATUS_RUNNING", "src.shared.background_processor.TASK_STATUS_COMPLETED", "src.shared.background_processor.TASK_STATUS_FAILED", "src.shared.background_processor.TASK_TYPE_HEALTH_CHECK", "src.shared.background_processor.TASK_TYPE_PROFILES", "src.shared.background_processor.TASK_TYPE_OVERVIEW", "src.shared.background_processor.TASK_TYPE_PMD_APEX_SECURITY", "src.shared.background_processor.TASK_TYPE_METADATA_EXTRACTION", "src.shared.task_status_service.get_task_status_service", "src.shared.execution_log_service.get_execution_log_service", "src.shared.cors_middleware.add_cors_headers", "src.shared.pmd_subtask_service.get_pmd_subtask_service", "src.shared.policy_management.get_policy_management_service"], "atomsec-func-db-r/src/api/task_hierarchy_endpoints.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "typing.Optional", "src.shared.task_status_service.get_task_status_service", "src.shared.execution_context_manager.ExecutionContextManager", "src.shared.auth_utils.get_user_from_request_or_default"], "atomsec-func-db-r/src/api/key_vault_endpoints.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "typing.Optional", "azure.functions.Blueprint", "src.shared.azure_services.is_local_dev", "src.shared.azure_services.get_current_user_id", "src.shared.azure_services.get_key_vault_client", "src.shared.service_bus_client.get_service_bus_client", "re", "secrets", "string"], "atomsec-func-db-r/src/api/rbac_endpoints.py": ["logging", "json", "datetime.datetime", "datetime.timezone", "datetime.<PERSON><PERSON><PERSON>", "typing.Dict", "typing.Any", "typing.Optional", "azure.functions", "src.shared.cors_middleware.add_cors_headers", "src.shared.rbac_service.rbac_service", "src.shared.rbac_service.require_permission", "src.shared.rbac_service.ResourceType", "src.shared.rbac_service.PermissionType", "src.shared.rbac_service.Role", "src.shared.rbac_service.Permission", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.auth_utils.get_current_user", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.auth_utils.get_current_user", "src.shared.rbac_service.AccessContext"], "atomsec-func-db-r/src/api/user_endpoints.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "repositories.user_repository.UserRepository", "src.shared.api_middleware.with_api_gateway", "src.shared.cors_middleware.add_cors_headers", "secrets", "jwt", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "src.shared.config.get_jwt_config", "secrets", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "jwt", "datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", "datetime.timezone", "src.shared.config.get_jwt_config", "src.shared.request_processor.get_request_processor", "src.shared.api_versioning.get_version_manager"], "atomsec-func-db-r/src/api/account_endpoints.py": ["logging", "json", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.azure_services.is_local_dev", "src.shared.data_access.TableStorageRepository", "src.shared.data_access.SqlDatabaseRepository", "src.shared.database_models_new.Account", "random"], "atomsec-func-db-r/src/api/sfdc_proxy_endpoints.py": ["logging", "json", "azure.functions", "src.shared.sfdc_service_client.get_sfdc_client", "src.shared.enhanced_sfdc_proxy.get_enhanced_sfdc_proxy", "src.shared.enhanced_sfdc_proxy.ProxyRequest", "src.shared.enhanced_sfdc_proxy.ProxyRequestType", "src.shared.cors_middleware.add_cors_headers", "src.shared.api_middleware.with_api_gateway", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight", "src.shared.cors_middleware.handle_cors_preflight"], "atomsec-func-db-r/src/api/user_profile_endpoints.py": ["logging", "json", "azure.functions", "typing.Dict", "typing.Any", "typing.Optional", "azure.functions.Blueprint", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository", "src.shared.azure_services.is_local_dev", "src.shared.service_bus_client.get_service_bus_client", "src.shared.auth_utils.get_current_user", "src.shared.auth_utils.get_user_id_from_request", "src.shared.cors_middleware.add_cors_headers", "src.shared.cors_middleware.handle_cors_preflight", "repositories.user_repository.UserRepository", "src.shared.cors_middleware.handle_cors_preflight"], "atomsec-func-db-r/src/api/policy_endpoints.py": ["logging", "json", "uuid", "azure.functions", "datetime.datetime", "typing.Dict", "typing.List", "typing.Any", "typing.Optional", "src.shared.common.is_local_dev", "src.shared.data_access.get_table_storage_repository", "src.shared.data_access.get_sql_database_repository", "src.shared.data_access.create_default_policies_and_rules_for_integration"]}, "potential_issues": {"circular_imports": [["atomsec-func-sfdc/src/shared/auth.py", "src.shared.auth_utils.get_jwt_secret"], ["atomsec-func-sfdc/src/shared/auth.py", "src.shared.auth_utils.get_jwt_algorithm"], ["atomsec-func-sfdc/src/shared/auth.py", "src.shared.auth_utils.decode_token"], ["atomsec-func-sfdc/src/shared/auth.py", "src.shared.auth_utils.get_token_from_header"], ["atomsec-func-sfdc/src/shared/auth.py", "src.shared.auth_utils.get_current_user"], ["atomsec-func-sfdc/src/shared/auth.py", "src.shared.auth_utils.require_auth"]], "problematic_imports": []}, "import_patterns": {"relative_imports": 0, "absolute_imports": 4686, "wildcard_imports": 0, "deep_imports": 47}}