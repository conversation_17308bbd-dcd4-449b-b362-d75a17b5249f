{"SFDC": {"test": ["test_local_startup.py", "test_queue_message_processor.py", "test_account_management.py", "test_endpoints.py", "test_integration.py", "test_api.py", "test_enhanced_error_handling.py", "test_queue_processing.py", "references/backend-ref/test_structure.md", "tests/test_requirements_verification.py", "tests/test_queue_message_processor.py", "tests/test_user_repository.py", "tests/test_poison_queue_functionality.py", "tests/test_auth.py", "tests/test_deployment_verification.py", "tests/test_queue_integration.py", "tests/test_fastapi.py", "tests/test_dummy.py", "tests/test_auth_fixed.py", "tests/test_poison_queue_client.py", "tests/test_deployment_simple.py", "tests/test_fastapi_integration.py", "tests/test_my_work.py", "tests/test_azurite_connection.py", "tests/test_simple_queue_processor.py", "tests/test_comprehensive_queue_processor.py", "tests/test_helpers.py", "tests/test_WrapperFunction.py", "tests/test_poison_queue_scenario.py", "tests/unit/test_monitoring_dashboards.py", "tests/unit/test_performance_optimizer.py", "tests/unit/test_monitoring_service.py", "tests/unit/test_task_sequence_configuration.py", "tests/unit/test_feature_flag_service.py", "tests/unit/test_enhanced_configuration_manager.py", "tests/unit/test_security_middleware.py", "tests/unit/test_enhanced_auth_service.py", "tests/unit/test_enhanced_parameter_validator.py", "tests/unit/test_error_handler.py", "tests/security/test_authentication_security.py", "tests/security/test_injection_attacks.py", "tests/security/security_config.yaml", "tests/integration/test_enhanced_integration_simple.py", "tests/integration/test_deployment_integration.py", "tests/integration/test_execution_log_coordination.py", "tests/integration/test_sequential_task_processing.py", "tests/integration/test_enhanced_components_integration.py", "tests/integration/__pycache__/test_enhanced_components_integration.cpython-312.pyc", "tests/__pycache__/test_requirements_verification.cpython-312.pyc", "tests/__pycache__/test_comprehensive_queue_processor.cpython-312.pyc", "tests/__pycache__/test_auth.cpython-312.pyc", "tests/__pycache__/test_WrapperFunction.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_deployment_simple.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_queue_integration.cpython-312-pytest-8.4.1.pyc", "tests/__pycache__/test_queue_integration.cpython-312.pyc", "tests/__pycache__/test_poison_queue_scenario.cpython-312.pyc", "tests/__pycache__/test_simple_queue_processor.cpython-312.pyc", "tests/__pycache__/test_poison_queue_client.cpython-312.pyc", "tests/__pycache__/test_dummy.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_deployment_verification.cpython-312.pyc", "tests/__pycache__/test_azurite_connection.cpython-312.pyc", "tests/__pycache__/test_my_work.cpython-312.pyc", "tests/__pycache__/test_my_work.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_user_repository.cpython-312.pyc", "tests/__pycache__/conftest.cpython-312.pyc", "tests/__pycache__/mock_azure_functions.cpython-312.pyc", "tests/__pycache__/test_user_repository.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_fastapi_integration.cpython-312.pyc", "tests/__pycache__/make_requests.cpython-312.pyc", "tests/__pycache__/test_auth.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_helpers.cpython-312.pyc", "tests/__pycache__/test_deployment_verification.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_auth_fixed.cpython-312.pyc", "tests/__pycache__/test_poison_queue_functionality.cpython-312.pyc", "tests/__pycache__/test_WrapperFunction.cpython-312.pyc", "tests/__pycache__/test_fastapi_integration.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/mock_db_service.cpython-312.pyc", "tests/__pycache__/test_auth_fixed.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_fastapi.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_dummy.cpython-312.pyc", "tests/__pycache__/mock_table_storage.cpython-312.pyc", "tests/__pycache__/test_queue_message_processor.cpython-312.pyc", "tests/__pycache__/__init__.cpython-312.pyc", "tests/__pycache__/test_fastapi.cpython-312.pyc", "tests/__pycache__/test_deployment_simple.cpython-312.pyc", "tests/performance/test_load_testing.py", "__pycache__/test_account_management.cpython-312.pyc", "__pycache__/test_queue_processing.cpython-312.pyc", "__pycache__/test_endpoints.cpython-312.pyc", "__pycache__/test_local_startup.cpython-312.pyc", "__pycache__/test_api.cpython-312.pyc", "__pycache__/test_integration.cpython-312.pyc", "src/pmd_components/test_pmd_components.py", "src/pmd_components/__pycache__/test_pmd_components.cpython-312.pyc"], "misplaced_test": ["test_local_startup.py", "test_queue_message_processor.py", "test_account_management.py", "test_endpoints.py", "test_integration.py", "test_api.py", "test_enhanced_error_handling.py", "test_queue_processing.py", "references/backend-ref/test_structure.md", "tests/test_requirements_verification.py", "tests/test_queue_message_processor.py", "tests/test_user_repository.py", "tests/test_poison_queue_functionality.py", "tests/test_auth.py", "tests/test_deployment_verification.py", "tests/test_queue_integration.py", "tests/test_fastapi.py", "tests/test_dummy.py", "tests/test_auth_fixed.py", "tests/test_poison_queue_client.py", "tests/test_deployment_simple.py", "tests/test_fastapi_integration.py", "tests/test_my_work.py", "tests/test_azurite_connection.py", "tests/test_simple_queue_processor.py", "tests/test_comprehensive_queue_processor.py", "tests/test_helpers.py", "tests/test_WrapperFunction.py", "tests/test_poison_queue_scenario.py", "tests/unit/test_monitoring_dashboards.py", "tests/unit/test_performance_optimizer.py", "tests/unit/test_monitoring_service.py", "tests/unit/test_task_sequence_configuration.py", "tests/unit/test_feature_flag_service.py", "tests/unit/test_enhanced_configuration_manager.py", "tests/unit/test_security_middleware.py", "tests/unit/test_enhanced_auth_service.py", "tests/unit/test_enhanced_parameter_validator.py", "tests/unit/test_error_handler.py", "tests/security/test_authentication_security.py", "tests/security/test_injection_attacks.py", "tests/security/security_config.yaml", "tests/integration/test_enhanced_integration_simple.py", "tests/integration/test_deployment_integration.py", "tests/integration/test_execution_log_coordination.py", "tests/integration/test_sequential_task_processing.py", "tests/integration/test_enhanced_components_integration.py", "tests/__pycache__/test_requirements_verification.cpython-312.pyc", "tests/__pycache__/test_comprehensive_queue_processor.cpython-312.pyc", "tests/__pycache__/test_auth.cpython-312.pyc", "tests/__pycache__/test_WrapperFunction.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_deployment_simple.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_queue_integration.cpython-312-pytest-8.4.1.pyc", "tests/__pycache__/test_queue_integration.cpython-312.pyc", "tests/__pycache__/test_poison_queue_scenario.cpython-312.pyc", "tests/__pycache__/test_simple_queue_processor.cpython-312.pyc", "tests/__pycache__/test_poison_queue_client.cpython-312.pyc", "tests/__pycache__/test_dummy.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_deployment_verification.cpython-312.pyc", "tests/__pycache__/test_azurite_connection.cpython-312.pyc", "tests/__pycache__/test_my_work.cpython-312.pyc", "tests/__pycache__/test_my_work.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_user_repository.cpython-312.pyc", "tests/__pycache__/conftest.cpython-312.pyc", "tests/__pycache__/mock_azure_functions.cpython-312.pyc", "tests/__pycache__/test_user_repository.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_fastapi_integration.cpython-312.pyc", "tests/__pycache__/make_requests.cpython-312.pyc", "tests/__pycache__/test_auth.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_helpers.cpython-312.pyc", "tests/__pycache__/test_deployment_verification.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_auth_fixed.cpython-312.pyc", "tests/__pycache__/test_poison_queue_functionality.cpython-312.pyc", "tests/__pycache__/test_WrapperFunction.cpython-312.pyc", "tests/__pycache__/test_fastapi_integration.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/mock_db_service.cpython-312.pyc", "tests/__pycache__/test_auth_fixed.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_fastapi.cpython-312-pytest-8.3.5.pyc", "tests/__pycache__/test_dummy.cpython-312.pyc", "tests/__pycache__/mock_table_storage.cpython-312.pyc", "tests/__pycache__/test_queue_message_processor.cpython-312.pyc", "tests/__pycache__/__init__.cpython-312.pyc", "tests/__pycache__/test_fastapi.cpython-312.pyc", "tests/__pycache__/test_deployment_simple.cpython-312.pyc", "tests/performance/test_load_testing.py", "__pycache__/test_account_management.cpython-312.pyc", "__pycache__/test_queue_processing.cpython-312.pyc", "__pycache__/test_endpoints.cpython-312.pyc", "__pycache__/test_local_startup.cpython-312.pyc", "__pycache__/test_api.cpython-312.pyc", "__pycache__/test_integration.cpython-312.pyc", "src/pmd_components/test_pmd_components.py"], "source": ["run_fixed_router_updated.py", "service_bus_processor.py", "conftest.py", "check_policies_result.py", "__init__.py", "akv_test.py", "function_app.py", "setup_local_dev.py", "function_app_production.py", "setup.py", "task_management.py", "simple_test.py", "account_management_asgi.py", "check_credentials.py", "create_policies_result.py", "create_queues.py", "routers/__init__.py", "routers/integration_router.py", "assets_project_maintenance/ConvertsProfilePermissionMetadataFromCSVtoXML.py", "tests/conftest.py", "tests/mock_table_storage.py", "tests/__init__.py", "tests/mock_azure_functions.py", "tests/run_unit_tests.py", "tests/make_requests.py", "tests/unit/__init__.py", "tests/unit/mocks/mock_performance_optimizer.py", "tests/unit/mocks/mock_service_bus.py", "tests/unit/mocks/mock_salesforce_client.py", "tests/unit/mocks/__init__.py", "tests/unit/mocks/mock_database.py", "tests/unit/mocks/mock_key_vault.py", "tests/unit/mocks/mock_azure_services.py", "tests/security/run_security_tests.py", "tests/integration/validate_service_integration_points.py", "tests/integration/validate_monitoring_alerting.py", "tests/integration/__init__.py", "tests/integration/validate_integration_configuration.py", "tests/integration/validate_enhanced_integration.py", "tests/integration/validate_deployment_pipeline.py", "tests/architecture/validate_microservices_compliance.py", "tests/fixtures/__init__.py", "tests/performance/locustfile.py", "tests/performance/comprehensive_load_test.py", "examples/salesforce_client_example.py", "examples/performance_optimization_example.py", "examples/configuration_management_example.py", "src/__init__.py", "src/blueprints/auth.py", "src/blueprints/azure_ad_auth.py", "src/blueprints/organization.py", "src/blueprints/profile.py", "src/blueprints/security.py", "src/blueprints/profile_metadata_fixed.py", "src/blueprints/__init__.py", "src/blueprints/scan_metadata_fixed.py", "src/blueprints/cors_handler.py", "src/blueprints/user_profile_blueprint.py", "src/blueprints/integration.py", "src/blueprints/account_management.py", "src/blueprints/key_vault_endpoints.py", "src/blueprints/integration_tabs.py", "src/blueprints/security_data.py", "src/blueprints/health_blueprint.py", "src/blueprints/scan.py", "src/blueprints/profiles_permissions.py", "src/blueprints/profile_system_permissions.py", "src/blueprints/utilities.py", "src/blueprints/security_analysis.py", "src/blueprints/general.py", "src/blueprints/security_health_check_simplified.py", "src/task_processor/__init__.py", "src/task_processor/tasks/device_activation.py", "src/task_processor/tasks/password_policy.py", "src/task_processor/tasks/pmd_task.py", "src/task_processor/tasks/mfa_enforcement.py", "src/task_processor/tasks/utils.py", "src/task_processor/tasks/login_hours.py", "src/task_processor/tasks/login_ip_ranges.py", "src/task_processor/tasks/profiles_permission_sets.py", "src/task_processor/tasks/api_whitelisting.py", "src/task_processor/tasks/session_timeout.py", "src/repositories/base_repository.py", "src/repositories/__init__.py", "src/repositories/pmd_repository.py", "src/repositories/user_repository.py", "src/shared/standalone_permission_set_profile_analysis.py", "src/shared/enhanced_auth_service.py", "src/shared/data_protection_service.py", "src/shared/auth.py", "src/shared/mock_service_bus.py", "src/shared/auth_service.py", "src/shared/cors_middleware.py", "src/shared/auto_scaling_service.py", "src/shared/execution_context_manager.py", "src/shared/parameter_validator.py", "src/shared/enhanced_parameter_validator.py", "src/shared/advanced_caching_service.py", "src/shared/advanced_auth_service.py", "src/shared/config.py", "src/shared/task_coordination_service.py", "src/shared/database_models.py", "src/shared/cache_performance_analytics.py", "src/shared/feature_flag_service.py", "src/shared/function_configuration.py", "src/shared/database_models_new.py", "src/shared/user_activity_monitor.py", "src/shared/service_bus_client.py", "src/shared/salesforce_jwt_auth.py", "src/shared/monitoring.py", "src/shared/azure_services.py", "src/shared/performance_optimization_framework.py", "src/shared/security.py", "src/shared/salesforce_client.py", "src/shared/auth_utils.py", "src/shared/session_management.py", "src/shared/__init__.py", "src/shared/enhanced_configuration_manager.py", "src/shared/queue_message_processor.py", "src/shared/db_service_client.py", "src/shared/task_lookup_service.py", "src/shared/sfdc_service_client.py", "src/shared/task_sequence_configuration.py", "src/shared/internal_service_security.py", "src/shared/intrusion_detection_system.py", "src/shared/asgi_middleware.py", "src/shared/task_sequence_coordinator.py", "src/shared/data_access.py", "src/shared/security_monitoring_service.py", "src/shared/security_middleware.py", "src/shared/event_publisher.py", "src/shared/common.py", "src/shared/metadata_utils.py", "src/shared/utils.py", "src/shared/service_communication.py", "src/shared/execution_log_coordination_service.py", "src/shared/gdpr_compliance_middleware.py", "src/shared/background_processor.py", "src/shared/standalone_profile_best_practice_analysis.py", "src/shared/metadata_extraction.py", "src/shared/user_repository.py", "src/shared/error_handler.py", "src/shared/task_performance_monitor.py", "src/shared/performance_optimizer.py", "src/shared/monitoring_dashboards.py", "src/shared/enhanced_salesforce_credential_manager.py", "src/shared/parameter_validation_service.py", "src/shared/api_utils.py", "src/shared/task_status_service.py", "src/shared/queue_manager.py", "src/shared/task_sequence_failure_handler.py", "src/shared/salesforce_utils.py", "src/api/auth_endpoints.py", "src/api/task_endpoints.py", "src/api/enhanced_task_endpoints.py", "src/api/integration_endpoints.py", "src/api/security_endpoints.py", "src/api/organization_endpoints.py", "src/api/__init__.py", "src/api/cors_handler.py", "src/api/pmd_endpoints.py", "src/api/key_vault_endpoints.py", "src/api/user_endpoints.py", "src/api/account_endpoints.py", "src/api/sfdc_proxy_endpoints.py", "src/api/user_profile_endpoints.py", "src/api/policy_endpoints.py", "src/pmd_components/pmd_rules_config.py", "src/pmd_components/pmd_results_processor.py", "src/pmd_components/__init__.py", "src/pmd_components/pmd_scanner.py", "src/pmd_components/pmd_blob_handler.py"], "pipeline": ["pipeline-func-sfdc-prod.yml", "pipeline-func-sfdc-dev.yml", "pipeline-func-sfdc-staging.yml", "pipeline-templates/deployment-template.yml", "pipeline-templates/build-template.yml"], "config": ["host.json", "local.settings.json", "config/deploy.local.json", "config/feature_flags.json", "config/production.json", "config/common.json", "config/metadata_config.json", "config/deploy.production.json", "config/local.json", "config/monitoring/app_insights_workbooks.json", ".vscode/settings.json"], "other": [".DS_Store", "pytest.ini", "styles.css", "AzuriteConfig", "local.settings.json.example", "pytest-deployment.ini", "host.optimized.json", "start-all.bat", "start.sh", "assets_project_maintenance/ProfileAndPermissionMetadataSample.csv", "references/frontend-ref/chart.svg", "scripts/database_migration_queue_based_processing.sql", "scripts/generate_function_json.bat", "best_practices/SessionTimeout-BestPractice.xml", "best_practices/MFA_PermissionSetRisks-BestPractice.xml", "best_practices/Profiles_PermissionSetRisks-BestPractice.xml", "best_practices/DeviceActivation_Profile-BestPractices.xml", "best_practices/OrgWideSettings-BestPractice.xml", "best_practices/Password_Policy-BestPractice.xml", "best_practices/SecurityHealthCheck-BestPractice.xml", ".vscode/extensions.json", ".vscode/launch.json", ".vscode/tasks.json", ".idea/atomsec-func-sfdc.iml", ".idea/AugmentWebviewStateStore.xml", ".idea/vcs.xml", ".idea/modules.xml", ".idea/misc.xml", ".idea/inspectionProfiles/profiles_settings.xml", "src/blueprints/.DS_Store", "src/shared/permission_set_debug_log.csv"], "dependencies": ["requirements.txt"], "documentation": ["ENHANCED_ERROR_HANDLING_SUMMARY.md", "README.md", "SFDC_PROXY_ARCHITECTURE.md", "INTEGRATION_VALIDATION_SUMMARY.md", "references/backend-ref/key_vault_guide.md", "references/backend-ref/salesforce_integration_guide.md", "references/backend-ref/restructure_plan.md", "references/backend-ref/local_development_guide.md", "references/backend-ref/function_app_structure_and_billing.md", "references/backend-ref/legacy_code_cleanup.md", "references/backend-ref/salesforce_client_credentials_guide.md", "references/backend-ref/azure_data_tables_url_format.md", "references/backend-ref/README.md", "references/backend-ref/salesforce_jwt_guide.md", "references/backend-ref/troubleshooting_guide.md", "references/backend-ref/salesforce_integration_guide_simplified.md", "references/backend-ref/chart.md", "references/backend-ref/architecture_diagram.md", "references/backend-ref/duplicate_files_cleanup.md", "references/backend-ref/salesforce metadata.md", "references/frontend-ref/Product Requirements Document.md", "references/frontend-ref/Agile Stories.md", "references/frontend-ref/AZURE_FUNCTION_AUTH.md", "references/frontend-ref/React wired APIs.md", "references/frontend-ref/WEB_APP_AUTH.md", "references/frontend-ref/AZURE_AD_SETUP.md", "references/frontend-ref/MSSQL-DB-ER-Diagram.md", "docs/ADDITIONAL_FUNCTION_CONSOLIDATION.md", "docs/UPDATED_DEPLOYMENT_STRATEGY.md", "docs/DEPLOYMENT_STRATEGY.md", "docs/DATABASE_OPERATIONS_REMOVAL_SUMMARY.md", "docs/MIGRATION_SUMMARY.md", "docs/README_QUEUE_SETUP.md", "docs/QUEUE_BASED_TASK_PROCESSING_IMPLEMENTATION_PLAN.md", "docs/README.md", "docs/task_priority_mapping.md", "docs/ARCHITECTURE_EXPORT.md", "docs/azure-deployment-config.md", "docs/TASK_ENQUEUE_EXPORT.md", "docs/SERVICE_BUS_FIX_DOCUMENTATION.md", "docs/DATA_MODELS_EXPORT.md", "docs/FINAL_DEPLOYMENT_GUIDE.md", "docs/FUNCTION_CONSOLIDATION.md", "docs/UPDATED_API_SPECIFICATIONS.md", "docs/ADVANCED_DEPLOYMENT_STRATEGIES.md", "docs/APIM-API-OpenAPI 2.0.json", "docs/salesforce_migration_guide.md", "docs/ENVIRONMENT_VARIABLES.md", "docs/FUNCTION_CONSOLIDATION_IMPLEMENTATION.md", "docs/API_SPECIFICATIONS_EXPORT.md", "docs/development/dependency-management.md", "docs/development/code-documentation-standards.md", "docs/openapi/sfdc-api-spec.yaml", "docs/openapi/README.md", "docs/openapi/swagger-ui.html", "docs/openapi/postman/sfdc-api.postman_collection.json", "docs/openapi/postman/sfdc-environments.postman_environment.json", "docs/operations/troubleshooting-guide.md", "docs/operations/deployment-runbook.md", "docs/operations/monitoring-alerting.md", "docs/architecture/service-communication-flows.md", "docs/architecture/adr/002-security-middleware-framework.md", "docs/architecture/adr/003-microservices-proxy-architecture.md", "docs/architecture/adr/001-sequential-task-processing.md", "docs/architecture/adr/README.md", "infrastructure/README.md", "src/pmd_components/PMD_STANDALONE_README.md"], "misplaced_documentation": ["ENHANCED_ERROR_HANDLING_SUMMARY.md", "README.md", "SFDC_PROXY_ARCHITECTURE.md", "INTEGRATION_VALIDATION_SUMMARY.md", "references/backend-ref/key_vault_guide.md", "references/backend-ref/salesforce_integration_guide.md", "references/backend-ref/restructure_plan.md", "references/backend-ref/local_development_guide.md", "references/backend-ref/function_app_structure_and_billing.md", "references/backend-ref/legacy_code_cleanup.md", "references/backend-ref/salesforce_client_credentials_guide.md", "references/backend-ref/azure_data_tables_url_format.md", "references/backend-ref/README.md", "references/backend-ref/salesforce_jwt_guide.md", "references/backend-ref/troubleshooting_guide.md", "references/backend-ref/salesforce_integration_guide_simplified.md", "references/backend-ref/chart.md", "references/backend-ref/architecture_diagram.md", "references/backend-ref/duplicate_files_cleanup.md", "references/backend-ref/salesforce metadata.md", "references/frontend-ref/Product Requirements Document.md", "references/frontend-ref/Agile Stories.md", "references/frontend-ref/AZURE_FUNCTION_AUTH.md", "references/frontend-ref/React wired APIs.md", "references/frontend-ref/WEB_APP_AUTH.md", "references/frontend-ref/AZURE_AD_SETUP.md", "references/frontend-ref/MSSQL-DB-ER-Diagram.md", "docs/ADDITIONAL_FUNCTION_CONSOLIDATION.md", "docs/UPDATED_DEPLOYMENT_STRATEGY.md", "docs/DEPLOYMENT_STRATEGY.md", "docs/DATABASE_OPERATIONS_REMOVAL_SUMMARY.md", "docs/MIGRATION_SUMMARY.md", "docs/README_QUEUE_SETUP.md", "docs/QUEUE_BASED_TASK_PROCESSING_IMPLEMENTATION_PLAN.md", "docs/README.md", "docs/task_priority_mapping.md", "docs/ARCHITECTURE_EXPORT.md", "docs/azure-deployment-config.md", "docs/TASK_ENQUEUE_EXPORT.md", "docs/SERVICE_BUS_FIX_DOCUMENTATION.md", "docs/DATA_MODELS_EXPORT.md", "docs/FINAL_DEPLOYMENT_GUIDE.md", "docs/FUNCTION_CONSOLIDATION.md", "docs/UPDATED_API_SPECIFICATIONS.md", "docs/ADVANCED_DEPLOYMENT_STRATEGIES.md", "docs/salesforce_migration_guide.md", "docs/ENVIRONMENT_VARIABLES.md", "docs/FUNCTION_CONSOLIDATION_IMPLEMENTATION.md", "docs/API_SPECIFICATIONS_EXPORT.md", "docs/development/dependency-management.md", "docs/development/code-documentation-standards.md", "docs/openapi/README.md", "docs/operations/troubleshooting-guide.md", "docs/operations/deployment-runbook.md", "docs/operations/monitoring-alerting.md", "docs/architecture/service-communication-flows.md", "infrastructure/README.md", "src/pmd_components/PMD_STANDALONE_README.md"], "project_config": [".funcignore", ".giti<PERSON>re", "Procfile", ".idea/.gitignore"], "temporary": ["routers/__pycache__/integration_router.cpython-312.pyc", "routers/__pycache__/__init__.cpython-312.pyc", "IntegrationFunction/__pycache__/__init__.cpython-312.pyc", "home/__pycache__/__init__.cpython-312.pyc", "DirectAccountsFunction/__pycache__/__init__.cpython-312.pyc", "assets_project_maintenance/__pycache__/ConvertsProfilePermissionMetadataFromCSVtoXML.cpython-312.pyc", "RolesApiFunction/__pycache__/__init__.cpython-312.pyc", "AccountsApiFunction/__pycache__/__init__.cpython-312.pyc", "__pycache__/task_management.cpython-312.pyc", "__pycache__/setup_local_dev.cpython-312.pyc", "__pycache__/conftest.cpython-312-pytest-8.4.1.pyc", "__pycache__/run_fixed_router_updated.cpython-312.pyc", "__pycache__/create_policies_result.cpython-312.pyc", "__pycache__/setup.cpython-312.pyc", "__pycache__/create_queues.cpython-312.pyc", "__pycache__/simple_test.cpython-312.pyc", "__pycache__/conftest.cpython-312-pytest-8.3.5.pyc", "__pycache__/function_app_production.cpython-312.pyc", "__pycache__/service_bus_processor.cpython-312.pyc", "__pycache__/check_policies_result.cpython-312.pyc", "__pycache__/conftest.cpython-312.pyc", "__pycache__/akv_test.cpython-312.pyc", "__pycache__/account_management_asgi.cpython-312.pyc", "__pycache__/app.cpython-312.pyc", "__pycache__/function_app.cpython-312.pyc", "__pycache__/check_credentials.cpython-312.pyc", "__pycache__/__init__.cpython-312.pyc", "DirectRolesFunction/__pycache__/__init__.cpython-312.pyc", "scripts/__pycache__/verify_deployment.cpython-312.pyc", "scripts/__pycache__/generate_function_json.cpython-312.pyc", "scripts/__pycache__/verify_fastapi_deployment.cpython-312.pyc", "scripts/__pycache__/deploy_queue_based_processing.cpython-312.pyc", "scripts/__pycache__/queue_management_utility.cpython-312.pyc", "scripts/__pycache__/verify_function_discovery.cpython-312.pyc", "scripts/__pycache__/setup_service_bus.cpython-312.pyc", "scripts/__pycache__/init_policy_rule_setting.cpython-312.pyc", "scripts/__pycache__/demo_queue_processor_with_azurite.cpython-312.pyc", "scripts/__pycache__/generate_certificate.cpython-312.pyc", "scripts/__pycache__/init_database.cpython-312.pyc", "scripts/__pycache__/init_policy_and_rule.cpython-312.pyc", "scripts/__pycache__/check_queues.cpython-312.pyc", "scripts/__pycache__/check_azurite.cpython-312.pyc", "scripts/__pycache__/deployment_health_check.cpython-312.pyc", "scripts/__pycache__/clear_poison_queue.cpython-312.pyc", "WrapperFunction/__pycache__/__init__.cpython-312.pyc", "RolesManagementFunction/__pycache__/__init__.cpython-312.pyc", "src/blueprints/__pycache__/utilities.cpython-312.pyc", "src/blueprints/__pycache__/profile_metadata_fixed.cpython-312.pyc", "src/blueprints/__pycache__/user_profile.cpython-312.pyc", "src/blueprints/__pycache__/scan.cpython-312.pyc", "src/blueprints/__pycache__/account_management.cpython-312.pyc", "src/blueprints/__pycache__/integration.cpython-312.pyc", "src/blueprints/__pycache__/security_health_check_simplified.cpython-312.pyc", "src/blueprints/__pycache__/security.cpython-312.pyc", "src/blueprints/__pycache__/key_vault_endpoints.cpython-312.pyc", "src/blueprints/__pycache__/azure_ad_auth.cpython-312.pyc", "src/blueprints/__pycache__/scan_metadata_fixed.cpython-312.pyc", "src/blueprints/__pycache__/profiles_permissions.cpython-312.pyc", "src/blueprints/__pycache__/security_data.cpython-312.pyc", "src/blueprints/__pycache__/organization.cpython-312.pyc", "src/blueprints/__pycache__/user_profile_blueprint.cpython-312.pyc", "src/blueprints/__pycache__/auth.cpython-312.pyc", "src/blueprints/__pycache__/security_analysis.cpython-312.pyc", "src/blueprints/__pycache__/integration_tabs.cpython-312.pyc", "src/blueprints/__pycache__/profile.cpython-312.pyc", "src/blueprints/__pycache__/profile_system_permissions.cpython-312.pyc", "src/blueprints/__pycache__/general.cpython-312.pyc", "src/blueprints/__pycache__/__init__.cpython-312.pyc", "src/blueprints/__pycache__/health_blueprint.cpython-312.pyc", "src/blueprints/__pycache__/cors_handler.cpython-312.pyc", "src/task_processor/tasks/__pycache__/password_policy.cpython-312.pyc", "src/task_processor/tasks/__pycache__/login_hours.cpython-312.pyc", "src/task_processor/tasks/__pycache__/mfa_enforcement.cpython-312.pyc", "src/task_processor/tasks/__pycache__/pmd_task.cpython-312.pyc", "src/task_processor/tasks/__pycache__/device_activation.cpython-312.pyc", "src/task_processor/tasks/__pycache__/login_ip_ranges.cpython-312.pyc", "src/task_processor/tasks/__pycache__/utils.cpython-312.pyc", "src/task_processor/tasks/__pycache__/profiles_permission_sets.cpython-312.pyc", "src/task_processor/tasks/__pycache__/api_whitelisting.cpython-312.pyc", "src/task_processor/tasks/__pycache__/session_timeout.cpython-312.pyc", "src/task_processor/__pycache__/__init__.cpython-312.pyc", "src/repositories/__pycache__/user_repository.cpython-312.pyc", "src/repositories/__pycache__/pmd_repository.cpython-312.pyc", "src/repositories/__pycache__/__init__.cpython-312.pyc", "src/repositories/__pycache__/base_repository.cpython-312.pyc", "src/shared/__pycache__/performance_optimizer.cpython-312.pyc", "src/shared/__pycache__/service_bus_client.cpython-312.pyc", "src/shared/__pycache__/salesforce_jwt_auth.cpython-312.pyc", "src/shared/__pycache__/parameter_validator.cpython-312.pyc", "src/shared/__pycache__/database_models_new.cpython-312.pyc", "src/shared/__pycache__/salesforce_utils.cpython-312.pyc", "src/shared/__pycache__/feature_flag_service.cpython-312.pyc", "src/shared/__pycache__/task_sequence_configuration.cpython-312.pyc", "src/shared/__pycache__/standalone_profile_best_practice_analysis.cpython-312.pyc", "src/shared/__pycache__/queue_message_processor.cpython-312.pyc", "src/shared/__pycache__/config.cpython-312.pyc", "src/shared/__pycache__/parameter_validation_service.cpython-312.pyc", "src/shared/__pycache__/security.cpython-312.pyc", "src/shared/__pycache__/security_middleware.cpython-312.pyc", "src/shared/__pycache__/database_models.cpython-312.pyc", "src/shared/__pycache__/api_utils.cpython-312.pyc", "src/shared/__pycache__/auto_scaling_service.cpython-312.pyc", "src/shared/__pycache__/error_handler.cpython-312.pyc", "src/shared/__pycache__/execution_log_coordination_service.cpython-312.pyc", "src/shared/__pycache__/common.cpython-312.pyc", "src/shared/__pycache__/enhanced_auth_service.cpython-312.pyc", "src/shared/__pycache__/user_repository.cpython-312.pyc", "src/shared/__pycache__/queue_manager.cpython-312.pyc", "src/shared/__pycache__/security_monitoring_service.cpython-312.pyc", "src/shared/__pycache__/task_status_service.cpython-312.pyc", "src/shared/__pycache__/utils.cpython-312.pyc", "src/shared/__pycache__/policies_data_manager.cpython-312.pyc", "src/shared/__pycache__/user_activity_monitor.cpython-312.pyc", "src/shared/__pycache__/execution_context_manager.cpython-312.pyc", "src/shared/__pycache__/event_publisher.cpython-312.pyc", "src/shared/__pycache__/metadata_extraction.cpython-312.pyc", "src/shared/__pycache__/cache_performance_analytics.cpython-312.pyc", "src/shared/__pycache__/background_processor.cpython-312.pyc", "src/shared/__pycache__/db_service_client.cpython-312.pyc", "src/shared/__pycache__/task_lookup_service.cpython-312.pyc", "src/shared/__pycache__/sfdc_service_client.cpython-312.pyc", "src/shared/__pycache__/enhanced_parameter_validator.cpython-312.pyc", "src/shared/__pycache__/auth_utils.cpython-312.pyc", "src/shared/__pycache__/salesforce_client.cpython-312.pyc", "src/shared/__pycache__/mock_service_bus.cpython-312.pyc", "src/shared/__pycache__/standalone_permission_set_profile_analysis.cpython-312.pyc", "src/shared/__pycache__/advanced_auth_service.cpython-312.pyc", "src/shared/__pycache__/enhanced_configuration_manager.cpython-312.pyc", "src/shared/__pycache__/auth_service.cpython-312.pyc", "src/shared/__pycache__/cors_middleware.cpython-312.pyc", "src/shared/__pycache__/auth.cpython-312.pyc", "src/shared/__pycache__/task_performance_monitor.cpython-312.pyc", "src/shared/__pycache__/advanced_caching_service.cpython-312.pyc", "src/shared/__pycache__/monitoring_dashboards.cpython-312.pyc", "src/shared/__pycache__/performance_optimization_framework.cpython-312.pyc", "src/shared/__pycache__/metadata_utils.cpython-312.pyc", "src/shared/__pycache__/monitoring.cpython-312.pyc", "src/shared/__pycache__/asgi_middleware.cpython-312.pyc", "src/shared/__pycache__/task_sequence_failure_handler.cpython-312.pyc", "src/shared/__pycache__/metadata_utils_fixed.cpython-312.pyc", "src/shared/__pycache__/__init__.cpython-312.pyc", "src/shared/__pycache__/azure_services.cpython-312.pyc", "src/shared/__pycache__/task_coordination_service.cpython-312.pyc", "src/shared/__pycache__/data_access.cpython-312.pyc", "src/shared/__pycache__/task_sequence_coordinator.cpython-312.pyc", "src/__pycache__/__init__.cpython-312.pyc", "src/api/__pycache__/pmd_endpoints.cpython-312.pyc", "src/api/__pycache__/key_vault_endpoints.cpython-312.pyc", "src/api/__pycache__/user_endpoints.cpython-312.pyc", "src/api/__pycache__/enhanced_task_endpoints.cpython-312.pyc", "src/api/__pycache__/policy_endpoints.cpython-312.pyc", "src/api/__pycache__/auth_endpoints.cpython-312.pyc", "src/api/__pycache__/user_profile_endpoints.cpython-312.pyc", "src/api/__pycache__/integration_endpoints.cpython-312.pyc", "src/api/__pycache__/organization_endpoints.cpython-312.pyc", "src/api/__pycache__/security_endpoints.cpython-312.pyc", "src/api/__pycache__/task_endpoints.cpython-312.pyc", "src/api/__pycache__/account_endpoints.cpython-312.pyc", "src/api/__pycache__/__init__.cpython-312.pyc", "src/api/__pycache__/sfdc_proxy_endpoints.cpython-312.pyc", "src/api/__pycache__/cors_handler.cpython-312.pyc", "src/pmd_components/__pycache__/pmd_rules_config.cpython-312.pyc", "src/pmd_components/__pycache__/pmd_scanner.cpython-312.pyc", "src/pmd_components/__pycache__/pmd_results_processor.cpython-312.pyc", "src/pmd_components/__pycache__/pmd_blob_handler.cpython-312.pyc", "src/pmd_components/__pycache__/__init__.cpython-312.pyc"], "deprecated_function": ["IntegrationFunction/__init__.py", "home/__init__.py", "DirectAccountsFunction/__init__.py", "RolesApiFunction/__init__.py", "AccountsApiFunction/__init__.py", "DirectRolesFunction/__init__.py", "RolesManagementFunction/__init__.py"], "example": ["docs/openapi/examples/task-management.md", "examples/README.md", "examples/__pycache__/salesforce_client_example.cpython-312.pyc"], "misplaced_example": ["examples/README.md", "examples/__pycache__/salesforce_client_example.cpython-312.pyc"], "script": ["scripts/smoke_tests.py", "scripts/queue_management_utility.py", "scripts/deploy_queue_based_processing.py", "scripts/clear_poison_queue.py", "scripts/generate_certificate.py", "scripts/generate_function_json.py", "scripts/canary_deployment.py", "scripts/demo_queue_processor_with_azurite.py", "scripts/rollback_deployment.py", "scripts/check_queues.py", "scripts/check_azurite.py", "scripts/verify_fastapi_deployment.py", "scripts/blue_green_deployment.py", "scripts/setup_service_bus.py", "scripts/deployment_monitoring.py", "scripts/deployment_health_check.py", "scripts/init_database.py", "scripts/verify_deployment.py", "scripts/init_policy_rule_setting.py", "scripts/init_policy_and_rule.py", "scripts/verify_function_discovery.py", "scripts/deploy_monitoring_dashboards.py"], "infrastructure": ["infrastructure/main.bicep", "infrastructure/deploy.sh", "infrastructure/deploy.ps1", "infrastructure/parameters/staging.bicepparam", "infrastructure/parameters/dev.bicepparam", "infrastructure/parameters/prod.bicepparam", "infrastructure/modules/appinsights.bicep", "infrastructure/modules/storage.bicep", "infrastructure/modules/servicebus.bicep", "infrastructure/modules/keyvault.bicep", "infrastructure/modules/functionapp.bicep"], "deprecated": ["src/api/general_endpoints.py.disabled"]}, "DB": {"documentation": ["API_CONSOLIDATION_SUMMARY.md", "ENHANCED_DATABASE_SECURITY_IMPLEMENTATION.md", "CONSOLIDATED_API_ENDPOINTS.md", "README.md", "QUEUE_BASED_PROCESSING_DEPLOYMENT_GUIDE.md", "MIGRATION_GUIDE.md", "SFDC_PROXY_ARCHITECTURE.md"], "misplaced_documentation": ["API_CONSOLIDATION_SUMMARY.md", "ENHANCED_DATABASE_SECURITY_IMPLEMENTATION.md", "CONSOLIDATED_API_ENDPOINTS.md", "README.md", "QUEUE_BASED_PROCESSING_DEPLOYMENT_GUIDE.md", "MIGRATION_GUIDE.md", "SFDC_PROXY_ARCHITECTURE.md"], "config": ["host.json", "local.settings.json", "config/default_policies_and_rules.yaml"], "other": [".DS_Store", ".env"], "test": ["test_enhanced_queue_manager.py", "test_enhanced_task_status_service.py", "test_enhanced_database_security.py", "test_task_coordination_service.py", "test_execution_context_manager.py", "test_enhanced_task_coordination_service.py", "tests/integration/test_queue_based_processing.py", "tests/integration/test_task_status_aggregation.py", "tests/integration/test_queue_message_processing.py", "__pycache__/test_enhanced_queue_manager.cpython-312-pytest-8.4.1.pyc", "__pycache__/test_enhanced_database_security.cpython-312-pytest-8.4.1.pyc", "__pycache__/test_task_coordination_service.cpython-312-pytest-8.4.1.pyc", "__pycache__/test_enhanced_task_status_service.cpython-312-pytest-8.4.1.pyc", "__pycache__/test_execution_context_manager.cpython-312-pytest-8.4.1.pyc", "__pycache__/test_enhanced_task_coordination_service.cpython-312-pytest-8.4.1.pyc", "scripts/test_migration.py", "scripts/__pycache__/test_migration.cpython-312-pytest-8.3.5.pyc"], "misplaced_test": ["test_enhanced_queue_manager.py", "test_enhanced_task_status_service.py", "test_enhanced_database_security.py", "test_task_coordination_service.py", "test_execution_context_manager.py", "test_enhanced_task_coordination_service.py", "tests/integration/test_queue_based_processing.py", "tests/integration/test_task_status_aggregation.py", "tests/integration/test_queue_message_processing.py", "__pycache__/test_enhanced_queue_manager.cpython-312-pytest-8.4.1.pyc", "__pycache__/test_enhanced_database_security.cpython-312-pytest-8.4.1.pyc", "__pycache__/test_task_coordination_service.cpython-312-pytest-8.4.1.pyc", "__pycache__/test_enhanced_task_status_service.cpython-312-pytest-8.4.1.pyc", "__pycache__/test_execution_context_manager.cpython-312-pytest-8.4.1.pyc", "__pycache__/test_enhanced_task_coordination_service.cpython-312-pytest-8.4.1.pyc", "scripts/test_migration.py", "scripts/__pycache__/test_migration.cpython-312-pytest-8.3.5.pyc"], "dependencies": ["requirements.txt"], "pipeline": ["pipeline-func-db-dev.yml"], "source": ["function_app.py", "run_integration_tests.py", "tests/__init__.py", "tests/unit/__init__.py", "tests/integration/__init__.py", "tests/fixtures/__init__.py", "src/__init__.py", "src/repositories/base_repository.py", "src/repositories/__init__.py", "src/repositories/pmd_repository.py", "src/repositories/user_repository.py", "src/shared/mock_service_bus.py", "src/shared/api_gateway.py", "src/shared/cors_middleware.py", "src/shared/advanced_connection_pool.py", "src/shared/execution_context_manager.py", "src/shared/config.py", "src/shared/secure_queue_manager.py", "src/shared/task_coordination_service.py", "src/shared/database_models.py", "src/shared/scan_initiation.py", "src/shared/request_processor.py", "src/shared/database_models_new.py", "src/shared/service_bus_client.py", "src/shared/azure_services.py", "src/shared/queue_performance_monitor.py", "src/shared/auth_utils.py", "src/shared/__init__.py", "src/shared/feature_flags.py", "src/shared/mfa_service.py", "src/shared/db_service_client.py", "src/shared/sfdc_service_client.py", "src/shared/execution_log_service.py", "src/shared/enhanced_sfdc_proxy.py", "src/shared/data_access.py", "src/shared/event_publisher.py", "src/shared/audit_logger.py", "src/shared/common.py", "src/shared/policy_management.py", "src/shared/enhanced_queue_manager.py", "src/shared/pmd_subtask_service.py", "src/shared/service_communication.py", "src/shared/optimized_query_manager.py", "src/shared/api_middleware.py", "src/shared/background_processor.py", "src/shared/enhanced_azure_ad_service.py", "src/shared/task_orchestration_validator.py", "src/shared/transaction_manager.py", "src/shared/enhanced_repository_security.py", "src/shared/enhanced_task_coordination_service.py", "src/shared/enhanced_jwt_service.py", "src/shared/task_status_service.py", "src/shared/api_versioning.py", "src/shared/db_service.py", "src/shared/queue_manager.py", "src/shared/enhanced_execution_context_manager.py", "src/shared/rbac_service.py", "src/api/general_endpoints.py", "src/api/compliance_endpoints.py", "src/api/auth_endpoints.py", "src/api/task_endpoints.py", "src/api/mfa_endpoints.py", "src/api/integration_endpoints.py", "src/api/security_endpoints.py", "src/api/organization_endpoints.py", "src/api/__init__.py", "src/api/cors_handler.py", "src/api/performance_endpoints.py", "src/api/scan_endpoints.py", "src/api/pmd_endpoints.py", "src/api/task_processor_endpoints.py", "src/api/task_hierarchy_endpoints.py", "src/api/key_vault_endpoints.py", "src/api/rbac_endpoints.py", "src/api/user_endpoints.py", "src/api/account_endpoints.py", "src/api/sfdc_proxy_endpoints.py", "src/api/user_profile_endpoints.py", "src/api/policy_endpoints.py"], "project_config": [".giti<PERSON>re"], "example": ["__pycache__/example_execution_context_manager_usage.cpython-312.pyc"], "misplaced_example": ["__pycache__/example_execution_context_manager_usage.cpython-312.pyc"], "temporary": ["__pycache__/function_app.cpython-312.pyc", "src/repositories/__pycache__/user_repository.cpython-312.pyc", "src/repositories/__pycache__/pmd_repository.cpython-312.pyc", "src/repositories/__pycache__/__init__.cpython-312.pyc", "src/repositories/__pycache__/base_repository.cpython-312.pyc", "src/shared/__pycache__/service_bus_client.cpython-312.pyc", "src/shared/__pycache__/advanced_connection_pool.cpython-312.pyc", "src/shared/__pycache__/policy_management.cpython-312.pyc", "src/shared/__pycache__/pmd_subtask_service.cpython-312.pyc", "src/shared/__pycache__/database_models_new.cpython-312.pyc", "src/shared/__pycache__/execution_log_service.cpython-312.pyc", "src/shared/__pycache__/enhanced_repository_security.cpython-312.pyc", "src/shared/__pycache__/task_orchestration_validator.cpython-312.pyc", "src/shared/__pycache__/audit_logger.cpython-312.pyc", "src/shared/__pycache__/config.cpython-312.pyc", "src/shared/__pycache__/database_models.cpython-312.pyc", "src/shared/__pycache__/common.cpython-312.pyc", "src/shared/__pycache__/enhanced_azure_ad_service.cpython-312.pyc", "src/shared/__pycache__/queue_manager.cpython-312.pyc", "src/shared/__pycache__/service_communication.cpython-312.pyc", "src/shared/__pycache__/task_status_service.cpython-312.pyc", "src/shared/__pycache__/execution_context_manager.cpython-312.pyc", "src/shared/__pycache__/event_publisher.cpython-312.pyc", "src/shared/__pycache__/background_processor.cpython-312.pyc", "src/shared/__pycache__/db_service_client.cpython-312.pyc", "src/shared/__pycache__/sfdc_service_client.cpython-312.pyc", "src/shared/__pycache__/auth_utils.cpython-312.pyc", "src/shared/__pycache__/mock_service_bus.cpython-312.pyc", "src/shared/__pycache__/cors_middleware.cpython-312.pyc", "src/shared/__pycache__/scan_initiation.cpython-312.pyc", "src/shared/__pycache__/secure_queue_manager.cpython-312.pyc", "src/shared/__pycache__/transaction_manager.cpython-312.pyc", "src/shared/__pycache__/optimized_query_manager.cpython-312.pyc", "src/shared/__pycache__/enhanced_queue_manager.cpython-312.pyc", "src/shared/__pycache__/__init__.cpython-312.pyc", "src/shared/__pycache__/azure_services.cpython-312.pyc", "src/shared/__pycache__/task_coordination_service.cpython-312.pyc", "src/shared/__pycache__/data_access.cpython-312.pyc", "src/shared/__pycache__/enhanced_task_coordination_service.cpython-312.pyc", "src/__pycache__/__init__.cpython-312.pyc", "src/api/__pycache__/general_endpoints.cpython-312.pyc", "src/api/__pycache__/pmd_endpoints.cpython-312.pyc", "src/api/__pycache__/scan_endpoints.cpython-312.pyc", "src/api/__pycache__/key_vault_endpoints.cpython-312.pyc", "src/api/__pycache__/user_endpoints.cpython-312.pyc", "src/api/__pycache__/policy_endpoints.cpython-312.pyc", "src/api/__pycache__/task_processor_endpoints.cpython-312.pyc", "src/api/__pycache__/auth_endpoints.cpython-312.pyc", "src/api/__pycache__/user_profile_endpoints.cpython-312.pyc", "src/api/__pycache__/integration_endpoints.cpython-312.pyc", "src/api/__pycache__/organization_endpoints.cpython-312.pyc", "src/api/__pycache__/security_endpoints.cpython-312.pyc", "src/api/__pycache__/task_endpoints.cpython-312.pyc", "src/api/__pycache__/account_endpoints.cpython-312.pyc", "src/api/__pycache__/__init__.cpython-312.pyc", "src/api/__pycache__/sfdc_proxy_endpoints.cpython-312.pyc", "src/api/__pycache__/cors_handler.cpython-312.pyc"], "script": ["scripts/migrate_database_schema.py", "scripts/migrate_to_queue_based_processing.py", "scripts/deploy_queue_infrastructure.py", "scripts/setup_queues.py"]}}