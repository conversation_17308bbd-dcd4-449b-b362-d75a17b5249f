# File Categorization Summary Report

## SFDC Service

**Total Files:** 747

### File Categories:
- **Config:** 11 files
- **Dependencies:** 1 files
- **Deprecated:** 1 files
- **Deprecated Function:** 7 files
- **Documentation:** 67 files
- **Example:** 3 files
- **Infrastructure:** 11 files
- **Misplaced Documentation:** 58 files
- **Misplaced Example:** 2 files
- **Misplaced Test:** 92 files
- **Other:** 31 files
- **Pipeline:** 5 files
- **Project Config:** 4 files
- **Script:** 22 files
- **Source:** 172 files
- **Temporary:** 166 files
- **Test:** 94 files

### Issues Found:
- **Misplaced Test:** 92 files need attention
  - test_local_startup.py
  - test_queue_message_processor.py
  - test_account_management.py
  - test_endpoints.py
  - test_integration.py
  - ... and 87 more
- **Misplaced Documentation:** 58 files need attention
  - ENHANCED_ERROR_HANDLING_SUMMARY.md
  - README.md
  - SFDC_PROXY_ARCHITECTURE.md
  - INTEGRATION_VALIDATION_SUMMARY.md
  - references/backend-ref/key_vault_guide.md
  - ... and 53 more
- **Temporary:** 166 files need attention
  - routers/__pycache__/integration_router.cpython-312.pyc
  - routers/__pycache__/__init__.cpython-312.pyc
  - IntegrationFunction/__pycache__/__init__.cpython-312.pyc
  - home/__pycache__/__init__.cpython-312.pyc
  - DirectAccountsFunction/__pycache__/__init__.cpython-312.pyc
  - ... and 161 more
- **Deprecated Function:** 7 files need attention
  - IntegrationFunction/__init__.py
  - home/__init__.py
  - DirectAccountsFunction/__init__.py
  - RolesApiFunction/__init__.py
  - AccountsApiFunction/__init__.py
  - ... and 2 more
- **Misplaced Example:** 2 files need attention
  - examples/README.md
  - examples/__pycache__/salesforce_client_example.cpython-312.pyc
- **Deprecated:** 1 files need attention
  - src/api/general_endpoints.py.disabled

## DB Service

**Total Files:** 198

### File Categories:
- **Config:** 3 files
- **Dependencies:** 1 files
- **Documentation:** 7 files
- **Example:** 1 files
- **Misplaced Documentation:** 7 files
- **Misplaced Example:** 1 files
- **Misplaced Test:** 17 files
- **Other:** 2 files
- **Pipeline:** 1 files
- **Project Config:** 1 files
- **Script:** 4 files
- **Source:** 79 files
- **Temporary:** 57 files
- **Test:** 17 files

### Issues Found:
- **Misplaced Documentation:** 7 files need attention
  - API_CONSOLIDATION_SUMMARY.md
  - ENHANCED_DATABASE_SECURITY_IMPLEMENTATION.md
  - CONSOLIDATED_API_ENDPOINTS.md
  - README.md
  - QUEUE_BASED_PROCESSING_DEPLOYMENT_GUIDE.md
  - ... and 2 more
- **Misplaced Test:** 17 files need attention
  - test_enhanced_queue_manager.py
  - test_enhanced_task_status_service.py
  - test_enhanced_database_security.py
  - test_task_coordination_service.py
  - test_execution_context_manager.py
  - ... and 12 more
- **Misplaced Example:** 1 files need attention
  - __pycache__/example_execution_context_manager_usage.cpython-312.pyc
- **Temporary:** 57 files need attention
  - __pycache__/function_app.cpython-312.pyc
  - src/repositories/__pycache__/user_repository.cpython-312.pyc
  - src/repositories/__pycache__/pmd_repository.cpython-312.pyc
  - src/repositories/__pycache__/__init__.cpython-312.pyc
  - src/repositories/__pycache__/base_repository.cpython-312.pyc
  - ... and 52 more
