# Import Dependencies Analysis

## Overview

This document provides a comprehensive analysis of import dependencies across both the SFDC service (`atomsec-func-sfdc`) and DB service (`atomsec-func-db-r`) before folder structure reorganization. This analysis will guide the import statement updates needed during the folder restructuring process.

## Analysis Summary

- **Total Python files analyzed:** 341
- **SFDC service files:** 248
- **DB service files:** 93
- **Total internal imports:** 1,458
- **Total external imports:** 3,228
- **Unique internal modules:** 480
- **Unique external packages:** 227

## Import Patterns Analysis

### Current Import Structure
- **Absolute imports:** 4,686 (100% - good practice)
- **Relative imports:** 0 (good - no relative imports to break)
- **Wildcard imports:** 0 (excellent - no wildcard imports)
- **Deep imports (>3 levels):** 47 (minimal deep nesting)

### Key Findings
1. **Well-structured imports:** All imports use absolute paths, making them easier to update
2. **No relative imports:** This simplifies the reorganization process
3. **No wildcard imports:** Reduces risk of breaking changes
4. **Consistent patterns:** Both services follow similar import conventions

## Critical Import Dependencies

### SFDC Service Core Dependencies
The most frequently imported internal modules in the SFDC service:

1. **`src.shared.azure_services.is_local_dev`** - 55 imports
2. **`src.shared.cors_middleware.handle_cors_preflight`** - 48 imports  
3. **`src.shared.common.is_local_dev`** - 46 imports
4. **`src.shared.data_access.TableStorageRepository`** - 38 imports
5. **`src.shared.azure_services.get_secret`** - 34 imports
6. **`src.shared.data_access.get_table_storage_repository`** - 33 imports
7. **`src.shared.db_service_client.get_db_client`** - 32 imports
8. **`src.shared.data_access.SqlDatabaseRepository`** - 32 imports

### DB Service Core Dependencies
The DB service has fewer but more focused internal dependencies, primarily around:
- Database repositories and connections
- Queue management systems
- Shared utilities and configuration

## Files Requiring Import Updates

### 1. Test Files to be Moved (SFDC Service)

#### Root-level test files that will move to `tests/unit/` or `tests/integration/`:

**Files with complex internal imports:**
- `test_enhanced_error_handling.py` - Imports from `src.shared.error_handler`, `src.shared.task_sequence_failure_handler`
- `test_account_management.py` - DEPRECATED file with database operations
- `test_api.py` - Simple test script with minimal imports
- `test_endpoints.py` - API endpoint tests
- `test_integration.py` - Integration tests
- `test_local_startup.py` - Local development tests
- `test_queue_message_processor.py` - Queue processing tests
- `test_queue_processing.py` - Queue system tests

**Import Update Strategy:**
- These files will move from root to `tests/unit/shared/`, `tests/unit/api/`, or `tests/integration/`
- Import paths will remain the same since they use absolute imports from `src.`
- No import changes needed due to absolute import structure

### 2. Test Files to be Moved (DB Service)

#### Root-level test files:
- `test_enhanced_database_security.py` - Imports from `shared.enhanced_repository_security`
- `test_enhanced_queue_manager.py` - Imports from `shared.enhanced_queue_manager`
- `test_enhanced_task_coordination_service.py` - Task coordination tests
- `test_enhanced_task_status_service.py` - Task status tests
- `test_execution_context_manager.py` - Context manager tests
- `test_task_coordination_service.py` - Task coordination tests

**Import Update Strategy:**
- These files will move to `tests/unit/shared/`
- Import paths use `shared.` prefix which matches current structure
- No import changes needed

### 3. Utility Scripts to be Moved (SFDC Service)

#### Scripts moving from root to `scripts/`:

**`check_credentials.py`:**
```python
from azure.data.tables import TableServiceClient
from src.shared.config import get_storage_connection_string
```

**`setup_local_dev.py`:**
- Minimal imports, mostly standard library
- No internal imports to update

**`task_management.py`:**
```python
from src.shared.background_processor import BackgroundProcessor
from src.shared.utils import create_json_response
from src.shared.cors_middleware import cors_middleware
```

**`check_policies_result.py`, `create_policies_result.py`, `create_queues.py`, `simple_test.py`:**
- Various internal imports from `src.shared.*`

**Import Update Strategy:**
- Scripts moving to `scripts/` directory
- All use absolute imports with `src.` prefix
- Import paths will remain unchanged due to absolute import structure

### 4. Example Files to be Moved (DB Service)

#### Example files moving to `examples/`:
- `example_enhanced_database_security_usage.py`
- `example_enhanced_execution_context_usage.py`
- `example_enhanced_queue_manager_usage.py`
- `example_enhanced_task_coordination_usage.py`
- `example_enhanced_task_status_usage.py`
- `example_execution_context_manager_usage.py`
- `example_task_coordination_usage.py`

**Import Update Strategy:**
- Moving to `examples/` directory
- Use `shared.` prefix imports which match current structure
- No import changes needed

## External Dependencies Analysis

### Top External Packages
1. **json** - 247 imports (standard library)
2. **logging** - 233 imports (standard library)
3. **datetime.datetime** - 231 imports (standard library)
4. **typing.Any** - 201 imports (type hints)
5. **typing.Dict** - 200 imports (type hints)
6. **typing.Optional** - 183 imports (type hints)
7. **typing.List** - 158 imports (type hints)
8. **os** - 124 imports (standard library)
9. **azure.functions** - 94 imports (Azure Functions framework)
10. **datetime.timedelta** - 92 imports (standard library)

### Azure-Specific Dependencies
- **azure.functions** - Core Azure Functions framework
- **azure.data.tables** - Table storage operations
- **azure.servicebus** - Service Bus operations
- **azure.keyvault** - Key Vault operations
- **azure.storage** - Storage operations

## Potential Issues and Risks

### Low Risk Areas
1. **Core application structure** - Already well-organized with absolute imports
2. **External dependencies** - No changes needed
3. **Main function apps** - Entry points remain unchanged

### Medium Risk Areas
1. **Test file imports** - Need validation after movement
2. **Utility script imports** - Need testing after movement to scripts/

### Identified Issues
1. **Potential circular imports** in `src.shared.auth.py` - imports from `src.shared.auth_utils`
2. **DEPRECATED files** - `test_account_management.py` should be removed rather than moved

## Recommendations for Folder Restructuring

### 1. Preserve Current Import Structure
- **Keep absolute imports:** Current structure uses absolute imports which are ideal
- **Maintain src/ prefix:** SFDC service uses `src.` prefix consistently
- **Maintain shared/ prefix:** DB service uses `shared.` prefix consistently

### 2. Import Update Strategy
- **Test files:** No import changes needed due to absolute imports
- **Utility scripts:** No import changes needed due to absolute imports
- **Example files:** No import changes needed due to absolute imports

### 3. Validation Steps
1. **Run import validation:** Test all moved files can import correctly
2. **Execute test suites:** Ensure all tests pass after movement
3. **Test utility scripts:** Verify scripts work from new locations

### 4. Clean Up Opportunities
1. **Remove deprecated files:** `test_account_management.py` and similar
2. **Consolidate duplicate functionality:** Identify and merge similar utilities
3. **Update documentation:** Reflect new file locations

## Implementation Impact

### Minimal Import Changes Required
Due to the excellent current import structure using absolute paths:
- **0 import statement changes** needed for test files
- **0 import statement changes** needed for utility scripts  
- **0 import statement changes** needed for example files

### Validation Requirements
1. **Python import resolution:** Verify all imports resolve correctly
2. **Test execution:** Ensure all tests can run from new locations
3. **Script functionality:** Verify utility scripts work from scripts/ directory
4. **Function app startup:** Ensure main applications start correctly

## Conclusion

The current import structure is exceptionally well-organized with consistent use of absolute imports. This makes the folder restructuring process much safer and simpler than typical reorganization projects. The main work will be:

1. **File movement** - Moving files to appropriate directories
2. **Validation** - Ensuring moved files work correctly
3. **Cleanup** - Removing deprecated and temporary files

The risk of breaking imports is minimal due to the excellent current structure using absolute import paths.