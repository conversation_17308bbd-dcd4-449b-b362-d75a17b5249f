# Root workspace .gitignore

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and editor files
.vscode/*
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/settings.json
.idea/
*.swp
*.swo
*~

# Virtual environments
.env
.env.*
.venv/
venv/
ENV/
env/

# Log files at workspace level
*.log
logs/

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Build artifacts
build/
dist/
*.whl

# Python cache files
__pycache__/
*.py[cod]
*$py.class

# Test output
test-output.xml
.coverage*
htmlcov/

# Workspace-specific temporary files
SFDC-log-tail.txt
TaskStatus*.csv
test_output_patch*.txt

# Summary and fixes files
FIXES_SUMMARY.md
summary*.md
*architecture*.md