# Rollback Procedure Documentation

**Created:** $(date)  
**Purpose:** Detailed rollback procedures for folder structure maintenance  
**Backup Branches:** `backup/before-folder-restructure`

## Quick Rollback Commands

### Emergency Rollback (Both Services)
```bash
# SFDC Service
cd atomsec-func-sfdc
git checkout backup/before-folder-restructure
git reset --hard backup/before-folder-restructure

# DB Service  
cd ../atomsec-func-db-r
git checkout backup/before-folder-restructure
git reset --hard backup/before-folder-restructure
```

## Detailed Rollback Procedures

### Step 1: Switch to Backup Branches

#### SFDC Service
```bash
cd atomsec-func-sfdc
git status                                    # Check current state
git stash                                     # Stash any uncommitted changes
git checkout backup/before-folder-restructure # Switch to backup branch
```

#### DB Service
```bash
cd atomsec-func-db-r
git status                                    # Check current state
git stash                                     # Stash any uncommitted changes
git checkout backup/before-folder-restructure # Switch to backup branch
```

### Step 2: Validate Backup State

#### SFDC Service Validation
```bash
# Check function app starts
func start --port 7071

# Run tests
python -m pytest tests/ -v

# Check key files exist
ls -la function_app.py host.json requirements.txt
ls -la api/ shared/ blueprints/
```

#### DB Service Validation
```bash
# Check function app starts
func start --port 7072

# Run tests
python -m pytest tests/ -v

# Check key files exist
ls -la function_app.py host.json requirements.txt
ls -la api/ shared/ repositories/
```

### Step 3: Clean Working Directory (If Needed)

```bash
# Remove untracked files and directories
git clean -fd

# Remove ignored files (be careful with this)
git clean -fdx
```

### Step 4: Restore Original Working Branch

If you need to return to the original working branch after rollback:

```bash
# SFDC Service
git checkout dev-db-queue-updates
git reset --hard origin/dev-db-queue-updates

# DB Service
git checkout dev-db-queue-updates  
git reset --hard origin/dev-db-queue-updates
```

## Rollback Triggers

### Automatic Rollback Conditions
Execute rollback immediately if:
- More than 50% of tests fail after changes
- Function app fails to start locally
- Critical API endpoints return 500 errors
- Import errors prevent module loading

### Warning Conditions
Consider rollback if:
- CI/CD pipeline fails completely
- Performance degradation > 25%
- New errors in application logs
- Developer workflow significantly impacted

## Validation After Rollback

### Functional Validation
1. **Function App Startup**
   ```bash
   func start
   # Should start without errors
   ```

2. **Test Suite Execution**
   ```bash
   python -m pytest tests/ -v
   # All tests should pass
   ```

3. **API Endpoint Testing**
   ```bash
   curl http://localhost:7071/api/health
   # Should return 200 OK
   ```

### Structural Validation
1. **File Structure Check**
   ```bash
   # Verify key directories exist
   ls -la api/ shared/ blueprints/ tests/
   ```

2. **Import Resolution**
   ```python
   # Test key imports work
   python -c "from shared.config import get_config"
   python -c "from api.account_endpoints import *"
   ```

3. **Configuration Validation**
   ```bash
   # Check configuration files
   cat host.json
   cat local.settings.json
   ```

## Recovery from Failed Rollback

### If Rollback Fails
1. **Force Reset to Backup**
   ```bash
   git reset --hard backup/before-folder-restructure
   git clean -fdx
   ```

2. **Re-clone Repository**
   ```bash
   cd ..
   git clone <repository-url> atomsec-func-sfdc-recovery
   cd atomsec-func-sfdc-recovery
   git checkout backup/before-folder-restructure
   ```

3. **Manual File Restoration**
   - Copy critical files from backup branch
   - Restore configuration files
   - Rebuild virtual environment

### Contact Information
- **Primary Contact:** Development Team Lead
- **Secondary Contact:** DevOps Team
- **Emergency Contact:** System Administrator

## Post-Rollback Actions

### Immediate Actions
1. Notify team of rollback
2. Document rollback reason
3. Schedule post-mortem meeting
4. Update task tracking system

### Investigation Steps
1. Analyze what went wrong
2. Review logs and error messages
3. Identify root cause
4. Plan corrective actions

### Prevention Measures
1. Improve testing procedures
2. Add validation checkpoints
3. Enhance monitoring
4. Update rollback procedures

## Backup Branch Maintenance

### Backup Branch Lifecycle
- **Creation:** Before major changes
- **Retention:** Keep for 30 days after successful deployment
- **Cleanup:** Remove after validation period

### Creating New Backup Branches
```bash
# Before next major change
git checkout -b backup/before-next-change-$(date +%Y%m%d)
```

### Backup Branch Naming Convention
- `backup/before-folder-restructure` - Current backup
- `backup/before-api-refactor-YYYYMMDD` - Future backups
- `backup/before-deployment-YYYYMMDD` - Deployment backups

---

**Important:** Always test the rollback procedure in a development environment before relying on it in production scenarios.