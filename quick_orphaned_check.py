#!/usr/bin/env python3
"""
Quick script to check first 10 tasks for orphaned status
"""

import json
import subprocess

def run_az_command(command):
    """Run Azure CLI command and return JSON output"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            return None
    except json.JSONDecodeError:
        return None

def check_task_relations(task_id):
    """Check if a task has any parent relations"""
    command = f'az boards work-item show --id {task_id} --organization "https://dev.azure.com/AtomSec" --expand links --output json'
    result = run_az_command(command)
    
    if result and 'relations' in result:
        return result['relations'] is None or len(result['relations']) == 0
    return True

def main():
    print("🔍 Quick check for orphaned tasks (first 10)...")
    print("=" * 50)
    
    # Get first 10 tasks
    command = 'az boards query --wiql "SELECT [System.Id], [System.Title], [System.State], [System.AssignedTo] FROM WorkItems WHERE [System.WorkItemType] = \'Task\' AND [System.State] <> \'Removed\'" --organization "https://dev.azure.com/AtomSec" --project "atomsec_app" --output json'
    tasks_data = run_az_command(command)
    
    if not tasks_data:
        print("❌ Failed to retrieve tasks")
        return
    
    tasks = tasks_data[:10]  # Only check first 10
    print(f"📊 Checking first {len(tasks)} tasks...")
    
    orphaned_tasks = []
    
    for i, task in enumerate(tasks, 1):
        task_id = task['fields']['System.Id']
        task_title = task['fields']['System.Title']
        task_state = task['fields']['System.State']
        assigned_to = task['fields'].get('System.AssignedTo', {}).get('displayName', 'Unassigned')
        
        print(f"Checking task {i}: {task_id} - {task_title[:40]}...")
        
        if check_task_relations(task_id):
            orphaned_tasks.append({
                'id': task_id,
                'title': task_title,
                'state': task_state,
                'assigned_to': assigned_to
            })
            print(f"  ✅ ORPHANED")
        else:
            print(f"  ❌ Has parent")
    
    print("\n" + "=" * 50)
    print(f"🎯 ORPHANED TASKS FOUND: {len(orphaned_tasks)}")
    print("=" * 50)
    
    if orphaned_tasks:
        print(f"{'ID':<6} {'State':<12} {'Assigned To':<20} {'Title'}")
        print("-" * 70)
        for task in orphaned_tasks:
            print(f"{task['id']:<6} {task['state']:<12} {task['assigned_to']:<20} {task['title']}")
    else:
        print("✅ No orphaned tasks found in the first 10 tasks!")

if __name__ == "__main__":
    main() 