<?xml version='1.0' encoding='utf-8'?>
<BestPractices>
    <UserType name="Salesforce">
        <Practice>
            <SalesforceSetting>minimumPasswordLength</SalesforceSetting>
            <StandardValue>12</StandardValue>
            <Description>Sets a strong minimum password length to prevent easy brute-force attacks.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>passwordComplexity</SalesforceSetting>
            <StandardValue>2</StandardValue>
            <Description>Enforces strong password complexity: letters, numbers, and symbols.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="0">No restriction</Value>
                <Value code="1">Must include alpha and numeric characters</Value>
                <Value code="2">Must include alpha, numeric, and special characters</Value>
                <Value code="3">Must include numbers and uppercase and lowercase letters</Value>
                <Value code="4">Must include numbers, uppercase and lowercase letters, and special characters</Value>
                <Value code="5">Must include 3 of the following: numbers, uppercase letters, lowercase letters, and special characters</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>passwordExpiration</SalesforceSetting>
            <StandardValue>60</StandardValue>
            <Description>Requires users to change passwords every 60 days.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="30">30 days</Value>
                <Value code="60">60 days</Value>
                <Value code="90">90 days</Value>
                <Value code="180">180 days</Value>
                <Value code="0">Never expires</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>passwordHistory</SalesforceSetting>
            <StandardValue>5</StandardValue>
            <Description>Prevents reuse of the last 5 passwords.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="0">No restriction</Value>
                <Value code="1">Cannot reuse last password</Value>
                <Value code="2">Cannot reuse last 2 passwords</Value>
                <Value code="3">Cannot reuse last 3 passwords</Value>
                <Value code="4">Cannot reuse last 4 passwords</Value>
                <Value code="5">Cannot reuse last 5 passwords</Value>
                <Value code="6">Cannot reuse last 6 passwords</Value>
                <Value code="7">Cannot reuse last 7 passwords</Value>
                <Value code="8">Cannot reuse last 8 passwords</Value>
                <Value code="9">Cannot reuse last 9 passwords</Value>
                <Value code="10">Cannot reuse last 10 passwords</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>maxLoginAttempts</SalesforceSetting>
            <StandardValue>5</StandardValue>
            <Description>Locks accounts after 5 failed login attempts.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="3">3</Value>
                <Value code="5">5</Value>
                <Value code="10">10</Value>
                <Value code="0">No Limit</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>lockoutInterval</SalesforceSetting>
            <StandardValue>30</StandardValue>
            <Description>Locks the user out for 30 minutes after repeated failures.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="15">15 minutes</Value>
                <Value code="30">30 minutes</Value>
                <Value code="60">60 minutes</Value>
                <Value code="0">Forever (must be reset by admin)</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>minimumPasswordLifetime</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Prevents users from cycling through passwords quickly.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>obscure</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Hides password rule hints on the login page.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>forgotPasswordRedirect</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Redirects to a custom secure page after password reset.</Description>
            <OWASP>A10: Insufficient Logging &amp; Monitoring</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>passwordQuestion</SalesforceSetting>
            <StandardValue>0</StandardValue>
            <Description>Disables weak password recovery questions.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="0">None</Value>
                <Value code="1">Cannot contain password</Value>
            </PicklistValues>
        </Practice>
    </UserType>
    <UserType name="BLANK">
        <Practice>
            <SalesforceSetting>minimumPasswordLength</SalesforceSetting>
            <StandardValue>12</StandardValue>
            <Description>Sets a strong minimum password length to prevent easy brute-force attacks.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>passwordComplexity</SalesforceSetting>
            <StandardValue>2</StandardValue>
            <Description>Enforces strong password complexity: letters, numbers, and symbols.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="0">No restriction</Value>
                <Value code="1">Must include alpha and numeric characters</Value>
                <Value code="2">Must include alpha, numeric, and special characters</Value>
                <Value code="3">Must include numbers and uppercase and lowercase letters</Value>
                <Value code="4">Must include numbers, uppercase and lowercase letters, and special characters</Value>
                <Value code="5">Must include 3 of the following: numbers, uppercase letters, lowercase letters, and special characters</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>passwordExpiration</SalesforceSetting>
            <StandardValue>60</StandardValue>
            <Description>Requires users to change passwords every 60 days.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="30">30 days</Value>
                <Value code="60">60 days</Value>
                <Value code="90">90 days</Value>
                <Value code="180">180 days</Value>
                <Value code="0">Never expires</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>passwordHistory</SalesforceSetting>
            <StandardValue>5</StandardValue>
            <Description>Prevents reuse of the last 5 passwords.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="0">No restriction</Value>
                <Value code="1">Cannot reuse last password</Value>
                <Value code="2">Cannot reuse last 2 passwords</Value>
                <Value code="3">Cannot reuse last 3 passwords</Value>
                <Value code="4">Cannot reuse last 4 passwords</Value>
                <Value code="5">Cannot reuse last 5 passwords</Value>
                <Value code="6">Cannot reuse last 6 passwords</Value>
                <Value code="7">Cannot reuse last 7 passwords</Value>
                <Value code="8">Cannot reuse last 8 passwords</Value>
                <Value code="9">Cannot reuse last 9 passwords</Value>
                <Value code="10">Cannot reuse last 10 passwords</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>maxLoginAttempts</SalesforceSetting>
            <StandardValue>5</StandardValue>
            <Description>Locks accounts after 5 failed login attempts.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="3">3</Value>
                <Value code="5">5</Value>
                <Value code="10">10</Value>
                <Value code="0">No Limit</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>lockoutInterval</SalesforceSetting>
            <StandardValue>30</StandardValue>
            <Description>Locks the user out for 30 minutes after repeated failures.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="15">15 minutes</Value>
                <Value code="30">30 minutes</Value>
                <Value code="60">60 minutes</Value>
                <Value code="0">Forever (must be reset by admin)</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>minimumPasswordLifetime</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Prevents users from cycling through passwords quickly.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>obscure</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Hides password rule hints on the login page.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>forgotPasswordRedirect</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Redirects to a custom secure page after password reset.</Description>
            <OWASP>A10: Insufficient Logging &amp; Monitoring</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>passwordQuestion</SalesforceSetting>
            <StandardValue>0</StandardValue>
            <Description>Disables weak password recovery questions.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="0">None</Value>
                <Value code="1">Cannot contain password</Value>
            </PicklistValues>
        </Practice>
    </UserType>
</BestPractices>
