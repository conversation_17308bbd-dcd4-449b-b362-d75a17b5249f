<?xml version='1.0' encoding='utf-8'?>
<BestPractices>
    <UserType name="BLANK">
        <Practice>
            <SalesforceSetting>disableTimeoutWarning</SalesforceSetting>
            <StandardValue>false</StandardValue>
            <Description>Show a warning before session timeout.</Description>
            <OWASP>A9: Security Logging &amp; Monitoring Failures</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableCSRFOnGet</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Enable CSRF protection on GET requests.</Description>
            <OWASP>A01: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableCSRFOnPost</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Enable CSRF protection on POST requests.</Description>
            <OWASP>A01: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableCacheAndAutocomplete</SalesforceSetting>
            <StandardValue>false</StandardValue>
            <Description>Disable browser caching and autocomplete on login page.</Description>
            <OWASP>A02: Cryptographic Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableClickjackNonsetupSFDC</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Enable clickjack protection for standard non-setup pages.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableClickjackNonsetupUser</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Enable clickjack protection for user non-setup pages.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableClickjackNonsetupUserHeaderless</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Enable clickjack protection for headerless Visualforce pages.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableClickjackSetup</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Enable clickjack protection on setup pages.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableCoepHeader</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Enable COEP header to block untrusted embedded content.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableContentSniffingProtection</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Prevent MIME-type sniffing by browsers.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableCoopHeader</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Enable COOP header to prevent cross-origin attacks.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableLightningLogin</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Allow secure Lightning Login with MFA support.</Description>
            <OWASP>A07: Identification and Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enableLightningLoginOnlyWithUserPerm</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Restrict Lightning Login to users with explicit permission.</Description>
            <OWASP>A07: Identification and Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enablePermissionsPolicy</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Enable Permissions-Policy HTTP header to restrict browser features.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enablePostForSessions</SalesforceSetting>
            <StandardValue>false</StandardValue>
            <Description>Disable POST-based session establishment to reduce attack surface.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>enforceIpRangesEveryRequest</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Apply IP restrictions on every request for session integrity.</Description>
            <OWASP>A01: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>forceLogoutOnSessionTimeout</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Ensure user is logged out after session timeout.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>forceRelogin</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Force users to re-authenticate after session expiry.</Description>
            <OWASP>A07: Identification and Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>grantCameraAccess</SalesforceSetting>
            <StandardValue>TrustedUrls</StandardValue>
            <Description>Limit camera access to trusted URLs only.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>grantMicrophoneAccess</SalesforceSetting>
            <StandardValue>TrustedUrls</StandardValue>
            <Description>Limit microphone access to trusted URLs only.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>lockSessionsToDomain</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Lock sessions to a specific domain to prevent hijacking.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>lockSessionsToIp</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Bind session to originating IP for enhanced security.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>redirectionWarning</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Show warning before redirecting users to external URLs.</Description>
            <OWASP>A10: Server-Side Request Forgery (SSRF)</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>referrerPolicy</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Enable strict control over the referrer information sent in headers.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>referrerPolicyDirective</SalesforceSetting>
            <StandardValue>origin-when-cross-origin</StandardValue>
            <Description>Send referrer only for same-origin or hide cross-origin details.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>requireHttpOnly</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Ensure cookies are inaccessible to JavaScript.</Description>
            <OWASP>A02: Cryptographic Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>sendCspForUncommonClients</SalesforceSetting>
            <StandardValue>true</StandardValue>
            <Description>Send CSP headers to all clients, including uncommon ones.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>sessionTimeout</SalesforceSetting>
            <StandardValue>ThirtyMinutes</StandardValue>
            <Description>Defines how long a session lasts before timeout.</Description>
            <OWASP>A05: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>sidToken3rdPartyAuraApp</SalesforceSetting>
            <StandardValue>false</StandardValue>
            <Description>Block SID token use for 3rd-party Aura apps.</Description>
            <OWASP>A01: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
</BestPractices>