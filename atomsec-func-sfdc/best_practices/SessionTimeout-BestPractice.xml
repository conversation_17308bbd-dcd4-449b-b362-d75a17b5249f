<?xml version='1.0' encoding='utf-8'?>
<BestPractices>
    <UserType name="BLANK">
        <Practice>
            <SalesforceSetting>externalCommunityUserIdentityVerif</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Prevents unauthorized access by forcing identity confirmation in sensitive contexts .</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>forceLogout</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Ensures sessions are forcefully terminated after logout or administrative actions like password reset. Important for account takeover prevention.</Description>
            <OWASP>A05:2021-Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>sessionPersistence</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Prevents persistent sessions that can be hijacked. Aligns with secure session management.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>sessionTimeout</SalesforceSetting>
            <StandardValue>15</StandardValue>
            <Description>2 hours is acceptable, but high-risk profiles may require shorter timeouts (e.g., 15 or 30 minutes).</Description>
            <OWASP>A05:2021-Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="1440">24 hours of inactivity</Value>
                <Value code="720">12 hours of inactivity</Value>
                <Value code="480">8 hours of inactivity</Value>
                <Value code="240">4 hours of inactivity</Value>
                <Value code="0">2 hours of inactivity</Value>
                <Value code="90">90 minutes of inactivity</Value>
                <Value code="60">1 hour of inactivity</Value>
                <Value code="30">30 minutes of inactivity</Value>
                <Value code="15">15 minutes of inactivity</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>sessionTimeoutWarning</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Improves user experience and encourages saving work before timeout.</Description>
            <OWASP>A05:2021-Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
    <UserType name="Salesforce">
        <Practice>
            <SalesforceSetting>externalCommunityUserIdentityVerif</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Prevents unauthorized access by forcing identity confirmation in sensitive contexts .</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>forceLogout</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Ensures sessions are forcefully terminated after logout or administrative actions like password reset. Important for account takeover prevention.</Description>
            <OWASP>A05:2021-Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>sessionPersistence</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Prevents persistent sessions that can be hijacked. Aligns with secure session management.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>sessionTimeout</SalesforceSetting>
            <StandardValue>15</StandardValue>
            <Description>2 hours is acceptable, but high-risk profiles may require shorter timeouts (e.g., 15 or 30 minutes).</Description>
            <OWASP>A05:2021-Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
            <PicklistValues>
                <Value code="1440">24 hours of inactivity</Value>
                <Value code="720">12 hours of inactivity</Value>
                <Value code="480">8 hours of inactivity</Value>
                <Value code="240">4 hours of inactivity</Value>
                <Value code="0">2 hours of inactivity</Value>
                <Value code="90">90 minutes of inactivity</Value>
                <Value code="60">1 hour of inactivity</Value>
                <Value code="30">30 minutes of inactivity</Value>
                <Value code="15">15 minutes of inactivity</Value>
            </PicklistValues>
        </Practice>
        <Practice>
            <SalesforceSetting>sessionTimeoutWarning</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Improves user experience and encourages saving work before timeout.</Description>
            <OWASP>A05:2021-Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
</BestPractices>