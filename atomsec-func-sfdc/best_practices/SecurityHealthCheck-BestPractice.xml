<?xml version="1.0" encoding="UTF-8"?>
<BestPractices>
    <Practice>
        <SalesforceSetting>Expired Certificate</SalesforceSetting>
        <Description>Expired certificates undermine HTTPS and TLS integrity.</Description>
        <OWASPCategory>A02:2021-Cryptographic Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>0</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Number of Objects with Default External Access Set to Public</SalesforceSetting>
        <Description>Public access creates unauthorized data exposure risk.</Description>
        <OWASPCategory>A01:2021- Broken Access Control</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>0</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Number of security risk file types with Hybrid behavior</SalesforceSetting>
        <Description>Permissive or unregulated file behavior is a misconfiguration risk.</Description>
        <OWASPCategory>A05:2021-Security Misconfiguration</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>0</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Maximum invalid login attempts</SalesforceSetting>
        <Description>Weak settings allow brute-force attacks.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>3</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Lock sessions to the domain in which they were first used</SalesforceSetting>
        <Description>Prevents session hijacking across domains.</Description>
        <OWASPCategory>A01:2021- Broken Access Control</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Let users verify their identity by text (SMS)</SalesforceSetting>
        <Description>SMS-based MFA is considered weak, especially if misconfigured.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Enable clickjack protection for Setup pages</SalesforceSetting>
        <Description>Clickjack protection is essential against UI redress attacks.</Description>
        <OWASPCategory>A05:2021-Security Misconfiguration</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Enable clickjack protection for non-Setup Salesforce pages</SalesforceSetting>
        <Description>Same as above, for user-side interfaces.</Description>
        <OWASPCategory>A05:2021-Security Misconfiguration</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Enable clickjack protection for customer Visualforce pages with standard headers</SalesforceSetting>
        <Description>Prevents clickjacking in customer-facing pages.</Description>
        <OWASPCategory>A05:2021-Security Misconfiguration</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Enable clickjack protection for customer Visualforce pages with headers disabled</SalesforceSetting>
        <Description>Headers disabled require extra scrutiny; misconfiguration risk.</Description>
        <OWASPCategory>A05:2021-Security Misconfiguration</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Enable CSRF protection on GET requests on non-setup pages</SalesforceSetting>
        <Description>Lack of CSRF protection makes users vulnerable to cross-site request forgery.</Description>
        <OWASPCategory>A05:2021-Security Misconfiguration</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Enable CSRF protection on POST requests on non-setup pages</SalesforceSetting>
        <Description>POST-based CSRFs can have higher impact; protection is essential.</Description>
        <OWASPCategory>A05:2021-Security Misconfiguration</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Require HttpOnly attribute</SalesforceSetting>
        <Description>Protects session cookies from JavaScript access (defense against XSS).</Description>
        <OWASPCategory>A05:2021-Security Misconfiguration</OWASPCategory>
        <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Require a minimum 1 day password lifetime</SalesforceSetting>
        <Description>Prevents users from circumventing password history by quick rotations.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Administrators Can Log in as Any User</SalesforceSetting>
        <Description>Elevated access without logging or controls is a serious access risk.</Description>
        <OWASPCategory>A01:2021- Broken Access Control</OWASPCategory>
        <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        <StandardValue>Disabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>User passwords expire in</SalesforceSetting>
        <Description>Password expiration helps reduce impact of stolen credentials.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        <StandardValue>30 days</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Password complexity requirement</SalesforceSetting>
        <Description>Weak passwords are easier to brute-force or guess.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        <StandardValue>Must include alpha, numeric, and special characters</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Enforce password history</SalesforceSetting>
        <Description>Prevents reusing previously compromised or weak passwords.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        <StandardValue>3 passwords remembered</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Minimum password length</SalesforceSetting>
        <Description>Short passwords are insecure; this enforces a basic standard.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        <StandardValue>8 characters</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Force relogin after Login-As-User</SalesforceSetting>
        <Description>Prevents session abuse post-login-as.</Description>
        <OWASPCategory>A01:2021- Broken Access Control</OWASPCategory>
        <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Enforce login IP ranges on every request</SalesforceSetting>
        <Description>Strengthens session controls and location-based access enforcement.</Description>
        <OWASPCategory>A01:2021- Broken Access Control</OWASPCategory>
        <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Enable Content Security Policy protection for email templates</SalesforceSetting>
        <Description>Prevents injection of malicious scripts in emails.</Description>
        <OWASPCategory>A05:2021-Security Misconfiguration</OWASPCategory>
        <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Enable Content Sniffing protection</SalesforceSetting>
        <Description>Prevents browsers from interpreting content as a different MIME type.</Description>
        <OWASPCategory>A05:2021-Security Misconfiguration</OWASPCategory>
        <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Obscure secret answer for password resets</SalesforceSetting>
        <Description>Prevents attackers from seeing or guessing secret answers.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Require identity verification during MFA registration</SalesforceSetting>
        <Description>Prevents fake MFA setups that compromise future login security.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Require identity verification for email address changes</SalesforceSetting>
        <Description>Stops attackers from hijacking account recovery options.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Lockout effective period</SalesforceSetting>
        <Description>Longer lockout reduces brute-force attempts, deterring attackers.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        <StandardValue>30 minutes</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Remote Site Settings</SalesforceSetting>
        <Description>Poorly validated remote URLs could be abused for SSRF.</Description>
        <OWASPCategory>A10:2021-Server-Side Request Forgery (SSRF)</OWASPCategory>
        <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        <StandardValue>No remote sites with the Disable Protocol Security option selected</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Password question requirement</SalesforceSetting>
        <Description>Security questions are often weak and guessable.</Description>
        <OWASPCategory>A07:2021-Identification and Authentication Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        <StandardValue>Cannot contain password</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Force logout on session timeout</SalesforceSetting>
        <Description>Ensures idle sessions don't remain open, reducing session hijack risk.</Description>
        <OWASPCategory>A01:2021- Broken Access Control</OWASPCategory>
        <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Session Timeout</SalesforceSetting>
        <Description>Shorter timeouts reduce risk from unattended sessions.</Description>
        <OWASPCategory>A01:2021- Broken Access Control</OWASPCategory>
        <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        <StandardValue>15 minutes</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Number of Objects to which Guest User Profiles have Edit Access</SalesforceSetting>
        <Description>Guest users shouldn't be able to edit data — high access risk.</Description>
        <OWASPCategory>A01:2021- Broken Access Control</OWASPCategory>
        <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        <StandardValue>4</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Number of Objects to which Guest User Profiles have Read Access</SalesforceSetting>
        <Description>Public read access can expose sensitive data unintentionally.</Description>
        <OWASPCategory>A01:2021- Broken Access Control</OWASPCategory>
        <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        <StandardValue>4</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Require permission to view record names in lookup fields</SalesforceSetting>
        <Description>Prevents metadata or sensitive object names from leaking.</Description>
        <OWASPCategory>A01:2021- Broken Access Control</OWASPCategory>
        <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        <StandardValue>Enabled</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Key Size</SalesforceSetting>
        <Description>Smaller keys are more vulnerable to brute-force attacks.</Description>
        <OWASPCategory>A02:2021-Cryptographic Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        <StandardValue>4096</StandardValue>
    </Practice>
    <Practice>
        <SalesforceSetting>Certificate Expiration</SalesforceSetting>
        <Description>Same as "Expired Certificate" — part of crypto hygiene.</Description>
        <OWASPCategory>A02:2021-Cryptographic Failures</OWASPCategory>
        <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        <StandardValue>179 days</StandardValue>
    </Practice>
</BestPractices> 