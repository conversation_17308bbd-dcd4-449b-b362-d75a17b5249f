<?xml version='1.0' encoding='utf-8'?>
<BestPractices>
    <UserType name="Salesforce">
        <Practice>
            <SalesforceSetting>AccessCMC</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to export data weekly.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageUsers</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create, edit, and deactivate users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicFilters</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and manage public list views.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicTemplates</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and manage public classic email templates.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ModifyAllData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants access to all data regardless of sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageCases</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Allows users to create, edit, and delete cases.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageSolutions</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage published solutions.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CustomizeApplication</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to customize the application.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditReadonlyFields</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit fields marked as read-only.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>RunReports</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to run reports.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewSetup</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view setup and configuration pages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyEntity</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of records.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ImportLeads</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to import leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageLeads</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create, edit, and delete leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyLead</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants read access to all data regardless of sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicDocuments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage public documents.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewEncryptedData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view encrypted data.</Description>
            <OWASP>A3: Sensitive Data Exposure</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditHtmlTemplates</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit HTML email templates.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageEncryptionKeys</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage encryption keys.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DeleteActivatedContract</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to delete activated contracts.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ApiUserOnly</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Restricts user access to API only.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageRemoteAccess</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage connected apps.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PasswordNeverExpires</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Prevents user passwords from expiring.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>InstallMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to download AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PublishMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to upload AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManagePartners</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage partner accounts and users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ChatterOwnGroups</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and own new Chatter groups.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CreateMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>BulkApiHardDelete</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to perform hard deletes using the Bulk API.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PortalSuperUser</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants super user access in portals.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DelegatedPortalUserAdmin</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows delegated administrators to manage portal users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewContent</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view content in portals.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageEmailClientConfig</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage email client configurations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageDataIntegrations</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage data integrations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewDataCategories</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view data categories in setup.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageDataCategories</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage data categories.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageMobile</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage mobile configurations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ApiEnabled</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to access Salesforce via API.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageCustomReportTypes</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage custom report types.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditCaseComments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit case comments.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyCase</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of cases.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ContentAdministrator</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage Salesforce CRM content.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CreateWorkspaces</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create content libraries.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageRoles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to create, edit, and delete roles in the role hierarchy.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageSharing</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to create, edit, and delete sharing rules for objects. This
                includes the ability to manage criteria-based and owner-based sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ResetPasswords</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the ability to reset passwords and unlock user accounts</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManagePasswordPolicies</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the ability to configure password policies for all users in the org.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>Packaging2Delete</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows a user to delete second-generation packages (2GP) in Salesforce Dev
                Hub-enabled orgs.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>Packaging2</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants users the ability to create, update, and manage Second-Generation
                Packages (2GP) using Salesforce DX and Dev Hub.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DownloadPackageVersionZips</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to download the zipped contents of a 2GP package version.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageFilesAndAttachments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the user ability to upload, edit, delete, and manage files and
                attachments in Salesforce. This includes files linked to records and stored in
                Content or Notes &amp; Attachments.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>QueryAllFiles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to query and access all files in the org, including those
                not explicitly shared with the user.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllUsers</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows a user to view all user records in the Salesforce org, regardless of
                role hierarchy or sharing settings.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PrivacyDataAccess</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants access to view, modify, and delete data subject records, including
                Individual records (used for GDPR/CCPA compliance). This also allows access to Do
                Not Contact flags and personal data that might otherwise be masked.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllProfiles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view all profiles and permission sets, including settings
                they may not otherwise be able to see (e.g., even if they can’t assign them).</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
    <UserType name="Salesforce Integration">
        <Practice>
            <SalesforceSetting>DataExport</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to export data weekly.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageUsers</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create, edit, and deactivate users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicFilters</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and manage public list views.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicTemplates</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and manage public classic email templates.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ModifyAllData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants access to all data regardless of sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageCases</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create, edit, and delete cases.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageSolutions</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage published solutions.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CustomizeApplication</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to customize the application.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditReadonlyFields</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit fields marked as read-only.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>RunReports</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to run reports.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewSetup</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view setup and configuration pages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyEntity</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of records.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ImportLeads</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to import leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageLeads</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create, edit, and delete leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyLead</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants read access to all data regardless of sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicDocuments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage public documents.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewEncryptedData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view encrypted data.</Description>
            <OWASP>A3: Sensitive Data Exposure</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditHtmlTemplates</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit HTML email templates.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageEncryptionKeys</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage encryption keys.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DeleteActivatedContract</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to delete activated contracts.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ApiUserOnly</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Restricts user access to API only.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageRemoteAccess</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage connected apps.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PasswordNeverExpires</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Prevents user passwords from expiring.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>InstallMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to download AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PublishMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to upload AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManagePartners</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage partner accounts and users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ChatterOwnGroups</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and own new Chatter groups.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CreateMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>BulkApiHardDelete</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to perform hard deletes using the Bulk API.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PortalSuperUser</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants super user access in portals.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DelegatedPortalUserAdmin</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows delegated administrators to manage portal users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewContent</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view content in portals.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageEmailClientConfig</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage email client configurations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageDataIntegrations</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage data integrations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewDataCategories</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view data categories in setup.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageDataCategories</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage data categories.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageMobile</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage mobile configurations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ApiEnabled</SalesforceSetting>
            <StandardValue>TRUE</StandardValue>
            <Description>Allows users to access Salesforce via API.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageCustomReportTypes</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage custom report types.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditCaseComments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit case comments.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyCase</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of cases.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ContentAdministrator</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage Salesforce CRM content.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CreateWorkspaces</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create content libraries.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageRoles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to create, edit, and delete roles in the role hierarchy.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageSharing</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to create, edit, and delete sharing rules for objects. This
                includes the ability to manage criteria-based and owner-based sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ResetPasswords</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the ability to reset passwords and unlock user accounts</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManagePasswordPolicies</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the ability to configure password policies for all users in the org.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>Packaging2Delete</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows a user to delete second-generation packages (2GP) in Salesforce Dev
                Hub-enabled orgs.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>Packaging2</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants users the ability to create, update, and manage Second-Generation
                Packages (2GP) using Salesforce DX and Dev Hub.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DownloadPackageVersionZips</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to download the zipped contents of a 2GP package version.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageFilesAndAttachments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the user ability to upload, edit, delete, and manage files and
                attachments in Salesforce. This includes files linked to records and stored in
                Content or Notes &amp; Attachments.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>QueryAllFiles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to query and access all files in the org, including those
                not explicitly shared with the user.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllUsers</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows a user to view all user records in the Salesforce org, regardless of
                role hierarchy or sharing settings.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PrivacyDataAccess</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants access to view, modify, and delete data subject records, including
                Individual records (used for GDPR/CCPA compliance). This also allows access to Do
                Not Contact flags and personal data that might otherwise be masked.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllProfiles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view all profiles and permission sets, including settings
                they may not otherwise be able to see (e.g., even if they can’t assign them).</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
    <UserType name="Guest User License">
        <Practice>
            <SalesforceSetting>DataExport</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to export data weekly.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageUsers</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create, edit, and deactivate users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicFilters</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and manage public list views.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicTemplates</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and manage public classic email templates.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ModifyAllData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants access to all data regardless of sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageCases</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create, edit, and delete cases.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageSolutions</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage published solutions.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CustomizeApplication</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to customize the application.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditReadonlyFields</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit fields marked as read-only.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>RunReports</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to run reports.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewSetup</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view setup and configuration pages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyEntity</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of records.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ImportLeads</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to import leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageLeads</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create, edit, and delete leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyLead</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants read access to all data regardless of sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicDocuments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage public documents.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewEncryptedData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view encrypted data.</Description>
            <OWASP>A3: Sensitive Data Exposure</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditHtmlTemplates</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit HTML email templates.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageEncryptionKeys</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage encryption keys.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DeleteActivatedContract</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to delete activated contracts.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ApiUserOnly</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Restricts user access to API only.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageRemoteAccess</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage connected apps.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PasswordNeverExpires</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Prevents user passwords from expiring.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>InstallMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to download AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PublishMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to upload AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManagePartners</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage partner accounts and users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ChatterOwnGroups</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and own new Chatter groups.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CreateMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>BulkApiHardDelete</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to perform hard deletes using the Bulk API.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PortalSuperUser</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants super user access in portals.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DelegatedPortalUserAdmin</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows delegated administrators to manage portal users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewContent</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view content in portals.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageEmailClientConfig</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage email client configurations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageDataIntegrations</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage data integrations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewDataCategories</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view data categories in setup.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageDataCategories</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage data categories.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageMobile</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage mobile configurations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ApiEnabled</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to access Salesforce via API.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageCustomReportTypes</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage custom report types.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditCaseComments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit case comments.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyCase</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of cases.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ContentAdministrator</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage Salesforce CRM content.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CreateWorkspaces</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create content libraries.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageRoles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to create, edit, and delete roles in the role hierarchy.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageSharing</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to create, edit, and delete sharing rules for objects. This
                includes the ability to manage criteria-based and owner-based sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ResetPasswords</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the ability to reset passwords and unlock user accounts</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManagePasswordPolicies</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the ability to configure password policies for all users in the org.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>Packaging2Delete</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows a user to delete second-generation packages (2GP) in Salesforce Dev
                Hub-enabled orgs.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>Packaging2</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants users the ability to create, update, and manage Second-Generation
                Packages (2GP) using Salesforce DX and Dev Hub.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DownloadPackageVersionZips</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to download the zipped contents of a 2GP package version.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageFilesAndAttachments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the user ability to upload, edit, delete, and manage files and
                attachments in Salesforce. This includes files linked to records and stored in
                Content or Notes &amp; Attachments.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>QueryAllFiles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to query and access all files in the org, including those
                not explicitly shared with the user.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllUsers</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows a user to view all user records in the Salesforce org, regardless of
                role hierarchy or sharing settings.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PrivacyDataAccess</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants access to view, modify, and delete data subject records, including
                Individual records (used for GDPR/CCPA compliance). This also allows access to Do
                Not Contact flags and personal data that might otherwise be masked.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllProfiles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view all profiles and permission sets, including settings
                they may not otherwise be able to see (e.g., even if they can’t assign them).</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
    <UserType name="Customer Community Login">
        <Practice>
            <SalesforceSetting>DataExport</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to export data weekly.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageUsers</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create, edit, and deactivate users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicFilters</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and manage public list views.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicTemplates</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and manage public classic email templates.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ModifyAllData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants access to all data regardless of sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageCases</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create, edit, and delete cases.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageSolutions</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage published solutions.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CustomizeApplication</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to customize the application.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditReadonlyFields</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit fields marked as read-only.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>RunReports</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to run reports.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewSetup</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view setup and configuration pages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyEntity</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of records.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ImportLeads</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to import leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageLeads</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create, edit, and delete leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyLead</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of leads.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants read access to all data regardless of sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditPublicDocuments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage public documents.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewEncryptedData</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view encrypted data.</Description>
            <OWASP>A3: Sensitive Data Exposure</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditHtmlTemplates</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit HTML email templates.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageEncryptionKeys</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage encryption keys.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DeleteActivatedContract</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to delete activated contracts.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ApiUserOnly</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Restricts user access to API only.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageRemoteAccess</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage connected apps.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PasswordNeverExpires</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Prevents user passwords from expiring.</Description>
            <OWASP>A7: Identification &amp; Authentication Failures</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>InstallMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to download AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PublishMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to upload AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManagePartners</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage partner accounts and users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ChatterOwnGroups</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create and own new Chatter groups.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CreateMultiforce</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create AppExchange packages.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>BulkApiHardDelete</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to perform hard deletes using the Bulk API.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PortalSuperUser</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants super user access in portals.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DelegatedPortalUserAdmin</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows delegated administrators to manage portal users.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewContent</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view content in portals.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageEmailClientConfig</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage email client configurations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageDataIntegrations</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage data integrations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewDataCategories</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view data categories in setup.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageDataCategories</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage data categories.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageMobile</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage mobile configurations.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ApiEnabled</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to access Salesforce via API.</Description>
            <OWASP>A6: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageCustomReportTypes</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage custom report types.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>EditCaseComments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to edit case comments.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>TransferAnyCase</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to transfer ownership of cases.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ContentAdministrator</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to manage Salesforce CRM content.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>CreateWorkspaces</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to create content libraries.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageRoles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to create, edit, and delete roles in the role hierarchy.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageSharing</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to create, edit, and delete sharing rules for objects. This
                includes the ability to manage criteria-based and owner-based sharing rules.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ResetPasswords</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the ability to reset passwords and unlock user accounts</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManagePasswordPolicies</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the ability to configure password policies for all users in the org.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>Packaging2Delete</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows a user to delete second-generation packages (2GP) in Salesforce Dev
                Hub-enabled orgs.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>Packaging2</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants users the ability to create, update, and manage Second-Generation
                Packages (2GP) using Salesforce DX and Dev Hub.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>DownloadPackageVersionZips</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to download the zipped contents of a 2GP package version.</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ManageFilesAndAttachments</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the user ability to upload, edit, delete, and manage files and
                attachments in Salesforce. This includes files linked to records and stored in
                Content or Notes &amp; Attachments.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>QueryAllFiles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to query and access all files in the org, including those
                not explicitly shared with the user.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllUsers</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows a user to view all user records in the Salesforce org, regardless of
                role hierarchy or sharing settings.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PrivacyDataAccess</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants access to view, modify, and delete data subject records, including
                Individual records (used for GDPR/CCPA compliance). This also allows access to Do
                Not Contact flags and personal data that might otherwise be masked.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllProfiles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view all profiles and permission sets, including settings
                they may not otherwise be able to see (e.g., even if they can’t assign them).</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
    <UserType name="Blank">
        <Practice>
            <SalesforceSetting>ApiEnabled</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants the user ability to upload, edit, delete, and manage files and
                attachments in Salesforce. This includes files linked to records and stored in
                Content or Notes &amp; Attachments.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>AccessCMC</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows the user to query and access all files in the org, including those
                not explicitly shared with the user.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Low</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllUsers</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows a user to view all user records in the Salesforce org, regardless of
                role hierarchy or sharing settings.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>PrivacyDataAccess</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Grants access to view, modify, and delete data subject records, including
                Individual records (used for GDPR/CCPA compliance). This also allows access to Do
                Not Contact flags and personal data that might otherwise be masked.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>Medium</RiskTypeBasedOnSeverity>
        </Practice>
        <Practice>
            <SalesforceSetting>ViewAllProfiles</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to view all profiles and permission sets, including settings
                they may not otherwise be able to see (e.g., even if they can’t assign them).</Description>
            <OWASP>A5: Security Misconfiguration</OWASP>
            <RiskTypeBasedOnSeverity>Informational</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
</BestPractices>