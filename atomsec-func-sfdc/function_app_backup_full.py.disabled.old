"""
Simple Test Function App

This is a minimal function app to test if Azure Functions is working properly.
"""

import azure.functions as func
import logging

# Create the function app
app = func.FunctionApp()

@app.route(route="test")
def test_function(req: func.HttpRequest) -> func.HttpResponse:
    """Simple test function"""
    logging.info('Python HTTP trigger function processed a request.')
    
    return func.HttpResponse(
        "Hello from Azure Functions!",
        status_code=200
    )

@app.route(route="health")
def health_check(req: func.HttpRequest) -> func.HttpResponse:
    """Health check function"""
    return func.HttpResponse(
        '{"status": "healthy", "service": "test"}',
        mimetype="application/json",
        status_code=200
    ) 