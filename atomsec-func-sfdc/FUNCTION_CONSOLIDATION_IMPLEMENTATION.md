# Function Consolidation Implementation

This document outlines the changes made to reduce the number of Azure Functions while maintaining the best performance of the application.

## Changes Implemented

### 1. Consolidated Task Processor Functions

The three separate task processor functions (`task_processor_high`, `task_processor_medium`, `task_processor_low`) have been consolidated into a single `task_processor` function that handles all priority levels.

**Before:**
```python
@app.queue_trigger(arg_name="msg", queue_name="task-queue-high", connection="AzureWebJobsStorage")
def task_processor_high(msg: func.QueueMessage) -> None:
    process_task_message(msg, "high", "task-queue-high")

@app.queue_trigger(arg_name="msg", queue_name="task-queue-medium", connection="AzureWebJobsStorage")
def task_processor_medium(msg: func.QueueMessage) -> None:
    process_task_message(msg, "medium", "task-queue-medium")

@app.queue_trigger(arg_name="msg", queue_name="task-queue-low", connection="AzureWebJobsStorage")
def task_processor_low(msg: func.QueueMessage) -> None:
    process_task_message(msg, "low", "task-queue-low")
```

**After:**
```python
@app.queue_trigger(arg_name="msg", queue_name="task-queue-high,task-queue-medium,task-queue-low", connection="AzureWebJobsStorage")
def task_processor(msg: func.QueueMessage) -> None:
    # Determine which queue triggered this function
    trigger_metadata = msg.trigger_metadata
    queue_name = trigger_metadata.get('QueueName', '')
    
    # Map queue name to priority level
    priority_map = {
        "task-queue-high": "high",
        "task-queue-medium": "medium",
        "task-queue-low": "low"
    }
    
    priority = priority_map.get(queue_name, "medium")  # Default to medium if unknown
    
    # Process the task message
    process_task_message(msg, priority, queue_name)
```

### 2. Expanded API Router Function

The API router function has been expanded to handle more routes, including:

1. Key Vault endpoints
2. Organization management endpoints

**Key Vault Routes:**
```python
# ===== 9. Key Vault Routes =====
elif path.startswith("api/key-vault/"):
    action = path[13:]  # Remove "api/key-vault/" prefix
    
    if action == "secrets" and method == "POST":
        from blueprints.key_vault_endpoints import store_secret_endpoint
        return store_secret_endpoint(req)
    elif action == "create" and method == "POST":
        from blueprints.key_vault_endpoints import create_key_vault_endpoint
        return create_key_vault_endpoint(req)
    elif action == "access-policy" and method == "POST":
        from blueprints.key_vault_endpoints import add_access_policy_endpoint
        return add_access_policy_endpoint(req)
    elif action == "client-credentials" and method == "POST":
        from blueprints.key_vault_endpoints import store_client_credentials_endpoint
        return store_client_credentials_endpoint(req)
```

**Organization Routes:**
```python
# ===== 10. Organization Management Routes =====
elif path.startswith("api/") and method == "POST":
    org_action = path[4:]  # Remove "api/" prefix
    
    if org_action == "connect-org":
        from blueprints.organization import connect_org
        return connect_org(req)
    elif org_action == "disconnect-org":
        from blueprints.organization import disconnect_org
        return disconnect_org(req)
    elif org_action == "rescan-org":
        from blueprints.organization import rescan_org
        return rescan_org(req)
```

### 3. Removed Blueprint Registration

The Key Vault blueprint registration has been removed since those endpoints are now handled by the API router:

```python
# ===== 6. Key Vault Management =====
# Key vault endpoints are now handled by the API router
```

## Benefits of the New Architecture

1. **Reduced Function Count**: The number of Azure Functions has been reduced, which:
   - Decreases cold start times
   - Reduces deployment complexity
   - Lowers resource consumption

2. **Simplified Routing**: The consolidated API router provides a centralized place for routing requests, making it easier to:
   - Debug routing issues
   - Add new endpoints
   - Maintain consistent error handling

3. **Improved Performance**: By consolidating functions, we:
   - Reduce the number of HTTP triggers
   - Minimize the overhead of multiple function instances
   - Optimize resource utilization

## Endpoints Consolidated

The following endpoints have been consolidated:

1. **Task Processors**:
   - `task_processor_high` → `task_processor`
   - `task_processor_medium` → `task_processor`
   - `task_processor_low` → `task_processor`

2. **Key Vault Endpoints** (moved to API router):
   - `add_access_policy_endpoint`
   - `create_key_vault_endpoint`
   - `store_client_credentials_endpoint`
   - `store_secret_endpoint`

3. **Organization Endpoints** (moved to API router):
   - `connect_org`
   - `disconnect_org`
   - `rescan_org`

## Next Steps for Further Consolidation

To further reduce the number of functions, consider:

1. **Moving More Endpoints to FastAPI**:
   - Account management endpoints
   - User management endpoints
   - Role management endpoints

2. **Consolidating More HTTP Triggers**:
   - Group related endpoints by domain
   - Use a single HTTP trigger per domain with path-based routing

3. **Optimizing Background Processing**:
   - Consider using Durable Functions for complex workflows
   - Implement fan-out/fan-in patterns for parallel processing

## Conclusion

The implemented changes have successfully reduced the number of Azure Functions while maintaining the application's performance. The new architecture provides a more maintainable and scalable solution with centralized routing and optimized resource utilization.
