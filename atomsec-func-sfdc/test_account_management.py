"""
DEPRECATED: Test Account Management

This script is DEPRECATED and contains direct database operations that should be handled
by the atomsec-func-db-r service. All account management testing should use the
dedicated database service.

DEPRECATED functionality:
1. Initializing the account tables → Use DB service endpoints
2. Creating an account → Use DB service endpoints
3. Retrieving accounts → Use DB service endpoints
"""

import os
import sys
import json
import logging
import requests
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_account_management')

# Base URL for API calls
BASE_URL = "http://localhost:7071/api"

def initialize_tables():
    """Initialize account tables"""
    logger.info("Initializing account tables...")
    
    # Create tables directly using the data_access module
    from shared.data_access import get_table_storage_repository
    
    # Initialize tables
    tables = ['account', 'UserAccount', 'roles', 'userroles']
    results = {}
    
    for table_name in tables:
        try:
            # Get table repository (this will create the table if it doesn't exist)
            repo = get_table_storage_repository(table_name)
            if repo:
                # Try to query the table to verify it exists
                entities = repo.query_entities()
                logger.info(f"Table '{table_name}' initialized successfully with {len(entities)} entities")
                results[table_name] = {
                    'success': True,
                    'count': len(entities),
                    'message': f"Table '{table_name}' initialized successfully"
                }
            else:
                logger.error(f"Failed to initialize table '{table_name}'")
                results[table_name] = {
                    'success': False,
                    'message': f"Failed to initialize table '{table_name}'"
                }
        except Exception as e:
            logger.error(f"Error initializing table '{table_name}': {str(e)}")
            results[table_name] = {
                'success': False,
                'message': f"Error initializing table '{table_name}': {str(e)}"
            }
    
    # Create default roles if they don't exist
    try:
        role_repo = get_table_storage_repository('roles')
        if role_repo:
            # Define standard roles
            standard_roles = [
                {
                    "RoleId": "1",
                    "Rolename": "Admin",
                    "Description": "Administrator role with full access"
                },
                {
                    "RoleId": "2",
                    "Rolename": "Account Admin",
                    "Description": "Can manage users and integrations within their account"
                },
                {
                    "RoleId": "3",
                    "Rolename": "Integration Manager",
                    "Description": "Can add and manage integrations"
                },
                {
                    "RoleId": "4",
                    "Rolename": "Viewer",
                    "Description": "Read-only access to integrations and their data"
                }
            ]
            
            # Create roles
            for role in standard_roles:
                role_entity = {
                    "PartitionKey": "role",
                    "RowKey": role["RoleId"],
                    "Rolename": role["Rolename"],
                    "Description": role["Description"]
                }
                role_repo.insert_entity(role_entity)
                logger.info(f"Created role: {role['Rolename']}")
    except Exception as e:
        logger.error(f"Error creating roles: {str(e)}")
    
    return results

def create_account_direct():
    """Create an account directly using the data_access module"""
    logger.info("Creating account directly...")
    
    from shared.data_access import get_table_storage_repository
    import random
    from datetime import datetime
    
    # Get account repository
    account_repo = get_table_storage_repository('account')
    if not account_repo:
        logger.error("Account table repository not available")
        return None
    
    # Generate account ID
    account_id = str(random.randint(1000, 9999))
    
    # Create entity
    created_at = datetime.utcnow()
    entity = {
        "PartitionKey": "account",
        "RowKey": account_id,
        "Name": f"Test Account {account_id}",
        "CreatedAt": created_at.isoformat(),
        "IsActive": True
    }
    
    # Insert entity
    logger.info(f"Inserting account entity: {entity}")
    success = account_repo.insert_entity(entity)
    
    if success:
        logger.info(f"Account created successfully with ID {account_id}")
        return {
            "ID": account_id,
            "Name": entity["Name"],
            "CreatedAt": entity["CreatedAt"],
            "IsActive": entity["IsActive"]
        }
    else:
        logger.error("Failed to create account")
        return None

def get_accounts_direct():
    """Get accounts directly using the data_access module"""
    logger.info("Getting accounts directly...")
    
    from shared.data_access import get_table_storage_repository
    
    # Get account repository
    account_repo = get_table_storage_repository('account')
    if not account_repo:
        logger.error("Account table repository not available")
        return []
    
    # Query accounts
    filter_query = "IsActive eq true"
    logger.info(f"Querying accounts with filter: {filter_query}")
    entities = account_repo.query_entities(filter_query)
    logger.info(f"Found {len(entities)} account entities")
    
    # Log the raw entities for debugging
    for entity in entities:
        logger.info(f"Account entity: {entity}")
    
    # Convert entities to accounts
    accounts = []
    for entity in entities:
        account = {
            "ID": entity.get("RowKey"),
            "Name": entity.get("Name", ""),
            "CreatedAt": entity.get("CreatedAt", ""),
            "IsActive": entity.get("IsActive", True)
        }
        accounts.append(account)
    
    logger.info(f"Converted {len(accounts)} account entities to accounts")
    return accounts

def main():
    """Main function"""
    logger.info("Starting account management test...")
    
    # Initialize tables
    initialize_results = initialize_tables()
    logger.info(f"Initialize results: {initialize_results}")
    
    # Create account
    account = create_account_direct()
    logger.info(f"Created account: {account}")
    
    # Get accounts
    accounts = get_accounts_direct()
    logger.info(f"Retrieved accounts: {accounts}")
    
    logger.info("Account management test completed")

if __name__ == "__main__":
    main()
