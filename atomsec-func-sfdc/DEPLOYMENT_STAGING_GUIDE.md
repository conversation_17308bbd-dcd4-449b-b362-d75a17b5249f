# SFDC Function App Staging Slot Deployment Guide

## Overview
This guide explains the new staging slot deployment process for the SFDC Function App, which provides zero-downtime deployments using Azure App Service staging slots and swap operations.

## Architecture Changes

### Before (Direct Production Deployment)
- **Risk**: Potential downtime during deployment
- **Validation**: Limited pre-production testing
- **Rollback**: Complex rollback procedures

### After (Staging Slot Deployment)
- **Zero Downtime**: Production remains unaffected during staging deployment
- **Pre-Production Validation**: Full testing in staging environment
- **Instant Rollback**: Quick swap back if issues arise
- **Blue-Green Deployment**: Production and staging slots provide blue-green deployment capability

## New Pipeline Features

### 1. Staging Slot Management
- **Automatic Creation**: Pipeline creates staging slot if it doesn't exist
- **Configuration Sync**: Staging slot inherits production configuration
- **Isolated Environment**: Staging runs independently from production

### 2. Deployment Flow
1. **Build & Package**: Same as before
2. **Staging Slot Creation**: Creates/verifies staging slot
3. **Staging Deployment**: Deploys to staging slot
4. **Staging Validation**: Comprehensive testing in staging
5. **Slot Swap**: Swaps staging with production
6. **Production Verification**: Final validation in production
7. **Cleanup**: Stops staging slot to save costs

### 3. Validation Steps
- **Health Checks**: Validates staging slot health before swap
- **Endpoint Testing**: Tests all critical endpoints
- **Function Discovery**: Verifies all functions are properly deployed
- **Production Verification**: Post-swap production validation

## Key Pipeline Changes

### New Variables
```yaml
variables:
  functionAppName: 'func-atomsec-sfdc-dev02'
  resourceGroupName: 'atomsec-dev-backend'
  stagingSlotName: 'stage'
```

### New Steps Added
1. **Create/Verify Staging Slot**: Ensures staging slot exists
2. **Deploy to Staging**: Deploys to staging instead of production
3. **Configure Staging**: Sets up staging slot configuration
4. **Validate Staging**: Comprehensive staging validation
5. **Swap Slots**: Performs the staging-to-production swap
6. **Verify Production**: Post-deployment verification
7. **Cleanup Staging**: Stops staging slot to save costs

## Usage Instructions

### 1. First Time Setup
The staging slot will be automatically created on the first pipeline run.

### 2. Regular Deployments
- Pipeline runs automatically on `dev`, `dev-db-working-pipeline`, and `dev-db` branches
- No manual intervention required for standard deployments

### 3. Manual Validation
To manually validate the staging slot:
```bash
# Check staging slot status
python scripts/manage_slots.py func-atomsec-sfdc-dev02 atomsec-dev-backend stage status

# Test staging slot health
python scripts/manage_slots.py func-atomsec-sfdc-dev02 atomsec-dev-backend stage health

# Verify staging functions
python scripts/verify_azure_functions.py https://func-atomsec-sfdc-dev02-stage.azurewebsites.net
```

### 4. Rollback Procedures
If issues are discovered after swap:

#### Option 1: Immediate Rollback
```bash
# Swap back production to staging (reverts to previous version)
az webapp deployment slot swap --name func-atomsec-sfdc-dev02 --resource-group atomsec-dev-backend --slot stage --target-slot production
```

#### Option 2: Fix and Redeploy
- Fix issues in code
- Push changes to trigger new pipeline run
- New deployment goes through staging validation again

## Monitoring and Troubleshooting

### Pipeline Logs
- **Staging Creation**: Look for "Creating/Verifying Staging Slot" step
- **Staging Validation**: Check "Validate Staging Slot Health" step
- **Swap Operation**: Monitor "Swap Staging to Production" step
- **Production Verification**: Review "Verify Production After Swap" step

### Common Issues and Solutions

#### Issue 1: Staging Slot Creation Fails
**Symptoms**: Pipeline fails at "Create/Verify Staging Slot" step
**Solution**: 
```bash
# Manually create staging slot
az webapp deployment slot create --name func-atomsec-sfdc-dev02 --resource-group atomsec-dev-backend --slot stage
```

#### Issue 2: Staging Validation Fails
**Symptoms**: Pipeline fails at "Validate Staging Slot Health" step
**Actions**:
1. Check staging slot logs in Azure Portal
2. Verify staging slot URL: `https://func-atomsec-sfdc-dev02-stage.azurewebsites.net`
3. Review application logs for errors

#### Issue 3: Swap Operation Fails
**Symptoms**: Pipeline fails at "Swap Staging to Production" step
**Solution**:
```bash
# Check slot status
az webapp deployment slot list --name func-atomsec-sfdc-dev02 --resource-group atomsec-dev-backend

# Reset slots if needed
python scripts/force_reset_slot.py func-atomsec-sfdc-dev02 atomsec-dev-backend stage
python scripts/force_reset_slot.py func-atomsec-sfdc-dev02 atomsec-dev-backend
```

## Cost Optimization
- **Staging Slot**: Automatically stopped after successful deployment
- **Runtime**: Only runs during deployment process
- **Storage**: Uses same App Service Plan as production

## Security Considerations
- **Isolated Environment**: Staging slot runs in separate environment
- **Same Security**: Inherits same security configuration as production
- **Network Access**: Staging slot has same network restrictions as production

## Migration Plan

### Phase 1: Testing (Recommended)
1. Test new pipeline in development environment
2. Validate all endpoints work correctly
3. Test rollback procedures

### Phase 2: Production Rollout
1. Replace existing pipeline with staging slot version
2. Monitor first few deployments closely
3. Update team documentation and procedures

## File Structure
- **Original**: `pipeline-func-sfdc-dev.yml` (direct production deployment)
- **New**: `pipeline-func-sfdc-dev-staging.yml` (staging slot deployment)
- **Guide**: `DEPLOYMENT_STAGING_GUIDE.md` (this document)

## Next Steps
1. Review the new pipeline file: `pipeline-func-sfdc-dev-staging.yml`
2. Test in development environment
3. Update Azure DevOps pipeline to use new file
4. Update team documentation
5. Schedule migration from old to new pipeline