# PMD Migration and Environment Variables Summary

## Overview

The AtomSec SFDC Function App has been successfully migrated to use a centralized database service for all database operations, including PMD (Programming Mistake Detector) functionality. This migration improves scalability, security, and maintainability by centralizing database operations.

## Key Changes Made

### 1. Database Service Integration

**Before**: Direct database access from SFDC function app
**After**: All database operations routed through centralized DB service

**Benefits**:
- Centralized database management
- Improved security through single point of access
- Better scalability and performance
- Simplified maintenance and updates

### 2. PMD Functionality Migration

**PMD Scan Logic**: Remains in SFDC function app
**Database Operations**: Moved to DB service

**Components Migrated**:
- PMD configuration retrieval
- PMD findings storage
- PMD issues display
- PMD subtasks and rules management

### 3. New Architecture

```
┌─────────────────┐    HTTP API    ┌─────────────────┐
│   SFDC Function │ ──────────────► │   DB Service    │
│      App        │                │                 │
│                 │                │                 │
│ • PMD Scanner   │                │ • PMD Config    │
│ • PMD Blob      │                │ • PMD Findings  │
│ • PMD Results   │                │ • PMD Issues    │
└─────────────────┘                └─────────────────┘
```

## Environment Variables Required

### Core Configuration

#### Azure AD Configuration
```bash
AZURE_AD_CLIENT_ID=<your_azure_ad_client_id>
AZURE_AD_CLIENT_SECRET=<your_azure_ad_client_secret>
AZURE_AD_TENANT_ID=<your_azure_ad_tenant_id>
AZURE_AD_REDIRECT_URI=https://your-frontend-app.azurewebsites.net
```

#### Key Vault Configuration
```bash
KEY_VAULT_URL=https://your-key-vault.vault.azure.net/
```

#### Storage Configuration
```bash
# Local Development
AzureStorageConnectionString=DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;

# Production
AZURE_STORAGE_CONNECTION_STRING=<your_storage_connection_string>
```

#### SQL Database Configuration
```bash
SQL_CONNECTION_STRING=<your_sql_connection_string>
```

#### Frontend URL Configuration
```bash
FRONTEND_URL=https://your-frontend-app.azurewebsites.net
```

### Database Service Configuration

#### DB Service URL
```bash
# Local Development
DB_SERVICE_URL=http://localhost:7072/api/db

# Production
DB_SERVICE_URL=https://atomsec-func-db-r.azurewebsites.net/api/db
```

#### DB Service Connection Settings
```bash
DB_SERVICE_TIMEOUT=30
DB_SERVICE_RETRY_ATTEMPTS=3
DB_SERVICE_RETRY_DELAY=1
```

#### DB Service Authentication
```bash
DB_SERVICE_API_KEY=<your_db_service_api_key>
```

#### DB Service Customization
```bash
DB_SERVICE_USER_AGENT=atomsec-func-sfdc/1.0.0
```

### PMD Configuration

#### PMD General Settings
```bash
PMD_ENABLED=true
```

#### PMD Scan Configuration
```bash
PMD_DEFAULT_CATEGORIES=security,performance,codestyle,design
```

#### PMD Performance Settings
```bash
PMD_MAX_FINDINGS=10000
PMD_SCAN_TIMEOUT=300
PMD_TEMP_DIR=/tmp
```

## Local Development Setup

### 1. Environment File

Create a `.env` file in the `atomsec-func-sfdc` directory:

```bash
# Azure AD (for local development)
AZURE_AD_CLIENT_ID=your_client_id
AZURE_AD_CLIENT_SECRET=your_client_secret
AZURE_AD_TENANT_ID=your_tenant_id
AZURE_AD_REDIRECT_URI=http://localhost:3000

# Storage (Azurite for local development)
AzureStorageConnectionString=DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;

# JWT (for local development only)
JWT_SECRET=dev_secret_key_do_not_use_in_production

# DB Service (local development)
DB_SERVICE_URL=http://localhost:7072/api/db
DB_SERVICE_TIMEOUT=30
DB_SERVICE_RETRY_ATTEMPTS=3
DB_SERVICE_RETRY_DELAY=1

# PMD (local development)
PMD_ENABLED=true
PMD_DEFAULT_CATEGORIES=security,performance
PMD_MAX_FINDINGS=10000
PMD_SCAN_TIMEOUT=300
PMD_TEMP_DIR=/tmp
```

### 2. Local Settings File

Create `local.settings.json` in the `atomsec-func-sfdc` directory:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "python",
    "FUNCTIONS_EXTENSION_VERSION": "~4",
    
    "AZURE_AD_CLIENT_ID": "your_azure_ad_client_id",
    "AZURE_AD_CLIENT_SECRET": "your_azure_ad_client_secret", 
    "AZURE_AD_TENANT_ID": "your_azure_ad_tenant_id",
    "AZURE_AD_REDIRECT_URI": "http://localhost:3000",
    
    "KEY_VAULT_URL": "https://akv-atomsec-dev.vault.azure.net/",
    
    "JWT_SECRET": "dev_secret_key_do_not_use_in_production",
    
    "AzureStorageConnectionString": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;",
    
    "DB_SERVICE_URL": "http://localhost:7072/api/db",
    "DB_SERVICE_TIMEOUT": "30",
    "DB_SERVICE_RETRY_ATTEMPTS": "3", 
    "DB_SERVICE_RETRY_DELAY": "1",
    
    "PMD_ENABLED": "true",
    "PMD_DEFAULT_CATEGORIES": "security,performance",
    "PMD_MAX_FINDINGS": "10000",
    "PMD_SCAN_TIMEOUT": "300",
    "PMD_TEMP_DIR": "/tmp"
  },
  "Host": {
    "LocalHttpPort": 7071,
    "CORS": "*",
    "CORSCredentials": false
  }
}
```

## Azure Production Deployment

### 1. Application Settings

Configure the following application settings in your Azure Function App:

```bash
# Core Configuration
AZURE_AD_CLIENT_ID=<your_azure_ad_client_id>
AZURE_AD_CLIENT_SECRET=<your_azure_ad_client_secret>
AZURE_AD_TENANT_ID=<your_azure_ad_tenant_id>
AZURE_AD_REDIRECT_URI=https://your-frontend-app.azurewebsites.net
KEY_VAULT_URL=https://your-key-vault.vault.azure.net/
FRONTEND_URL=https://your-frontend-app.azurewebsites.net
base_url=https://your-api-gateway.azure-api.net

# DB Service Configuration
DB_SERVICE_URL=https://atomsec-func-db-r.azurewebsites.net/api/db
DB_SERVICE_TIMEOUT=30
DB_SERVICE_RETRY_ATTEMPTS=3
DB_SERVICE_RETRY_DELAY=1
DB_SERVICE_API_KEY=<your_db_service_api_key>
DB_SERVICE_USER_AGENT=atomsec-func-sfdc/1.0.0-prod

# PMD Configuration
PMD_ENABLED=true
PMD_DEFAULT_CATEGORIES=security,performance,codestyle,design
PMD_MAX_FINDINGS=10000
PMD_SCAN_TIMEOUT=300
PMD_TEMP_DIR=/tmp
```

### 2. Key Vault Secrets

Store the following secrets in Azure Key Vault:

```bash
# Core Secrets
jwt-secret=<your_jwt_secret>
storage-connection-string=<your_storage_connection_string>
sql-connection-string=<your_sql_connection_string>
frontend-url=https://your-frontend-app.azurewebsites.net

# DB Service Secrets
db-service-url=https://atomsec-func-db-r.azurewebsites.net/api/db
db-service-api-key=<your_db_service_api_key>
```

### 3. Managed Identity

Enable managed identity for Key Vault access:

```bash
# Enable managed identity
az functionapp identity assign --name your-function-app-name --resource-group your-resource-group

# Grant Key Vault access
az keyvault set-policy --name your-key-vault-name --object-id <managed-identity-object-id> --secret-permissions get list
```

## PMD Functionality Details

### PMD Components

1. **PMD Scanner** (`pmd_components/pmd_scanner.py`)
   - Handles PMD tool execution
   - Manages PMD rules and categories
   - Processes scan results

2. **PMD Blob Handler** (`pmd_components/pmd_blob_handler.py`)
   - Manages blob storage operations
   - Downloads Apex classes for scanning
   - Uploads scan results

3. **PMD Results Processor** (`pmd_components/pmd_results_processor.py`)
   - Processes PMD findings
   - Stores results via DB service
   - Generates findings summaries

4. **PMD Task** (`task_processor/tasks/pmd_task.py`)
   - Orchestrates PMD scan process
   - Manages task status and progress
   - Handles error scenarios

### PMD Database Operations

All PMD database operations are now handled by the DB service:

1. **Configuration Retrieval**
   - `get_enabled_pmd_subtasks(integration_id)`
   - `get_enabled_pmd_rules(subtask_id)`
   - `get_pmd_configuration(integration_id)`

2. **Findings Storage**
   - `store_pmd_findings(findings)`

3. **Findings Retrieval**
   - `get_pmd_findings(integration_id, limit, offset, severity, category)`

### PMD API Endpoints

The DB service provides the following PMD endpoints:

- `GET /api/pmd/subtasks/{integration_id}` - Get enabled PMD subtasks
- `GET /api/pmd/rules/{subtask_id}` - Get enabled PMD rules
- `GET /api/pmd/configuration/{integration_id}` - Get complete PMD configuration
- `POST /api/pmd/findings` - Store PMD findings
- `GET /api/pmd/findings/{integration_id}` - Get PMD findings

## Testing and Validation

### 1. Health Check

Test the overall system health:

```bash
curl -X GET "https://atomsec-func-sfdc.azurewebsites.net/api/health"
```

### 2. DB Service Connectivity

Test DB service connectivity:

```bash
curl -X GET "https://atomsec-func-db-r.azurewebsites.net/api/health"
```

### 3. PMD Configuration

Test PMD configuration retrieval:

```bash
curl -X GET "https://atomsec-func-db-r.azurewebsites.net/api/pmd/configuration/{integration_id}"
```

### 4. PMD Scan

Test PMD scan functionality:

```bash
curl -X POST "https://atomsec-func-sfdc.azurewebsites.net/api/integrations/{integration_id}/scan"
```

## Troubleshooting

### Common Issues

1. **DB Service Connection Failures**
   - Verify `DB_SERVICE_URL` is correct
   - Check network connectivity
   - Verify API key authentication

2. **PMD Scan Failures**
   - Check if `PMD_ENABLED` is set to "true"
   - Verify `PMD_TEMP_DIR` is writable
   - Increase `PMD_SCAN_TIMEOUT` for large codebases

3. **Key Vault Access Issues**
   - Verify managed identity is enabled
   - Check Key Vault access policies
   - Ensure secrets exist in Key Vault

### Log Analysis

Check application logs for:

1. **Configuration Loading**: Look for configuration validation messages
2. **DB Service Calls**: Monitor HTTP request/response logs
3. **PMD Operations**: Check PMD scan and processing logs
4. **Error Handling**: Review error messages and stack traces

## Migration Checklist

### Pre-Migration
- [ ] DB service deployed and accessible
- [ ] Environment variables configured
- [ ] Key Vault secrets stored
- [ ] Network connectivity verified

### Migration
- [ ] Update SFDC function app code
- [ ] Configure environment variables
- [ ] Test database operations
- [ ] Verify PMD functionality

### Post-Migration
- [ ] Monitor application logs
- [ ] Test all PMD operations
- [ ] Verify data consistency
- [ ] Update documentation

## Benefits of Migration

1. **Scalability**: Centralized database operations improve performance
2. **Security**: Single point of access for database operations
3. **Maintainability**: Database schema changes only need to be made in one place
4. **Reliability**: Better error handling and retry logic
5. **Monitoring**: Centralized logging and monitoring capabilities

## Future Enhancements

1. **Caching**: Implement caching for frequently accessed data
2. **Rate Limiting**: Add rate limiting for API calls
3. **Metrics**: Enhanced metrics and monitoring
4. **Performance**: Optimize database queries and API responses 