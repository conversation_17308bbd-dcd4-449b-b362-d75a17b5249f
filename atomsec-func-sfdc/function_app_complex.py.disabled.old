"""
AtomSec SFDC Function App

This is a dedicated Azure Function App for handling Salesforce operations.
It provides FastAPI integration and Service Bus event processing.

Features:
- FastAPI with ASGI for complex API scenarios
- Service Bus event consumer for async processing
- Salesforce integration and security scanning
- Background task processing
- Event-driven architecture
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
import uvicorn
from azure.functions import AsgiMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="AtomSec SFDC Service",
    description="Salesforce integration and security scanning service",
    version="1.0.0"
)

# Import blueprints
from blueprints.user_profile import bp as profile_bp
from blueprints.security_analysis import bp as security_bp
from blueprints.integration import bp as integration_bp
from blueprints.general import bp as health_bp

# Register blueprints
app.include_router(profile_bp, prefix="/api/profiles", tags=["profiles"])
app.include_router(security_bp, prefix="/api/security", tags=["security"])
app.include_router(integration_bp, prefix="/api/integration", tags=["integration"])
app.include_router(health_bp, prefix="/api/health", tags=["health"])

# Global error handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for FastAPI"""
    logger.error(f"Global exception handler: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "details": str(exc) if app.debug else "An unexpected error occurred"
        }
    )

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        health_status = {
            "status": "healthy",
            "service": "atomsec-func-sfdc",
            "version": "1.0.0",
            "checks": {}
        }
        
        # Test Service Bus connectivity
        try:
            from shared.service_bus_processor import get_service_bus_processor
            processor = get_service_bus_processor()
            if processor:
                health_status["checks"]["service_bus"] = "connected"
            else:
                health_status["checks"]["service_bus"] = "failed"
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["checks"]["service_bus"] = f"error: {str(e)}"
            health_status["status"] = "degraded"
        
        # Test Salesforce connectivity
        try:
            from shared.salesforce_client import get_salesforce_client
            sf_client = get_salesforce_client()
            if sf_client:
                health_status["checks"]["salesforce"] = "connected"
            else:
                health_status["checks"]["salesforce"] = "failed"
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["checks"]["salesforce"] = f"error: {str(e)}"
            health_status["status"] = "degraded"
        
        # Test Key Vault connectivity
        try:
            from shared.azure_services import get_key_vault_client
            kv_client = get_key_vault_client()
            if kv_client:
                health_status["checks"]["key_vault"] = "connected"
            else:
                health_status["checks"]["key_vault"] = "failed"
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["checks"]["key_vault"] = f"error: {str(e)}"
            health_status["status"] = "degraded"
        
        status_code = 200 if health_status["status"] == "healthy" else 503
        return JSONResponse(content=health_status, status_code=status_code)
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            content={
                "status": "unhealthy",
                "error": str(e)
            },
            status_code=503
        )

# Info endpoint
@app.get("/info")
async def info():
    """Information endpoint"""
    info_data = {
        "service": "atomsec-func-sfdc",
        "version": "1.0.0",
        "description": "AtomSec SFDC Service - Salesforce integration and security scanning",
        "endpoints": {
            "profiles": {
                "base": "/api/profiles",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/list", "/details", "/permissions", "/assignment-counts"]
            },
            "security": {
                "base": "/api/security",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/scan", "/health-check", "/risk-assessment", "/compliance"]
            },
            "integration": {
                "base": "/api/integration",
                "operations": ["GET", "POST", "PUT"],
                "sub_endpoints": ["/connect", "/scan", "/health-check", "/sync"]
            },
            "health": {
                "base": "/api/health",
                "operations": ["GET"],
                "sub_endpoints": ["/check", "/metrics", "/status"]
            }
        },
        "features": [
            "FastAPI with ASGI integration",
            "Service Bus event processing",
            "Salesforce API integration",
            "Security scanning and analysis",
            "Background task processing",
            "Event-driven architecture",
            "Key Vault integration",
            "Comprehensive logging"
        ]
    }
    
    return JSONResponse(content=info_data, status_code=200)

# Create Azure Function App
func_app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

# Register FastAPI app as Azure Function
@func_app.route(route="{*route}")
async def fastapi_handler(req: func.HttpRequest, route: str) -> func.HttpResponse:
    """Handle all routes through FastAPI"""
    try:
        # Convert Azure Function request to FastAPI request
        asgi_handler = AsgiMiddleware(app)
        return await asgi_handler(req, route)
    except Exception as e:
        logger.error(f"FastAPI handler error: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Request processing failed: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

# Service Bus event processor
@func_app.service_bus_topic_trigger(
    arg_name="message",
    topic_name="atomsec-events",
    subscription_name="sfdc-service",
    connection="AzureServiceBusConnectionString"
)
def process_service_bus_event(message: func.ServiceBusMessage) -> None:
    """
    Process Service Bus events for SFDC service
    
    Args:
        message: Service Bus message
    """
    try:
        # Parse message body
        message_body = message.get_body().decode('utf-8')
        event_data = json.loads(message_body)
        
        logger.info(f"Processing Service Bus event: {event_data.get('eventType', 'unknown')}")
        
        # Route event to appropriate handler
        event_type = event_data.get('eventType')
        
        if event_type == 'TaskCreated':
            from shared.task_processor import process_task_created_event
            process_task_created_event(event_data)
        elif event_type == 'IntegrationCreated':
            from shared.integration_processor import process_integration_created_event
            process_integration_created_event(event_data)
        elif event_type == 'IntegrationUpdated':
            from shared.integration_processor import process_integration_updated_event
            process_integration_updated_event(event_data)
        elif event_type == 'SecurityScanRequested':
            from shared.security_processor import process_security_scan_event
            process_security_scan_event(event_data)
        elif event_type == 'UserProfileUpdated':
            from shared.user_processor import process_user_profile_updated_event
            process_user_profile_updated_event(event_data)
        else:
            logger.warning(f"Unknown event type: {event_type}")
        
        # Mark message as completed
        message.complete()
        
    except Exception as e:
        logger.error(f"Error processing Service Bus event: {str(e)}")
        # Dead letter the message for retry
        message.dead_letter(
            reason="Processing failed",
            error_description=str(e)
        )

# Timer trigger for background tasks
@func_app.schedule(schedule="0 */5 * * * *", arg_name="timer", run_on_startup=True)
def background_task_processor(timer: func.TimerRequest) -> None:
    """
    Process background tasks every 5 minutes
    
    Args:
        timer: Timer trigger
    """
    try:
        logger.info("Starting background task processing")
        
        # Process pending tasks
        from shared.task_management import process_pending_tasks
        process_pending_tasks()
        
        # Process scheduled scans
        from shared.scan_scheduler import process_scheduled_scans
        process_scheduled_scans()
        
        # Clean up old data
        from shared.data_cleanup import cleanup_old_data
        cleanup_old_data()
        
        logger.info("Background task processing completed")
        
    except Exception as e:
        logger.error(f"Error in background task processing: {str(e)}")

# HTTP trigger for manual task processing
@func_app.route(route="process-tasks", methods=["POST"])
def manual_task_processing(req: func.HttpRequest) -> func.HttpResponse:
    """
    Manually trigger task processing
    
    Returns:
        JSON response with processing status
    """
    try:
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON in request body"}),
                mimetype="application/json",
                status_code=400
            )
        
        task_type = request_data.get("task_type", "all")
        force_processing = request_data.get("force", False)
        
        # Process tasks
        from shared.task_management import process_pending_tasks
        result = process_pending_tasks(task_type=task_type, force=force_processing)
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": result,
                "message": "Task processing completed"
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error in manual task processing: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Task processing failed: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

# HTTP trigger for manual scan processing
@func_app.route(route="process-scans", methods=["POST"])
def manual_scan_processing(req: func.HttpRequest) -> func.HttpResponse:
    """
    Manually trigger scan processing
    
    Returns:
        JSON response with processing status
    """
    try:
        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON in request body"}),
                mimetype="application/json",
                status_code=400
            )
        
        scan_type = request_data.get("scan_type", "all")
        integration_id = request_data.get("integration_id")
        
        # Process scans
        from shared.scan_scheduler import process_scheduled_scans
        result = process_scheduled_scans(scan_type=scan_type, integration_id=integration_id)
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": result,
                "message": "Scan processing completed"
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error in manual scan processing: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Scan processing failed: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

def handle_error(e: Exception, operation: str) -> func.HttpResponse:
    """
    Standard error handler for SFDC service operations
    
    Args:
        e: Exception that occurred
        operation: Description of the operation that failed
        
    Returns:
        func.HttpResponse: Error response
    """
    logger.error(f"Error in {operation}: {str(e)}")
    return func.HttpResponse(
        json.dumps({
            "success": False,
            "error": f"SFDC service operation failed: {str(e)}"
        }),
        mimetype="application/json",
        status_code=500
    )

