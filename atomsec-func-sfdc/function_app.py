"""
AtomSec SFDC Function App

This is a dedicated Azure Function App for handling Salesforce integration and security scanning.
It provides SFDC-specific functionality including integration management, security analysis, and PMD scanning.

Features:
- Salesforce integration management
- Security health checks and analysis
- PMD (Programming Mistake Detector) scanning
- Task management for SFDC operations
- User profile management
- Key Vault integration for credentials
- Event publishing to Service Bus for async operations
- RESTful API endpoints for SFDC operations
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any
from datetime import datetime
import sys
import traceback
import os

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.StreamHandler(sys.stderr)
    ]
)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Log Azure Functions environment information
logger.info("=== Azure Functions Environment Info ===")
logger.info(f"Python version: {sys.version}")
logger.info(f"Azure Functions version: {os.environ.get('FUNCTIONS_WORKER_RUNTIME_VERSION', 'Unknown')}")
logger.info(f"Function directory: {os.environ.get('AzureWebJobsScriptRoot', 'Unknown')}")
logger.info(f"Current working directory: {os.getcwd()}")
logger.info(f"Python path: {sys.path}")
logger.info("========================================")

# Import API endpoints with error handling
logger.info("Starting API endpoint imports...")

try:
    from api.user_endpoints import bp as user_bp
    logger.info("✓ user_endpoints imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import user_endpoints: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    user_bp = None

try:
    from api.account_endpoints import bp as account_bp
    logger.info("✓ account_endpoints imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import account_endpoints: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    account_bp = None

try:
    from api.organization_endpoints import bp as organization_bp
    logger.info("✓ organization_endpoints imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import organization_endpoints: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    organization_bp = None

try:
    from api.integration_endpoints import bp as integration_bp
    logger.info("✓ integration_endpoints imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import integration_endpoints: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    integration_bp = None

try:
    from api.security_endpoints import bp as security_bp
    logger.info("✓ security_endpoints imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import security_endpoints: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    security_bp = None

try:
    from api.task_endpoints import bp as task_bp
    logger.info("✓ task_endpoints imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import task_endpoints: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    task_bp = None

try:
    from api.auth_endpoints import bp as auth_bp
    logger.info("✓ auth_endpoints imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import auth_endpoints: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    auth_bp = None

try:
    from api.policy_endpoints import bp as policy_bp
    logger.info("✓ policy_endpoints imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import policy_endpoints: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    policy_bp = None

try:
    from api.cors_handler import bp as cors_bp
    logger.info("✓ cors_handler imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import cors_handler: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    cors_bp = None

try:
    from api.user_profile_endpoints import bp as user_profile_bp
    logger.info("✓ user_profile_endpoints imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import user_profile_endpoints: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    user_profile_bp = None

try:
    from api.key_vault_endpoints import bp as key_vault_bp
    logger.info("✓ key_vault_endpoints imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import key_vault_endpoints: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    key_vault_bp = None

try:
    from api.pmd_endpoints import bp as pmd_bp
    logger.info("✓ pmd_endpoints imported successfully")
except Exception as e:
    logger.error(f"✗ Failed to import pmd_endpoints: {str(e)}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    pmd_bp = None

logger.info("API endpoint imports completed")
# Conditional import for service bus processor
try:
    from service_bus_processor import bp as service_bus_bp
    SERVICE_BUS_AVAILABLE = True
except Exception as e:
    logger.warning(f"Service bus processor not available: {str(e)}")
    SERVICE_BUS_AVAILABLE = False
    service_bus_bp = None
# from task_management import bp as task_management_bp  # Task status should be in DB service only

# Create the Function App
app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

# Test function to verify V2 function discovery works
@app.route(route="test", methods=["GET"])
def test_function(req: func.HttpRequest) -> func.HttpResponse:
    """Test function to verify V2 function discovery"""
    logger.info("Test function called - V2 function discovery is working")
    return func.HttpResponse(
        json.dumps({"message": "Test function works", "status": "success"}),
        mimetype="application/json"
    )

# Register all blueprints with error handling
blueprint_count = 0
blueprints_to_register = [
    ("user_bp", user_bp),
    ("account_bp", account_bp),
    ("organization_bp", organization_bp),
    ("integration_bp", integration_bp),
    ("security_bp", security_bp),
    ("task_bp", task_bp),
    ("auth_bp", auth_bp),
    ("policy_bp", policy_bp),
    ("cors_bp", cors_bp),
    ("user_profile_bp", user_profile_bp),
    ("key_vault_bp", key_vault_bp),
    ("pmd_bp", pmd_bp),
]

logger.info("Starting blueprint registration...")

for name, blueprint in blueprints_to_register:
    if blueprint is None:
        logger.warning(f"Skipping {name} - blueprint is None (import failed)")
        continue
        
    try:
        logger.info(f"Registering {name}...")
        app.register_functions(blueprint)
        blueprint_count += 1
        logger.info(f"Successfully registered {name}")
    except Exception as e:
        logger.error(f"Failed to register {name}: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Continue with other blueprints instead of failing completely

# Conditionally register service bus blueprint
if SERVICE_BUS_AVAILABLE and service_bus_bp:
    try:
        logger.info("Registering service_bus_bp...")
        app.register_functions(service_bus_bp)
        blueprint_count += 1
        logger.info("Successfully registered service_bus_bp")
    except Exception as e:
        logger.error(f"Failed to register service_bus_bp: {str(e)}")
else:
    logger.info("Service bus blueprint not available, skipping...")

logger.info(f"Successfully registered {blueprint_count} blueprints")

# Global exception handler for Azure Functions worker
def handle_global_exception(exc_type, exc_value, exc_traceback):
    """Global exception handler to catch any unhandled errors"""
    logger.error("=== UNHANDLED EXCEPTION ===")
    logger.error(f"Exception type: {exc_type}")
    logger.error(f"Exception value: {exc_value}")
    logger.error("Traceback:")
    for line in traceback.format_tb(exc_traceback):
        logger.error(line.strip())
    logger.error("==========================")

# Set up global exception handler
sys.excepthook = handle_global_exception

logger.info("Function app initialization completed successfully")

# Health check endpoint
@app.route(route="health", methods=["GET"])
def health_check(req: func.HttpRequest) -> func.HttpResponse:
    """
    Health check endpoint for the SFDC function app

    Returns:
        JSON response with health status
    """
    try:
        # Test database connectivity using new DB service client
        from shared.db_service_client import get_db_client
        from shared.azure_services import is_local_dev

        health_status = {
            "status": "healthy",
            "service": "atomsec-func-sfdc",
            "environment": "local" if is_local_dev() else "production",
            "checks": {}
        }

        # Test database connectivity
        try:
            db_client = get_db_client()
            if db_client:
                health_status["checks"]["database"] = "connected"
            else:
                health_status["checks"]["database"] = "failed"
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["checks"]["database"] = f"error: {str(e)}"
            health_status["status"] = "unhealthy"

        # Test Service Bus connectivity
        try:
            from shared.service_bus_client import get_service_bus_client
            sb_client = get_service_bus_client()
            if sb_client:
                health_status["checks"]["service_bus"] = "connected"
            else:
                health_status["checks"]["service_bus"] = "failed"
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["checks"]["service_bus"] = f"error: {str(e)}"
            health_status["status"] = "degraded"

        # Test Key Vault connectivity
        try:
            from shared.azure_services import get_key_vault_client
            kv_client = get_key_vault_client()
            if kv_client:
                health_status["checks"]["key_vault"] = "connected"
            else:
                health_status["checks"]["key_vault"] = "failed"
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["checks"]["key_vault"] = f"error: {str(e)}"
            health_status["status"] = "degraded"

        # Test SFDC service connectivity
        try:
            from shared.sfdc_service_client import get_sfdc_client
            sfdc_client = get_sfdc_client()
            if sfdc_client:
                health_status["checks"]["sfdc_service"] = "connected"
            else:
                health_status["checks"]["sfdc_service"] = "failed"
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["checks"]["sfdc_service"] = f"error: {str(e)}"
            health_status["status"] = "degraded"

        status_code = 200 if health_status["status"] == "healthy" else 503

        return func.HttpResponse(
            json.dumps(health_status),
            mimetype="application/json",
            status_code=status_code
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "status": "unhealthy",
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=503
        )

# Info endpoint
@app.route(route="info", methods=["GET"])
def info(req: func.HttpRequest) -> func.HttpResponse:
    """
    Information endpoint for the SFDC function app

    Returns:
        JSON response with service information
    """
    info_data = {
        "service": "atomsec-func-sfdc",
        "version": "1.0.0",
        "description": "AtomSec SFDC Service - Salesforce integration and security scanning",
        "endpoints": {
            "users": {
                "base": "/api/users",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "accounts": {
                "base": "/api/accounts",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "organizations": {
                "base": "/api/organizations",
                "operations": ["GET", "POST", "PUT", "DELETE"]
            },
            "integrations": {
                "base": "/api/integrations",
                "operations": ["GET", "POST", "PUT", "DELETE"],
                "sub_endpoints": ["/overview", "/health-check", "/profiles", "/credentials", "/pmd-issues"]
            },
            "security": {
                "base": "/api/security",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/health-checks", "/profiles", "/overview"]
            },
            "tasks": {
                "base": "/api/tasks",
                "operations": ["GET", "POST", "PUT"],
                "sub_endpoints": ["/status", "/results"]
            },
            "auth": {
                "base": "/api/auth",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/login", "/signup", "/token/refresh", "/azure/login", "/azure/callback", "/azure/me"]
            },
            "policies": {
                "base": "/api/policies",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/rules", "/policy-rule-settings", "/policy-rule-settings/enabled-tasks"]
            },
            "user_profile": {
                "base": "/api/user",
                "operations": ["GET", "PUT"],
                "sub_endpoints": ["/profile", "/password"]
            },
            "key_vault": {
                "base": "/api/key-vault",
                "operations": ["GET", "POST", "PUT", "DELETE"],
                "sub_endpoints": ["/secrets", "/credentials"]
            },
            "pmd": {
                "base": "/api/pmd",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/scan", "/findings", "/configuration"]
            },
            "sfdc_proxy": {
                "base": "/api/sfdc-proxy",
                "operations": ["GET", "POST"],
                "sub_endpoints": ["/scan", "/health", "/profiles"]
            }
        },
        "architecture": {
            "type": "microservices",
            "database": "SQL Server via DB Service Client",
            "storage": "Azure Blob Storage",
            "messaging": "Azure Service Bus",
            "secrets": "Azure Key Vault"
        }
    }

    return func.HttpResponse(
        json.dumps(info_data),
        mimetype="application/json",
        status_code=200
    )

def handle_error(e: Exception, operation: str) -> func.HttpResponse:
    """
    Standard error handler for database operations

    Args:
        e: Exception that occurred
        operation: Description of the operation that failed

    Returns:
        func.HttpResponse: Error response
    """
    logger.error(f"Error in {operation}: {str(e)}")
    return func.HttpResponse(
        json.dumps({
            "success": False,
            "error": f"SFDC service operation failed: {str(e)}"
        }),
        mimetype="application/json",
        status_code=500
    )

logger.info("AtomSec SFDC Function App initialized successfully")

# Timer-based task polling (DISABLED - replaced by queue triggers)
# @app.timer_trigger(schedule="0 */1 * * * *", arg_name="timer", run_on_startup=True, use_monitor=False)
# def task_poller(timer: func.TimerRequest) -> None:
#     """
#     DISABLED: This function has been replaced by queue triggers.
#     Tasks are now processed via Azure Storage Queue triggers instead of database polling.
#     """
#     logger.info("[POLL-DISABLED] Timer-based polling is disabled - using queue triggers instead")
#     return

# Queue-based task processing (NEW - replaces timer-based polling)
@app.queue_trigger(arg_name="msg", queue_name="task-queue-high", connection="AzureWebJobsStorage")
def process_high_priority_task_queue(msg: func.QueueMessage) -> None:
    """Process high priority tasks from queue"""
    process_queue_message(msg, "high")

@app.queue_trigger(arg_name="msg", queue_name="task-queue-medium", connection="AzureWebJobsStorage")
def process_medium_priority_task_queue(msg: func.QueueMessage) -> None:
    """Process medium priority tasks from queue"""
    process_queue_message(msg, "medium")

@app.queue_trigger(arg_name="msg", queue_name="task-queue-low", connection="AzureWebJobsStorage")
def process_low_priority_task_queue(msg: func.QueueMessage) -> None:
    """Process low priority tasks from queue"""
    process_queue_message(msg, "low")

def process_queue_message(msg: func.QueueMessage, priority: str) -> None:
    """
    Process a task message from the queue
    
    Args:
        msg: Queue message containing task data
        priority: Task priority level
    """
    try:
        # Decode message content - handle both base64 encoded and plain JSON
        message_content = msg.get_body().decode('utf-8')
        
        # Try to decode as base64 first (DB Function App format)
        try:
            import base64
            decoded_content = base64.b64decode(message_content).decode('utf-8')
            task_data = json.loads(decoded_content)
            logger.debug(f"[QUEUE] Successfully decoded base64 message for {priority} priority")
        except (base64.binascii.Error, UnicodeDecodeError, json.JSONDecodeError):
            # If base64 decode fails, try as plain JSON (direct format)
            try:
                task_data = json.loads(message_content)
                logger.debug(f"[QUEUE] Successfully parsed plain JSON message for {priority} priority")
            except json.JSONDecodeError as json_error:
                logger.error(f"[QUEUE] Failed to parse message content: {message_content[:100]}...")
                raise json_error
        
        task_id = task_data.get('task_id')
        task_type = task_data.get('task_type')
        org_id = task_data.get('org_id')
        user_id = task_data.get('user_id')
        params = task_data.get('params', {})
        execution_log_id = task_data.get('execution_log_id')
        
        logger.info(f"[QUEUE] Processing {priority} priority task: {task_id} ({task_type}) for org {org_id}")
        logger.info(f"[QUEUE] Execution log ID: {execution_log_id}")
        
        # Update task status to running
        from shared.background_processor import BackgroundProcessor, TASK_STATUS_RUNNING
        processor = BackgroundProcessor()
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=0,
            message=f"Task started from {priority} priority queue"
        )
        
        # Process the task using existing task processor
        from task_processor import main as process_task
        process_task(task_data)
        
        logger.info(f"[QUEUE] Successfully processed task {task_id} from {priority} priority queue")
        
    except Exception as e:
        logger.error(f"[QUEUE] Error processing {priority} priority task: {str(e)}")
        import traceback
        logger.error(f"[QUEUE] Full traceback: {traceback.format_exc()}")
        
        # Update task status to failed
        try:
            # Try to extract task_id from the original message
            message_content = msg.get_body().decode('utf-8')
            try:
                import base64
                decoded_content = base64.b64decode(message_content).decode('utf-8')
                task_data = json.loads(decoded_content)
            except:
                task_data = json.loads(message_content)
            
            task_id = task_data.get('task_id')
            from shared.background_processor import BackgroundProcessor, TASK_STATUS_FAILED
            processor = BackgroundProcessor()
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=100,
                message=f"Task failed during queue processing: {str(e)}"
            )
        except Exception as update_error:
            logger.error(f"[QUEUE] Failed to update task status: {str(update_error)}")

# Task processing endpoints for receiving tasks from DB service
@app.route(route="task/high", methods=["POST"])
def process_high_priority_task(req: func.HttpRequest) -> func.HttpResponse:
    """Process high priority task sent from DB service"""
    return process_task_from_db_service(req, "high")

@app.route(route="task/medium", methods=["POST"])
def process_medium_priority_task(req: func.HttpRequest) -> func.HttpResponse:
    """Process medium priority task sent from DB service"""
    return process_task_from_db_service(req, "medium")

@app.route(route="task/low", methods=["POST"])
def process_low_priority_task(req: func.HttpRequest) -> func.HttpResponse:
    """Process low priority task sent from DB service"""
    return process_task_from_db_service(req, "low")

def process_task_from_db_service(req: func.HttpRequest, priority: str) -> func.HttpResponse:
    """
    Process a task sent from the DB service

    Args:
        req: HTTP request containing task data
        priority: Task priority level

    Returns:
        HTTP response indicating success or failure
    """
    try:
        # Parse task data from request
        task_data = req.get_json()
        if not task_data:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "No task data provided"
                }),
                mimetype="application/json",
                status_code=400
            )

        task_id = task_data.get('task_id')
        task_type = task_data.get('task_type')
        org_id = task_data.get('org_id')
        user_id = task_data.get('user_id', 'system')
        # Support both 'params' (from DB service) and 'data' (for backward compatibility)
        params = task_data.get('params', task_data.get('data', {}))

        if not all([task_id, task_type, org_id]):
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Missing required task fields: task_id, task_type, org_id"
                }),
                mimetype="application/json",
                status_code=400
            )

        logger.info(f"Received {priority} priority task {task_id} of type {task_type} for org {org_id}")

        # Process the task using the existing task processor
        from task_processor import (
            process_sfdc_authenticate_task,
            process_metadata_extraction_task,
            process_health_check_task,
            process_profiles_task,
            process_overview_task,
            process_mfa_enforcement_task,
            process_device_activation_task,
            process_login_ip_ranges_task,
            process_login_hours_task,
            process_session_timeout_task,
            process_api_whitelisting_task,
            process_password_policy_task,
            process_pmd_task,
            check_task_dependencies_and_wait,
            TASK_TYPE_MAPPING
        )
        from shared.background_processor import (
            BackgroundProcessor,
            TASK_TYPE_SFDC_AUTHENTICATE,
            TASK_TYPE_METADATA_EXTRACTION,
            TASK_TYPE_HEALTH_CHECK,
            TASK_TYPE_PROFILES,
            TASK_TYPE_OVERVIEW,
            TASK_TYPE_MFA_ENFORCEMENT,
            TASK_TYPE_DEVICE_ACTIVATION,
            TASK_TYPE_LOGIN_IP_RANGES,
            TASK_TYPE_LOGIN_HOURS,
            TASK_TYPE_SESSION_TIMEOUT,
            TASK_TYPE_API_WHITELISTING,
            TASK_TYPE_PASSWORD_POLICY,
            TASK_TYPE_PMD_APEX_SECURITY,
            TASK_STATUS_FAILED
        )

        processor = BackgroundProcessor()

        execution_log_id = task_data.get('execution_log_id')
        if not execution_log_id:
            import uuid
            execution_log_id = str(uuid.uuid4())
            logger.warning(f"execution_log_id not found for task {task_id}, generated a new one: {execution_log_id}")

        execution_log_id = task_data.get('execution_log_id')
        if not execution_log_id:
            import uuid
            execution_log_id = str(uuid.uuid4())
            logger.warning(f"execution_log_id not found for task {task_id}, generated a new one: {execution_log_id}")

        # Check task dependencies and implement sequential delay before processing
        can_proceed, dependency_message = check_task_dependencies_and_wait(processor, task_type, org_id, execution_log_id)
        if not can_proceed:
            logger.warning(f"Task {task_id} dependencies not met: {dependency_message}")
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Task dependencies not met: {dependency_message}"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Map task type to constant
        constant_task_type = TASK_TYPE_MAPPING.get(task_type, task_type)

        # Generate execution_log_id for task tracking
        import uuid
        execution_log_id = str(uuid.uuid4())

        # Process task based on type
        if constant_task_type == TASK_TYPE_SFDC_AUTHENTICATE:
            process_sfdc_authenticate_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_METADATA_EXTRACTION:
            process_metadata_extraction_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_HEALTH_CHECK:
            process_health_check_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PROFILES:
            process_profiles_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_OVERVIEW:
            process_overview_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_MFA_ENFORCEMENT:
            process_mfa_enforcement_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_DEVICE_ACTIVATION:
            process_device_activation_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_LOGIN_IP_RANGES:
            process_login_ip_ranges_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_LOGIN_HOURS:
            process_login_hours_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_SESSION_TIMEOUT:
            process_session_timeout_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_API_WHITELISTING:
            process_api_whitelisting_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PASSWORD_POLICY:
            process_password_policy_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PMD_APEX_SECURITY:
            process_pmd_task(processor, task_id, org_id, user_id, params, execution_log_id)
        else:
            logger.warning(f"Unsupported task type: {task_type}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=100,
                message=f"Unsupported task type: {task_type}"
            )
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": f"Unsupported task type: {task_type}"
                }),
                mimetype="application/json",
                status_code=400
            )

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": f"Task {task_id} processed successfully"
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error processing task from DB service: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": f"Error processing task: {str(e)}"
            }),
            mimetype="application/json",
            status_code=500
        )

# Manual task processing endpoint for testing
@app.route(route="process-tasks", methods=["POST"])
def manual_task_processor(req: func.HttpRequest) -> func.HttpResponse:
    """
    Manually trigger task processing for testing purposes
    """
    try:
        from shared.azure_services import is_local_dev
        from shared.db_service_client import get_db_client

        logger.info("Manual task processing triggered")

        # Get pending tasks from DB service
        db_client = get_db_client()
        pending_tasks = db_client.get_pending_tasks(limit=5)

        if not pending_tasks:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "No pending tasks found",
                    "processed": 0
                }),
                mimetype="application/json",
                status_code=200
            )

        logger.info(f"Found {len(pending_tasks)} pending tasks to process")
        processed_count = 0
        errors = []

        for task in pending_tasks:
            try:
                task_id = task.get('task_id')
                org_id = task.get('org_id')
                task_type = task.get('task_type')
                user_id = task.get('created_by', 'system')
                params = task.get('data', {})

                logger.info(f"Processing task {task_id} of type {task_type} for org {org_id}")

                # Process the task using the existing task processor
                from task_processor import (
                    process_sfdc_authenticate_task,
                    process_metadata_extraction_task,
                    process_health_check_task,
                    process_profiles_task,
                    process_profiles_permission_sets_task,
                    process_permission_sets_task,
                    process_overview_task,
                    process_data_export_task,
                    process_report_generation_task,
                    process_scheduled_scan_task,
                    process_notification_task,
                    process_mfa_enforcement_task,
                    process_device_activation_task,
                    process_login_ip_ranges_task,
                    process_login_hours_task,
                    process_session_timeout_task,
                    process_api_whitelisting_task,
                    process_password_policy_task,
                    process_pmd_task,
                    check_task_dependencies_and_wait,
                    TASK_TYPE_MAPPING
                )
                from shared.background_processor import BackgroundProcessor, TASK_STATUS_FAILED

                # Create processor instance
                processor = BackgroundProcessor()

                execution_log_id = task.get('execution_log_id')
                if not execution_log_id:
                    import uuid
                    execution_log_id = str(uuid.uuid4())
                    logger.warning(f"execution_log_id not found for task {task_id}, generated a new one: {execution_log_id}")

                # Check task dependencies and implement sequential delay before processing
                logger.info(f"Checking dependencies and implementing sequential delay for task {task_id} of type '{task_type}'")
                can_proceed, dependency_message = check_task_dependencies_and_wait(processor, task_type, org_id, execution_log_id)
                if not can_proceed:
                    logger.warning(f"Task {task_id} cannot proceed: {dependency_message}")
                    processor.update_task_status(
                        task_id=task_id,
                        status=TASK_STATUS_FAILED,
                        progress=0,
                        message=f"Dependencies not met: {dependency_message}"
                    )
                    continue

                logger.info(f"Dependencies satisfied for task {task_id}: {dependency_message}")

                # Generate execution_log_id for task tracking
                import uuid
                execution_log_id = str(uuid.uuid4())
                logger.info(f"Generated execution_log_id for manual task {task_id}: {execution_log_id}")

                # Process task based on type
                if task_type == "sfdc_authenticate":
                    process_sfdc_authenticate_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "metadata_extraction":
                    process_metadata_extraction_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "health_check":
                    process_health_check_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "profiles":
                    process_profiles_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "profiles_permission_sets":
                    process_profiles_permission_sets_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "permission_sets":
                    process_permission_sets_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "overview":
                    process_overview_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "data_export":
                    process_data_export_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "report_generation":
                    process_report_generation_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "scheduled_scan":
                    process_scheduled_scan_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "notification":
                    process_notification_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "mfa_enforcement":
                    process_mfa_enforcement_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "device_activation":
                    process_device_activation_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "login_ip_ranges":
                    process_login_ip_ranges_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "login_hours":
                    process_login_hours_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "session_timeout":
                    process_session_timeout_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "api_whitelisting":
                    process_api_whitelisting_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "password_policy":
                    process_password_policy_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                elif task_type == "pmd_apex_security":
                    process_pmd_task(processor, task_id, org_id, user_id, params, execution_log_id=execution_log_id)
                else:
                    logger.warning(f"Unsupported task type: {task_type}")
                    processor.update_task_status(
                        task_id=task_id,
                        status=TASK_STATUS_FAILED,
                        progress=100,
                        message=f"Unsupported task type: {task_type}"
                    )

                processed_count += 1

            except Exception as task_error:
                error_msg = f"Error processing task {task.get('task_id')}: {str(task_error)}"
                logger.error(error_msg)
                errors.append(error_msg)
                continue

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "message": f"Processed {processed_count} tasks",
                "processed": processed_count,
                "total_found": len(pending_tasks),
                "errors": errors
            }),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error in manual task processor: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )

@app.route(route="queue-status", methods=["GET"])
def queue_status_api(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get queue status and monitoring information
    
    Returns:
        func.HttpResponse: Queue status information
    """
    try:
        from shared.queue_manager import get_queue_manager
        from shared.cors_middleware import cors_middleware
        
        queue_manager = get_queue_manager()
        if not queue_manager:
            return func.HttpResponse(
                json.dumps({"error": "Queue manager not available"}),
                mimetype="application/json",
                status_code=500
            )
        
        # Get queue properties
        queue_properties = queue_manager.get_all_queue_properties()
        
        # Get task processing metrics
        from shared.db_service_client import get_db_client
        db_client = get_db_client()
        
        metrics = {}
        if db_client:
            try:
                # Get recent task statistics (if method exists)
                if hasattr(db_client, 'get_recent_tasks'):
                    recent_tasks = db_client.get_recent_tasks(hours=24)
                    
                    total_tasks = len(recent_tasks)
                    completed_tasks = len([t for t in recent_tasks if t.get('Status') in ['Completed', 'Success']])
                    failed_tasks = len([t for t in recent_tasks if t.get('Status') == 'Failed'])
                    pending_tasks = len([t for t in recent_tasks if t.get('Status') in ['Pending', 'Running']])
                    
                    metrics = {
                        "total_tasks_24h": total_tasks,
                        "completed_tasks_24h": completed_tasks,
                        "failed_tasks_24h": failed_tasks,
                        "pending_tasks_24h": pending_tasks,
                        "success_rate_24h": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
                    }
                else:
                    metrics = {"note": "get_recent_tasks method not available"}
            except Exception as metrics_error:
                logger.warning(f"Could not get task metrics: {str(metrics_error)}")
                metrics = {"error": "Could not retrieve task metrics"}
        
        response_data = {
            "timestamp": datetime.now().isoformat(),
            "queues": queue_properties,
            "metrics": metrics,
            "system_status": "healthy"
        }
        
        response = func.HttpResponse(
            json.dumps(response_data, indent=2),
            mimetype="application/json",
            status_code=200
        )
        
        return cors_middleware(req, response)
        
    except Exception as e:
        error_message = f"Error getting queue status: {str(e)}"
        logger.error(error_message)
        
        response = func.HttpResponse(
            json.dumps({"error": error_message}),
            mimetype="application/json",
            status_code=500
        )
        
        return cors_middleware(req, response)

