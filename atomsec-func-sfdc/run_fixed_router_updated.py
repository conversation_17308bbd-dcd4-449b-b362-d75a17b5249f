"""
Fixed Router FastAPI App

This module provides a FastAPI application with fixed routing.
It is used as a fallback for the ASGI integration.
"""

import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Optional, Any
from datetime import datetime

# Configure logging
logger = logging.getLogger('fixed_router_app')
logger.setLevel(logging.INFO)

# Create FastAPI app
app = FastAPI(
    title="Fixed Router API",
    description="API with fixed routing",
    version="1.0.0",
    docs_url="/fixed-docs",
    redoc_url="/fixed-redoc",
    openapi_url="/fixed-openapi.json"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define response model
class ApiResponse(BaseModel):
    success: bool = Field(..., description="Success status")
    statusCode: int = Field(..., description="HTTP status code")
    timestamp: str = Field(..., description="Response timestamp")
    data: Any = Field(None, description="Response data")
    message: Optional[str] = Field(None, description="Response message")

# Import and include the integration router
try:
    from routers.integration_router import router as integration_router
    app.include_router(integration_router)
    logger.info("Successfully included integration router")
except ImportError as e:
    logger.error(f"Error importing integration router: {str(e)}")

    # Create a simple mock integration endpoint for local development
    @app.get("/integration/integrations")
    async def mock_get_integrations():
        logger.info("Using mock integrations endpoint")
        return {
            "integrations": [
                {
                    "id": "mock-integration-id-1",
                    "name": "Mock Integration 1",
                    "tenantUrl": "https://example.my.salesforce.com",
                    "type": "salesforce",
                    "description": "Mock integration 1",
                    "environment": "development",
                    "isActive": True,
                    "lastScan": datetime.now().isoformat(),
                    "createdAt": datetime.now().isoformat(),
                    "userEmail": "<EMAIL>",
                    "healthScore": "85"
                },
                {
                    "id": "mock-integration-id-2",
                    "name": "Mock Integration 2",
                    "tenantUrl": "https://example2.my.salesforce.com",
                    "type": "salesforce",
                    "description": "Mock integration 2",
                    "environment": "production",
                    "isActive": True,
                    "lastScan": datetime.now().isoformat(),
                    "createdAt": datetime.now().isoformat(),
                    "userEmail": "<EMAIL>",
                    "healthScore": "92"
                }
            ],
            "count": 2
        }

