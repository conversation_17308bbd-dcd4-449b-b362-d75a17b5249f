# Standalone PMD Scanner for Salesforce Apex

This directory contains a standalone Python script for scanning Salesforce Apex code with PMD, along with test data and helper scripts.

## Overview

The standalone PMD scanner is a self-contained Python script that can:

1. Scan Apex classes from a local directory or from blob storage
2. Run PMD with all available rulesets for Salesforce Apex
3. Process the results and generate both CSV and JSON output
4. Provide a summary of the findings

This allows you to test the PMD scanning functionality without relying on the full task processor system.

## Prerequisites

- Python 3.8 or higher
- PMD 7.x installed and available in the PATH
- Java 11 or higher (required by PMD)
- Azure Storage SDK (for accessing Azure Blob Storage)
  ```bash
  pip install azure-storage-blob
  ```

## Files

- `test_pmd_standalone.py`: The main Python script for running PMD scans
- `run_pmd_test.sh`: Shell script for running the scanner on test data (Linux/macOS)
- `run_pmd_test.bat`: Batch script for running the scanner on test data (Windows)
- `test_data/`: Directory containing test Apex classes with intentional code quality issues
- `test_data/TestClass.cls`: Example Apex class with various code quality issues
- `test_data/README.md`: Documentation for the test data

## Usage

### Basic Usage

```bash
python test_pmd_standalone.py --source-dir ./path/to/apex/classes --output-dir ./path/to/output
```

### Using Test Data

```bash
# On Linux/macOS
chmod +x ./run_pmd_test.sh  # Make the script executable
./run_pmd_test.sh

# Alternative method without making the script executable
# Copy and paste this exact command:
python test_pmd_standalone.py --use-hardcoded-path --output-dir ./pmd_results

# On Windows
run_pmd_test.bat
```

> **Note**: Be careful with the `--use-hardcoded-path` flag - there should be no space after the hyphen.
> Correct: `--use-hardcoded-path`
> Incorrect: `--use- hardcoded-path`

### Using Hardcoded Blob Path

The script includes a hardcoded blob path for testing with real Salesforce data:

```
my_new_testing_for_rules/19ba2cdc-3fee-4029-bbcb-34e8f06789c6/metadata_extraction-19ba2cdc-3fee-4029-bbcb-34e8f06789c6-daf2232a
```

You can use this path by adding the `--use-hardcoded-path` flag:

```bash
# Copy and paste this exact command:
python test_pmd_standalone.py --use-hardcoded-path --output-dir ./pmd_results
```

### Configuring Azure Storage Connection

The script will automatically try to connect to Azurite using the default connection string. If you need to connect to a different Azure Storage account, set the `AZURE_STORAGE_CONNECTION_STRING` environment variable:

```bash
# On Linux/macOS
export AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=your_account;AccountKey=your_key;EndpointSuffix=core.windows.net"

# On Windows
set AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=your_account;AccountKey=your_key;EndpointSuffix=core.windows.net
```

For Azurite, the default connection string is:
```
DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;
```

The helper scripts (`run_pmd_test.sh` and `run_pmd_test.bat`) are already configured to use this hardcoded path.

### Advanced Usage

```bash
# Scan from blob storage
python test_pmd_standalone.py --blob-prefix org_name/integration_id/metadata_extraction-id --output-dir ./path/to/output

# Specify a custom task ID
python test_pmd_standalone.py --source-dir ./path/to/apex/classes --output-dir ./path/to/output --task-id custom-task-id
```

## Output

The scanner generates the following output:

1. **CSV File**: Contains the raw PMD findings with file paths, line numbers, rule names, and descriptions
2. **JSON File**: Contains the processed findings with standardized severity levels and categorized rules
3. **Console Output**: Provides a summary of the scan, including the number of classes scanned and findings detected

## Integrating with the Main Application

Once you've verified that the standalone scanner works correctly, you can integrate it with the main application by:

1. Ensuring the `process_pmd_task` function in `task_processor/tasks/pmd_task.py` is up to date
2. Registering the task type in `shared/background_processor.py`
3. Adding the task handling in `function_app.py`
4. Updating the pipeline to install PMD in the Azure environment

## Troubleshooting

### PMD Command Syntax

If you encounter errors with the PMD command, ensure you're using the correct syntax for your PMD version:

- PMD 7.x requires the `check` subcommand: `pmd check -d ... -R ... -f ...`
- PMD 6.x uses the direct syntax: `pmd -d ... -R ... -f ...`

You can check your PMD version with:

```bash
pmd --version
```

### Java Version

PMD requires Java 11 or higher. If you encounter Java-related errors, check your Java version:

```bash
java -version
```

### Missing Rulesets

If PMD cannot find the rulesets, ensure you're using the correct ruleset paths for your PMD version. The paths used in this script are for PMD 7.x. 