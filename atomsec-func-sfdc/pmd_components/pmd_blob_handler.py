"""
PMD Blob Storage Handler Module

This module handles blob storage operations for PMD scanning of Salesforce Apex classes.
It provides functionality to iterate through classes without downloading them to a folder.
"""

import logging
import os
import tempfile
from typing import Dict, Any, List, Optional, Iterator, Tuple
from datetime import datetime

# Import from the parent directory's shared module
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from shared.data_access import BlobStorageRepository

logger = logging.getLogger(__name__)

class PMDBlobHandler:
    """Handler for blob storage operations in PMD scanning"""
    
    def __init__(self, container_name: str = "salesforce-metadata"):
        """
        Initialize PMD blob handler
        
        Args:
            container_name: Name of the blob storage container
        """
        self.container_name = container_name
        self.blob_repo = BlobStorageRepository(container_name=container_name)
    
    def get_apex_classes_from_blob(self, blob_prefix: str) -> List[str]:
        """
        Get list of Apex class blob names from blob storage
        
        Args:
            blob_prefix: Blob storage prefix path
            
        Returns:
            List of blob names for Apex classes
        """
        apex_classes_path_in_blob = f"{blob_prefix}/classes/"
        blob_list = self.blob_repo.list_blobs(name_starts_with=apex_classes_path_in_blob)
        
        # Filter for .cls files only
        apex_classes = [blob_name for blob_name in blob_list if blob_name.endswith('.cls')]
        
        logger.info(f"Found {len(apex_classes)} Apex classes in blob storage at {apex_classes_path_in_blob}")
        return apex_classes
    
    def iterate_apex_classes(self, blob_prefix: str) -> Iterator[Tuple[str, bytes]]:
        """
        Iterate through Apex classes from blob storage without downloading to folder
        
        Args:
            blob_prefix: Blob storage prefix path
            
        Yields:
            Tuple of (file_name, file_content_bytes)
        """
        apex_classes = self.get_apex_classes_from_blob(blob_prefix)
        
        for blob_name in apex_classes:
            try:
                file_content = self.blob_repo.get_blob_bytes(blob_name)
                if file_content:
                    file_name = os.path.basename(blob_name)
                    yield file_name, file_content
                else:
                    logger.warning(f"Failed to get content for blob: {blob_name}")
            except Exception as e:
                logger.error(f"Error processing blob {blob_name}: {e}")
                continue
    
    def create_temp_scan_directory(self, blob_prefix: str, temp_dir: Optional[str] = None) -> Tuple[str, int]:
        """
        Create a temporary directory with Apex classes for scanning
        
        Args:
            blob_prefix: Blob storage prefix path
            temp_dir: Optional temporary directory path
            
        Returns:
            Tuple of (temp_directory_path, number_of_files_created)
        """
        if temp_dir:
            os.makedirs(temp_dir, exist_ok=True)
            temp_path = temp_dir
        else:
            temp_path = tempfile.mkdtemp(prefix="pmd_scan_")
        
        files_created = 0
        
        for file_name, file_content in self.iterate_apex_classes(blob_prefix):
            try:
                file_path = os.path.join(temp_path, file_name)
                with open(file_path, "wb") as f:
                    f.write(file_content)
                files_created += 1
                logger.debug(f"Created temporary file: {file_path}")
            except Exception as e:
                logger.error(f"Error creating temporary file {file_name}: {e}")
                continue
        
        logger.info(f"Created {files_created} temporary files in {temp_path}")
        return temp_path, files_created
    
    def scan_classes_in_blob(self, 
                            blob_prefix: str, 
                            scanner, 
                            output_file: str,
                            categories: Optional[List[str]] = None,
                            temp_dir: Optional[str] = None,
                            custom_ruleset_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Scan Apex classes directly from blob storage
        
        Args:
            blob_prefix: Blob storage prefix path
            scanner: PMD scanner instance
            output_file: Path to output CSV file
            categories: Optional list of rule categories to include
            temp_dir: Optional temporary directory for files
            custom_ruleset_file: Optional path to custom ruleset file
            
        Returns:
            Dictionary containing scan results and metadata
        """
        logger.info(f"Starting PMD scan of Apex classes from blob prefix: {blob_prefix}")
        
        # Get class count first
        apex_classes = self.get_apex_classes_from_blob(blob_prefix)
        if not apex_classes:
            logger.warning(f"No Apex classes found in blob storage at {blob_prefix}/classes/")
            return {
                "success": True,
                "findings_count": 0,
                "files_scanned": 0,
                "message": "No Apex classes found to scan"
            }
        
        # Create temporary directory with classes
        temp_path, files_created = self.create_temp_scan_directory(blob_prefix, temp_dir)
        
        try:
            # Run PMD scan on the temporary directory
            if custom_ruleset_file and os.path.exists(custom_ruleset_file):
                logger.info(f"Using custom ruleset file: {custom_ruleset_file}")
                scan_result = scanner.scan_directory_with_ruleset(
                    source_dir=temp_path,
                    output_file=output_file,
                    ruleset_file=custom_ruleset_file
                )
            else:
                scan_result = scanner.scan_directory(
                    source_dir=temp_path,
                    output_file=output_file,
                    categories=categories
                )
            
            # Add blob-specific metadata
            scan_result.update({
                "blob_prefix": blob_prefix,
                "temp_directory": temp_path,
                "files_created": files_created,
                "total_classes_found": len(apex_classes),
                "custom_ruleset_used": custom_ruleset_file is not None and os.path.exists(custom_ruleset_file)
            })
            
            return scan_result
            
        except Exception as e:
            logger.error(f"Error during blob-based PMD scan: {e}")
            return {
                "success": False,
                "error": str(e),
                "findings_count": 0,
                "blob_prefix": blob_prefix,
                "temp_directory": temp_path
            }
        finally:
            # Clean up temporary directory if we created it
            if not temp_dir and os.path.exists(temp_path):
                try:
                    import shutil
                    shutil.rmtree(temp_path)
                    logger.debug(f"Cleaned up temporary directory: {temp_path}")
                except Exception as e:
                    logger.warning(f"Failed to clean up temporary directory {temp_path}: {e}")
    
    def upload_scan_results(self, 
                           results_file: str, 
                           blob_prefix: str,
                           results_type: str = "pmd_results") -> Optional[str]:
        """
        Upload PMD scan results to blob storage
        
        Args:
            results_file: Path to results file to upload
            blob_prefix: Blob storage prefix path
            results_type: Type of results (e.g., "pmd_results", "findings")
            
        Returns:
            Blob URL of uploaded file or None if upload failed
        """
        if not os.path.exists(results_file):
            logger.error(f"Results file not found: {results_file}")
            return None
        
        try:
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            blob_name = f"{blob_prefix}/{results_type}/pmd_scan_results_{timestamp}.csv"
            
            with open(results_file, 'rb') as f:
                self.blob_repo.upload_blob(blob_name, f.read())
            
            blob_url = self.blob_repo.get_blob_url(blob_name)
            logger.info(f"Uploaded scan results to blob storage: {blob_url}")
            
            return blob_url
            
        except Exception as e:
            logger.error(f"Error uploading scan results: {e}")
            return None
    
    def get_blob_info(self, blob_prefix: str) -> Dict[str, Any]:
        """
        Get information about Apex classes in blob storage
        
        Args:
            blob_prefix: Blob storage prefix path
            
        Returns:
            Dictionary containing blob information
        """
        apex_classes = self.get_apex_classes_from_blob(blob_prefix)
        
        # Calculate total size
        total_size = 0
        for blob_name in apex_classes:
            try:
                content = self.blob_repo.get_blob_bytes(blob_name)
                if content:
                    total_size += len(content)
            except Exception as e:
                logger.warning(f"Error getting size for blob {blob_name}: {e}")
        
        return {
            "blob_prefix": blob_prefix,
            "classes_count": len(apex_classes),
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "classes": [os.path.basename(cls) for cls in apex_classes]
        } 