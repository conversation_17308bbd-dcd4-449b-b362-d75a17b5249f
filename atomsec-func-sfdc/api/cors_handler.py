"""
CORS Handler Blueprint for Database Service

This module provides a global handler for OPTIONS requests to support CORS.
"""

import logging
import azure.functions as func
from shared.cors_middleware import handle_cors_preflight

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

@bp.route(route="{*route}", methods=["OPTIONS"])
def options_handler(req: func.HttpRequest) -> func.HttpResponse:
    """
    Global handler for OPTIONS requests to support CORS

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response with CORS headers
    """
    logger.info(f'Processing OPTIONS request for route: {req.route_params.get("route", "unknown")}')
    return handle_cors_preflight(req)
