"""
SFDC Proxy Endpoints

This module provides proxy endpoints that forward requests to the SFDC service.
This centralizes all frontend calls through the DB service while keeping business logic in SFDC service.

Endpoints:
- POST /integration/scan/{id} - Trigger integration scan
- GET /task-status - Get task status
- POST /tasks/cancel - Cancel task
- POST /tasks/schedule - Schedule task
- GET /health-score - Get health score
- GET /health-risks - Get health risks
- GET /profiles - Get profiles
- GET /permission-sets - Get permission sets
- GET /scan/accounts - Get scan accounts
- GET /scan/history - Get scan history
- GET /sfdc/health - Get SFDC service health
- POST /sfdc/info - Get SFDC service info
"""

import logging
import json
import azure.functions as func
from shared.sfdc_service_client import get_sfdc_client
from shared.cors_middleware import add_cors_headers

logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

def _forward_auth_headers(req: func.HttpRequest) -> dict:
    """Extract and forward authentication headers"""
    headers = {}
    
    # Forward Authorization header
    auth_header = req.headers.get('Authorization')
    if auth_header:
        headers['Authorization'] = auth_header
    
    # Forward other relevant headers
    for header_name in ['X-User-ID', 'X-User-Email', 'X-Org-ID']:
        header_value = req.headers.get(header_name)
        if header_value:
            headers[header_name] = header_value
    
    return headers

def _create_error_response(message: str, status_code: int = 500) -> func.HttpResponse:
    """Create standardized error response"""
    response = func.HttpResponse(
        json.dumps({
            "success": False,
            "error": message
        }),
        mimetype="application/json",
        status_code=status_code
    )
    return response

# Integration scan endpoint
@bp.route(route="integrations/{integration_id}/scan", methods=["POST", "OPTIONS"])
def scan_integration_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for integration scanning"""
    logger.info('Processing integration scan proxy request...')
    
    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)
    
    try:
        integration_id = req.route_params.get('integration_id')
        if not integration_id:
            response = _create_error_response("Integration ID is required", 400)
            return add_cors_headers(req, response)
        
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        
        result = sfdc_client.scan_integration(integration_id, headers)
        
        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)
        
        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']
        
        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)
        
    except Exception as e:
        logger.error(f"Error in scan_integration_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

# Task management endpoints
@bp.route(route="task-status", methods=["GET", "OPTIONS"])
def task_status_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for task status"""
    logger.info('Processing task status proxy request...')
    
    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)
    
    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)
        
        result = sfdc_client.get_task_status(params, headers)
        
        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)
        
        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']
        
        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)
        
    except Exception as e:
        logger.error(f"Error in task_status_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="tasks/cancel", methods=["POST", "OPTIONS"])
def cancel_task_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for canceling tasks"""
    logger.info('Processing cancel task proxy request...')
    
    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)
    
    try:
        # Get request body
        try:
            body = req.get_json()
        except ValueError:
            body = None
        
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        
        result = sfdc_client.cancel_task(body, headers)
        
        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)
        
        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']
        
        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)
        
    except Exception as e:
        logger.error(f"Error in cancel_task_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="tasks/schedule", methods=["POST", "OPTIONS"])
def schedule_task_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for scheduling tasks"""
    logger.info('Processing schedule task proxy request...')
    
    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)
    
    try:
        # Get request body
        try:
            body = req.get_json()
        except ValueError:
            body = None
        
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        
        result = sfdc_client.schedule_task(body, headers)
        
        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)
        
        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']
        
        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)
        
    except Exception as e:
        logger.error(f"Error in schedule_task_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

# Security analysis endpoints
@bp.route(route="health-score", methods=["GET", "OPTIONS"])
def health_score_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for health score"""
    logger.info('Processing health score proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_health_score(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in health_score_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="health-risks", methods=["GET", "OPTIONS"])
def health_risks_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for health risks"""
    logger.info('Processing health risks proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_health_risks(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in health_risks_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="profiles", methods=["GET", "OPTIONS"])
def profiles_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for profiles"""
    logger.info('Processing profiles proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_profiles(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in profiles_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="permission-sets", methods=["GET", "OPTIONS"])
def permission_sets_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for permission sets"""
    logger.info('Processing permission sets proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_permission_sets(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in permission_sets_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

# Scan results endpoints
@bp.route(route="scan/accounts", methods=["GET", "OPTIONS"])
def scan_accounts_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for scan accounts"""
    logger.info('Processing scan accounts proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_scan_accounts(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in scan_accounts_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="scan/history", methods=["GET", "OPTIONS"])
def scan_history_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for scan history"""
    logger.info('Processing scan history proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)
        params = dict(req.params)

        result = sfdc_client.get_scan_history(params, headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in scan_history_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

# System endpoints
@bp.route(route="sfdc/health", methods=["GET", "OPTIONS"])
def sfdc_health_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for SFDC service health"""
    logger.info('Processing SFDC health proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)

        result = sfdc_client.get_health(headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in sfdc_health_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)

@bp.route(route="sfdc/info", methods=["POST", "OPTIONS"])
def sfdc_info_proxy(req: func.HttpRequest) -> func.HttpResponse:
    """Proxy endpoint for SFDC service info"""
    logger.info('Processing SFDC info proxy request...')

    # Handle CORS preflight
    if req.method == "OPTIONS":
        from shared.cors_middleware import handle_cors_preflight
        return handle_cors_preflight(req)

    try:
        # Get SFDC client and forward request
        sfdc_client = get_sfdc_client()
        headers = _forward_auth_headers(req)

        result = sfdc_client.get_info(headers)

        if result is None:
            response = _create_error_response("Failed to communicate with SFDC service", 503)
            return add_cors_headers(req, response)

        # Forward the response
        status_code = result.get('status_code', 200)
        if 'status_code' in result:
            del result['status_code']

        response = func.HttpResponse(
            json.dumps(result),
            mimetype="application/json",
            status_code=status_code
        )
        return add_cors_headers(req, response)

    except Exception as e:
        logger.error(f"Error in sfdc_info_proxy: {str(e)}")
        response = _create_error_response(str(e))
        return add_cors_headers(req, response)
