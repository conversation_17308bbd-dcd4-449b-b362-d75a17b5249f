"""
Account Management Endpoints

This module provides account-related database operations for the AtomSec application.
Moved from atomsec-func-sfdc to centralize database operations.

Endpoints:
- GET /api/accounts - List all accounts
- POST /api/accounts - Create new account
- GET /api/accounts/{id} - Get account by ID
- PUT /api/accounts/{id} - Update account
- DELETE /api/accounts/{id} - Delete account
"""

import logging
import json
import azure.functions as func
from datetime import datetime
from typing import Dict, List, Any, Optional

from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.database_models_new import Account

# Configure module-level logger
logger = logging.getLogger(__name__)

# Global repository instances (lazy initialized)
_account_table_repo = None
_account_sql_repo = None


def get_account_table_repo() -> Optional[TableStorageRepository]:
    """Get account table repository for local development"""
    global _account_table_repo
    if _account_table_repo is None:
        try:
            _account_table_repo = TableStorageRepository(table_name="Account")
            logger.info("Initialized account table repository")
        except Exception as e:
            logger.error(f"Failed to initialize account table repository: {str(e)}")
            _account_table_repo = None
    return _account_table_repo


def get_account_sql_repo() -> Optional[SqlDatabaseRepository]:
    """Get account SQL repository for production"""
    global _account_sql_repo
    if _account_sql_repo is None and not is_local_dev():
        try:
            _account_sql_repo = SqlDatabaseRepository(table_name="App_Account")
            logger.info("Initialized account SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize account SQL repository: {str(e)}")
            _account_sql_repo = None
    return _account_sql_repo


def get_accounts() -> List[Dict[str, Any]]:
    """
    Get all accounts

    Returns:
        List of account dictionaries
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_account_table_repo()
            if not repo:
                logger.error("Account table repository not available")
                return []

            filter_query = "PartitionKey eq 'account'"
            entities = list(repo.query_entities(filter_query))

            accounts = []
            for entity in entities:
                accounts.append({
                    'id': entity.get('AccountId'),
                    'name': entity.get('Name', ''),
                    'description': entity.get('Description', ''),
                    'is_active': entity.get('IsActive', True),
                    'created_at': entity.get('CreatedAt', ''),
                    'created_by': entity.get('CreatedBy', 0)
                })

            return accounts
        else:
            # Use SQL Database for production
            repo = get_account_sql_repo()
            if not repo:
                logger.error("Account SQL repository not available")
                return []

            query = """
            SELECT AccountId, Name, Description, IsActive, CreatedAt, CreatedBy
            FROM App_Account
            WHERE IsActive = 1
            ORDER BY Name
            """

            results = repo.execute_query(query)

            accounts = []
            for row in results:
                accounts.append({
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'is_active': row[3],
                    'created_at': row[4].isoformat() if row[4] else None,
                    'created_by': row[5]
                })

            return accounts

    except Exception as e:
        logger.error(f"Error getting accounts: {str(e)}")
        return []


def create_account(account_data: Dict[str, Any]) -> Optional[int]:
    """
    Create a new account

    Args:
        account_data: Dictionary containing account information

    Returns:
        Account ID if successful, None otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_account_table_repo()
            if not repo:
                logger.error("Account table repository not available")
                return None

            # Generate account ID
            import random
            account_id = random.randint(1000, 9999)

            # Create account entity
            account = Account(
                AccountId=account_id,
                Name=account_data.get('name', ''),
                Description=account_data.get('description', ''),
                IsActive=True,
                CreatedAt=datetime.now(),
                CreatedBy=account_data.get('created_by', 0)
            )

            entity = account.to_table_entity()

            if repo.insert_entity(entity):
                logger.info(f"Created account: {account.Name} with ID {account_id}")
                return account_id
            else:
                logger.error("Failed to insert account entity")
                return None

        else:
            # Use SQL Database for production
            repo = get_account_sql_repo()
            if not repo:
                logger.error("Account SQL repository not available")
                return None

            query = """
            INSERT INTO App_Account (Name, Description, IsActive, CreatedAt, CreatedBy)
            VALUES (?, ?, ?, ?, ?);
            SELECT SCOPE_IDENTITY();
            """

            params = (
                account_data.get('name', ''),
                account_data.get('description', ''),
                True,
                datetime.now(),
                account_data.get('created_by', 0)
            )

            results = repo.execute_query(query, params)
            if results:
                account_id = int(results[0][0])
                logger.info(f"Created account: {account_data.get('name')} with ID {account_id}")
                return account_id
            else:
                logger.error("Failed to create account in SQL database")
                return None

    except Exception as e:
        logger.error(f"Error creating account: {str(e)}")
        return None


def get_account_by_id(account_id: int) -> Optional[Dict[str, Any]]:
    """
    Get account by ID

    Args:
        account_id: Account ID

    Returns:
        Account data dictionary or None
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_account_table_repo()
            if not repo:
                logger.error("Account table repository not available")
                return None

            filter_query = f"PartitionKey eq 'account' and AccountId eq {account_id}"
            entities = list(repo.query_entities(filter_query))

            if entities:
                entity = entities[0]
                return {
                    'id': entity.get('AccountId'),
                    'name': entity.get('Name', ''),
                    'description': entity.get('Description', ''),
                    'is_active': entity.get('IsActive', True),
                    'created_at': entity.get('CreatedAt', ''),
                    'created_by': entity.get('CreatedBy', 0)
                }
            else:
                logger.warning(f"Account not found: {account_id}")
                return None

        else:
            # Use SQL Database for production
            repo = get_account_sql_repo()
            if not repo:
                logger.error("Account SQL repository not available")
                return None

            query = """
            SELECT AccountId, Name, Description, IsActive, CreatedAt, CreatedBy
            FROM App_Account
            WHERE AccountId = ?
            """

            results = repo.execute_query(query, (account_id,))

            if results:
                row = results[0]
                return {
                    'id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'is_active': row[3],
                    'created_at': row[4].isoformat() if row[4] else None,
                    'created_by': row[5]
                }
            else:
                logger.warning(f"Account not found: {account_id}")
                return None

    except Exception as e:
        logger.error(f"Error getting account by ID: {str(e)}")
        return None


def update_account(account_id: int, account_data: Dict[str, Any]) -> bool:
    """
    Update account information

    Args:
        account_id: Account ID
        account_data: Dictionary containing fields to update

    Returns:
        True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            repo = get_account_table_repo()
            if not repo:
                logger.error("Account table repository not available")
                return False

            # Get existing account
            filter_query = f"PartitionKey eq 'account' and AccountId eq {account_id}"
            entities = list(repo.query_entities(filter_query))

            if not entities:
                logger.warning(f"Account not found: {account_id}")
                return False

            entity = entities[0]

            # Update fields
            if 'name' in account_data:
                entity['Name'] = account_data['name']
            if 'description' in account_data:
                entity['Description'] = account_data['description']
            if 'is_active' in account_data:
                entity['IsActive'] = account_data['is_active']

            entity['UpdatedAt'] = datetime.now().isoformat()

            return repo.update_entity(entity)

        else:
            # Use SQL Database for production
            repo = get_account_sql_repo()
            if not repo:
                logger.error("Account SQL repository not available")
                return False

            # Build update query
            update_fields = []
            params = []

            if 'name' in account_data:
                update_fields.append("Name = ?")
                params.append(account_data['name'])

            if 'description' in account_data:
                update_fields.append("Description = ?")
                params.append(account_data['description'])

            if 'is_active' in account_data:
                update_fields.append("IsActive = ?")
                params.append(account_data['is_active'])

            if not update_fields:
                return True  # Nothing to update

            update_fields.append("UpdatedAt = ?")
            params.append(datetime.now())
            params.append(account_id)

            query = f"UPDATE App_Account SET {', '.join(update_fields)} WHERE AccountId = ?"

            return repo.execute_non_query(query, tuple(params))

    except Exception as e:
        logger.error(f"Error updating account: {str(e)}")
        return False


def delete_account(account_id: int) -> bool:
    """
    Delete an account (soft delete by setting IsActive = False)

    Args:
        account_id: Account ID

    Returns:
        True if successful, False otherwise
    """
    return update_account(account_id, {'is_active': False})


# Create blueprint
bp = func.Blueprint()

@bp.route(route="accounts", methods=["GET"])
def list_accounts(req: func.HttpRequest) -> func.HttpResponse:
    """List all accounts"""
    try:
        accounts = get_accounts()

        return func.HttpResponse(
            json.dumps({
                "success": True,
                "data": {"accounts": accounts, "count": len(accounts)}
            }),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error listing accounts: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="accounts", methods=["POST"])
def create_account_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Create new account"""
    try:
        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        if 'name' not in req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Account name is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Create account
        account_id = create_account(req_body)

        if account_id:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": {"account_id": account_id}
                }),
                mimetype="application/json",
                status_code=201
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to create account"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error creating account: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="accounts/{account_id:int}", methods=["GET"])
def get_account_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Get account by ID"""
    try:
        account_id = int(req.route_params.get('account_id'))

        account = get_account_by_id(account_id)

        if account:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "data": account
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Account not found"
                }),
                mimetype="application/json",
                status_code=404
            )

    except Exception as e:
        logger.error(f"Error getting account: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="accounts/{account_id:int}", methods=["PUT"])
def update_account_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Update account"""
    try:
        account_id = int(req.route_params.get('account_id'))

        # Parse request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Request body is required"
                }),
                mimetype="application/json",
                status_code=400
            )

        # Update account
        success = update_account(account_id, req_body)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Account updated successfully"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to update account"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error updating account: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )


@bp.route(route="accounts/{account_id:int}", methods=["DELETE"])
def delete_account_endpoint(req: func.HttpRequest) -> func.HttpResponse:
    """Delete account (soft delete)"""
    try:
        account_id = int(req.route_params.get('account_id'))

        # Delete account (soft delete)
        success = delete_account(account_id)

        if success:
            return func.HttpResponse(
                json.dumps({
                    "success": True,
                    "message": "Account deleted successfully"
                }),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "success": False,
                    "error": "Failed to delete account"
                }),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logger.error(f"Error deleting account: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "success": False,
                "error": str(e)
            }),
            mimetype="application/json",
            status_code=500
        )
