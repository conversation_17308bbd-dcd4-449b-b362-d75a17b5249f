#!/usr/bin/env python3

import os
import sys
import json
from datetime import datetime
from azure.data.tables import TableServiceClient
from azure.core.exceptions import ResourceNotFoundError

def check_policies_result_table():
    """Check the PoliciesResult table for duplicate records and health check data"""
    
    # Azure Storage connection string for local development
    connection_string = "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10002/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10002/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;"
    
    try:
        # Create table service client
        table_service = TableServiceClient.from_connection_string(connection_string)
        table_client = table_service.get_table_client("PoliciesResult")
        
        print("🔍 Checking PoliciesResult table...")
        print("=" * 80)
        
        # Query all entities
        entities = list(table_client.list_entities())
        
        if not entities:
            print("❌ No records found in PoliciesResult table")
            return
        
        print(f"📊 Total records found: {len(entities)}")
        print()
        
        # Group by PartitionKey (org_id)
        org_groups = {}
        for entity in entities:
            org_id = entity.get('PartitionKey', 'unknown')
            if org_id not in org_groups:
                org_groups[org_id] = []
            org_groups[org_id].append(entity)
        
        print(f"🏢 Organizations found: {len(org_groups)}")
        print()
        
        # Analyze each organization
        for org_id, records in org_groups.items():
            print(f"📋 Organization: {org_id}")
            print(f"   Records: {len(records)}")
            
            # Group by Type
            type_groups = {}
            for record in records:
                record_type = record.get('Type', 'unknown')
                if record_type not in type_groups:
                    type_groups[record_type] = []
                type_groups[record_type].append(record)
            
            print(f"   Types found: {list(type_groups.keys())}")
            
            # Check for duplicates by Type
            for record_type, type_records in type_groups.items():
                print(f"   📝 Type '{record_type}': {len(type_records)} records")
                
                # Check for duplicates within each type
                if len(type_records) > 1:
                    print(f"      ⚠️  DUPLICATES DETECTED!")
                    
                    # Group by ProfileName or PermissionSetName
                    name_groups = {}
                    for record in type_records:
                        profile_name = record.get('ProfileName', '')
                        permission_set_name = record.get('PermissionSetName', '')
                        name = profile_name if profile_name else permission_set_name
                        
                        if name not in name_groups:
                            name_groups[name] = []
                        name_groups[name].append(record)
                    
                    for name, name_records in name_groups.items():
                        if len(name_records) > 1:
                            print(f"         🔴 '{name}': {len(name_records)} duplicates")
                            for i, record in enumerate(name_records[:3]):  # Show first 3
                                execution_log = record.get('TaskStatusId', 'unknown')
                                created_at = record.get('CreatedAt', 'unknown')
                                row_key = record.get('RowKey', 'unknown')
                                print(f"            {i+1}. RowKey: {row_key}, ExecutionLog: {execution_log}, Created: {created_at}")
                            if len(name_records) > 3:
                                print(f"            ... and {len(name_records) - 3} more")
                        else:
                            print(f"         ✅ '{name}': 1 record")
                else:
                    print(f"      ✅ No duplicates")
            
            print()
        
        # Check for health check data specifically
        print("🏥 Health Check Data Analysis:")
        print("-" * 40)
        
        health_check_records = []
        for org_id, records in org_groups.items():
            for record in records:
                if record.get('Type') == 'HealthCheck':
                    health_check_records.append(record)
        
        if health_check_records:
            print(f"✅ Health check records found: {len(health_check_records)}")
            for record in health_check_records:
                org_id = record.get('PartitionKey', 'unknown')
                execution_log = record.get('TaskStatusId', 'unknown')
                created_at = record.get('CreatedAt', 'unknown')
                row_key = record.get('RowKey', 'unknown')
                print(f"   📋 Org: {org_id}, ExecutionLog: {execution_log}, Created: {created_at}, RowKey: {row_key}")
        else:
            print("❌ No health check records found!")
            print("   This explains why health check data is not appearing.")
        
        print()
        
        # Summary
        print("📊 SUMMARY:")
        print("-" * 40)
        total_records = len(entities)
        total_orgs = len(org_groups)
        
        # Count duplicates
        duplicate_count = 0
        for org_id, records in org_groups.items():
            type_groups = {}
            for record in records:
                record_type = record.get('Type', 'unknown')
                if record_type not in type_groups:
                    type_groups[record_type] = []
                type_groups[record_type].append(record)
            
            for record_type, type_records in type_groups.items():
                if len(type_records) > 1:
                    name_groups = {}
                    for record in type_records:
                        profile_name = record.get('ProfileName', '')
                        permission_set_name = record.get('PermissionSetName', '')
                        name = profile_name if profile_name else permission_set_name
                        
                        if name not in name_groups:
                            name_groups[name] = []
                        name_groups[name].append(record)
                    
                    for name, name_records in name_groups.items():
                        if len(name_records) > 1:
                            duplicate_count += len(name_records) - 1
        
        print(f"Total Records: {total_records}")
        print(f"Organizations: {total_orgs}")
        print(f"Duplicate Records: {duplicate_count}")
        print(f"Health Check Records: {len(health_check_records)}")
        
        if duplicate_count > 0:
            print(f"🔴 DUPLICATE RATE: {(duplicate_count / total_records * 100):.1f}%")
        else:
            print("✅ No duplicates found!")
        
        if len(health_check_records) == 0:
            print("🔴 HEALTH CHECK ISSUE: No health check data found!")
        
    except Exception as e:
        print(f"❌ Error checking PoliciesResult table: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_policies_result_table()
