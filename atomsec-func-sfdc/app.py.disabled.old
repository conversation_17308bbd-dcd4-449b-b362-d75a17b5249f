"""
Main FastAPI Application

This is the main entry point for the FastAPI application.
It provides a more structured approach to API development with:
- Automatic OpenAPI documentation
- Request validation with Pydantic models
- Dependency injection
- Better error handling
"""

import logging
import json
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

import fastapi
from fastapi import Depends, HTTPException, status, Request, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field

# Import shared modules
from shared.config import is_local_dev
from shared.utils import create_json_response, handle_exception
from shared.auth_utils import require_auth, get_current_user
from shared.data_access import get_table_storage_repository

# Import organization functions
from blueprints.organization import create_organization

# Import routers
from routers.integration_router import router as integration_router

# Configure logging
logger = logging.getLogger('fastapi_app')
logger.setLevel(logging.INFO)

# Create FastAPI app
app = fastapi.FastAPI(
    title="AtomSec API",
    description="AtomSec API for Salesforce security analysis",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    # Ensure no root_path is set that might interfere with routing
    root_path=""
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Update with your frontend URL in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define Pydantic models for request/response validation
class AccountCreate(BaseModel):
    name: str = Field(..., description="Account name")

class AccountResponse(BaseModel):
    ID: str = Field(..., description="Account ID")
    Name: str = Field(..., description="Account name")
    CreatedAt: str = Field(..., description="Creation timestamp")
    IsActive: bool = Field(..., description="Account active status")

class RoleResponse(BaseModel):
    RoleId: str = Field(..., description="Role ID")
    Rolename: str = Field(..., description="Role name")
    Description: str = Field(None, description="Role description")

class ApiResponse(BaseModel):
    success: bool = Field(..., description="Success status")
    statusCode: int = Field(..., description="HTTP status code")
    timestamp: str = Field(..., description="Response timestamp")
    data: Any = Field(None, description="Response data")
    message: Optional[str] = Field(None, description="Response message")

class UserCreate(BaseModel):
    name: str = Field(..., description="User name")
    email: str = Field(..., description="User email")
    phone: Optional[str] = Field(None, description="User phone")
    account_id: str = Field(..., description="Account ID")

class UserRoleAssign(BaseModel):
    role_id: str = Field(..., description="Role ID")

class OrganizationCreate(BaseModel):
    name: str = Field(..., description="Organization name")
    instanceUrl: str = Field(..., description="Salesforce instance URL")
    type: str = Field("Salesforce", description="Organization type")
    description: Optional[str] = Field("", description="Organization description")

# Create API routers for organization
account_router = APIRouter(prefix="/api/accounts", tags=["Accounts"])
role_router = APIRouter(prefix="/api/roles", tags=["Roles"])
user_router = APIRouter(prefix="/api/users", tags=["Users"])
auth_router = APIRouter(prefix="/api/auth", tags=["Authentication"])
health_router = APIRouter(prefix="/api", tags=["Health"])
org_router = APIRouter(prefix="/api", tags=["Organization"])

# Define API routes for accounts
@account_router.get("", response_model=ApiResponse)
async def get_accounts(include_inactive: bool = False):
    """
    Get all accounts endpoint

    This endpoint retrieves all accounts from the database.
    """
    logger.info('Processing get accounts request...')

    try:
        # Get accounts
        accounts = []

        if is_local_dev():
            # Use Azure Table Storage for local development
            account_repo = get_table_storage_repository('accounts')
            if not account_repo:
                logger.error("Account table repository not available")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to get accounts"
                )

            # Query accounts
            filter_query = "IsActive eq true" if not include_inactive else None
            entities = account_repo.query_entities(filter_query)

            # Convert entities to accounts
            for entity in entities:
                account = {
                    "ID": entity.get("RowKey"),
                    "Name": entity.get("Name", ""),
                    "CreatedAt": entity.get("CreatedAt", ""),
                    "IsActive": entity.get("IsActive", True)
                }
                accounts.append(account)
        else:
            # Use SQL Database for production
            # This would be implemented based on your SQL repository
            pass

        # Return accounts
        return {
            "success": True,
            "statusCode": 200,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": accounts
        }
    except Exception as e:
        logger.error(f"Error getting accounts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting accounts: {str(e)}"
        )

@account_router.post("", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
async def create_account(account: AccountCreate):
    """
    Create account endpoint

    This endpoint creates a new account in the database.
    """
    logger.info('Processing create account request...')

    try:
        # Extract account data
        name = account.name

        if not name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Name is required"
            )

        # Create account
        if is_local_dev():
            # Use Azure Table Storage for local development
            account_repo = get_table_storage_repository('accounts')
            if not account_repo:
                logger.error("Account table repository not available")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create account"
                )

            # Generate account ID
            import random
            account_id = str(random.randint(1000, 9999))

            # Create entity
            created_at = datetime.now(timezone.utc)
            entity = {
                "PartitionKey": "account",
                "RowKey": account_id,
                "Name": name,
                "CreatedAt": created_at.isoformat(),
                "IsActive": True
            }

            # Insert entity
            success = account_repo.insert_entity(entity)
            if not success:
                logger.error("Failed to insert account entity")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create account"
                )

            # Return created account
            created_account = {
                "ID": account_id,
                "Name": name,
                "CreatedAt": created_at.isoformat(),
                "IsActive": True
            }

            return {
                "success": True,
                "statusCode": 201,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": created_account
            }
        else:
            # Use SQL Database for production
            # This would be implemented based on your SQL repository
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail="Not implemented for production"
            )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error creating account: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating account: {str(e)}"
        )

@account_router.get("/{account_id}/users", response_model=ApiResponse)
async def get_users_for_account(account_id: str):
    """
    Get users for an account endpoint

    This endpoint retrieves all users for a specific account.
    """
    logger.info(f'Processing get users for account {account_id} request...')

    try:
        users = []

        if is_local_dev():
            # Use Azure Table Storage for local development
            user_repo = get_table_storage_repository('UserAccount')
            if not user_repo:
                logger.error("UserAccount table repository not available")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to get users"
                )

            # Query users by AccountId
            filter_query = f"AccountId eq '{account_id}'"
            entities = user_repo.query_entities(filter_query)

            # Convert entities to users
            for entity in entities:
                user = {
                    "UserId": entity.get("UserId"),
                    "FirstName": entity.get("FirstName", ""),
                    "LastName": entity.get("LastName", ""),
                    "Email": entity.get("Email", ""),
                    "Contact": entity.get("Contact", ""),
                    "State": entity.get("State", ""),
                    "Country": entity.get("Country", ""),
                    "Organization": entity.get("Organization", "")
                }
                users.append(user)
        else:
            # Use SQL Database for production
            # This would be implemented based on your SQL repository
            pass

        return {"data": users}
    except Exception as e:
        logger.error(f"Error in get_users_for_account: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get users"
        )

# Define API routes for roles
@role_router.get("", response_model=ApiResponse)
async def get_roles():
    """
    Get all roles endpoint

    This endpoint retrieves all roles from the database.
    """
    logger.info('Processing get roles request...')

    try:
        # Get roles
        roles = []

        if is_local_dev():
            # Use Azure Table Storage for local development
            role_repo = get_table_storage_repository('roles')
            if not role_repo:
                logger.error("Role table repository not available")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to get roles"
                )

            # Query roles
            entities = role_repo.query_entities()

            # Convert entities to roles
            for entity in entities:
                role = {
                    "RoleId": entity.get("RowKey"),
                    "Rolename": entity.get("Rolename", ""),
                    "Description": entity.get("Description", "")
                }
                roles.append(role)
        else:
            # Use SQL Database for production
            # This would be implemented based on your SQL repository
            pass

        # Return roles
        return {
            "success": True,
            "statusCode": 200,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": roles
        }
    except Exception as e:
        logger.error(f"Error getting roles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting roles: {str(e)}"
        )

# Define API routes for users
@user_router.post("", response_model=ApiResponse, status_code=status.HTTP_201_CREATED)
async def create_user(user: UserCreate):
    """
    Create user endpoint

    This endpoint creates a new user in the database.
    """
    logger.info('Processing create user request...')

    try:
        # Extract user data
        name = user.name
        email = user.email
        phone = user.phone
        account_id = user.account_id

        if not name or not email or not account_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Name, email, and account_id are required"
            )

        # Create user
        if is_local_dev():
            # Use Azure Table Storage for local development
            user_repo = get_table_storage_repository('users')
            if not user_repo:
                logger.error("User table repository not available")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create user"
                )

            # Check if user already exists
            filter_query = f"Email eq '{email}'"
            entities = user_repo.query_entities(filter_query)

            if entities:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User with this email already exists"
                )

            # Generate user ID
            import random
            user_id = str(random.randint(1000, 9999))

            # Create entity
            created_at = datetime.now(timezone.utc)
            entity = {
                "PartitionKey": "user",
                "RowKey": user_id,
                "Name": name,
                "Email": email,
                "Phone": phone or "",
                "AccountId": account_id,
                "CreatedAt": created_at.isoformat(),
                "IsActive": True
            }

            # Insert entity
            success = user_repo.insert_entity(entity)
            if not success:
                logger.error("Failed to insert user entity")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create user"
                )

            # Return created user
            created_user = {
                "UserId": user_id,
                "Name": name,
                "Email": email,
                "Phone": phone or "",
                "AccountId": account_id,
                "CreatedAt": created_at.isoformat(),
                "IsActive": True,
                "Roles": []
            }

            return {
                "success": True,
                "statusCode": 201,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": created_user
            }
        else:
            # Use SQL Database for production
            # This would be implemented based on your SQL repository
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail="Not implemented for production"
            )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating user: {str(e)}"
        )

@user_router.post("/{user_id}/roles", response_model=ApiResponse)
async def assign_role_to_user(user_id: str, role_assignment: UserRoleAssign):
    """
    Assign role to user endpoint

    This endpoint assigns a role to a user.
    """
    logger.info(f'Processing assign role to user {user_id} request...')

    try:
        # Extract role data
        role_id = role_assignment.role_id

        if not role_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role ID is required"
            )

        # Assign role to user
        if is_local_dev():
            # Use Azure Table Storage for local development
            role_repo = get_table_storage_repository('user_roles')
            if not role_repo:
                logger.error("User role table repository not available")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to assign role"
                )

            # Check if role assignment already exists
            filter_query = f"UserId eq '{user_id}' and RoleId eq '{role_id}'"
            entities = role_repo.query_entities(filter_query)

            if entities:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Role already assigned to user"
                )

            # Create entity
            import uuid
            assignment_id = str(uuid.uuid4())
            entity = {
                "PartitionKey": "user_role",
                "RowKey": assignment_id,
                "UserId": user_id,
                "RoleId": role_id,
                "AssignedAt": datetime.now(timezone.utc).isoformat()
            }

            # Insert entity
            success = role_repo.insert_entity(entity)
            if not success:
                logger.error("Failed to insert user role entity")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to assign role"
                )

            # Get role details
            role_details_repo = get_table_storage_repository('roles')
            role_details = None
            if role_details_repo:
                filter_query = f"RowKey eq '{role_id}'"
                role_entities = role_details_repo.query_entities(filter_query)
                if role_entities:
                    role_details = {
                        "RoleId": role_id,
                        "Rolename": role_entities[0].get("Rolename", ""),
                        "Description": role_entities[0].get("Description", "")
                    }

            # Return assigned role
            return {
                "success": True,
                "statusCode": 200,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": {
                    "UserId": user_id,
                    "Role": role_details or {"RoleId": role_id}
                }
            }
        else:
            # Use SQL Database for production
            # This would be implemented based on your SQL repository
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail="Not implemented for production"
            )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error assigning role to user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error assigning role to user: {str(e)}"
        )

# Define API routes for health
@health_router.get("/health", response_model=dict)
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

# Root route that redirects to the docs
@app.get("/")
async def root():
    """Redirect to API documentation"""
    return RedirectResponse(url="/api/docs")

# Define API routes for organization
@org_router.post("/connect-org", response_model=ApiResponse, status_code=status.HTTP_200_OK)
async def connect_org(org: OrganizationCreate):
    """
    Connect a new organization endpoint

    This endpoint creates a new organization in the database.
    """
    logger.info('Processing connect organization request...')

    try:
        # Extract organization data
        name = org.name
        instance_url = org.instanceUrl
        org_type = org.type
        description = org.description

        # Get current user from token (in a real implementation)
        # For now, we'll use a placeholder email
        user_email = "<EMAIL>"

        # Create organization using the existing function
        org_id = create_organization(name, instance_url, org_type, description, user_email)
        if not org_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to connect organization"
            )

        # Return organization ID
        response_data = {
            "id": org_id,
            "name": name,
            "instanceUrl": instance_url,
            "type": org_type,
            "description": description,
            "isActive": True,
            "lastScan": "",
            "createdAt": datetime.now(timezone.utc).isoformat(),
            "userEmail": user_email
        }

        return {
            "success": True,
            "statusCode": 200,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": response_data
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error connecting organization: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error connecting organization: {str(e)}"
        )

# Register routers
app.include_router(account_router)
app.include_router(role_router)
app.include_router(user_router)
app.include_router(health_router)
app.include_router(org_router)
app.include_router(integration_router)

# Exception handler for all unhandled exceptions
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "statusCode": 500,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "message": "An unexpected error occurred"
        }
    )

# Run the application with Uvicorn when executed directly
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=7071)
