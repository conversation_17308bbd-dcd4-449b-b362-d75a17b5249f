"""
Integration Router

This module provides a FastAPI router for integration-related endpoints.
It consolidates the following endpoints:
- GET /api/integrations
- GET /api/integration/{tenant_url}/guest-user-risks
- GET /api/integration/{tenant_url}/health-check
- GET /api/integration/{tenant_url}/overview
- GET /api/integration/{tenant_url}/pmd-issues
- GET /api/integration/{tenant_url}/profiles
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Union

from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# Import shared modules
from shared.config import is_local_dev
from shared.utils import create_json_response, handle_exception
from shared.auth_utils import require_auth, get_current_user
from shared.db_service_client import get_db_client

# Import background processor
from shared.background_processor import BackgroundProcessor
from shared.background_processor import (
    TASK_TYPE_OVERVIEW,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_PROFILES,
)

# Configure logging
logger = logging.getLogger('integration_router')
logger.setLevel(logging.INFO)

# Create router
# Use a prefix without /api/ since the frontend proxy already adds /api/
router = APIRouter(prefix="/integration", tags=["Integration"])

# Define response models
class IntegrationResponse(BaseModel):
    id: str
    name: str
    tenantUrl: str
    type: str
    description: Optional[str] = None
    environment: Optional[str] = None
    isActive: bool
    lastScan: Optional[str] = None
    createdAt: Optional[str] = None
    userEmail: Optional[str] = None
    healthScore: Optional[str] = None

class IntegrationsResponse(BaseModel):
    integrations: List[IntegrationResponse]
    count: int

class DataStatusResponse(BaseModel):
    dataStatus: str
    message: Optional[str] = None
    timestamp: str
    tenantUrl: str
    taskId: Optional[str] = None

# Function get_integration_by_tenant_url has been removed in favor of direct integration ID lookup

# Define routes
@router.get("/integrations", response_model=IntegrationsResponse)
async def get_integrations(
    include_inactive: bool = Query(False, description="Include inactive integrations"),
    integration_type: Optional[str] = Query(None, description="Filter by integration type"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get all integrations for the current user's account
    """
    logger.info("Getting integrations...")

    try:
        db_client = get_db_client()
        if not db_client:
            logger.error("DB service client not available")
            raise HTTPException(status_code=500, detail="Database service not available")

        # Get integrations using new DB service client
        integrations = db_client.get_integrations(
            include_inactive=include_inactive,
            integration_type=integration_type
        )

        # Return integrations
        return {
            "integrations": integrations,
            "count": len(integrations)
        }
    except Exception as e:
        logger.error(f"Error in get_integrations: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{integration_id}/overview")
async def get_integration_overview(
    integration_id: str = Path(..., description="Integration ID"),
    force_refresh: bool = Query(False, description="Force refresh data"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get integration overview
    """
    logger.info("Getting integration overview...")
    logger.info(f"Integration ID: {integration_id}")
    logger.info(f"Force refresh: {force_refresh}")

    try:
        db_client = get_db_client()
        if not db_client:
            logger.error("DB service client not available")
            raise HTTPException(status_code=500, detail="Database service not available")

                # Get integration by ID
        integration = db_client.get_integration_by_id(integration_id)
                if not integration:
                    logger.warning(f"Integration not found for ID: {integration_id}")
                    raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")

            logger.info(f"Found integration: {integration}")

        # Check if integration is active
        if not integration.get("isActive", False):
            raise HTTPException(status_code=400, detail="Integration is not active")

        # Try to get data from database first
        if not force_refresh:
            # Get overview data using DB service client
            overview_data = db_client.get_integration_overview(integration_id)
            
            if overview_data:
                logger.info("Found overview data in database")
                        return {
                    "dataStatus": "available",
                    "data": overview_data,
                    "timestamp": overview_data.get("timestamp", datetime.now().isoformat()),
                    "tenantUrl": integration.get("tenantUrl", ""),
                    "message": "Data retrieved from database"
                }

        # If no data found or force refresh requested, trigger background fetch
        logger.info("No data found or force refresh requested, triggering background fetch...")
        
        # Initialize background processor
        background_processor = BackgroundProcessor()

        # Trigger overview task
        task_id = await background_processor.trigger_task(
            task_type=TASK_TYPE_OVERVIEW,
            integration_id=integration_id,
            force_refresh=force_refresh
        )
        
        if task_id:
            logger.info(f"Triggered overview task with ID: {task_id}")
                    return {
                "dataStatus": "processing",
                "message": "Data fetch initiated",
                        "timestamp": datetime.now().isoformat(),
                "tenantUrl": integration.get("tenantUrl", ""),
                "taskId": task_id
                    }
            else:
            logger.error("Failed to trigger overview task")
            raise HTTPException(status_code=500, detail="Failed to trigger data fetch")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_integration_overview: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{integration_id}/health-check")
async def get_integration_health_check(
    integration_id: str = Path(..., description="Integration ID"),
    force_refresh: bool = Query(False, description="Force refresh data"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get integration health check
    """
    logger.info("Getting integration health check...")
    logger.info(f"Integration ID: {integration_id}")
    logger.info(f"Force refresh: {force_refresh}")

    try:
        db_client = get_db_client()
        if not db_client:
            logger.error("DB service client not available")
            raise HTTPException(status_code=500, detail="Database service not available")

                # Get integration by ID
        integration = db_client.get_integration_by_id(integration_id)
                if not integration:
                    logger.warning(f"Integration not found for ID: {integration_id}")
                    raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")

            logger.info(f"Found integration: {integration}")

        # Check if integration is active
        if not integration.get("isActive", False):
            raise HTTPException(status_code=400, detail="Integration is not active")

        # Try to get data from database first
        if not force_refresh:
            # Get health check data using DB service client
            health_check_data = db_client.get_integration_health_check(integration_id)
            
            if health_check_data:
                logger.info("Found health check data in database")
                        return {
                        "dataStatus": "available",
                    "data": health_check_data,
                    "timestamp": health_check_data.get("timestamp", datetime.now().isoformat()),
                    "tenantUrl": integration.get("tenantUrl", ""),
                    "message": "Data retrieved from database"
                }

        # If no data found or force refresh requested, trigger background fetch
        logger.info("No data found or force refresh requested, triggering background fetch...")
        
        # Initialize background processor
        background_processor = BackgroundProcessor()

        # Trigger health check task
        task_id = await background_processor.trigger_task(
                task_type=TASK_TYPE_HEALTH_CHECK,
            integration_id=integration_id,
            force_refresh=force_refresh
            )

        if task_id:
            logger.info(f"Triggered health check task with ID: {task_id}")
            return {
                "dataStatus": "processing",
                "message": "Data fetch initiated",
                "timestamp": datetime.now().isoformat(),
                "tenantUrl": integration.get("tenantUrl", ""),
                "taskId": task_id
            }
        else:
            logger.error("Failed to trigger health check task")
            raise HTTPException(status_code=500, detail="Failed to trigger data fetch")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_integration_health_check: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{integration_id}/profiles")
async def get_integration_profiles(
    integration_id: str = Path(..., description="Integration ID"),
    force_refresh: bool = Query(False, description="Force refresh data"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get integration profiles
    """
    logger.info("Getting integration profiles...")
    logger.info(f"Integration ID: {integration_id}")
    logger.info(f"Force refresh: {force_refresh}")

    try:
        db_client = get_db_client()
        if not db_client:
            logger.error("DB service client not available")
            raise HTTPException(status_code=500, detail="Database service not available")

                # Get integration by ID
        integration = db_client.get_integration_by_id(integration_id)
                if not integration:
                    logger.warning(f"Integration not found for ID: {integration_id}")
                    raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")

            logger.info(f"Found integration: {integration}")

        # Check if integration is active
        if not integration.get("isActive", False):
            raise HTTPException(status_code=400, detail="Integration is not active")

        # Try to get data from database first
        if not force_refresh:
            # Get profiles data using DB service client
            profiles_data = db_client.get_integration_profiles(integration_id)
            
            if profiles_data:
                logger.info("Found profiles data in database")
                        return {
                    "dataStatus": "available",
                    "data": profiles_data,
                    "timestamp": profiles_data.get("timestamp", datetime.now().isoformat()),
                    "tenantUrl": integration.get("tenantUrl", ""),
                    "message": "Data retrieved from database"
                }

        # If no data found or force refresh requested, trigger background fetch
        logger.info("No data found or force refresh requested, triggering background fetch...")

        # Initialize background processor
        background_processor = BackgroundProcessor()
        
        # Trigger profiles task
        task_id = await background_processor.trigger_task(
            task_type=TASK_TYPE_PROFILES,
            integration_id=integration_id,
            force_refresh=force_refresh
        )
        
        if task_id:
            logger.info(f"Triggered profiles task with ID: {task_id}")
                    return {
                "dataStatus": "processing",
                "message": "Data fetch initiated",
                        "timestamp": datetime.now().isoformat(),
                "tenantUrl": integration.get("tenantUrl", ""),
                "taskId": task_id
                    }
            else:
            logger.error("Failed to trigger profiles task")
            raise HTTPException(status_code=500, detail="Failed to trigger data fetch")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_integration_profiles: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

# @router.get("/{integration_id}/guest-user-risks") // Removed Guest User Risks endpoint
# async def get_integration_guest_user_risks(
#     integration_id: str = Path(..., description="Integration ID"),
#     force_refresh: bool = Query(False, description="Force refresh data"),
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     Get integration guest user profile risks
#     """
#     logger.info("Getting integration guest user profile risks...")
#     logger.info(f"Integration ID: {integration_id}")
#     logger.info(f"Force refresh: {force_refresh}")
# 
#     try:
#         # Get integration by ID
#         if is_local_dev():
#             # For local development, try to get integration from Azure Table Storage
#             try:
#                 integration_repo = get_table_storage_repository("Integrations")
#                 if not integration_repo:
#                     logger.error("Integration table repository not available")
#                     raise HTTPException(status_code=500, detail="Integration repository not available")
# 
#                 # Get integration by ID
#                 integration = integration_repo.get_entity("integration", integration_id)
#                 if not integration:
#                     logger.warning(f"Integration not found for ID: {integration_id}")
#                     raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")
# 
#                 # Convert to dictionary
#                 integration = {
#                     "id": integration.get("RowKey"),
#                     "name": integration.get("Name", ""),
#                     "tenantUrl": integration.get("TenantUrl", ""),
#                     "type": integration.get("Type", "Salesforce"),
#                     "description": integration.get("Description", ""),
#                     "environment": integration.get("Environment", "production"),
#                     "isActive": integration.get("IsActive", True),
#                     "lastScan": integration.get("LastScan", ""),
#                     "createdAt": integration.get("CreatedAt", ""),
#                     "userEmail": integration.get("UserEmail", "")
#                 }
#                 logger.info(f"Found integration: {integration}")
#             except Exception as e:
#                 logger.error(f"Error getting integration from Azure Table Storage: {str(e)}")
#                 raise HTTPException(status_code=500, detail=f"Error getting integration: {str(e)}")
#         else:
#             # Use SQL Database for production
#             integration_repo = get_integration_sql_repo()
#             if not integration_repo:
#                 logger.error("Integration SQL repository not available")
#                 raise HTTPException(status_code=500, detail="Integration repository not available")
# 
#             # Get integration by ID
#             query = """
#             SELECT Id, Name, TenantUrl, Type, Description, Environment, IsActive, LastScan, CreatedAt, UserEmail
#             FROM App_Organization
#             WHERE Id = ?
#             """
#             results = integration_repo.execute_query(query, (integration_id,))
# 
#             if not results or len(results) == 0:
#                 logger.warning(f"Integration not found for ID: {integration_id}")
#                 raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")
# 
#             # Convert to dictionary
#             integration = {
#                 "id": results[0][0],
#                 "name": results[0][1],
#                 "tenantUrl": results[0][2],
#                 "type": results[0][3],
#                 "description": results[0][4],
#                 "environment": results[0][5],
#                 "isActive": results[0][6],
#                 "lastScan": results[0][7].isoformat() if results[0][7] else None,
#                 "createdAt": results[0][8].isoformat() if results[0][8] else None,
#                 "userEmail": results[0][9]
#             }
#             logger.info(f"Found integration: {integration}")
# 
#         # Check if integration is active
#         if not integration.get("isActive", False):
#             raise HTTPException(status_code=400, detail="Integration is not active")
# 
#         # Try to get data from database first
#         if not force_refresh:
#             # Get integration ID
#             integration_id = integration.get("id")
# 
#             # Query the database for guest user risks data
#             if is_local_dev():
#                 # For local development, try to get data from Azure Table Storage
#                 try:
#                     # Initialize the GuestUserRisks table repository
#                     guest_user_risks_repo = get_table_storage_repository("GuestUserRisks")
#                     if not guest_user_risks_repo:
#                         logger.error("GuestUserRisks table repository not available")
#                         return {
#                             "dataStatus": "empty",
#                             "message": "No guest user profile risks data available. Please sync to fetch data.",
#                             "timestamp": datetime.now().isoformat(),
#                             "tenantUrl": integration.get("tenantUrl", "")
#                         }
# 
#                     # Query for the guest user risks data for this organization
#                     filter_query = f"PartitionKey eq '{integration_id}'"
#                     logger.info(f"Querying GuestUserRisks table with filter: {filter_query}")
#                     entities = guest_user_risks_repo.query_entities(filter_query)
#                     logger.info(f"Found {len(entities) if entities else 0} guest user risks entities")
# 
#                     if not entities or len(entities) == 0:
#                         # No data found, return a response indicating data needs to be fetched
#                         logger.info(f"No guest user risks data found for integration {integration_id}")
#                         return {
#                             "dataStatus": "empty",
#                             "message": "No guest user profile risks data available. Please sync to fetch data.",
#                             "timestamp": datetime.now().isoformat(),
#                             "tenantUrl": integration.get("tenantUrl", "")
#                         }
# 
#                     # Extract risks from the entities
#                     risks = []
#                     last_updated = None
# 
#                     for entity in entities:
#                         risk = {
#                             "riskType": entity.get("RiskType", "UNKNOWN_RISK"),
#                             "description": entity.get("Description", "Unknown Risk"),
#                             "impact": entity.get("Impact", "Unknown"),
#                             "recommendation": entity.get("Recommendation", "Unknown")
#                         }
#                         risks.append(risk)
# 
#                         # Track the latest update time
#                         entity_timestamp = entity.get("Timestamp")
#                         if entity_timestamp and (last_updated is None or entity_timestamp > last_updated):
#                             last_updated = entity_timestamp
# 
#                     # Create the response data
#                     guest_user_risks_data = {
#                         "dataStatus": "available",
#                         "risks": risks,
#                         "lastUpdated": last_updated.isoformat() if isinstance(last_updated, datetime) else (datetime.now().isoformat() if last_updated is None else last_updated)
#                     }
#                     logger.info(f"Created guest user risks data response with {len(risks)} risks")
#                 except Exception as e:
#                     logger.error(f"Error retrieving guest user risks data from Azure Table Storage: {str(e)}")
#                     # Return empty data response
#                     return {
#                         "dataStatus": "empty",
#                         "message": "No guest user profile risks data available. Please sync to fetch data.",
#                         "timestamp": datetime.now().isoformat(),
#                         "tenantUrl": integration.get("tenantUrl", "")
#                     }
#             else:
#                 # Use SQL Database for production
#                 guest_user_risks_repo = get_guest_user_risks_repo()
#                 if not guest_user_risks_repo:
#                     logger.error("Guest user risks SQL repository not available")
#                     raise HTTPException(status_code=500, detail="Guest user risks repository not available")
# 
#                 # Query for the latest guest user risks data
#                 query = """
#                 SELECT g.RiskType, g.Description, g.Impact, g.Recommendation, g.LastUpdated
#                 FROM App_GuestUserRisks g
#                 INNER JOIN App_ExecutionLog e ON g.ExecutionLogId = e.Id
#                 WHERE g.OrgId = ? AND e.Status = 'Completed'
#                 ORDER BY e.StartTime DESC
#                 """
#                 results = guest_user_risks_repo.execute_query(query, (integration_id,))
# 
#                 if not results or len(results) == 0:
#                     # No data found, return a response indicating data needs to be fetched
#                     return {
#                         "dataStatus": "empty",
#                         "message": "No guest user profile risks data available. Please sync to fetch data.",
#                         "timestamp": datetime.now().isoformat(),
#                         "tenantUrl": integration.get("tenantUrl", "")
#                     }
# 
#                 # Process risks
#                 risks = []
#                 last_updated = None
#                 for risk_row in results:
#                     risk_type, description, impact, recommendation, updated_at = risk_row
#                     risks.append({
#                         "riskType": risk_type,
#                         "description": description,
#                         "impact": impact,
#                         "recommendation": recommendation
#                     })
#                     if not last_updated:
#                         last_updated = updated_at
# 
#                 # Create the response data
#                 guest_user_risks_data = {
#                     "dataStatus": "available",
#                     "risks": risks,
#                     "lastUpdated": last_updated.isoformat() if isinstance(last_updated, datetime) else last_updated
#                 }
# 
#             # Return the guest user risks data
#             return guest_user_risks_data
# 
#         # If force refresh or no data in database, trigger a background fetch
#         # Create a background processor
#         # processor = BackgroundProcessor()
# 
#         # Enqueue a task
#         # task_id = processor.enqueue_task(
#         #     task_type=TASK_TYPE_GUEST_USER_RISKS,
#         #     org_id=integration.get("id"),
#         #     user_id=current_user.get("id")
#         # )
# 
#         # if not task_id:
#         #     raise HTTPException(status_code=500, detail="Failed to enqueue task")
# 
#         # Return pending status
#         # response_data = {
#         #     "dataStatus": "pending",
#         #     "message": "The guest user risks data will be available shortly. Please refresh to check if data is available.",
#         #     "timestamp": datetime.now().isoformat(),
#         #     "tenantUrl": integration.get("tenantUrl", ""),
#         #     "taskId": task_id
#         # }
#         response_data = {
#             "dataStatus": "empty",
#             "message": "Development in progress.", # User requested message
#             "timestamp": datetime.now().isoformat(),
#             "tenantUrl": integration.get("tenantUrl", ""),
#             "taskId": None # Or an empty string
#         }
# 
#         return response_data
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"Error in get_integration_guest_user_risks: {str(e)}")
#         import traceback
#         logger.error(f"Traceback: {traceback.format_exc()}")
#         raise HTTPException(status_code=500, detail=str(e))

# @router.get("/{integration_id}/pmd-issues") // Removed PMD Issues endpoint
# async def get_integration_pmd_issues(
#     integration_id: str = Path(..., description="Integration ID"),
#     force_refresh: bool = Query(False, description="Force refresh data"),
#     current_user: Dict[str, Any] = Depends(get_current_user)
# ):
#     """
#     Get integration PMD issues (development and security issues)
#     """
#     logger.info("Getting integration PMD issues...")
#     logger.info(f"Integration ID: {integration_id}")
#     logger.info(f"Force refresh: {force_refresh}")
# 
#     try:
#         # Get integration by ID
#         if is_local_dev():
#             # For local development, try to get integration from Azure Table Storage
#             try:
#                 integration_repo = get_table_storage_repository("Integrations")
#                 if not integration_repo:
#                     logger.error("Integration table repository not available")
#                     raise HTTPException(status_code=500, detail="Integration repository not available")
# 
#                 # Get integration by ID
#                 integration = integration_repo.get_entity("integration", integration_id)
#                 if not integration:
#                     logger.warning(f"Integration not found for ID: {integration_id}")
#                     raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")
# 
#                 # Convert to dictionary
#                 integration = {
#                     "id": integration.get("RowKey"),
#                     "name": integration.get("Name", ""),
#                     "tenantUrl": integration.get("TenantUrl", ""),
#                     "type": integration.get("Type", "Salesforce"),
#                     "description": integration.get("Description", ""),
#                     "environment": integration.get("Environment", "production"),
#                     "isActive": integration.get("IsActive", True),
#                     "lastScan": integration.get("LastScan", ""),
#                     "createdAt": integration.get("CreatedAt", ""),
#                     "userEmail": integration.get("UserEmail", "")
#                 }
#                 logger.info(f"Found integration: {integration}")
#             except Exception as e:
#                 logger.error(f"Error getting integration from Azure Table Storage: {str(e)}")
#                 raise HTTPException(status_code=500, detail=f"Error getting integration: {str(e)}")
#         else:
#             # Use SQL Database for production
#             integration_repo = get_integration_sql_repo()
#             if not integration_repo:
#                 logger.error("Integration SQL repository not available")
#                 raise HTTPException(status_code=500, detail="Integration repository not available")
# 
#             # Get integration by ID
#             query = """
#             SELECT Id, Name, TenantUrl, Type, Description, Environment, IsActive, LastScan, CreatedAt, UserEmail
#             FROM App_Organization
#             WHERE Id = ?
#             """
#             results = integration_repo.execute_query(query, (integration_id,))
# 
#             if not results or len(results) == 0:
#                 logger.warning(f"Integration not found for ID: {integration_id}")
#                 raise HTTPException(status_code=404, detail=f"Integration not found for ID: {integration_id}")
# 
#             # Convert to dictionary
#             integration = {
#                 "id": results[0][0],
#                 "name": results[0][1],
#                 "tenantUrl": results[0][2],
#                 "type": results[0][3],
#                 "description": results[0][4],
#                 "environment": results[0][5],
#                 "isActive": results[0][6],
#                 "lastScan": results[0][7].isoformat() if results[0][7] else None,
#                 "createdAt": results[0][8].isoformat() if results[0][8] else None,
#                 "userEmail": results[0][9]
#             }
#             logger.info(f"Found integration: {integration}")
# 
#         # Check if integration is active
#         if not integration.get("isActive", False):
#             raise HTTPException(status_code=400, detail="Integration is not active")
# 
#         # Check if we have PMD issues data in the database
#         pmd_issues_data = None
#         if not force_refresh and not is_local_dev():
#             pmd_issues_repo = get_pmd_issues_repo()
#             if pmd_issues_repo:
#                 # Query for PMD issues data
#                 query = """
#                 SELECT Id, OrgId, Data, CreatedAt, UpdatedAt
#                 FROM App_PMDIssues
#                 WHERE OrgId = ?
#                 ORDER BY UpdatedAt DESC
#                 """
#                 results = pmd_issues_repo.execute_query(query, (integration.get("id"),))
# 
#                 if results and len(results) > 0:
#                     # Parse the data
#                     try:
#                         pmd_issues_json = results[0][2]  # Data column
#                         pmd_issues_data = json.loads(pmd_issues_json)
# 
#                         # Add metadata
#                         pmd_issues_data["lastUpdated"] = results[0][4]  # UpdatedAt column
#                         pmd_issues_data["tenantUrl"] = integration.get("tenantUrl", "")
#                         pmd_issues_data["dataStatus"] = "available"
# 
#                         logger.info(f"Found PMD issues data for integration {integration.get('id')}")
#                     except Exception as parse_error:
#                         logger.error(f"Error parsing PMD issues data: {str(parse_error)}")
#                         pmd_issues_data = None
# 
#         # If we're in local development mode and don't have data, try to get it from Azure Table Storage
#         if is_local_dev() and not pmd_issues_data:
#             try:
#                 # Get integration ID
#                 org_id = integration.get("id")
# 
#                 # Initialize the PMDIssues table repository
#                 pmd_issues_repo = get_table_storage_repository("PMDIssues")
#                 if not pmd_issues_repo:
#                     logger.error("PMDIssues table repository not available")
#                     return {
#                         "dataStatus": "empty",
#                         "message": "No PMD issues data available. Please sync to fetch data.",
#                         "timestamp": datetime.now().isoformat(),
#                         "tenantUrl": integration.get("tenantUrl", "")
#                     }
# 
#                 # Query for the PMD issues data for this organization
#                 filter_query = f"PartitionKey eq '{org_id}'"
#                 logger.info(f"Querying PMDIssues table with filter: {filter_query}")
#                 entities = pmd_issues_repo.query_entities(filter_query)
#                 logger.info(f"Found {len(entities) if entities else 0} PMD issues entities")
# 
#                 if entities and len(entities) > 0:
#                     # Sort entities by timestamp (RowKey) to get the latest
#                     sorted_entities = sorted(entities, key=lambda x: x.get('RowKey', ''), reverse=True)
#                     latest_entity = sorted_entities[0]
#                     logger.info(f"Latest entity RowKey: {latest_entity.get('RowKey')}")
# 
#                     # Parse the data
#                     try:
#                         pmd_issues_json = latest_entity.get('Data')
#                         if pmd_issues_json:
#                             pmd_issues_data = json.loads(pmd_issues_json)
# 
#                             # Add metadata
#                             pmd_issues_data["lastUpdated"] = latest_entity.get('Timestamp', datetime.now().isoformat())
#                             pmd_issues_data["tenantUrl"] = integration.get("tenantUrl", "")
#                             pmd_issues_data["dataStatus"] = "available"
# 
#                             logger.info(f"Found PMD issues data for integration {org_id}")
#                         else:
#                             logger.warning("PMD issues entity found but Data field is empty")
#                     except Exception as parse_error:
#                         logger.error(f"Error parsing PMD issues data: {str(parse_error)}")
#                 else:
#                     # No data found, return a response indicating data needs to be fetched
#                     return {
#                         "dataStatus": "empty",
#                         "message": "No PMD issues data available. Please sync to fetch data.",
#                         "timestamp": datetime.now().isoformat(),
#                         "tenantUrl": integration.get("tenantUrl", "")
#                     }
#             except Exception as e:
#                 logger.error(f"Error retrieving PMD issues data from Azure Table Storage: {str(e)}")
#                 # Return empty data response
#                 return {
#                     "dataStatus": "empty",
#                     "message": "No PMD issues data available. Please sync to fetch data.",
#                     "timestamp": datetime.now().isoformat(),
#                     "tenantUrl": integration.get("tenantUrl", "")
#                 }
# 
#         # If we have data, return it
#         if pmd_issues_data:
#             logger.info("Returning PMD issues data")
#             return pmd_issues_data
# 
#         # If force refresh or no data in database, trigger a background fetch
#         # Create a background processor
#         # processor = BackgroundProcessor()
# 
#         # Enqueue a task
#         # task_id = processor.enqueue_task(
#         #     task_type=TASK_TYPE_PMD_ISSUES,
#         #     org_id=integration.get("id"),
#         #     user_id=current_user.get("id")
#         # )
# 
#         # if not task_id:
#         #     raise HTTPException(status_code=500, detail="Failed to enqueue task")
# 
#         # Return pending status
#         # response_data = {
#         #     "dataStatus": "pending",
#         #     "message": "The PMD issues data will be available shortly. Please refresh to check if data is available.",
#         #     "timestamp": datetime.now().isoformat(),
#         #     "tenantUrl": integration.get("tenantUrl", ""),
#         #     "taskId": task_id
#         # }
#         response_data = {
#             "dataStatus": "empty",
#             "message": "Development in progress.", # User requested message
#             "timestamp": datetime.now().isoformat(),
#             "tenantUrl": integration.get("tenantUrl", ""),
#             "taskId": None # Or an empty string
#         }
# 
#         return response_data
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"Error in get_integration_pmd_issues: {str(e)}")
#         import traceback
#         logger.error(f"Traceback: {traceback.format_exc()}")
#         raise HTTPException(status_code=500, detail=str(e))
