#!/usr/bin/env python3
"""
Test Endpoints Locally Script

This script tests the health and info endpoints locally to see if they work.
"""

import sys
import os
import json
import requests
import time

def test_endpoint(url: str, name: str) -> bool:
    """Test a specific endpoint"""
    print(f"\n=== TESTING {name.upper()} ENDPOINT ===")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=30)
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response JSON: {json.dumps(data, indent=2)}")
            except:
                print(f"Response Text: {response.text}")
            print(f"✅ {name} endpoint working")
            return True
        else:
            print(f"Response Text: {response.text}")
            print(f"❌ {name} endpoint failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {name} endpoint error: {e}")
        return False

def test_production_endpoints():
    """Test production endpoints"""
    print("=== TESTING PRODUCTION ENDPOINTS ===")
    
    base_url = "https://func-atomsec-sfdc-dev.azurewebsites.net"
    
    endpoints = [
        (f"{base_url}/api/test", "Test"),
        (f"{base_url}/api/health", "Health"),
        (f"{base_url}/health", "Health (no prefix)"),
        (f"{base_url}/api/info", "Info"),
        (f"{base_url}/info", "Info (no prefix)")
    ]
    
    results = []
    for url, name in endpoints:
        success = test_endpoint(url, name)
        results.append((name, success))
    
    print("\n=== ENDPOINT TEST SUMMARY ===")
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{name}: {status}")
    
    return results

def test_staging_endpoints():
    """Test staging endpoints"""
    print("\n=== TESTING STAGING ENDPOINTS ===")
    
    base_url = "https://func-atomsec-sfdc-dev-stage.azurewebsites.net"
    
    endpoints = [
        (f"{base_url}/api/test", "Test"),
        (f"{base_url}/api/health", "Health"),
        (f"{base_url}/health", "Health (no prefix)"),
        (f"{base_url}/api/info", "Info"),
        (f"{base_url}/info", "Info (no prefix)")
    ]
    
    results = []
    for url, name in endpoints:
        success = test_endpoint(url, name)
        results.append((name, success))
    
    print("\n=== STAGING ENDPOINT TEST SUMMARY ===")
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{name}: {status}")
    
    return results

def main():
    """Main function"""
    print("=== ENDPOINT TESTING SCRIPT ===")
    
    # Test production endpoints
    production_results = test_production_endpoints()
    
    # Test staging endpoints
    staging_results = test_staging_endpoints()
    
    # Overall summary
    print("\n=== OVERALL SUMMARY ===")
    
    production_passed = sum(1 for _, success in production_results if success)
    production_total = len(production_results)
    
    staging_passed = sum(1 for _, success in staging_results if success)
    staging_total = len(staging_results)
    
    print(f"Production: {production_passed}/{production_total} endpoints working")
    print(f"Staging: {staging_passed}/{staging_total} endpoints working")
    
    if production_passed == 0 and staging_passed == 0:
        print("❌ No endpoints are working - function app may not be deployed properly")
        return False
    elif production_passed > 0:
        print("✅ Production has working endpoints")
        return True
    else:
        print("⚠️ Only staging has working endpoints")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 