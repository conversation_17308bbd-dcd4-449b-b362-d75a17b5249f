#!/usr/bin/env python3
"""
Basic Deployment Tests Script

This script runs basic deployment readiness tests without heavy dependencies.
It's designed to run in the Azure DevOps pipeline to verify deployment success.

Usage:
    python scripts/run_deployment_tests.py
"""

import sys
import subprocess
import os

def run_pytest():
    """Run pytest for basic deployment tests"""
    try:
        print("🔍 Running basic deployment readiness tests...")
        
        # Check if pytest is available
        try:
            import pytest
        except ImportError:
            print("⚠️  pytest not available, installing...")
            subprocess.run([sys.executable, "-m", "pip", "install", "pytest"], check=True)
        
        # Run pytest
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/test_deployment_simple.py", 
            "-v", "--tb=short"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Deployment verification tests passed")
            print(result.stdout)
            return True
        else:
            print("⚠️  Some verification tests failed")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error running deployment tests: {str(e)}")
        return False

def main():
    """Main function"""
    print("🚀 Running deployment verification tests...")
    
    success = run_pytest()
    
    if success:
        print("✅ All deployment verification tests passed")
        sys.exit(0)
    else:
        print("⚠️  Some verification tests failed")
        print("Continuing with deployment as tests are informational")
        sys.exit(0)  # Don't fail the pipeline for basic tests

if __name__ == "__main__":
    main() 