"""
<PERSON><PERSON><PERSON> to initialize PolicyRuleSetting table with test data for Azurite/Azure Table Storage.
Enables or disables specific tasks for a user/integration.
"""

import os
from datetime import datetime
from shared.db_service_client import get_db_client

# Example test data (customize as needed)
TEST_USER_ID = "test-user-1"
TEST_INTEGRATION_ID = "test-integration-1"
TEST_POLICY_ID = "policy-1"

# Tasks to enable/disable (besides sfdc_authenticate and metadata_extraction, which are always enabled)
TASKS = [
    ("health_check", 1),  # enabled
    ("profiles_permission_sets", 1),  # enabled
    ("overview", 0),  # disabled
    ("data_export", 0),  # disabled
]

def main():
    db_client = get_db_client()
    if not db_client:
        print("Could not get DB service client.")
        return
    
    for task_type, enabled in TASKS:
        entity = {
            "PartitionKey": TEST_POLICY_ID,
            "RowKey": f"{TEST_USER_ID}-{TEST_INTEGRATION_ID}-{task_type}",
            "PolicyId": TEST_POLICY_ID,
            "UserId": TEST_USER_ID,
            "IntegrationId": TEST_INTEGRATION_ID,
            "TaskType": task_type,
            "Enabled": int(enabled),
            "CreatedAt": datetime.now().isoformat(),
        }
        success = db_client.insert_policy_rule_setting(entity)
        print(f"Inserted/updated PolicyRuleSetting for task '{task_type}' (enabled={enabled}): {success}")

if __name__ == "__main__":
    main() 