#!/usr/bin/env python3
"""
Deployment Health Check Script

This script performs basic health checks on the deployed SFDC Function App
without requiring heavy dependencies like pyodbc or database connections.
It's designed to run in the Azure DevOps pipeline to verify deployment success.
"""

import sys
import time
import urllib.request
import urllib.error
import json
from typing import Dict, Any, Optional

def make_request(url: str, timeout: int = 30) -> Dict[str, Any]:
    """
    Make an HTTP request and return response details
    
    Args:
        url: URL to request
        timeout: Request timeout in seconds
        
    Returns:
        Dictionary with response details
    """
    try:
        with urllib.request.urlopen(url, timeout=timeout) as response:
            status_code = response.getcode()
            content_type = response.headers.get('Content-Type', '')
            
            # Try to read response body
            try:
                body = response.read().decode('utf-8')
                if 'application/json' in content_type:
                    try:
                        body = json.loads(body)
                    except json.JSONDecodeError:
                        pass
            except Exception:
                body = "Could not read response body"
            
            return {
                'success': True,
                'status_code': status_code,
                'content_type': content_type,
                'body': body,
                'error': None
            }
    except urllib.error.HTTPError as e:
        return {
            'success': False,
            'status_code': e.code,
            'content_type': '',
            'body': None,
            'error': f"HTTP Error {e.code}: {e.reason}"
        }
    except urllib.error.URLError as e:
        return {
            'success': False,
            'status_code': 0,
            'content_type': '',
            'body': None,
            'error': f"URL Error: {e.reason}"
        }
    except Exception as e:
        return {
            'success': False,
            'status_code': 0,
            'content_type': '',
            'body': None,
            'error': f"Request failed: {str(e)}"
        }

def test_endpoint(base_url: str, endpoint: str, expected_status: int = 200) -> bool:
    """
    Test a specific endpoint
    
    Args:
        base_url: Base URL of the service
        endpoint: Endpoint path to test
        expected_status: Expected HTTP status code
        
    Returns:
        True if test passed, False otherwise
    """
    url = f"{base_url.rstrip('/')}/{endpoint.lstrip('/')}"
    print(f"Testing: {url}")
    
    result = make_request(url)
    
    if result['success'] and result['status_code'] == expected_status:
        print(f"✅ PASS - Status: {result['status_code']}")
        if isinstance(result['body'], dict):
            print(f"   Response: {json.dumps(result['body'], indent=2)[:200]}...")
        return True
    else:
        print(f"❌ FAIL - Status: {result['status_code']}, Error: {result['error']}")
        return False

def wait_for_deployment(base_url: str, max_wait: int = 300, check_interval: int = 10) -> bool:
    """
    Wait for deployment to be ready
    
    Args:
        base_url: Base URL to check
        max_wait: Maximum wait time in seconds
        check_interval: Check interval in seconds
        
    Returns:
        True if deployment is ready, False if timeout
    """
    print(f"Waiting for deployment to be ready (max {max_wait}s)...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        result = make_request(f"{base_url}/api/health")
        if result['success'] and result['status_code'] == 200:
            print(f"✅ Deployment ready after {int(time.time() - start_time)}s")
            return True
        
        print(f"⏳ Waiting... (Status: {result['status_code']}, Error: {result['error']})")
        time.sleep(check_interval)
    
    print(f"❌ Timeout waiting for deployment after {max_wait}s")
    return False

def run_health_checks(base_url: str) -> bool:
    """
    Run comprehensive health checks
    
    Args:
        base_url: Base URL of the deployed service
        
    Returns:
        True if all checks pass, False otherwise
    """
    print(f"Running health checks for: {base_url}")
    print("=" * 60)
    
    # Wait for deployment to be ready
    if not wait_for_deployment(base_url):
        return False
    
    # Define test cases
    test_cases = [
        # Basic endpoints
        ("api/health", 200),
        ("api/info", 200),
        
        # Function app endpoints that should be accessible
        ("", 200),  # Root endpoint
        
        # API endpoints (may return 401/403 but should not be 404/500)
        # We expect these to be accessible but may require auth
    ]
    
    passed = 0
    total = len(test_cases)
    
    for endpoint, expected_status in test_cases:
        if test_endpoint(base_url, endpoint, expected_status):
            passed += 1
        print()  # Add spacing between tests
    
    print("=" * 60)
    print(f"Health Check Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All health checks passed!")
        return True
    else:
        print(f"⚠️  {total - passed} health checks failed")
        return False

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python deployment_health_check.py <base_url>")
        print("Example: python deployment_health_check.py https://func-atomsec-sfdc-dev-stage.azurewebsites.net")
        sys.exit(1)
    
    base_url = sys.argv[1].rstrip('/')
    
    try:
        success = run_health_checks(base_url)
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Health check interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Health check failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
