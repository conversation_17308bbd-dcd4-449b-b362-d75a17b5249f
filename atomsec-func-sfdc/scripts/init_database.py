"""
DEPRECATED: Database Initialization Script

This script is DEPRECATED and should not be used.
Database initialization is now handled by the atomsec-func-db-r service.

All database operations have been moved to the dedicated database service.
Use the database service endpoints for database initialization and management.

DEPRECATED Usage:
    python scripts/init_database.py
"""

import os
import sys
import logging
from datetime import datetime

# Add parent directory to path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import shared modules
from shared.azure_services import is_local_dev
from shared.data_access import SqlDatabaseRepository, TableStorageRepository
from shared.user_repository import (
    get_user_account_table_repo, get_user_login_table_repo,
    get_hashing_algorithm_table_repo, get_user_role_table_repo,
    ensure_default_hashing_algorithms
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_tables_sql():
    """Create tables in SQL Server"""
    logger.info("Creating tables in SQL Server...")
    
    # Create SQL repository
    sql_repo = SqlDatabaseRepository(table_name="")
    
    # Create User_Account table
    user_account_query = """
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'User_Account')
    BEGIN
        CREATE TABLE [dbo].[User_Account] (
            [UserId] INT IDENTITY(1,1) PRIMARY KEY,
            [FirstName] VARCHAR(50) NOT NULL,
            [MiddleName] VARCHAR(50) NULL,
            [LastName] VARCHAR(50) NOT NULL,
            [DoB] DATE NULL,
            [Email] VARCHAR(50) NOT NULL UNIQUE,
            [Contact] VARCHAR(20) NULL,
            [State] VARCHAR(50) NULL,
            [Country] VARCHAR(50) NULL
        );
        PRINT 'Created User_Account table';
    END
    ELSE
    BEGIN
        PRINT 'User_Account table already exists';
    END
    """
    
    # Create HashingAlgorithm table
    hashing_algorithm_query = """
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'HashingAlgorithm')
    BEGIN
        CREATE TABLE [dbo].[HashingAlgorithm] (
            [HashAlgorithmId] INT PRIMARY KEY,
            [AlgorithmName] VARCHAR(50) NOT NULL
        );
        
        -- Insert default algorithms
        INSERT INTO [dbo].[HashingAlgorithm] ([HashAlgorithmId], [AlgorithmName])
        VALUES (1, 'SHA256'), (2, 'PBKDF2'), (3, 'Bcrypt');
        
        PRINT 'Created HashingAlgorithm table';
    END
    ELSE
    BEGIN
        PRINT 'HashingAlgorithm table already exists';
    END
    """
    
    # Create App_User_Login table
    user_login_query = """
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'App_User_Login')
    BEGIN
        CREATE TABLE [dbo].[App_User_Login] (
            [UserId] INT PRIMARY KEY,
            [Username] VARCHAR(50) NOT NULL UNIQUE,
            [PasswordHash] NVARCHAR(255) NOT NULL,
            [PasswordSalt] NVARCHAR(255) NOT NULL,
            [HashAlgorithmId] INT NOT NULL,
            FOREIGN KEY ([UserId]) REFERENCES [dbo].[User_Account]([UserId]),
            FOREIGN KEY ([HashAlgorithmId]) REFERENCES [dbo].[HashingAlgorithm]([HashAlgorithmId])
        );
        PRINT 'Created App_User_Login table';
    END
    ELSE
    BEGIN
        PRINT 'App_User_Login table already exists';
    END
    """
    
    # Create Role table
    role_query = """
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Role')
    BEGIN
        CREATE TABLE [dbo].[Role] (
            [RoleId] INT IDENTITY(1,1) PRIMARY KEY,
            [Rolename] VARCHAR(50) NOT NULL UNIQUE,
            [Description] VARCHAR(MAX) NULL,
            [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
        );
        
        -- Insert default roles
        INSERT INTO [dbo].[Role] ([Rolename], [Description])
        VALUES 
            ('Admin', 'Administrator with full access'),
            ('User', 'Regular user with limited access'),
            ('Analyst', 'User with access to analytics');
            
        PRINT 'Created Role table';
    END
    ELSE
    BEGIN
        PRINT 'Role table already exists';
    END
    """
    
    # Create App_User_Roles table
    user_role_query = """
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'App_User_Roles')
    BEGIN
        CREATE TABLE [dbo].[App_User_Roles] (
            [UserId] INT NOT NULL,
            [RoleId] INT NOT NULL,
            [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
            PRIMARY KEY ([UserId], [RoleId]),
            FOREIGN KEY ([UserId]) REFERENCES [dbo].[User_Account]([UserId]),
            FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Role]([RoleId])
        );
        PRINT 'Created App_User_Roles table';
    END
    ELSE
    BEGIN
        PRINT 'App_User_Roles table already exists';
    END
    """
    
    # Create Permission table
    permission_query = """
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Permission')
    BEGIN
        CREATE TABLE [dbo].[Permission] (
            [PermissionId] INT IDENTITY(1,1) PRIMARY KEY,
            [PermissionLabel] VARCHAR(50) NOT NULL UNIQUE,
            [Action] VARCHAR(MAX) NOT NULL,
            [Description] VARCHAR(MAX) NULL,
            [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
        );
        
        -- Insert default permissions
        INSERT INTO [dbo].[Permission] ([PermissionLabel], [Action], [Description])
        VALUES 
            ('READ_USERS', 'Read user data', 'Permission to read user data'),
            ('WRITE_USERS', 'Create/update user data', 'Permission to create or update user data'),
            ('DELETE_USERS', 'Delete user data', 'Permission to delete user data'),
            ('READ_ORGS', 'Read organization data', 'Permission to read organization data'),
            ('WRITE_ORGS', 'Create/update organization data', 'Permission to create or update organization data'),
            ('DELETE_ORGS', 'Delete organization data', 'Permission to delete organization data');
            
        PRINT 'Created Permission table';
    END
    ELSE
    BEGIN
        PRINT 'Permission table already exists';
    END
    """
    
    # Create Role_Permissions table
    role_permission_query = """
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Role_Permissions')
    BEGIN
        CREATE TABLE [dbo].[Role_Permissions] (
            [RoleId] INT NOT NULL,
            [PermissionId] INT NOT NULL,
            [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
            PRIMARY KEY ([RoleId], [PermissionId]),
            FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Role]([RoleId]),
            FOREIGN KEY ([PermissionId]) REFERENCES [dbo].[Permission]([PermissionId])
        );
        
        -- Assign default permissions to roles
        INSERT INTO [dbo].[Role_Permissions] ([RoleId], [PermissionId])
        SELECT r.RoleId, p.PermissionId
        FROM [dbo].[Role] r
        CROSS JOIN [dbo].[Permission] p
        WHERE r.Rolename = 'Admin';
        
        INSERT INTO [dbo].[Role_Permissions] ([RoleId], [PermissionId])
        SELECT r.RoleId, p.PermissionId
        FROM [dbo].[Role] r
        CROSS JOIN [dbo].[Permission] p
        WHERE r.Rolename = 'User' AND p.PermissionLabel IN ('READ_USERS', 'READ_ORGS');
        
        INSERT INTO [dbo].[Role_Permissions] ([RoleId], [PermissionId])
        SELECT r.RoleId, p.PermissionId
        FROM [dbo].[Role] r
        CROSS JOIN [dbo].[Permission] p
        WHERE r.Rolename = 'Analyst' AND p.PermissionLabel IN ('READ_USERS', 'READ_ORGS');
        
        PRINT 'Created Role_Permissions table';
    END
    ELSE
    BEGIN
        PRINT 'Role_Permissions table already exists';
    END
    """
    
    # Create App_User_Token table for refresh tokens
    user_token_query = """
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'App_User_Token')
    BEGIN
        CREATE TABLE [dbo].[App_User_Token] (
            [Id] INT IDENTITY(1,1) PRIMARY KEY,
            [Email] VARCHAR(50) NOT NULL,
            [RefreshToken] VARCHAR(255) NOT NULL UNIQUE,
            [CreatedAt] DATETIME NOT NULL,
            [ExpiresAt] DATETIME NOT NULL
        );
        PRINT 'Created App_User_Token table';
    END
    ELSE
    BEGIN
        PRINT 'App_User_Token table already exists';
    END
    """
    
    # Execute queries
    queries = [
        user_account_query,
        hashing_algorithm_query,
        user_login_query,
        role_query,
        user_role_query,
        permission_query,
        role_permission_query,
        user_token_query
    ]
    
    for query in queries:
        try:
            sql_repo.execute_non_query(query)
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            logger.error(f"Query: {query}")
    
    logger.info("Finished creating tables in SQL Server")


def create_tables_azure():
    """Create tables in Azure Table Storage"""
    logger.info("Creating tables in Azure Table Storage...")
    
    # Get repositories
    user_account_repo = get_user_account_table_repo()
    user_login_repo = get_user_login_table_repo()
    hashing_algorithm_repo = get_hashing_algorithm_table_repo()
    user_role_repo = get_user_role_table_repo()
    
    # Ensure default hashing algorithms
    ensure_default_hashing_algorithms()
    
    logger.info("Finished creating tables in Azure Table Storage")


def main():
    """Main function"""
    logger.info("Starting database initialization...")
    
    if is_local_dev():
        create_tables_azure()
    else:
        create_tables_sql()
    
    logger.info("Database initialization completed")


if __name__ == "__main__":
    main()
