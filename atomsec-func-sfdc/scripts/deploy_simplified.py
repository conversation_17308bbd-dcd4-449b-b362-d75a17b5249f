#!/usr/bin/env python3
"""
Deploy Simplified Function App Script

This script deploys the simplified function app as a fallback when the main function app fails.
"""

import os
import shutil
import sys
from datetime import datetime

def backup_original_function_app():
    """Backup the original function_app.py"""
    try:
        if os.path.exists("function_app.py"):
            backup_name = f"function_app_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            shutil.copy2("function_app.py", backup_name)
            print(f"✅ Original function_app.py backed up as {backup_name}")
            return True
        else:
            print("❌ function_app.py not found")
            return False
    except Exception as e:
        print(f"❌ Failed to backup function_app.py: {e}")
        return False

def deploy_simplified_function_app():
    """Deploy the simplified function app"""
    try:
        if not os.path.exists("function_app_simple.py"):
            print("❌ function_app_simple.py not found")
            return False
        
        # Backup original
        if not backup_original_function_app():
            return False
        
        # Copy simplified version to main function app
        shutil.copy2("function_app_simple.py", "function_app.py")
        print("✅ Simplified function app deployed as function_app.py")
        
        # Verify the deployment
        if os.path.exists("function_app.py"):
            with open("function_app.py", "r") as f:
                content = f.read()
                if "Simplified AtomSec SFDC Function App" in content:
                    print("✅ Simplified function app content verified")
                    return True
                else:
                    print("❌ Simplified function app content verification failed")
                    return False
        else:
            print("❌ function_app.py not found after deployment")
            return False
            
    except Exception as e:
        print(f"❌ Failed to deploy simplified function app: {e}")
        return False

def restore_original_function_app():
    """Restore the original function app from backup"""
    try:
        # Find the most recent backup
        backup_files = [f for f in os.listdir(".") if f.startswith("function_app_backup_")]
        if not backup_files:
            print("❌ No backup files found")
            return False
        
        # Get the most recent backup
        backup_files.sort()
        latest_backup = backup_files[-1]
        
        # Restore from backup
        shutil.copy2(latest_backup, "function_app.py")
        print(f"✅ Original function app restored from {latest_backup}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to restore original function app: {e}")
        return False

def main():
    """Main function"""
    print("=== SIMPLIFIED FUNCTION APP DEPLOYMENT ===")
    
    if len(sys.argv) < 2:
        print("Usage: python deploy_simplified.py <action>")
        print("Actions:")
        print("  deploy    - Deploy simplified function app")
        print("  restore   - Restore original function app")
        print("  status    - Check deployment status")
        sys.exit(1)
    
    action = sys.argv[1].lower()
    
    if action == "deploy":
        print("Deploying simplified function app...")
        if deploy_simplified_function_app():
            print("✅ Simplified function app deployment successful")
            sys.exit(0)
        else:
            print("❌ Simplified function app deployment failed")
            sys.exit(1)
    
    elif action == "restore":
        print("Restoring original function app...")
        if restore_original_function_app():
            print("✅ Original function app restoration successful")
            sys.exit(0)
        else:
            print("❌ Original function app restoration failed")
            sys.exit(1)
    
    elif action == "status":
        print("Checking deployment status...")
        
        if os.path.exists("function_app.py"):
            with open("function_app.py", "r") as f:
                content = f.read()
                if "Simplified AtomSec SFDC Function App" in content:
                    print("✅ Simplified function app is currently deployed")
                else:
                    print("✅ Original function app is currently deployed")
        else:
            print("❌ function_app.py not found")
        
        # Check for backup files
        backup_files = [f for f in os.listdir(".") if f.startswith("function_app_backup_")]
        if backup_files:
            print(f"📁 Found {len(backup_files)} backup file(s)")
            for backup in sorted(backup_files)[-3:]:  # Show last 3
                print(f"   - {backup}")
        else:
            print("📁 No backup files found")
        
        sys.exit(0)
    
    else:
        print(f"❌ Unknown action: {action}")
        sys.exit(1)

if __name__ == "__main__":
    main() 