"""
<PERSON><PERSON><PERSON> to initialize Policy and Rule tables with default data for a new integration.
Creates a Policy for the integration and adds default Rule entries for all tasks.
"""

import uuid
from datetime import datetime
from shared.db_service_client import get_db_client

# Example input (customize as needed)
USER_ID = "test-user-1"
INTEGRATION_ID = "test-integration-1"
POLICY_NAME = "Profiles and Permission Sets"

# Default tasks/rules for this policy (customize as needed)
DEFAULT_RULES = [
    ("health_check", 1),
    ("profiles_permission_sets", 1),
    ("overview", 0),
    ("data_export", 0),
]

def main():
    db_client = get_db_client()
    if not db_client:
        print("Could not get DB service client.")
        return
    
    # Create Policy
    policy_id = str(uuid.uuid4())
    policy_entity = {
        "PolicyId": policy_id,
        "Name": POLICY_NAME,
        "UserId": USER_ID,
        "IntegrationId": INTEGRATION_ID,
        "CreatedAt": datetime.now().isoformat(),
        "PartitionKey": INTEGRATION_ID,
        "RowKey": policy_id,
    }
    db_client.insert_policy(policy_entity)
    print(f"Inserted Policy: {policy_entity}")
    
    # Create Rules
    for task_type, enabled in DEFAULT_RULES:
        rule_id = str(uuid.uuid4())
        rule_entity = {
            "RuleId": rule_id,
            "PolicyId": policy_id,
            "TaskType": task_type,
            "Enabled": int(enabled),
            "CreatedAt": datetime.now().isoformat(),
            "PartitionKey": policy_id,
            "RowKey": rule_id,
        }
        db_client.insert_rule(rule_entity)
        print(f"Inserted Rule: {rule_entity}")

if __name__ == "__main__":
    main() 