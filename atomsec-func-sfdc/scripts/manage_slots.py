#!/usr/bin/env python3
"""
Azure App Service Slot Management Script

This script helps manage Azure App Service slots and handle swap-prepared states.
"""

import subprocess
import json
import time
import sys
from typing import Dict, Any, Optional

def run_azure_cli_command(command: str) -> Dict[str, Any]:
    """Run an Azure CLI command and return the result"""
    try:
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            check=True
        )
        return {
            "success": True,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "return_code": result.returncode
        }
    except subprocess.CalledProcessError as e:
        return {
            "success": False,
            "stdout": e.stdout,
            "stderr": e.stderr,
            "return_code": e.returncode
        }

def get_slot_status(app_name: str, resource_group: str, slot_name: str) -> Dict[str, Any]:
    """Get the status of a specific slot"""
    command = f"az webapp show --name {app_name} --resource-group {resource_group} --slot {slot_name}"
    return run_azure_cli_command(command)

def stop_slot(app_name: str, resource_group: str, slot_name: str) -> Dict[str, Any]:
    """Stop a specific slot"""
    command = f"az webapp stop --name {app_name} --resource-group {resource_group} --slot {slot_name}"
    return run_azure_cli_command(command)

def start_slot(app_name: str, resource_group: str, slot_name: str) -> Dict[str, Any]:
    """Start a specific slot"""
    command = f"az webapp start --name {app_name} --resource-group {resource_group} --slot {slot_name}"
    return run_azure_cli_command(command)

def restart_slot(app_name: str, resource_group: str, slot_name: str) -> Dict[str, Any]:
    """Restart a specific slot"""
    command = f"az webapp restart --name {app_name} --resource-group {resource_group} --slot {slot_name}"
    return run_azure_cli_command(command)

def reset_slot_prepared_state(app_name: str, resource_group: str, slot_name: str) -> bool:
    """Reset a slot that's in swap-prepared state"""
    print(f"Attempting to reset slot {slot_name} for app {app_name}...")
    
    # Step 1: Stop the slot
    print("1. Stopping slot...")
    stop_result = stop_slot(app_name, resource_group, slot_name)
    if not stop_result["success"]:
        print(f"Warning: Failed to stop slot: {stop_result['stderr']}")
    
    # Wait for stop to complete
    print("Waiting for slot to stop...")
    time.sleep(30)
    
    # Step 2: Start the slot
    print("2. Starting slot...")
    start_result = start_slot(app_name, resource_group, slot_name)
    if not start_result["success"]:
        print(f"Warning: Failed to start slot: {start_result['stderr']}")
        return False
    
    # Wait for start to complete
    print("Waiting for slot to start...")
    time.sleep(30)
    
    # Step 3: Check status
    print("3. Checking slot status...")
    status_result = get_slot_status(app_name, resource_group, slot_name)
    if status_result["success"]:
        try:
            status_data = json.loads(status_result["stdout"])
            state = status_data.get("state", "unknown")
            print(f"Slot state: {state}")
            return state.lower() in ["running", "started"]
        except json.JSONDecodeError:
            print("Could not parse slot status")
            return False
    else:
        print(f"Failed to get slot status: {status_result['stderr']}")
        return False

def check_slot_health(app_name: str, resource_group: str, slot_name: str) -> bool:
    """Check if a slot is healthy and accessible"""
    print(f"Checking health of slot {slot_name}...")
    
    # Get slot status
    status_result = get_slot_status(app_name, resource_group, slot_name)
    if not status_result["success"]:
        print(f"Failed to get slot status: {status_result['stderr']}")
        return False
    
    try:
        status_data = json.loads(status_result["stdout"])
        state = status_data.get("state", "unknown")
        print(f"Slot state: {state}")
        
        # Check if slot is running
        if state.lower() not in ["running", "started"]:
            print(f"Slot is not running (state: {state})")
            return False
        
        # Try to access the slot
        slot_url = f"https://{app_name}-{slot_name}.azurewebsites.net"
        print(f"Testing slot accessibility at: {slot_url}")
        
        # Use curl to test basic connectivity
        curl_command = f"curl -s -o /dev/null -w '%{{http_code}}' --max-time 10 {slot_url}"
        curl_result = subprocess.run(curl_command, shell=True, capture_output=True, text=True)
        
        if curl_result.returncode == 0:
            http_code = curl_result.stdout.strip()
            print(f"HTTP response code: {http_code}")
            return http_code in ["200", "404", "503"]  # Acceptable responses
        else:
            print(f"Curl failed: {curl_result.stderr}")
            return False
            
    except json.JSONDecodeError:
        print("Could not parse slot status")
        return False

def main():
    """Main function"""
    if len(sys.argv) < 4:
        print("Usage: python manage_slots.py <app_name> <resource_group> <slot_name> [action]")
        print("Actions: status, stop, start, restart, reset, health")
        sys.exit(1)
    
    app_name = sys.argv[1]
    resource_group = sys.argv[2]
    slot_name = sys.argv[3]
    action = sys.argv[4] if len(sys.argv) > 4 else "status"
    
    print(f"Managing slot '{slot_name}' for app '{app_name}' in resource group '{resource_group}'")
    
    if action == "status":
        result = get_slot_status(app_name, resource_group, slot_name)
        if result["success"]:
            print("Slot status:")
            print(result["stdout"])
        else:
            print(f"Failed to get status: {result['stderr']}")
            sys.exit(1)
    
    elif action == "stop":
        result = stop_slot(app_name, resource_group, slot_name)
        if result["success"]:
            print("Slot stopped successfully")
        else:
            print(f"Failed to stop slot: {result['stderr']}")
            sys.exit(1)
    
    elif action == "start":
        result = start_slot(app_name, resource_group, slot_name)
        if result["success"]:
            print("Slot started successfully")
        else:
            print(f"Failed to start slot: {result['stderr']}")
            sys.exit(1)
    
    elif action == "restart":
        result = restart_slot(app_name, resource_group, slot_name)
        if result["success"]:
            print("Slot restarted successfully")
        else:
            print(f"Failed to restart slot: {result['stderr']}")
            sys.exit(1)
    
    elif action == "reset":
        success = reset_slot_prepared_state(app_name, resource_group, slot_name)
        if success:
            print("Slot reset successfully")
            sys.exit(0)
        else:
            print("Failed to reset slot")
            sys.exit(1)
    
    elif action == "health":
        success = check_slot_health(app_name, resource_group, slot_name)
        if success:
            print("Slot is healthy")
            sys.exit(0)
        else:
            print("Slot is not healthy")
            sys.exit(1)
    
    else:
        print(f"Unknown action: {action}")
        sys.exit(1)

if __name__ == "__main__":
    main() 