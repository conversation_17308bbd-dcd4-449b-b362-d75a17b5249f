#!/usr/bin/env python
"""
Verify FastAPI Deployment

This script verifies that the FastAPI application is deployed and running correctly.
It checks the health endpoints and API documentation.
"""

import argparse
import requests
import sys
import time
import logging
import xml.etree.ElementTree as ET
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('verify_deployment')

def check_endpoint(base_url, endpoint, expected_status=200, max_retries=3, retry_delay=5):
    """Check if an endpoint is available and returns the expected status code"""
    url = f"{base_url.rstrip('/')}/{endpoint.lstrip('/')}"
    logger.info(f"Checking endpoint: {url}")

    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=10)
            logger.info(f"Response status code: {response.status_code}")

            if response.status_code == expected_status:
                logger.info(f"Endpoint {endpoint} is available")
                return True, response
            else:
                logger.warning(f"Endpoint {endpoint} returned status code {response.status_code}, expected {expected_status}")
                if attempt < max_retries - 1:
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
        except requests.RequestException as e:
            logger.warning(f"Error accessing endpoint {endpoint}: {str(e)}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)

    logger.error(f"Endpoint {endpoint} is not available after {max_retries} attempts")
    return False, None

def verify_deployment(base_url):
    """Verify that the FastAPI application is deployed and running correctly"""
    results = []

    # Check regular health endpoint
    health_success, health_response = check_endpoint(base_url, "api/health")
    results.append({
        "name": "Health Endpoint",
        "endpoint": "api/health",
        "success": health_success,
        "status_code": health_response.status_code if health_response else None
    })

    # Check API documentation
    docs_success, docs_response = check_endpoint(base_url, "api/docs")
    results.append({
        "name": "API Documentation",
        "endpoint": "api/docs",
        "success": docs_success,
        "status_code": docs_response.status_code if docs_response else None
    })

    # Check OpenAPI schema
    openapi_success, openapi_response = check_endpoint(base_url, "api/openapi.json")
    results.append({
        "name": "OpenAPI Schema",
        "endpoint": "api/openapi.json",
        "success": openapi_success,
        "status_code": openapi_response.status_code if openapi_response else None
    })

    # Check accounts endpoint
    accounts_success, accounts_response = check_endpoint(base_url, "api/accounts")
    results.append({
        "name": "Accounts Endpoint",
        "endpoint": "api/accounts",
        "success": accounts_success,
        "status_code": accounts_response.status_code if accounts_response else None
    })

    # Check roles endpoint
    roles_success, roles_response = check_endpoint(base_url, "api/roles")
    results.append({
        "name": "Roles Endpoint",
        "endpoint": "api/roles",
        "success": roles_success,
        "status_code": roles_response.status_code if roles_response else None
    })

    # Print results
    logger.info("\nDeployment Verification Results:")
    logger.info("=" * 80)
    logger.info(f"{'Endpoint':<30} {'Status':<10} {'Result':<10}")
    logger.info("-" * 80)

    all_success = True
    for result in results:
        status = "SUCCESS" if result["success"] else "FAILURE"
        logger.info(f"{result['endpoint']:<30} {result['status_code'] or 'N/A':<10} {status:<10}")
        if not result["success"]:
            all_success = False

    logger.info("=" * 80)
    logger.info(f"Overall Result: {'SUCCESS' if all_success else 'FAILURE'}")

    # Generate JUnit XML report
    generate_junit_report(results)

    return all_success

def generate_junit_report(results):
    """Generate a JUnit XML report from the verification results"""
    testsuite = ET.Element("testsuite")
    testsuite.set("name", "Deployment Verification")
    testsuite.set("tests", str(len(results)))
    testsuite.set("timestamp", datetime.now().isoformat())

    failures = sum(1 for result in results if not result["success"])
    testsuite.set("failures", str(failures))

    for result in results:
        testcase = ET.SubElement(testsuite, "testcase")
        testcase.set("name", result["name"])
        testcase.set("classname", "DeploymentVerification")

        if not result["success"]:
            failure = ET.SubElement(testcase, "failure")
            failure.set("message", f"Endpoint {result['endpoint']} is not available")
            failure.set("type", "EndpointUnavailable")
            failure.text = f"Expected status code 200, got {result['status_code'] or 'N/A'}"

    tree = ET.ElementTree(testsuite)
    tree.write("deployment-verification-results.xml")
    logger.info("JUnit XML report generated: deployment-verification-results.xml")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Verify FastAPI deployment")
    parser.add_argument("--url", default="https://func-atomsec-sfdc-dev.azurewebsites.net", help="Base URL of the deployed application")
    args = parser.parse_args()

    logger.info(f"Verifying deployment at {args.url}")
    success = verify_deployment(args.url)

    if not success:
        logger.error("Deployment verification failed")
        sys.exit(1)

    logger.info("Deployment verification succeeded")
    sys.exit(0)

if __name__ == "__main__":
    main()
