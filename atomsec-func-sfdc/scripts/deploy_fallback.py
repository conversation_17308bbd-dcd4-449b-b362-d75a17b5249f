#!/usr/bin/env python3
"""
Fallback deployment script for SFDC Function App

This script deploys a simplified version of the function app if the main one fails.
"""

import os
import shutil
import sys

def deploy_fallback():
    """Deploy the simplified function app as fallback"""
    print("=== FALLBACK DEPLOYMENT ===")
    
    # Check if simplified function app exists
    if not os.path.exists("function_app_simple.py"):
        print("❌ Simplified function app not found")
        return False
    
    # Backup current function app
    if os.path.exists("function_app.py"):
        print("Creating backup of current function_app.py...")
        shutil.copy("function_app.py", "function_app_backup.py")
        print("✅ Backup created")
    
    # Replace with simplified version
    print("Deploying simplified function app...")
    shutil.copy("function_app_simple.py", "function_app.py")
    print("✅ Simplified function app deployed")
    
    # Update host.json if needed
    if os.path.exists("host.json"):
        print("✅ host.json exists")
    else:
        print("❌ host.json not found")
        return False
    
    print("=== FALLBACK DEPLOYMENT COMPLETE ===")
    return True

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "deploy":
        success = deploy_fallback()
        if success:
            print("✅ Fallback deployment successful")
            sys.exit(0)
        else:
            print("❌ Fallback deployment failed")
            sys.exit(1)
    else:
        print("Usage: python deploy_fallback.py deploy")
        sys.exit(1)

if __name__ == "__main__":
    main() 