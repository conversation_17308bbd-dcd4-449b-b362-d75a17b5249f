#!/usr/bin/env python3
"""
Debug script to test function app startup and identify import issues
"""

import sys
import os
import traceback
from typing import List, Dict, Any

def test_import(module_name: str) -> Dict[str, Any]:
    """Test importing a module and return results"""
    try:
        module = __import__(module_name)
        return {
            "success": True,
            "module": module_name,
            "error": None
        }
    except Exception as e:
        return {
            "success": False,
            "module": module_name,
            "error": str(e),
            "traceback": traceback.format_exc()
        }

def test_function_app_imports() -> List[Dict[str, Any]]:
    """Test all the imports that function_app.py uses"""
    imports_to_test = [
        "azure.functions",
        "logging",
        "json",
        "requests",
        "pandas",
        "simple_salesforce",
        "azure.identity",
        "azure.keyvault.secrets",
        "azure.storage.blob",
        "azure.storage.queue",
        "azure.servicebus",
        "fastapi",
        "uvicorn",
        "jinja2",
        "httpx",
        "aiohttp",
        "python_dateutil",
        "pytz",
        "loguru",
        "yaml",
        "jwt",
        "cryptography",
        "passlib",
        "jose"
    ]
    
    results = []
    for module in imports_to_test:
        result = test_import(module)
        results.append(result)
        status = "✅" if result["success"] else "❌"
        print(f"{status} {module}: {result['error'] if not result['success'] else 'OK'}")
    
    return results

def test_function_app_structure():
    """Test the function app structure"""
    print("\n=== FUNCTION APP STRUCTURE TEST ===")
    
    # Check if function_app.py exists
    if os.path.exists("function_app.py"):
        print("✅ function_app.py exists")
        
        # Try to read the first few lines
        try:
            with open("function_app.py", "r") as f:
                lines = f.readlines()[:20]
                print("✅ function_app.py is readable")
                print("First 20 lines:")
                for i, line in enumerate(lines, 1):
                    print(f"{i:2d}: {line.rstrip()}")
        except Exception as e:
            print(f"❌ Error reading function_app.py: {e}")
    else:
        print("❌ function_app.py not found")
    
    # Check if host.json exists
    if os.path.exists("host.json"):
        print("✅ host.json exists")
        try:
            with open("host.json", "r") as f:
                content = f.read()
                print("✅ host.json is readable")
                print("host.json content:")
                print(content)
        except Exception as e:
            print(f"❌ Error reading host.json: {e}")
    else:
        print("❌ host.json not found")
    
    # Check if requirements.txt exists
    if os.path.exists("requirements.txt"):
        print("✅ requirements.txt exists")
        try:
            with open("requirements.txt", "r") as f:
                lines = f.readlines()
                print(f"✅ requirements.txt is readable ({len(lines)} lines)")
                print("Dependencies:")
                for line in lines:
                    if line.strip() and not line.startswith("#"):
                        print(f"  - {line.strip()}")
        except Exception as e:
            print(f"❌ Error reading requirements.txt: {e}")
    else:
        print("❌ requirements.txt not found")

def test_api_endpoints():
    """Test if API endpoint modules can be imported"""
    print("\n=== API ENDPOINTS TEST ===")
    
    api_modules = [
        "api.user_endpoints",
        "api.account_endpoints", 
        "api.organization_endpoints",
        "api.integration_endpoints",
        "api.security_endpoints",
        "api.task_endpoints",
        "api.auth_endpoints",
        "api.policy_endpoints",
        "api.cors_handler",
        "api.user_profile_endpoints",
        "api.key_vault_endpoints",
        "api.pmd_endpoints"
    ]
    
    results = []
    for module in api_modules:
        result = test_import(module)
        results.append(result)
        status = "✅" if result["success"] else "❌"
        print(f"{status} {module}: {result['error'] if not result['success'] else 'OK'}")
    
    return results

def test_shared_modules():
    """Test if shared modules can be imported"""
    print("\n=== SHARED MODULES TEST ===")
    
    shared_modules = [
        "shared.azure_services",
        "shared.auth_service",
        "shared.config",
        "shared.common",
        "shared.db_service_client",
        "shared.service_bus_client",
        "shared.sfdc_service_client"
    ]
    
    results = []
    for module in shared_modules:
        result = test_import(module)
        results.append(result)
        status = "✅" if result["success"] else "❌"
        print(f"{status} {module}: {result['error'] if not result['success'] else 'OK'}")
    
    return results

def main():
    """Main debug function"""
    print("=== AZURE FUNCTIONS STARTUP DEBUG ===")
    print(f"Python version: {sys.version}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python path: {sys.path}")
    
    # Test basic imports
    print("\n=== BASIC IMPORTS TEST ===")
    basic_results = test_function_app_imports()
    
    # Test function app structure
    test_function_app_structure()
    
    # Test API endpoints
    api_results = test_api_endpoints()
    
    # Test shared modules
    shared_results = test_shared_modules()
    
    # Summary
    print("\n=== SUMMARY ===")
    total_tests = len(basic_results) + len(api_results) + len(shared_results)
    passed_tests = sum(1 for r in basic_results + api_results + shared_results if r["success"])
    
    print(f"Total tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    
    if passed_tests == total_tests:
        print("✅ All tests passed - function app should start successfully")
        return 0
    else:
        print("❌ Some tests failed - function app may have startup issues")
        print("Note: This is expected in build environment where dependencies may not be fully installed")
        return 0  # Don't fail the pipeline, just warn

if __name__ == "__main__":
    sys.exit(main()) 