#!/usr/bin/env python3
"""
Fix Swap-Prepared Slot Script

This script specifically handles Azure App Service slots that are in a "swap-prepared" state.
It uses Azure CLI commands to properly reset the slot and clear the swap-prepared state.
"""

import subprocess
import json
import time
import sys
from typing import Dict, Any

def run_azure_cli_command(command: str) -> Dict[str, Any]:
    """Run an Azure CLI command and return the result"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            check=True
        )
        print(f"Command succeeded: {result.stdout}")
        return {
            "success": True,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "return_code": result.returncode
        }
    except subprocess.CalledProcessError as e:
        print(f"Command failed: {e.stderr}")
        return {
            "success": False,
            "stdout": e.stdout,
            "stderr": e.stderr,
            "return_code": e.returncode
        }

def get_slot_status(app_name: str, resource_group: str, slot_name: str) -> Dict[str, Any]:
    """Get detailed status of a slot"""
    command = f"az webapp show --name {app_name} --resource-group {resource_group} --slot {slot_name}"
    return run_azure_cli_command(command)

def force_stop_slot(app_name: str, resource_group: str, slot_name: str) -> bool:
    """Force stop a slot using multiple methods"""
    print(f"Force stopping slot {slot_name}...")
    
    # Method 1: Standard stop
    stop_result = run_azure_cli_command(f"az webapp stop --name {app_name} --resource-group {resource_group} --slot {slot_name}")
    if stop_result["success"]:
        print("Standard stop succeeded")
    else:
        print(f"Standard stop failed: {stop_result['stderr']}")
    
    # Wait for stop to complete
    print("Waiting for slot to stop...")
    time.sleep(45)
    
    # Method 2: Force stop using REST API if needed
    if not stop_result["success"]:
        print("Attempting force stop...")
        force_stop_result = run_azure_cli_command(f"az rest --method POST --uri '/subscriptions/$(az account show --query id -o tsv)/resourceGroups/{resource_group}/providers/Microsoft.Web/sites/{app_name}/slots/{slot_name}/stop'")
        if force_stop_result["success"]:
            print("Force stop succeeded")
            time.sleep(30)
        else:
            print(f"Force stop failed: {force_stop_result['stderr']}")
    
    return True

def force_start_slot(app_name: str, resource_group: str, slot_name: str) -> bool:
    """Force start a slot using multiple methods"""
    print(f"Force starting slot {slot_name}...")
    
    # Method 1: Standard start
    start_result = run_azure_cli_command(f"az webapp start --name {app_name} --resource-group {resource_group} --slot {slot_name}")
    if start_result["success"]:
        print("Standard start succeeded")
    else:
        print(f"Standard start failed: {start_result['stderr']}")
    
    # Wait for start to complete
    print("Waiting for slot to start...")
    time.sleep(45)
    
    # Method 2: Force start using REST API if needed
    if not start_result["success"]:
        print("Attempting force start...")
        force_start_result = run_azure_cli_command(f"az rest --method POST --uri '/subscriptions/$(az account show --query id -o tsv)/resourceGroups/{resource_group}/providers/Microsoft.Web/sites/{app_name}/slots/{slot_name}/start'")
        if force_start_result["success"]:
            print("Force start succeeded")
            time.sleep(30)
        else:
            print(f"Force start failed: {force_start_result['stderr']}")
    
    return True

def reset_slot_configuration(app_name: str, resource_group: str, slot_name: str) -> bool:
    """Reset slot configuration to clear swap-prepared state"""
    print(f"Resetting slot configuration for {slot_name}...")
    
    # Get current slot configuration
    config_result = run_azure_cli_command(f"az webapp config show --name {app_name} --resource-group {resource_group} --slot {slot_name}")
    if config_result["success"]:
        print("Current slot configuration retrieved")
    else:
        print(f"Failed to get slot configuration: {config_result['stderr']}")
    
    # Reset slot configuration
    reset_result = run_azure_cli_command(f"az webapp config set --name {app_name} --resource-group {resource_group} --slot {slot_name} --generic-configurations '{{\"WEBSITE_RUN_FROM_PACKAGE\":\"1\"}}'")
    if reset_result["success"]:
        print("Slot configuration reset succeeded")
        return True
    else:
        print(f"Slot configuration reset failed: {reset_result['stderr']}")
        return False

def clear_swap_prepared_state(app_name: str, resource_group: str, slot_name: str) -> bool:
    """Clear the swap-prepared state using multiple methods"""
    print(f"Clearing swap-prepared state for slot {slot_name}...")
    
    # Step 1: Force stop the slot
    if not force_stop_slot(app_name, resource_group, slot_name):
        print("Failed to stop slot")
        return False
    
    # Step 2: Wait longer for any pending operations
    print("Waiting for pending operations to complete...")
    time.sleep(60)
    
    # Step 3: Reset slot configuration
    if not reset_slot_configuration(app_name, resource_group, slot_name):
        print("Failed to reset slot configuration")
        return False
    
    # Step 4: Force start the slot
    if not force_start_slot(app_name, resource_group, slot_name):
        print("Failed to start slot")
        return False
    
    # Step 5: Verify slot is in a good state
    print("Verifying slot state...")
    time.sleep(30)
    
    status_result = get_slot_status(app_name, resource_group, slot_name)
    if status_result["success"]:
        try:
            status_data = json.loads(status_result["stdout"])
            state = status_data.get("state", "unknown")
            print(f"Final slot state: {state}")
            return state.lower() in ["running", "started"]
        except json.JSONDecodeError:
            print("Could not parse slot status")
            return False
    else:
        print(f"Failed to get final slot status: {status_result['stderr']}")
        return False

def main():
    """Main function"""
    if len(sys.argv) != 4:
        print("Usage: python fix_swap_prepared_slot.py <app_name> <resource_group> <slot_name>")
        print("Example: python fix_swap_prepared_slot.py func-atomsec-sfdc-dev atomsec-dev-backend stage")
        sys.exit(1)
    
    app_name = sys.argv[1]
    resource_group = sys.argv[2]
    slot_name = sys.argv[3]
    
    print(f"=== FIXING SWAP-PREPARED SLOT ===")
    print(f"App: {app_name}")
    print(f"Resource Group: {resource_group}")
    print(f"Slot: {slot_name}")
    print("=" * 40)
    
    # Get initial status
    print("Getting initial slot status...")
    initial_status = get_slot_status(app_name, resource_group, slot_name)
    if initial_status["success"]:
        try:
            status_data = json.loads(initial_status["stdout"])
            state = status_data.get("state", "unknown")
            print(f"Initial slot state: {state}")
        except json.JSONDecodeError:
            print("Could not parse initial status")
    else:
        print(f"Failed to get initial status: {initial_status['stderr']}")
    
    # Clear swap-prepared state
    success = clear_swap_prepared_state(app_name, resource_group, slot_name)
    
    if success:
        print("✅ Successfully cleared swap-prepared state")
        sys.exit(0)
    else:
        print("❌ Failed to clear swap-prepared state")
        sys.exit(1)

if __name__ == "__main__":
    main() 