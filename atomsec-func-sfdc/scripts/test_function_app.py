#!/usr/bin/env python3
"""
Test Function App Startup Script

This script tests if the function app can start properly and identifies any import or startup issues.
"""

import sys
import os
import traceback
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_basic_imports():
    """Test basic Python imports"""
    print("=== TESTING BASIC IMPORTS ===")
    
    try:
        import azure.functions as func
        print("✅ azure.functions imported successfully")
    except Exception as e:
        print(f"❌ azure.functions import failed: {e}")
        return False
    
    try:
        import json
        print("✅ json imported successfully")
    except Exception as e:
        print(f"❌ json import failed: {e}")
        return False
    
    try:
        import logging
        print("✅ logging imported successfully")
    except Exception as e:
        print(f"❌ logging import failed: {e}")
        return False
    
    return True

def test_function_app_import():
    """Test importing the function app"""
    print("\n=== TESTING FUNCTION APP IMPORT ===")
    
    try:
        # Add current directory to Python path
        sys.path.insert(0, os.getcwd())
        
        # Try to import function_app
        import function_app
        print("✅ function_app imported successfully")
        
        # Check if app object exists
        if hasattr(function_app, 'app'):
            print("✅ app object found")
        else:
            print("❌ app object not found")
            return False
            
        return True
    except Exception as e:
        print(f"❌ function_app import failed: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_endpoint_definitions():
    """Test if endpoints are properly defined"""
    print("\n=== TESTING ENDPOINT DEFINITIONS ===")
    
    try:
        import function_app
        
        # Check for health endpoint
        if hasattr(function_app, 'health_check'):
            print("✅ health_check function found")
        else:
            print("❌ health_check function not found")
            return False
        
        # Check for info endpoint
        if hasattr(function_app, 'info'):
            print("✅ info function found")
        else:
            print("❌ info function not found")
            return False
        
        # Check for test endpoint
        if hasattr(function_app, 'test_function'):
            print("✅ test_function found")
        else:
            print("❌ test_function not found")
            return False
        
        return True
    except Exception as e:
        print(f"❌ endpoint definition test failed: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_shared_modules():
    """Test importing shared modules"""
    print("\n=== TESTING SHARED MODULES ===")
    
    shared_modules = [
        'shared.db_service_client',
        'shared.azure_services',
        'shared.service_bus_client',
        'shared.sfdc_service_client'
    ]
    
    all_success = True
    
    for module in shared_modules:
        try:
            __import__(module)
            print(f"✅ {module} imported successfully")
        except Exception as e:
            print(f"❌ {module} import failed: {e}")
            all_success = False
    
    return all_success

def test_api_modules():
    """Test importing API modules"""
    print("\n=== TESTING API MODULES ===")
    
    api_modules = [
        'api.user_endpoints',
        'api.account_endpoints',
        'api.organization_endpoints',
        'api.integration_endpoints',
        'api.security_endpoints',
        'api.task_endpoints',
        'api.auth_endpoints'
    ]
    
    all_success = True
    
    for module in api_modules:
        try:
            __import__(module)
            print(f"✅ {module} imported successfully")
        except Exception as e:
            print(f"❌ {module} import failed: {e}")
            all_success = False
    
    return all_success

def main():
    """Main test function"""
    print("=== FUNCTION APP STARTUP TEST ===")
    print(f"Current directory: {os.getcwd()}")
    print(f"Python version: {sys.version}")
    print(f"Python path: {sys.path[:3]}...")  # Show first 3 entries
    
    # Test basic imports
    if not test_basic_imports():
        print("\n❌ Basic imports failed")
        return False
    
    # Test function app import
    if not test_function_app_import():
        print("\n❌ Function app import failed")
        return False
    
    # Test endpoint definitions
    if not test_endpoint_definitions():
        print("\n❌ Endpoint definitions failed")
        return False
    
    # Test shared modules
    if not test_shared_modules():
        print("\n⚠️ Some shared modules failed to import (this might be expected)")
    
    # Test API modules
    if not test_api_modules():
        print("\n⚠️ Some API modules failed to import (this might be expected)")
    
    print("\n✅ Function app startup test completed")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 