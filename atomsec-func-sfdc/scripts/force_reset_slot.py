#!/usr/bin/env python3
"""
Force Reset Slot Script

This script aggressively resets an Azure App Service slot that's in swap-prepared state.
It uses multiple methods including Azure CLI and REST API calls to force the slot reset.
"""

import subprocess
import json
import time
import sys
from typing import Dict, Any

def run_azure_cli_command(command: str) -> Dict[str, Any]:
    """Run an Azure CLI command and return the result"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            check=True
        )
        print(f"Command succeeded: {result.stdout}")
        return {
            "success": True,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "return_code": result.returncode
        }
    except subprocess.CalledProcessError as e:
        print(f"Command failed: {e.stderr}")
        return {
            "success": False,
            "stdout": e.stdout,
            "stderr": e.stderr,
            "return_code": e.returncode
        }

def get_subscription_id() -> str:
    """Get the current subscription ID"""
    result = run_azure_cli_command("az account show --query id -o tsv")
    if result["success"]:
        return result["stdout"].strip()
    else:
        print("Failed to get subscription ID")
        return ""

def force_stop_slot_aggressive(app_name: str, resource_group: str, slot_name: str = None) -> bool:
    """Aggressively stop a slot using multiple methods"""
    slot_param = f"--slot {slot_name}" if slot_name else ""
    print(f"Aggressively stopping {'slot ' + slot_name if slot_name else 'production'}...")
    
    subscription_id = get_subscription_id()
    if not subscription_id:
        return False
    
    # Method 1: Standard Azure CLI stop
    print("Method 1: Standard Azure CLI stop")
    stop_result = run_azure_cli_command(f"az webapp stop --name {app_name} --resource-group {resource_group} {slot_param}")
    
    # Wait for stop to complete
    print("Waiting for stop to complete...")
    time.sleep(60)
    
    # Method 2: REST API stop
    print("Method 2: REST API stop")
    if slot_name:
        rest_stop_url = f"/subscriptions/{subscription_id}/resourceGroups/{resource_group}/providers/Microsoft.Web/sites/{app_name}/slots/{slot_name}/stop"
    else:
        rest_stop_url = f"/subscriptions/{subscription_id}/resourceGroups/{resource_group}/providers/Microsoft.Web/sites/{app_name}/stop"
    rest_stop_result = run_azure_cli_command(f"az rest --method POST --uri '{rest_stop_url}'")
    
    # Wait longer for any pending operations
    print("Waiting for pending operations...")
    time.sleep(90)
    
    # Method 3: Force stop using additional parameters
    print("Method 3: Force stop with additional parameters")
    force_stop_result = run_azure_cli_command(f"az webapp stop --name {app_name} --resource-group {resource_group} {slot_param} --soft-restart")
    
    return True

def force_start_slot_aggressive(app_name: str, resource_group: str, slot_name: str = None) -> bool:
    """Aggressively start a slot using multiple methods"""
    slot_param = f"--slot {slot_name}" if slot_name else ""
    print(f"Aggressively starting {'slot ' + slot_name if slot_name else 'production'}...")
    
    subscription_id = get_subscription_id()
    if not subscription_id:
        return False
    
    # Method 1: Standard Azure CLI start
    print("Method 1: Standard Azure CLI start")
    start_result = run_azure_cli_command(f"az webapp start --name {app_name} --resource-group {resource_group} {slot_param}")
    
    # Wait for start to complete
    print("Waiting for start to complete...")
    time.sleep(60)
    
    # Method 2: REST API start
    print("Method 2: REST API start")
    if slot_name:
        rest_start_url = f"/subscriptions/{subscription_id}/resourceGroups/{resource_group}/providers/Microsoft.Web/sites/{app_name}/slots/{slot_name}/start"
    else:
        rest_start_url = f"/subscriptions/{subscription_id}/resourceGroups/{resource_group}/providers/Microsoft.Web/sites/{app_name}/start"
    rest_start_result = run_azure_cli_command(f"az rest --method POST --uri '{rest_start_url}'")
    
    # Wait longer for any pending operations
    print("Waiting for pending operations...")
    time.sleep(90)
    
    return True

def reset_slot_configuration_aggressive(app_name: str, resource_group: str, slot_name: str = None) -> bool:
    """Aggressively reset slot configuration"""
    slot_param = f"--slot {slot_name}" if slot_name else ""
    print(f"Aggressively resetting slot configuration for {'slot ' + slot_name if slot_name else 'production'}...")
    
    # Method 1: Reset app settings
    print("Method 1: Resetting app settings")
    reset_settings_result = run_azure_cli_command(f"az webapp config appsettings set --name {app_name} --resource-group {resource_group} {slot_param} --settings WEBSITE_RUN_FROM_PACKAGE=1")
    
    # Method 2: Reset general configuration
    print("Method 2: Resetting general configuration")
    reset_config_result = run_azure_cli_command(f"az webapp config set --name {app_name} --resource-group {resource_group} {slot_param} --generic-configurations '{{\"WEBSITE_RUN_FROM_PACKAGE\":\"1\"}}'")
    
    # Method 3: Reset connection strings
    print("Method 3: Resetting connection strings")
    reset_conn_result = run_azure_cli_command(f"az webapp config connection-string set --name {app_name} --resource-group {resource_group} {slot_param} --connection-string-type All --settings '{{}}'")
    
    return True

def clear_swap_prepared_state_aggressive(app_name: str, resource_group: str, slot_name: str = None) -> bool:
    """Aggressively clear the swap-prepared state"""
    slot_display = f"slot {slot_name}" if slot_name else "production"
    print(f"Aggressively clearing swap-prepared state for {slot_display}...")
    
    # Step 1: Force stop the slot
    print("Step 1: Force stopping slot")
    if not force_stop_slot_aggressive(app_name, resource_group, slot_name):
        print("Warning: Failed to stop slot, continuing anyway")
    
    # Step 2: Wait longer for any pending operations
    print("Step 2: Waiting for pending operations")
    time.sleep(120)
    
    # Step 3: Reset slot configuration
    print("Step 3: Resetting slot configuration")
    if not reset_slot_configuration_aggressive(app_name, resource_group, slot_name):
        print("Warning: Failed to reset slot configuration, continuing anyway")
    
    # Step 4: Force start the slot
    print("Step 4: Force starting slot")
    if not force_start_slot_aggressive(app_name, resource_group, slot_name):
        print("Warning: Failed to start slot, continuing anyway")
    
    # Step 5: Wait for slot to stabilize
    print("Step 5: Waiting for slot to stabilize")
    time.sleep(120)
    
    # Step 6: Verify slot state
    print("Step 6: Verifying slot state")
    slot_param = f"--slot {slot_name}" if slot_name else ""
    status_result = run_azure_cli_command(f"az webapp show --name {app_name} --resource-group {resource_group} {slot_param}")
    
    if status_result["success"]:
        try:
            status_data = json.loads(status_result["stdout"])
            state = status_data.get("state", "unknown")
            print(f"Final slot state: {state}")
            
            # Check if slot is accessible
            print("Testing slot accessibility...")
            if slot_name:
                test_url = f"https://{app_name}-{slot_name}.azurewebsites.net"
            else:
                test_url = f"https://{app_name}.azurewebsites.net"
            
            test_result = run_azure_cli_command(f"curl -s -o /dev/null -w '%{{http_code}}' --max-time 30 {test_url}")
            if test_result["success"]:
                print(f"Slot accessibility test: {test_result['stdout']}")
                return True
            else:
                print("Slot accessibility test failed")
                return False
                
        except json.JSONDecodeError:
            print("Could not parse slot status")
            return False
    else:
        print(f"Failed to get slot status: {status_result['stderr']}")
        return False

def main():
    """Main function"""
    if len(sys.argv) < 3:
        print("Usage: python force_reset_slot.py <app_name> <resource_group> [slot_name]")
        print("Example: python force_reset_slot.py func-atomsec-sfdc-dev atomsec-dev-backend stage")
        print("Example: python force_reset_slot.py func-atomsec-sfdc-dev atomsec-dev-backend production")
        print("Example: python force_reset_slot.py func-atomsec-sfdc-dev atomsec-dev-backend")
        sys.exit(1)
    
    app_name = sys.argv[1]
    resource_group = sys.argv[2]
    slot_name = sys.argv[3] if len(sys.argv) > 3 else None
    
    print(f"=== AGGRESSIVE SLOT RESET ===")
    print(f"App: {app_name}")
    print(f"Resource Group: {resource_group}")
    print(f"Slot: {slot_name if slot_name else 'production'}")
    print("=" * 40)
    
    # Get initial status
    print("Getting initial slot status...")
    slot_param = f"--slot {slot_name}" if slot_name else ""
    initial_status = run_azure_cli_command(f"az webapp show --name {app_name} --resource-group {resource_group} {slot_param}")
    if initial_status["success"]:
        try:
            status_data = json.loads(initial_status["stdout"])
            state = status_data.get("state", "unknown")
            print(f"Initial slot state: {state}")
        except json.JSONDecodeError:
            print("Could not parse initial status")
    else:
        print(f"Failed to get initial status: {initial_status['stderr']}")
    
    # Clear swap-prepared state aggressively
    success = clear_swap_prepared_state_aggressive(app_name, resource_group, slot_name)
    
    if success:
        print("✅ Successfully cleared swap-prepared state")
        sys.exit(0)
    else:
        print("❌ Failed to clear swap-prepared state")
        print("Note: This may still allow deployment to proceed")
        sys.exit(0)  # Don't fail the pipeline

if __name__ == "__main__":
    main() 