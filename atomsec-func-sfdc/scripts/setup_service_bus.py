#!/usr/bin/env python3
"""
Service Bus Setup Script

This script sets up the Azure Service Bus topic and subscriptions required
for the refactored task processing system.
"""

import os
import sys
import logging
from azure.servicebus.management import ServiceBusAdministrationClient
from azure.servicebus.management import CreateTopicOptions, CreateSubscriptionOptions, SqlRuleFilter

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_service_bus():
    """Set up Service Bus topic and subscriptions"""
    
    # Get connection string from environment
    connection_string = os.getenv('AZURE_SERVICE_BUS_CONNECTION_STRING')
    if not connection_string:
        logger.error("AZURE_SERVICE_BUS_CONNECTION_STRING environment variable not set")
        return False
    
    topic_name = os.getenv('AZURE_SERVICE_BUS_TOPIC_NAME', 'atomsec-tasks')
    
    try:
        # Create administration client
        admin_client = ServiceBusAdministrationClient.from_connection_string(connection_string)
        
        # Create topic if it doesn't exist
        try:
            admin_client.get_topic(topic_name)
            logger.info(f"Topic '{topic_name}' already exists")
        except:
            topic_options = CreateTopicOptions()
            topic_options.max_size_in_megabytes = 1024
            topic_options.default_message_time_to_live = "P14D"  # 14 days
            
            admin_client.create_topic(topic_name, options=topic_options)
            logger.info(f"Created topic '{topic_name}'")
        
        # Define subscriptions with their priority filters
        subscriptions = [
            {
                'name': 'sfdc-service-high',
                'priority': 'high',
                'max_delivery_count': 3,
                'lock_duration_minutes': 5
            },
            {
                'name': 'sfdc-service-medium', 
                'priority': 'medium',
                'max_delivery_count': 3,
                'lock_duration_minutes': 5
            },
            {
                'name': 'sfdc-service-low',
                'priority': 'low',
                'max_delivery_count': 3,
                'lock_duration_minutes': 10
            }
        ]
        
        # Create subscriptions
        for sub_config in subscriptions:
            subscription_name = sub_config['name']
            priority = sub_config['priority']
            
            try:
                admin_client.get_subscription(topic_name, subscription_name)
                logger.info(f"Subscription '{subscription_name}' already exists")
            except:
                # Create subscription
                from datetime import timedelta
                
                subscription_options = CreateSubscriptionOptions()
                subscription_options.max_delivery_count = sub_config['max_delivery_count']
                subscription_options.lock_duration = timedelta(minutes=sub_config['lock_duration_minutes'])
                subscription_options.dead_lettering_on_message_expiration = True
                subscription_options.dead_lettering_on_filter_evaluation_exceptions = True
                
                admin_client.create_subscription(
                    topic_name=topic_name,
                    subscription_name=subscription_name,
                    options=subscription_options
                )
                
                # Remove default rule
                try:
                    admin_client.delete_rule(topic_name, subscription_name, "$Default")
                except:
                    pass  # Rule might not exist
                
                # Create priority filter rule
                rule_filter = SqlRuleFilter(f"priority = '{priority}'")
                admin_client.create_rule(
                    topic_name=topic_name,
                    subscription_name=subscription_name,
                    rule_name=f"priority-{priority}-filter",
                    filter=rule_filter
                )
                
                logger.info(f"Created subscription '{subscription_name}' with priority filter '{priority}'")
        
        # Create monitoring subscription (optional - for debugging)
        monitoring_subscription = 'monitoring'
        try:
            admin_client.get_subscription(topic_name, monitoring_subscription)
            logger.info(f"Monitoring subscription '{monitoring_subscription}' already exists")
        except:
            from datetime import timedelta
            
            subscription_options = CreateSubscriptionOptions()
            subscription_options.max_delivery_count = 1
            subscription_options.lock_duration = timedelta(minutes=1)
            subscription_options.auto_delete_on_idle = timedelta(days=7)
            
            admin_client.create_subscription(
                topic_name=topic_name,
                subscription_name=monitoring_subscription,
                options=subscription_options
            )
            
            # This subscription gets all messages (no filter)
            logger.info(f"Created monitoring subscription '{monitoring_subscription}'")
        
        logger.info("Service Bus setup completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error setting up Service Bus: {str(e)}")
        return False

def main():
    """Main function"""
    logger.info("Starting Service Bus setup...")
    
    if setup_service_bus():
        logger.info("Service Bus setup completed successfully")
        sys.exit(0)
    else:
        logger.error("Service Bus setup failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
