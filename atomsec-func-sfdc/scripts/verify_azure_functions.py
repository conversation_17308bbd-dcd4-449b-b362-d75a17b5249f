#!/usr/bin/env python3
"""
Azure Functions Deployment Verification Script

This script verifies that functions are properly deployed and visible in Azure portal.
It will fail the deployment if functions are not accessible.

Usage:
    python scripts/verify_azure_functions.py <function_app_url>

Example:
    python scripts/verify_azure_functions.py https://func-atomsec-sfdc-dev-stage.azurewebsites.net
"""

import sys
import time
import urllib.request
import urllib.error
import json
from typing import Dict, Any, Optional

def make_request(url: str, timeout: int = 30) -> Dict[str, Any]:
    """Make an HTTP request and return response details"""
    try:
        with urllib.request.urlopen(url, timeout=timeout) as response:
            status_code = response.getcode()
            content_type = response.headers.get('Content-Type', '')
            
            try:
                body = response.read().decode('utf-8')
                if 'application/json' in content_type:
                    try:
                        body = json.loads(body)
                    except json.JSONDecodeError:
                        pass
            except Exception:
                body = "Could not read response body"
            
            return {
                'success': True,
                'status_code': status_code,
                'content_type': content_type,
                'body': body,
                'error': None
            }
    except urllib.error.HTTPError as e:
        return {
            'success': False,
            'status_code': e.code,
            'content_type': '',
            'body': None,
            'error': f"HTTP Error {e.code}: {e.reason}"
        }
    except urllib.error.URLError as e:
        return {
            'success': False,
            'status_code': 0,
            'content_type': '',
            'body': None,
            'error': f"URL Error: {e.reason}"
        }
    except Exception as e:
        return {
            'success': False,
            'status_code': 0,
            'content_type': '',
            'body': None,
            'error': f"Request failed: {str(e)}"
        }

def test_endpoint(base_url: str, endpoint: str, expected_status: int = 200, critical: bool = True) -> bool:
    """Test a specific endpoint"""
    url = f"{base_url.rstrip('/')}/{endpoint.lstrip('/')}"
    print(f"Testing: {url}")
    
    result = make_request(url)
    
    if result['success'] and result['status_code'] == expected_status:
        print(f"✅ PASS - Status: {result['status_code']}")
        if isinstance(result['body'], dict):
            print(f"   Response: {json.dumps(result['body'], indent=2)[:200]}...")
        return True
    else:
        print(f"❌ FAIL - Status: {result['status_code']}, Error: {result['error']}")
        if critical:
            print(f"   CRITICAL: This endpoint must work for deployment to succeed")
        return False

def verify_function_discovery(base_url: str) -> bool:
    """Verify that functions are discoverable"""
    print(f"\n🔍 Verifying function discovery...")
    
    # Test admin endpoint
    admin_url = f"{base_url.rstrip('/')}/admin/functions"
    result = make_request(admin_url)
    
    if result['success'] and result['status_code'] == 200:
        print("✅ Admin endpoint accessible")
        if isinstance(result['body'], dict) and 'functions' in result['body']:
            function_count = len(result['body']['functions'])
            print(f"✅ Found {function_count} functions via admin endpoint")
            return function_count > 0
        else:
            print("⚠️  Admin endpoint accessible but no function data found")
            return False
    elif result['status_code'] == 404:
        print("⚠️  Admin endpoint not available (this is normal for some configurations)")
        return True  # Not critical
    elif result['status_code'] == 401:
        print("⚠️  Admin endpoint requires authentication (this is normal)")
        return True  # Not critical
    else:
        print(f"❌ Admin endpoint failed: {result['status_code']}")
        return False

def verify_health_endpoint(base_url: str) -> bool:
    """Verify health endpoint is working"""
    print(f"\n🔍 Verifying health endpoint...")
    # Try both with and without /api prefix
    if test_endpoint(base_url, "api/health", 200, critical=False):
        return True
    elif test_endpoint(base_url, "health", 200, critical=False):
        return True
    else:
        print("❌ Health endpoint not accessible at /api/health or /health")
        return False

def verify_info_endpoint(base_url: str) -> bool:
    """Verify info endpoint is working"""
    print(f"\n🔍 Verifying info endpoint...")
    # Try both with and without /api prefix
    if test_endpoint(base_url, "api/info", 200, critical=False):
        return True
    elif test_endpoint(base_url, "info", 200, critical=False):
        return True
    else:
        print("❌ Info endpoint not accessible at /api/info or /info")
        return False

def verify_basic_connectivity(base_url: str) -> bool:
    """Verify basic connectivity to function app"""
    print(f"\n🔍 Verifying basic connectivity...")
    result = make_request(base_url)
    
    if result['success'] and result['status_code'] in [200, 404]:  # 404 is normal for root
        print(f"✅ Function app is responding (Status: {result['status_code']})")
        return True
    else:
        print(f"❌ Function app is not responding: {result['error']}")
        return False

def wait_for_deployment(base_url: str, max_wait: int = 120, check_interval: int = 10) -> bool:
    """Wait for deployment to stabilize"""
    print(f"⏳ Waiting for deployment to stabilize (max {max_wait}s)...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        result = make_request(base_url, timeout=10)
        if result['success']:
            print(f"✅ Function app is responding after {int(time.time() - start_time)}s")
            return True
        else:
            print(f"⏳ Still waiting... ({int(time.time() - start_time)}s elapsed)")
            time.sleep(check_interval)
    
    print(f"❌ Function app did not respond within {max_wait}s")
    return False

def main():
    """Main verification function"""
    if len(sys.argv) != 2:
        print("Usage: python verify_azure_functions.py <function_app_url>")
        print("Example: python verify_azure_functions.py https://func-atomsec-sfdc-dev-stage.azurewebsites.net")
        sys.exit(1)
    
    base_url = sys.argv[1]
    print(f"🔍 Verifying Azure Functions deployment for: {base_url}")
    
    # Wait for deployment to stabilize
    if not wait_for_deployment(base_url):
        print("❌ Deployment verification failed: Function app not responding")
        sys.exit(1)
    
    # Run verification checks
    checks = [
        ("Basic Connectivity", lambda: verify_basic_connectivity(base_url)),
        ("Health Endpoint", lambda: verify_health_endpoint(base_url)),
        ("Info Endpoint", lambda: verify_info_endpoint(base_url)),
        ("Function Discovery", lambda: verify_function_discovery(base_url))
    ]
    
    results = []
    for check_name, check_func in checks:
        print(f"\n{'='*50}")
        print(f"Running {check_name} check...")
        print(f"{'='*50}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} check failed: {str(e)}")
            results.append((check_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("AZURE FUNCTIONS DEPLOYMENT VERIFICATION SUMMARY")
    print(f"{'='*60}")
    
    passed = 0
    critical_failures = []
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{check_name}: {status}")
        if result:
            passed += 1
        else:
            if check_name in ["Health Endpoint", "Info Endpoint"]:
                critical_failures.append(check_name)
    
    print(f"\nOverall: {passed}/{len(results)} checks passed")
    
    if len(critical_failures) > 0:
        print(f"\n🚨 CRITICAL FAILURES DETECTED:")
        for failure in critical_failures:
            print(f"   - {failure}")
        print(f"\n❌ DEPLOYMENT FAILED: Functions are not properly deployed")
        print("   The deployment will be marked as failed.")
        sys.exit(1)
    elif passed == len(results):
        print(f"\n🎉 All verification checks passed!")
        print("✅ Functions are properly deployed and visible in Azure portal")
        print("✅ Deployment is successful")
        sys.exit(0)
    else:
        print(f"\n⚠️  Some non-critical checks failed")
        print("✅ Deployment is successful (critical checks passed)")
        sys.exit(0)

if __name__ == "__main__":
    main() 