"""
Security Data Collection Blueprint

This module provides functions for retrieving and storing Salesforce security data:
- SecurityHealthCheckRisks
- PermissionSet permissions
- Profile permissions

All data is linked with an organization ID for cross-referencing.
"""

import logging
import azure.functions as func
import pandas as pd
import json
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

# Import shared modules
from shared.utils import create_json_response
from shared.salesforce_utils import get_salesforce_access_token, execute_salesforce_query, execute_salesforce_tooling_query
from shared.data_access import BlobStorageRepository, TableStorageRepository, SqlDatabaseRepository
from shared.azure_services import is_local_dev
from shared.cors_middleware import cors_middleware

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Lazy-initialized repositories
_blob_repo = None
_table_repos = {}
_sql_repo = None

def get_blob_repo():
    """Lazy initialize the blob repository"""
    global _blob_repo
    if _blob_repo is None:
        try:
            _blob_repo = BlobStorageRepository(container_name="security-data")
        except Exception as e:
            logger.warning(f"Failed to initialize blob repository: {str(e)}")
            _blob_repo = None
    return _blob_repo

def get_table_repo(table_name: str):
    """Lazy initialize a table repository"""
    global _table_repos
    if table_name not in _table_repos or _table_repos[table_name] is None:
        try:
            _table_repos[table_name] = TableStorageRepository(table_name=table_name)
        except Exception as e:
            logger.warning(f"Failed to initialize table repository for {table_name}: {str(e)}")
            _table_repos[table_name] = None
    return _table_repos[table_name]

def get_sql_repo():
    """Lazy initialize the SQL repository"""
    global _sql_repo
    if _sql_repo is None:
        try:
            _sql_repo = SqlDatabaseRepository()
        except Exception as e:
            logger.warning(f"Failed to initialize SQL repository: {str(e)}")
            _sql_repo = None
    return _sql_repo

async def get_salesforce_org_id(access_token: str, instance_url: str) -> str:
    """
    Get the Salesforce organization ID

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL

    Returns:
        str: Salesforce organization ID
    """
    query = "SELECT Id FROM Organization LIMIT 1"

    try:
        response = await execute_salesforce_query(query, access_token, instance_url, api_version="v62.0")
        if response and "records" in response and response["records"]:
            org_id = response["records"][0].get("Id", "")
            logger.info(f"Retrieved Salesforce organization ID: {org_id}")
            return org_id
        else:
            logger.warning("No organization ID found")
            return "unknown-org"
    except Exception as e:
        logger.error(f"Error fetching organization ID: {str(e)}")
        return "unknown-org"

async def fetch_security_health_check_risks(access_token: str, instance_url: str) -> List[Dict[str, Any]]:
    """
    Fetch SecurityHealthCheckRisks data from Salesforce using the Tooling API

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL

    Returns:
        List[Dict[str, Any]]: SecurityHealthCheckRisks data
    """
    query = "SELECT RiskType, Setting, SettingGroup, OrgValue, StandardValue FROM SecurityHealthCheckRisks"

    try:
        # Use the tooling API for SecurityHealthCheckRisks
        response = await execute_salesforce_tooling_query(query, access_token, instance_url, api_version="v62.0")
        if response and "records" in response:
            logger.info(f"Successfully retrieved {len(response['records'])} SecurityHealthCheckRisks records")
            return response["records"]
        else:
            logger.warning("No SecurityHealthCheckRisks data found using Tooling API")

            # Try alternative approach - query SecuritySettings
            logger.info("Attempting to fetch SecuritySettings data instead")
            security_settings_query = "SELECT Id, PasswordPolicies.MinimumPasswordLength, PasswordPolicies.Complexity FROM SecuritySettings"
            settings_response = await execute_salesforce_tooling_query(security_settings_query, access_token, instance_url, api_version="v62.0")

            if settings_response and "records" in settings_response and settings_response["records"]:
                # Transform SecuritySettings data to match SecurityHealthCheckRisks format
                transformed_records = []
                for record in settings_response["records"]:
                    # Add password length record
                    password_length = record.get("PasswordPolicies", {}).get("MinimumPasswordLength", 0)
                    transformed_records.append({
                        "Id": record.get("Id", "") + "-pwlen",
                        "RiskType": "MEDIUM_RISK" if int(password_length) < 8 else "COMPLIANT",
                        "Setting": "Minimum Password Length",
                        "SettingGroup": "Password Policies",
                        "OrgValue": str(password_length),
                        "StandardValue": "8"
                    })

                    # Add password complexity record
                    complexity = record.get("PasswordPolicies", {}).get("Complexity", "")
                    transformed_records.append({
                        "Id": record.get("Id", "") + "-pwcomplx",
                        "RiskType": "MEDIUM_RISK" if complexity != "ALPHANUMERIC" else "COMPLIANT",
                        "Setting": "Password Complexity",
                        "SettingGroup": "Password Policies",
                        "OrgValue": complexity,
                        "StandardValue": "ALPHANUMERIC"
                    })

                logger.info(f"Successfully transformed {len(transformed_records)} SecuritySettings records")
                return transformed_records
            else:
                logger.warning("No SecuritySettings data found either")
                return []
    except Exception as e:
        logger.error(f"Error fetching SecurityHealthCheckRisks data: {str(e)}")
        return []

async def fetch_permission_sets(access_token: str, instance_url: str) -> List[Dict[str, Any]]:
    """
    Fetch PermissionSet data from Salesforce

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL

    Returns:
        List[Dict[str, Any]]: PermissionSet data
    """
    query = """
    SELECT Name,
           PermissionsModifyAllData,
           PermissionsViewAllData,
           PermissionsResetPasswords,
           PermissionsDataExport,
           PermissionsManageUsers,
           PermissionsManageProfilesPermissionsets,
           PermissionsManageRoles,
           PermissionsManageSharing,
           PermissionsEditReadonlyFields,
           PermissionsViewSetup,
           PermissionsAuthorApex,
           PermissionsViewAllUsers,
           PermissionsManageEncryptionKeys,
           PermissionsManageTwoFactor,
           PermissionsModifyMetadata,
           PermissionsCustomizeApplication,
           PermissionsManageIpAddresses
    FROM PermissionSet
    """

    try:
        response = await execute_salesforce_query(query, access_token, instance_url, api_version="v62.0")
        if response and "records" in response:
            logger.info(f"Successfully retrieved {len(response['records'])} PermissionSet records")
            return response["records"]
        else:
            logger.warning("No PermissionSet data found")
            return []
    except Exception as e:
        logger.error(f"Error fetching PermissionSet data: {str(e)}")
        return []

async def fetch_profiles(access_token: str, instance_url: str) -> List[Dict[str, Any]]:
    """
    Fetch Profile data from Salesforce

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL

    Returns:
        List[Dict[str, Any]]: Profile data
    """
    query = """
    SELECT Name,
           PermissionsModifyAllData,
           PermissionsViewAllData,
           PermissionsResetPasswords,
           PermissionsDataExport,
           PermissionsManageUsers,
           PermissionsManageProfilesPermissionsets,
           PermissionsManageRoles,
           PermissionsManageSharing,
           PermissionsEditReadonlyFields,
           PermissionsViewSetup,
           PermissionsAuthorApex,
           PermissionsViewAllUsers,
           PermissionsManageEncryptionKeys,
           PermissionsManageTwoFactor,
           PermissionsModifyMetadata,
           PermissionsCustomizeApplication,
           PermissionsManageIpAddresses
    FROM Profile
    """

    try:
        response = await execute_salesforce_query(query, access_token, instance_url, api_version="v62.0")
        if response and "records" in response:
            logger.info(f"Successfully retrieved {len(response['records'])} Profile records")
            return response["records"]
        else:
            logger.warning("No Profile data found")
            return []
    except Exception as e:
        logger.error(f"Error fetching Profile data: {str(e)}")
        return []

def create_tables_if_not_exist():
    """Create the necessary tables in local storage if they don't exist"""
    if is_local_dev():
        # Create SecurityHealthCheckRisks table
        get_table_repo("SecurityHealthCheckRisks")

        # Create PermissionSetData table
        get_table_repo("PermissionSetData")

        # Create ProfileData table
        get_table_repo("ProfileData")

        logger.info("Created tables in local storage")
    else:
        # In production, tables should already exist in SQL database
        pass

def store_security_health_check_risks(risks_data: List[Dict[str, Any]], org_id: str) -> bool:
    """
    Store SecurityHealthCheckRisks data in the database

    Args:
        risks_data: SecurityHealthCheckRisks data
        org_id: Salesforce organization ID

    Returns:
        bool: True if successful, False otherwise
    """
    if is_local_dev():
        # Use Azure Table Storage for local development
        table_repo = get_table_repo("SecurityHealthCheckRisks")
        if table_repo is None:
            logger.warning("Table repository is not available")
            return False

        success_count = 0
        for record in risks_data:
            entity = {
                'PartitionKey': org_id,
                'RowKey': f"{datetime.now().strftime('%Y%m%d%H%M%S')}-{success_count}",
                'OrgId': org_id,
                'RiskType': record.get('RiskType', ''),
                'Setting': record.get('Setting', ''),
                'SettingGroup': record.get('SettingGroup', ''),
                'OrgValue': record.get('OrgValue', ''),
                'StandardValue': record.get('StandardValue', ''),
                'ScanDate': datetime.now().isoformat()
            }

            if table_repo.insert_entity(entity):
                success_count += 1

        logger.info(f"Inserted {success_count} SecurityHealthCheckRisks records")
        return success_count > 0
    else:
        # Use SQL Database in production
        sql_repo = get_sql_repo()
        if sql_repo is None:
            logger.warning("SQL repository is not available")
            return False

        query = """
            INSERT INTO SecurityHealthChecks
            (RiskType, Setting, SettingGroup, OrgValue, StandardValue, ExecutionLogId)
            VALUES (?, ?, ?, ?, ?, ?)
        """

        # Get or create execution log ID
        execution_log_id = get_or_create_execution_log(org_id)
        if not execution_log_id:
            logger.warning("Failed to get or create execution log")
            return False

        success_count = 0
        for record in risks_data:
            params = (
                record.get('RiskType', ''),
                record.get('Setting', ''),
                record.get('SettingGroup', ''),
                record.get('OrgValue', ''),
                record.get('StandardValue', ''),
                execution_log_id
            )

            if sql_repo.execute_non_query(query, params):
                success_count += 1

        logger.info(f"Inserted {success_count} SecurityHealthCheckRisks records")
        return success_count > 0

def store_permission_sets(permission_sets: List[Dict[str, Any]], org_id: str) -> bool:
    """
    Store PermissionSet data in the database

    Args:
        permission_sets: PermissionSet data
        org_id: Salesforce organization ID

    Returns:
        bool: True if successful, False otherwise
    """
    if is_local_dev():
        # Use Azure Table Storage for local development
        table_repo = get_table_repo("PermissionSetData")
        if table_repo is None:
            logger.warning("Table repository is not available")
            return False

        success_count = 0
        for record in permission_sets:
            entity = {
                'PartitionKey': org_id,
                'RowKey': f"{record.get('Name', 'Unknown')}-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                'OrgId': org_id,
                'Name': record.get('Name', ''),
                'PermissionsModifyAllData': str(record.get('PermissionsModifyAllData', False)).lower(),
                'PermissionsViewAllData': str(record.get('PermissionsViewAllData', False)).lower(),
                'PermissionsResetPasswords': str(record.get('PermissionsResetPasswords', False)).lower(),
                'PermissionsDataExport': str(record.get('PermissionsDataExport', False)).lower(),
                'PermissionsManageUsers': str(record.get('PermissionsManageUsers', False)).lower(),
                'PermissionsManageProfilesPermissionsets': str(record.get('PermissionsManageProfilesPermissionsets', False)).lower(),
                'PermissionsManageRoles': str(record.get('PermissionsManageRoles', False)).lower(),
                'PermissionsManageSharing': str(record.get('PermissionsManageSharing', False)).lower(),
                'PermissionsEditReadonlyFields': str(record.get('PermissionsEditReadonlyFields', False)).lower(),
                'PermissionsViewSetup': str(record.get('PermissionsViewSetup', False)).lower(),
                'PermissionsAuthorApex': str(record.get('PermissionsAuthorApex', False)).lower(),
                'PermissionsViewAllUsers': str(record.get('PermissionsViewAllUsers', False)).lower(),
                'PermissionsManageEncryptionKeys': str(record.get('PermissionsManageEncryptionKeys', False)).lower(),
                'PermissionsManageTwoFactor': str(record.get('PermissionsManageTwoFactor', False)).lower(),
                'PermissionsModifyMetadata': str(record.get('PermissionsModifyMetadata', False)).lower(),
                'PermissionsCustomizeApplication': str(record.get('PermissionsCustomizeApplication', False)).lower(),
                'PermissionsManageIpAddresses': str(record.get('PermissionsManageIpAddresses', False)).lower(),
                'ScanDate': datetime.now().isoformat()
            }

            if table_repo.insert_entity(entity):
                success_count += 1

        logger.info(f"Inserted {success_count} PermissionSet records")
        return success_count > 0
    else:
        # Use SQL Database in production
        # Implementation would be similar to store_security_health_check_risks
        # but with the appropriate table and columns
        return False

def store_profiles(profiles: List[Dict[str, Any]], org_id: str) -> bool:
    """
    Store Profile data in the database

    Args:
        profiles: Profile data
        org_id: Salesforce organization ID

    Returns:
        bool: True if successful, False otherwise
    """
    if is_local_dev():
        # Use Azure Table Storage for local development
        table_repo = get_table_repo("ProfileData")
        if table_repo is None:
            logger.warning("Table repository is not available")
            return False

        success_count = 0
        for record in profiles:
            entity = {
                'PartitionKey': org_id,
                'RowKey': f"{record.get('Name', 'Unknown')}-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                'OrgId': org_id,
                'Name': record.get('Name', ''),
                'PermissionsModifyAllData': str(record.get('PermissionsModifyAllData', False)).lower(),
                'PermissionsViewAllData': str(record.get('PermissionsViewAllData', False)).lower(),
                'PermissionsResetPasswords': str(record.get('PermissionsResetPasswords', False)).lower(),
                'PermissionsDataExport': str(record.get('PermissionsDataExport', False)).lower(),
                'PermissionsManageUsers': str(record.get('PermissionsManageUsers', False)).lower(),
                'PermissionsManageProfilesPermissionsets': str(record.get('PermissionsManageProfilesPermissionsets', False)).lower(),
                'PermissionsManageRoles': str(record.get('PermissionsManageRoles', False)).lower(),
                'PermissionsManageSharing': str(record.get('PermissionsManageSharing', False)).lower(),
                'PermissionsEditReadonlyFields': str(record.get('PermissionsEditReadonlyFields', False)).lower(),
                'PermissionsViewSetup': str(record.get('PermissionsViewSetup', False)).lower(),
                'PermissionsAuthorApex': str(record.get('PermissionsAuthorApex', False)).lower(),
                'PermissionsViewAllUsers': str(record.get('PermissionsViewAllUsers', False)).lower(),
                'PermissionsManageEncryptionKeys': str(record.get('PermissionsManageEncryptionKeys', False)).lower(),
                'PermissionsManageTwoFactor': str(record.get('PermissionsManageTwoFactor', False)).lower(),
                'PermissionsModifyMetadata': str(record.get('PermissionsModifyMetadata', False)).lower(),
                'PermissionsCustomizeApplication': str(record.get('PermissionsCustomizeApplication', False)).lower(),
                'PermissionsManageIpAddresses': str(record.get('PermissionsManageIpAddresses', False)).lower(),
                'ScanDate': datetime.now().isoformat()
            }

            if table_repo.insert_entity(entity):
                success_count += 1

        logger.info(f"Inserted {success_count} Profile records")
        return success_count > 0
    else:
        # Use SQL Database in production
        # Implementation would be similar to store_security_health_check_risks
        # but with the appropriate table and columns
        return False

def get_or_create_execution_log(org_id: str) -> int:
    """
    Get or create an execution log entry

    Args:
        org_id: Salesforce organization ID

    Returns:
        int: Execution log ID
    """
    if not is_local_dev():
        sql_repo = get_sql_repo()
        if sql_repo is None:
            return 0

        # Check if there's a recent execution log
        query = """
            SELECT TOP 1 Id FROM App_ExecutionLog
            WHERE OrgId = ? AND CAST(Timestamp as datetime) > DATEADD(hour, -1, GETDATE())
            ORDER BY Id DESC
        """

        results = sql_repo.execute_query(query, (org_id,))
        if results and len(results) > 0:
            return results[0][0]

        # Create a new execution log
        query = """
            INSERT INTO App_ExecutionLog (Timestamp, OrgId)
            VALUES (?, ?)
        """

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        sql_repo.execute_non_query(query, (timestamp, org_id))

        # Get the ID of the new execution log
        query = """
            SELECT TOP 1 Id FROM App_ExecutionLog
            WHERE OrgId = ?
            ORDER BY Id DESC
        """

        results = sql_repo.execute_query(query, (org_id,))
        if results and len(results) > 0:
            return results[0][0]

    return 0

def get_integration_id_for_instance(instance_url: str) -> str:
    """
    Get integration ID for a Salesforce instance URL

    Args:
        instance_url: Salesforce instance URL

    Returns:
        str: Integration ID or None if not found
    """
    try:
        integration_repo = TableStorageRepository(table_name="Integrations")
        entities = list(integration_repo.query_entities())

        # Normalize instance URL for comparison
        normalized_instance = instance_url.lower()
        if normalized_instance.startswith("http://"):
            normalized_instance = normalized_instance[7:]
        elif normalized_instance.startswith("https://"):
            normalized_instance = normalized_instance[8:]
        normalized_instance = normalized_instance.rstrip("/")

        # Find matching active integration
        for entity in entities:
            if entity.get("PartitionKey") == "integration" and entity.get("IsActive") is True:
                tenant_url = entity.get("TenantUrl", "").lower()

                # Normalize for comparison
                if tenant_url.startswith("http://"):
                    tenant_url = tenant_url[7:]
                elif tenant_url.startswith("https://"):
                    tenant_url = tenant_url[8:]
                tenant_url = tenant_url.rstrip("/")

                if tenant_url and (tenant_url == normalized_instance or normalized_instance.endswith(tenant_url)):
                    return entity.get("RowKey")

        logger.warning(f"No active integration found for instance URL: {instance_url}")
        return None
    except Exception as e:
        logger.error(f"Error finding integration ID for instance URL {instance_url}: {str(e)}")
        return None

async def fetch_and_store_security_data(instance_url: str) -> Dict[str, Any]:
    """
    Fetch and store security data from Salesforce in the background

    Args:
        instance_url: Salesforce instance URL

    Returns:
        Dict[str, Any]: Results of the operation
    """
    try:
        logger.info(f"Starting background fetch of security data for {instance_url}")

        # Create tables if they don't exist
        create_tables_if_not_exist()

        # Get integration ID
        integration_id = get_integration_id_for_instance(instance_url)
        if not integration_id:
            logger.error(f"No active integration found for instance URL: {instance_url}")
            return {
                "success": False,
                "message": f"No active integration found for instance URL: {instance_url}"
            }

        # Get Salesforce access token
        access_token, sf_instance_url = get_salesforce_access_token(instance_url)
        if not access_token:
            logger.error(f"Failed to obtain Salesforce access token for {instance_url}")
            return {
                "success": False,
                "message": f"Failed to obtain Salesforce access token for {instance_url}"
            }

        # Get Salesforce organization ID
        org_id = await get_salesforce_org_id(access_token, sf_instance_url)
        if not org_id:
            logger.error(f"Failed to obtain Salesforce organization ID for {instance_url}")
            return {
                "success": False,
                "message": f"Failed to obtain Salesforce organization ID for {instance_url}"
            }

        # Fetch data from Salesforce
        logger.info(f"Fetching security health check risks for {instance_url}")
        risks_data = await fetch_security_health_check_risks(access_token, sf_instance_url)

        logger.info(f"Fetching permission sets for {instance_url}")
        permission_sets = await fetch_permission_sets(access_token, sf_instance_url)

        logger.info(f"Fetching profiles for {instance_url}")
        profiles = await fetch_profiles(access_token, sf_instance_url)

        # Store data in database
        logger.info(f"Storing security health check risks for {instance_url}")
        risks_stored = store_security_health_check_risks(risks_data, org_id)

        logger.info(f"Storing permission sets for {instance_url}")
        permission_sets_stored = store_permission_sets(permission_sets, org_id)

        logger.info(f"Storing profiles for {instance_url}")
        profiles_stored = store_profiles(profiles, org_id)

        # Save to blob storage for backup
        blob_repo = get_blob_repo()
        if blob_repo is not None:
            try:
                blob_name = f"security-data-{instance_url}-{datetime.now().strftime('%Y%m%d-%H%M%S')}.json"
                blob_data = {
                    "timestamp": datetime.now().isoformat(),
                    "instance_url": instance_url,
                    "org_id": org_id,
                    "risks_data": risks_data,
                    "permission_sets": permission_sets,
                    "profiles": profiles
                }
                blob_repo.save_json(blob_name, blob_data)
                logger.info(f"Saved security data to blob: {blob_name}")
            except Exception as e:
                logger.warning(f"Failed to save to blob storage: {str(e)}")

        # Prepare results
        results = {
            "success": True,
            "instance_url": instance_url,
            "org_id": org_id,
            "timestamp": datetime.now().isoformat(),
            "risks_data": {
                "count": len(risks_data),
                "stored": risks_stored
            },
            "permission_sets": {
                "count": len(permission_sets),
                "stored": permission_sets_stored
            },
            "profiles": {
                "count": len(profiles),
                "stored": profiles_stored
            }
        }

        logger.info(f"Successfully fetched and stored security data for {instance_url}")
        return results

    except Exception as e:
        error_message = f"Error fetching security data for {instance_url}: {str(e)}"
        logger.error(error_message)
        return {
            "success": False,
            "message": error_message
        }

# The fetch_security_data endpoint has been removed as requested
# This functionality is now provided by the integration_tabs.py blueprint
# which handles fetching security data for each integration

# Note: Both view_security_data and fetch_security_data functions have been removed
# This functionality is now provided by the integration_tabs.py blueprint
# which handles fetching and displaying security data for each integration
