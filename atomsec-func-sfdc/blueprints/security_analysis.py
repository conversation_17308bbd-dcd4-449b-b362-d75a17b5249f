"""
Salesforce Security Analysis Blueprint

This module provides endpoints for analyzing Salesforce security:
- Health score calculation
- Security risks analysis
- Profile permissions analysis
- Permission set analysis

Best practices implemented:
- Proper error handling and logging
- Input validation
- Centralized configuration
- Reusable utility functions
"""

import logging
import azure.functions as func
import json
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
import re
import os
import xml.etree.ElementTree as ET
import uuid
from azure.data.tables import TableServiceClient
from azure.core.exceptions import ResourceExistsError

# Import shared modules
from shared.db_service_client import get_db_client
from shared.azure_services import is_local_dev
from shared.utils import create_json_response, handle_exception
from shared.salesforce_utils import get_salesforce_access_token, execute_salesforce_query, execute_salesforce_tooling_query
from shared.auth_utils import get_current_user
from shared.cors_middleware import cors_middleware

# Configure module-level logger
logger = logging.getLogger(__name__)

# Helper functions
def check_integration_status(instance_url: str) -> str:
    """
    Check if an integration exists and is active

    Args:
        instance_url: Salesforce instance URL

    Returns:
        str: Error message
    """
    from shared.data_access import TableStorageRepository

    try:
        integration_repo = TableStorageRepository(table_name="Integrations")
        entities = list(integration_repo.query_entities())

        inactive_integration = False
        for entity in entities:
            if entity.get("PartitionKey") == "integration" and entity.get("IsActive") is False:
                tenant_url = entity.get("TenantUrl", "").lower()

                # Normalize for comparison
                if tenant_url.startswith("http://"):
                    tenant_url = tenant_url[7:]
                elif tenant_url.startswith("https://"):
                    tenant_url = tenant_url[8:]

                tenant_url = tenant_url.rstrip("/")

                normalized_instance = instance_url.lower()
                if normalized_instance.startswith("http://"):
                    normalized_instance = normalized_instance[7:]
                elif normalized_instance.startswith("https://"):
                    normalized_instance = normalized_instance[8:]

                normalized_instance = normalized_instance.rstrip("/")

                if tenant_url and (tenant_url == normalized_instance or normalized_instance.endswith(tenant_url)):
                    inactive_integration = True
                    break

        if inactive_integration:
            return f"Integration for {instance_url} exists but is inactive. Please activate it at the /scan endpoint."
        else:
            return f"Org not registered or credentials not found for {instance_url}. Please add the integration at the /scan endpoint."
    except Exception as e:
        logger.error(f"Error checking integration status: {str(e)}")
        return f"Org not registered or credentials not found for {instance_url}. Please add the integration at the /scan endpoint."

def get_integration_id_for_instance(instance_url: str) -> str:
    """
    Get integration ID for a Salesforce instance URL

    Args:
        instance_url: Salesforce instance URL

    Returns:
        str: Integration ID or None if not found
    """
    from shared.data_access import TableStorageRepository

    try:
        integration_repo = TableStorageRepository(table_name="Integrations")
        entities = list(integration_repo.query_entities())

        # Normalize instance URL for comparison
        normalized_instance = instance_url.lower()
        if normalized_instance.startswith("http://"):
            normalized_instance = normalized_instance[7:]
        elif normalized_instance.startswith("https://"):
            normalized_instance = normalized_instance[8:]
        normalized_instance = normalized_instance.rstrip("/")

        # Find matching active integration
        for entity in entities:
            if entity.get("PartitionKey") == "integration" and entity.get("IsActive") is True:
                tenant_url = entity.get("TenantUrl", "").lower()

                # Normalize for comparison
                if tenant_url.startswith("http://"):
                    tenant_url = tenant_url[7:]
                elif tenant_url.startswith("https://"):
                    tenant_url = tenant_url[8:]
                tenant_url = tenant_url.rstrip("/")

                if tenant_url and (tenant_url == normalized_instance or normalized_instance.endswith(tenant_url)):
                    return entity.get("RowKey")

        return None
    except Exception as e:
        logger.error(f"Error finding integration ID for instance URL {instance_url}: {str(e)}")
        return None

def get_security_data_from_db(integration_id: str, data_type: str) -> dict:
    """
    Get security data from database

    Args:
        integration_id: Integration ID
        data_type: Type of data to retrieve (health_score, health_risks, profiles, permission_sets)

    Returns:
        dict: Security data or None if not found
    """
    from shared.data_access import TableStorageRepository

    try:
        security_repo = TableStorageRepository(table_name="SecurityData")

        # Query for the specific data type for this integration
        filter_query = f"PartitionKey eq '{integration_id}' and DataType eq '{data_type}'"
        entities = list(security_repo.query_entities(filter_query))

        if entities:
            # Sort by timestamp to get the most recent data
            entities.sort(key=lambda x: x.get("Timestamp", ""), reverse=True)
            latest_data = entities[0]

            # Check if data is recent (less than 24 hours old)
            data_timestamp = latest_data.get("Timestamp", "")
            if data_timestamp:
                from datetime import datetime, timedelta
                try:
                    data_time = datetime.fromisoformat(data_timestamp)
                    now = datetime.now()
                    if now - data_time < timedelta(hours=24):
                        # Data is recent, return it
                        data_json = latest_data.get("Data", "{}")
                        return json.loads(data_json)
                except Exception as e:
                    logger.error(f"Error parsing timestamp: {str(e)}")

        # No recent data found
        return None
    except Exception as e:
        logger.error(f"Error retrieving security data from database: {str(e)}")
        return None

def store_security_data_in_db(integration_id: str, data_type: str, data: dict) -> bool:
    """
    Store security data in database

    Args:
        integration_id: Integration ID
        data_type: Type of data to store (health_score, health_risks, profiles, permission_sets)
        data: Data to store

    Returns:
        bool: Success status
    """
    from shared.data_access import TableStorageRepository
    import uuid

    try:
        security_repo = TableStorageRepository(table_name="SecurityData")

        # Create entity
        entity = {
            "PartitionKey": integration_id,
            "RowKey": str(uuid.uuid4()),
            "DataType": data_type,
            "Data": json.dumps(data),
            "Timestamp": datetime.now().isoformat()
        }

        # Store entity
        security_repo.insert_entity(entity)
        logger.info(f"Stored {data_type} data for integration {integration_id}")
        return True
    except Exception as e:
        logger.error(f"Error storing security data in database: {str(e)}")
        return False

async def trigger_background_fetch(instance_url: str, data_type: str) -> bool:
    """
    Trigger background fetch of security data

    Args:
        instance_url: Salesforce instance URL
        data_type: Type of data to fetch (health_score, health_risks, profiles, permission_sets)

    Returns:
        bool: Success status
    """
    try:
        # Get integration ID
        integration_id = get_integration_id_for_instance(instance_url)
        if not integration_id:
            logger.error(f"No active integration found for {instance_url}")
            return False

        # Get Salesforce access token
        access_token, sf_instance_url = get_salesforce_access_token(instance_url)
        if not access_token:
            logger.error(f"Failed to get access token for {instance_url}")
            return False

        # Fetch data based on type
        data = None
        if data_type == "health_score" or data_type == "health_risks":
            # Fetch security health check risks
            risks = await fetch_security_health_check_risks(access_token, sf_instance_url)

            if data_type == "health_score":
                # Try to get the score directly from SecurityHealthCheck
                score_query = "SELECT Score FROM SecurityHealthCheck"
                score_result = await execute_salesforce_tooling_query(score_query, access_token, sf_instance_url, api_version="v62.0")

                if score_result and "records" in score_result and len(score_result["records"]) > 0:
                    # Use the score directly from Salesforce
                    score = score_result["records"][0].get("Score", 0)
                    logger.info(f"Retrieved health score directly from Salesforce: {score}")
                else:
                    logger.warning("Could not retrieve health score directly from Salesforce, using fallback calculation")
                    # Calculate health score based on risks
                    score = await calculate_health_score(risks, access_token, sf_instance_url)

                # Prepare data
                data = {
                    "score": score,
                    "timestamp": datetime.now().isoformat(),
                    "instanceUrl": instance_url,
                    "totalRisks": len(risks),
                    "riskCounts": {
                        "HIGH_RISK": len([r for r in risks if r.get("RiskType") == "HIGH_RISK"]),
                        "MEDIUM_RISK": len([r for r in risks if r.get("RiskType") == "MEDIUM_RISK"]),
                        "LOW_RISK": len([r for r in risks if r.get("RiskType") == "LOW_RISK"]),
                        "COMPLIANT": len([r for r in risks if r.get("RiskType") == "COMPLIANT"]),
                        "INFORMATIONAL": len([r for r in risks if r.get("RiskType") == "INFORMATIONAL"])
                    }
                }
            else:  # health_risks
                # Group risks by type
                risks_by_type = {}
                for risk in risks:
                    risk_type = risk.get("RiskType", "UNKNOWN")
                    if risk_type not in risks_by_type:
                        risks_by_type[risk_type] = []
                    risks_by_type[risk_type].append(risk)

                # Prepare data
                data = {
                    "risks": risks,
                    "risksByType": risks_by_type,
                    "timestamp": datetime.now().isoformat(),
                    "instanceUrl": instance_url,
                    "totalRisks": len(risks)
                }
        elif data_type == "profiles":
            # Fetch profiles
            profiles_data = await fetch_profiles(access_token, sf_instance_url)

            # Fetch permissions for each profile
            for profile in profiles_data:
                profile["permissions"] = await fetch_profile_permissions(access_token, sf_instance_url, profile["Id"])

            # Prepare data
            data = {
                "profiles": profiles_data,
                "timestamp": datetime.now().isoformat(),
                "instanceUrl": instance_url,
                "totalProfiles": len(profiles_data)
            }
        elif data_type == "permission_sets":
            # Fetch permission sets
            permission_sets_data = await fetch_permission_sets(access_token, sf_instance_url)

            # Fetch permissions for each permission set
            for permission_set in permission_sets_data:
                permission_set["permissions"] = await fetch_permission_set_permissions(access_token, sf_instance_url, permission_set["Id"])

            # Prepare data
            data = {
                "permissionSets": permission_sets_data,
                "timestamp": datetime.now().isoformat(),
                "instanceUrl": instance_url,
                "totalPermissionSets": len(permission_sets_data)
            }

        # Store data in database
        if data:
            return store_security_data_in_db(integration_id, data_type, data)

        return False
    except Exception as e:
        logger.error(f"Error triggering background fetch: {str(e)}")
        return False

# Create blueprint
bp = func.Blueprint()

# Lazy-initialized repositories
_blob_repo = None
_table_repos = {}
_sql_repo = None

def get_blob_repo():
    """Lazy initialize the blob repository"""
    global _blob_repo
    if _blob_repo is None:
        try:
            _blob_repo = BlobStorageRepository(container_name="security-analysis")
            logger.info("Initialized blob repository")
        except Exception as e:
            logger.error(f"Failed to initialize blob repository: {str(e)}")
            _blob_repo = None
    return _blob_repo

def get_table_repo(table_name: str):
    """Lazy initialize a table repository"""
    global _table_repos
    if table_name not in _table_repos or _table_repos[table_name] is None:
        try:
            _table_repos[table_name] = TableStorageRepository(table_name=table_name)
            logger.info(f"Initialized table repository for {table_name}")
        except Exception as e:
            logger.error(f"Failed to initialize table repository for {table_name}: {str(e)}")
            _table_repos[table_name] = None
    return _table_repos[table_name]

def get_sql_repo():
    """Lazy initialize the SQL repository"""
    global _sql_repo
    if _sql_repo is None and not is_local_dev():
        try:
            _sql_repo = SqlDatabaseRepository()
            logger.info("Initialized SQL repository")
        except Exception as e:
            logger.error(f"Failed to initialize SQL repository: {str(e)}")
            _sql_repo = None
    return _sql_repo

def _strip_api_path_from_instance_url(instance_url: str) -> str:
    """
    Ensure instance_url is just the base domain (no /services/data/vXX.X/ suffix)
    """
    return re.sub(r"/services/data/v[0-9]+\.[0-9]+/?$", "", instance_url)

async def fetch_security_health_check_risks(access_token: str, instance_url: str) -> List[Dict[str, Any]]:
    """
    Fetch security health check risks from Salesforce

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL

    Returns:
        List[Dict[str, Any]]: Security health check risks
    """
    try:
        # Ensure instance_url is just the base domain
        base_instance_url = _strip_api_path_from_instance_url(instance_url)
        query = "SELECT RiskType, SettingRiskCategory, SettingGroup, Setting, OrgValue, StandardValue FROM SecurityHealthCheckRisks"

        # Always try Tooling API first for these objects
        tooling_url = f"{base_instance_url}/services/data/v62.0/tooling/query/?q={query.replace(' ', '+')}"
        logger.info(f"[DEBUG] Tooling API full URL: {tooling_url}")
        logger.info(f"[DEBUG] Using access token (masked): {access_token[:10]}...{access_token[-5:]}")
        result = await execute_salesforce_tooling_query(query, access_token, base_instance_url)
        logger.info(f"[DEBUG] Tooling API response for query: {query}")
        if hasattr(result, 'status_code'):
            logger.info(f"[DEBUG] Response status: {result.status_code}")
        logger.info(f"[DEBUG] Response body: {result}")

        if result and "records" in result and len(result["records"]) > 0:
            logger.info(f"Successfully executed tooling query: {query}")

            # Transform data to consistent format
            records = result["records"]

            # Handle different response formats
            if "SecurityHealthCheckRisks" in query:
                # Use the correct field names for this org
                for record in records:
                    pass
            elif "HealthCheckRisk" in query:
                # Map CurrentValue to OrgValue for consistency
                for record in records:
                    if "CurrentValue" in record:
                        record["OrgValue"] = record["CurrentValue"]
                    if "SettingGroup" in record:
                        record["SettingRiskCategory"] = record["SettingGroup"]
            return records

        # If Tooling API fails, try regular API as fallback
        try:
            result = await execute_salesforce_query(query, access_token, instance_url)
            if result and "records" in result and len(result["records"]) > 0:
                logger.info(f"Successfully executed query: {query}")

                # Transform data to consistent format
                records = result["records"]

                # Handle different response formats
                if "SecurityHealthCheckRisks" in query:
                    # Use the correct field names for this org
                    for record in records:
                        pass
                elif "HealthCheckRisk" in query:
                    # Map CurrentValue to OrgValue for consistency
                    for record in records:
                        if "CurrentValue" in record:
                            record["OrgValue"] = record["CurrentValue"]
                        if "SettingGroup" in record:
                            record["SettingRiskCategory"] = record["SettingGroup"]

                return records
        except Exception as e:
            # Log and continue to next query
            logger.warning(f"Query failed for {query}: {e}")
            return []

        # If all queries fail, log error and return empty list
        logger.error("All Salesforce security health check queries failed")
        return []
    except Exception as e:
        logger.error(f"Error fetching security health check risks: {str(e)}")
        return []

async def fetch_profiles(access_token: str, instance_url: str) -> List[Dict[str, Any]]:
    """
    Fetch profiles from Salesforce

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL

    Returns:
        List[Dict[str, Any]]: Profiles
    """
    try:
        # Query profiles
        query = "SELECT Id, Name, Description, UserType, CreatedDate, LastModifiedDate FROM Profile"

        result = await execute_salesforce_query(query, access_token, instance_url)

        if result and "records" in result:
            logger.info(f"Successfully fetched {len(result['records'])} profiles")
            return result["records"]

        # If query fails, return mock data
        logger.warning("Profile query failed, returning mock data")
        return [
            {
                "Id": "00e58000001EpSiAAK",
                "Name": "DUMMY Administrator",
                "Description": "DUMMY Administrator",
                "UserType": "Standard",
                "CreatedDate": "2022-01-01T00:00:00.000Z",
                "LastModifiedDate": "2022-01-01T00:00:00.000Z"
            },
            {
                "Id": "00e58000001EpSjAAK",
                "Name": "DUMMY User",
                "Description": "DUMMY User",
                "UserType": "Standard",
                "CreatedDate": "2022-01-01T00:00:00.000Z",
                "LastModifiedDate": "2022-01-01T00:00:00.000Z"
            }
        ]
    except Exception as e:
        logger.error(f"Error fetching profiles: {str(e)}")
        return []

async def fetch_profile_permissions(access_token: str, instance_url: str, profile_id: str) -> List[Dict[str, Any]]:
    """
    Fetch profile permissions from Salesforce

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL
        profile_id: Profile ID

    Returns:
        List[Dict[str, Any]]: Profile permissions
    """
    try:
        # Query object permissions
        query = f"""
        SELECT Id, Parent.Label, PermissionsCreate, PermissionsRead, PermissionsEdit, PermissionsDelete,
               PermissionsViewAllRecords, PermissionsModifyAllRecords
        FROM ObjectPermissions
        WHERE ParentId = '{profile_id}'
        """

        result = await execute_salesforce_query(query, access_token, instance_url)

        if result and "records" in result:
            logger.info(f"Successfully fetched {len(result['records'])} object permissions for profile {profile_id}")
            return result["records"]

        # If query fails, return mock data
        logger.warning(f"Profile permissions query failed for profile {profile_id}, returning mock data")
        return [
            {
                "Id": "0Ps58000001EpSiAAK",
                "Parent": {"Label": "Account"},
                "PermissionsCreate": True,
                "PermissionsRead": True,
                "PermissionsEdit": True,
                "PermissionsDelete": True,
                "PermissionsViewAllRecords": True,
                "PermissionsModifyAllRecords": True
            },
            {
                "Id": "0Ps58000001EpSjAAK",
                "Parent": {"Label": "Contact"},
                "PermissionsCreate": True,
                "PermissionsRead": True,
                "PermissionsEdit": True,
                "PermissionsDelete": True,
                "PermissionsViewAllRecords": True,
                "PermissionsModifyAllRecords": True
            }
        ]
    except Exception as e:
        logger.error(f"Error fetching profile permissions: {str(e)}")
        return []

async def fetch_permission_sets(access_token: str, instance_url: str) -> List[Dict[str, Any]]:
    """
    Fetch permission sets from Salesforce

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL

    Returns:
        List[Dict[str, Any]]: Permission sets
    """
    try:
        # Query permission sets
        query = "SELECT Id, Name, Description, IsCustom, CreatedDate, LastModifiedDate FROM PermissionSet"

        result = await execute_salesforce_query(query, access_token, instance_url)

        if result and "records" in result:
            logger.info(f"Successfully fetched {len(result['records'])} permission sets")
            return result["records"]

        # If query fails, return mock data
        logger.warning("Permission sets query failed, returning mock data")
        return [
            {
                "Id": "0PS58000001EpSiAAK",
                "Name": "Marketing User",
                "Description": "Marketing User Permission Set",
                "IsCustom": True,
                "CreatedDate": "2022-01-01T00:00:00.000Z",
                "LastModifiedDate": "2022-01-01T00:00:00.000Z"
            },
            {
                "Id": "0PS58000001EpSjAAK",
                "Name": "Sales User",
                "Description": "Sales User Permission Set",
                "IsCustom": True,
                "CreatedDate": "2022-01-01T00:00:00.000Z",
                "LastModifiedDate": "2022-01-01T00:00:00.000Z"
            }
        ]
    except Exception as e:
        logger.error(f"Error fetching permission sets: {str(e)}")
        return []

async def fetch_permission_set_permissions(access_token: str, instance_url: str, permission_set_id: str) -> List[Dict[str, Any]]:
    """
    Fetch permission set permissions from Salesforce

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL
        permission_set_id: Permission set ID

    Returns:
        List[Dict[str, Any]]: Permission set permissions
    """
    try:
        # Query object permissions
        query = f"""
        SELECT Id, Parent.Label, PermissionsCreate, PermissionsRead, PermissionsEdit, PermissionsDelete,
               PermissionsViewAllRecords, PermissionsModifyAllRecords
        FROM ObjectPermissions
        WHERE ParentId = '{permission_set_id}'
        """

        result = await execute_salesforce_query(query, access_token, instance_url)

        if result and "records" in result:
            logger.info(f"Successfully fetched {len(result['records'])} object permissions for permission set {permission_set_id}")
            return result["records"]

        # If query fails, return mock data
        logger.warning(f"Permission set permissions query failed for permission set {permission_set_id}, returning mock data")
        return [
            {
                "Id": "0Ps58000001EpSiAAK",
                "Parent": {"Label": "Account"},
                "PermissionsCreate": True,
                "PermissionsRead": True,
                "PermissionsEdit": True,
                "PermissionsDelete": False,
                "PermissionsViewAllRecords": False,
                "PermissionsModifyAllRecords": False
            },
            {
                "Id": "0Ps58000001EpSjAAK",
                "Parent": {"Label": "Contact"},
                "PermissionsCreate": True,
                "PermissionsRead": True,
                "PermissionsEdit": True,
                "PermissionsDelete": False,
                "PermissionsViewAllRecords": False,
                "PermissionsModifyAllRecords": False
            }
        ]
    except Exception as e:
        logger.error(f"Error fetching permission set permissions: {str(e)}")
        return []

async def calculate_health_score(risks: List[Dict[str, Any]], access_token: str = None, instance_url: str = None) -> int:
    """
    Get health score directly from Salesforce or calculate based on security risks

    Args:
        risks: Security risks
        access_token: Optional Salesforce access token
        instance_url: Optional Salesforce instance URL

    Returns:
        int: Health score (0-100)
    """
    # Try to get the score directly from Salesforce if access token and instance URL are provided
    if access_token and instance_url:
        try:
            # Query the Score field directly from SecurityHealthCheck
            score_query = "SELECT Score FROM SecurityHealthCheck"
            score_result = await execute_salesforce_tooling_query(score_query, access_token, instance_url, api_version="v62.0")

            if score_result and "records" in score_result and len(score_result["records"]) > 0:
                # Use the score directly from Salesforce
                score = score_result["records"][0].get("Score", 0)
                logger.info(f"Retrieved health score directly from Salesforce: {score}")
                return int(score)
            else:
                logger.warning("Could not retrieve health score directly from Salesforce, using fallback calculation")
        except Exception as e:
            logger.error(f"Error retrieving health score directly from Salesforce: {str(e)}")
            logger.warning("Using fallback calculation method")

    # Fallback to calculating the score if we can't get it directly
    if not risks:
        return 0

    # Count risks by type
    risk_counts = {
        "HIGH_RISK": 0,
        "MEDIUM_RISK": 0,
        "LOW_RISK": 0,
        "COMPLIANT": 0,
        "INFORMATIONAL": 0
    }

    for risk in risks:
        risk_type = risk.get("RiskType", "")
        if risk_type in risk_counts:
            risk_counts[risk_type] += 1

    # Calculate total risks
    total_risks = sum(risk_counts.values())

    if total_risks == 0:
        return 0

    # Calculate weighted score
    weighted_score = (
        risk_counts["COMPLIANT"] * 100 +
        risk_counts["INFORMATIONAL"] * 75 +
        risk_counts["LOW_RISK"] * 50 +
        risk_counts["MEDIUM_RISK"] * 25 +
        risk_counts["HIGH_RISK"] * 0
    ) / total_risks

    return int(weighted_score)

# health_score function removed as requested

# health_risks function removed as requested

# The profiles function has been removed as it's redundant
# This functionality is now provided by the /api/api/integration/{tenant_url}/profiles endpoint
# in the integration_tabs.py blueprint

# The permission_sets function has been removed as it's redundant
# This functionality is now provided by the /api/api/integration/{tenant_url}/permission-sets endpoint
# in the integration_tabs.py blueprint

def load_best_practices(xml_path='best_practices/SecurityHealthCheck-BestPractice.xml'):
    """Parse the best practice XML and return a dict keyed by Salesforce Setting."""
    if not os.path.exists(xml_path):
        logger.error(f"Best practice XML not found at {xml_path}")
        return {}
    tree = ET.parse(xml_path)
    root = tree.getroot()
    best_practices = {}
    for practice in root.findall('Practice'):
        setting = practice.findtext('SalesforceSetting')
        if setting:
            best_practices[setting] = {
                'Description': practice.findtext('Description'),
                'OWASPCategory': practice.findtext('OWASPCategory'),
                'Severity': practice.findtext('RiskTypeBasedOnSeverity'),
                'StandardValue': practice.findtext('StandardValue')
            }
    logger.info(f"Loaded {len(best_practices)} best practices from XML")
    return best_practices

def get_policies_result_table_repo():
    """Get repository for PoliciesResult table"""
    try:
        repo = TableStorageRepository(table_name="PoliciesResult")
        logger.info("Successfully initialized PoliciesResult table repository")
        return repo
    except Exception as e:
        logger.error(f"Error initializing PoliciesResult table repository: {str(e)}")
        return None

def process_and_store_policies_results(health_check_risks, integration_id, task_status_id=None):
    """
    Store health check risks in PoliciesResult table via DB service, using best practices for OWASPCategory and IssueDescription, SettingRiskCategory for Severity, and SettingGroup from the risk dict.
    """
    try:
        from shared.db_service_client import get_db_client
        raw_best_practices = load_best_practices()
        best_practices = { (k or '').strip().lower(): v for k, v in raw_best_practices.items() }
        db_client = get_db_client()
        if db_client and task_status_id:
            # Idempotency check: does a record already exist for this execution_log_id?
            logger.info(f"process_and_store_policies_results: Checking for existing health check records with org_id={integration_id}, execution_log_id={task_status_id}, policy_type=HealthCheck")
            try:
                existing_health_check_records = db_client.get_policies_by_execution_log_and_type(
                    org_id=str(integration_id),
                    execution_log_id=str(task_status_id),
                    policy_type='HealthCheck'
                )
            except Exception as query_error:
                logger.error(f"process_and_store_policies_results: Error during idempotency query: {str(query_error)}")
                existing_health_check_records = None
            logger.info(f"process_and_store_policies_results: Idempotency check result: found {len(existing_health_check_records) if existing_health_check_records else 0} existing records")
            if existing_health_check_records:
                logger.info(f"Health check data already exists for execution_log_id {task_status_id}. Skipping duplicate storage.")
                return
            # Final check before insert (race condition protection)
            try:
                final_check_records = db_client.get_policies_by_execution_log_and_type(
                    org_id=str(integration_id),
                    execution_log_id=str(task_status_id),
                    policy_type='HealthCheck'
                )
                if final_check_records:
                    logger.warning(f"process_and_store_policies_results: RACE CONDITION DETECTED! Found {len(final_check_records)} records during final check. Skipping storage.")
                    return
            except Exception as final_check_error:
                logger.error(f"process_and_store_policies_results: Error during final idempotency check: {str(final_check_error)}")
        if not health_check_risks or len(health_check_risks) == 0:
            logger.info(f"process_and_store_policies_results: No health check risks data to store for integration {integration_id}")
            return
        policies_data = []
        for risk in health_check_risks:
            # CRITICAL FIX: Validate required fields before creating policy data
            setting = (risk.get('Setting') or '').strip()
            if not setting:
                logger.warning(f"Skipping risk with empty Setting field: {risk}")
                continue
                
            best_practice = best_practices.get(setting.lower(), {})
            policy_data = {
                'Setting': setting,
                'OrgValue': risk.get('OrgValue'),
                'OWASPCategory': best_practice.get('OWASPCategory', ''),
                'StandardValue': risk.get('StandardValue', ''),
                'IssueDescription': best_practice.get('Description', ''),
                'Recommendations': risk.get('Recommendations', ''),
                'Severity': risk.get('SettingRiskCategory', ''),
                'Weakness': risk.get('RiskType', ''),
                'IntegrationId': str(integration_id),
                'TaskStatusId': task_status_id,
                'CreatedAt': datetime.now().isoformat(),
                'SettingGroup': risk.get('SettingGroup', ''),
                'Type': 'HealthCheck'  # CRITICAL: Always set Type field to prevent empty values
            }
            policies_data.append(policy_data)
        logger.info(f"process_and_store_policies_results: About to call db_client.store_policies_result_data with {len(policies_data)} records")
        try:
            success = db_client.store_policies_result_data(
                org_id=str(integration_id),
                execution_log_id=str(task_status_id) if task_status_id else str(uuid.uuid4()),
                policies_data=policies_data
            )
            if success:
                logger.info(f"Successfully stored {len(policies_data)} PoliciesResult entities for integration {integration_id} via DB service")
            else:
                logger.error(f"Failed to store PoliciesResult entities for integration {integration_id} via DB service")
        except Exception as storage_error:
            error_message = str(storage_error).lower()
            if any(keyword in error_message for keyword in ['duplicate', 'unique constraint', 'primary key', 'already exists']):
                logger.warning(f"process_and_store_policies_results: Duplicate key violation detected during storage. This indicates a race condition. Error: {str(storage_error)}")
                logger.info(f"process_and_store_policies_results: Skipping storage due to duplicate key violation.")
                return
            else:
                logger.error(f"process_and_store_policies_results: Error during storage (not a duplicate key error): {str(storage_error)}")
                raise
    except Exception as e:
        logger.error(f"Error in process_and_store_policies_results: {str(e)}")
        import traceback
        logger.error(f"process_and_store_policies_results traceback: {traceback.format_exc()}")
        # Don't raise the exception to avoid breaking the health check task

@bp.route(route="policies-result/{integration_id}", methods=["GET"])
def get_policies_result(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Function endpoint to fetch latest PoliciesResult data for an integration.
    """
    integration_id = req.route_params.get("integration_id")
    if not integration_id:
        return func.HttpResponse(
            json.dumps({"success": False, "message": "integration_id is required"}),
            mimetype="application/json",
            status_code=400
        )
    
    # Get execution log ID from query parameters if provided
    execution_log_id = req.params.get('execution_log_id')
    
    repo = get_policies_result_table_repo()
    # Query all results for this integration, sorted by CreatedAt descending
    filter_query = f"PartitionKey eq '{integration_id}'"
    if execution_log_id:
        filter_query += f" and TaskStatusId eq '{execution_log_id}'"
    
    results = repo.query_entities(filter_query)
    # Sort by CreatedAt descending (string sort is fine for ISO format)
    results_sorted = sorted(results, key=lambda x: x.get('CreatedAt', ''), reverse=True)
    
    return func.HttpResponse(
        json.dumps({"success": True, "data": results_sorted}),
        mimetype="application/json",
        status_code=200
    )

@bp.route(route="pmd-scans/{integration_id}", methods=["GET"])
def get_pmd_scans(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Function endpoint to get PMD scan results for an integration.
    """
    integration_id = req.route_params.get("integration_id")
    if not integration_id:
        return func.HttpResponse(
            json.dumps({"success": False, "message": "integration_id is required"}),
            mimetype="application/json",
            status_code=400
        )
    
    # Get query parameters
    execution_log_id = req.params.get('execution_log_id')
    latest = req.params.get('latest', 'false').lower() == 'true'
    
    try:
        db_client = get_db_client()
        
        # If latest is requested, find the latest execution log ID for PMD scans
        if latest and not execution_log_id:
            # Get the latest PMD findings for this integration
            pmd_findings = db_client.get_pmd_findings(
                integration_id=integration_id,
                limit=1,
                offset=0
            )
            
            if pmd_findings and pmd_findings.get('findings'):
                # Get the execution log ID from the latest finding
                latest_finding = pmd_findings['findings'][0]
                execution_log_id = latest_finding.get('ExecutionLogId')
                logger.info(f"Found latest PMD execution ID: {execution_log_id}")
            else:
                logger.info("No PMD scan results found for this integration")
        
        # Get PMD findings using the DB service client
        pmd_findings = db_client.get_pmd_findings(
            integration_id=integration_id,
            limit=1000,  # Get a large number to ensure we get all results
            offset=0
        )
        
        results_sorted = pmd_findings.get('findings', [])
        
        # Filter by execution log ID if specified
        if execution_log_id:
            results_sorted = [r for r in results_sorted if r.get('ExecutionLogId') == execution_log_id]
        
        # Sort by CreatedAt descending
        results_sorted.sort(key=lambda x: x.get('CreatedAt', ''), reverse=True)
        
        # Prepare response
        response_data = {
            "success": True,
            "data": results_sorted,
            "total_findings": len(results_sorted),
            "execution_log_id": execution_log_id,
            "integration_id": integration_id
        }
        
        # Add summary if we have results
        if results_sorted:
            # Count by severity
            severity_counts = {}
            category_counts = {}
            weakness_type_counts = {}
            weakness_type_descriptions = {}
            files_affected = set()
            
            for result in results_sorted:
                severity = result.get('Severity', 'Unknown')
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
                
                category = result.get('RuleCategory', 'Unknown')
                category_counts[category] = category_counts.get(category, 0) + 1
                
                weakness_type = result.get('WeaknessType', 'Unknown')
                weakness_type_counts[weakness_type] = weakness_type_counts.get(weakness_type, 0) + 1
                
                # Store description for each weakness type
                weakness_desc = result.get('Description', '')
                if weakness_type not in weakness_type_descriptions and weakness_desc:
                    weakness_type_descriptions[weakness_type] = weakness_desc
                
                file_name = result.get('FileName', 'Unknown')
                files_affected.add(file_name)
            
            response_data.update({
                "summary": {
                    "severity_breakdown": severity_counts,
                    "category_breakdown": category_counts,
                    "weakness_type_breakdown": weakness_type_counts,
                    "weakness_type_descriptions": weakness_type_descriptions,
                    "files_affected": len(files_affected),
                    "latest_scan_timestamp": results_sorted[0].get('CreatedAt')
                }
            })
        
        return func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )
    except Exception as e:
        logger.error(f"Error fetching PMD scans for integration {integration_id}: {e}")
        return func.HttpResponse(
            json.dumps({"success": False, "message": f"Error fetching PMD scans: {str(e)}"}),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="pmd-subtasks/{integration_id}", methods=["GET"])
def get_pmd_subtasks(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Function endpoint to get PMD subtasks for an integration.
    """
    integration_id = req.route_params.get("integration_id")
    if not integration_id:
        return func.HttpResponse(
            json.dumps({"success": False, "message": "integration_id is required"}),
            mimetype="application/json",
            status_code=400
        )
    
    try:
        db_client = get_db_client()
        
        # Get enabled PMD subtasks for this integration
        subtasks = db_client.get_enabled_pmd_subtasks(integration_id)
        
        # Format response
        subtasks_data = []
        for subtask in subtasks:
            subtasks_data.append({
                "subtask_id": subtask.get("subtask_id"),
                "name": subtask.get("name"),
                "description": subtask.get("description"),
                "enabled": subtask.get("enabled", True),
                "created_at": subtask.get("created_at")
            })
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "integration_id": integration_id,
                "subtasks": subtasks_data,
                "total_subtasks": len(subtasks_data),
                "enabled_subtasks": len([s for s in subtasks_data if s["enabled"]])
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error fetching PMD subtasks for integration {integration_id}: {e}")
        return func.HttpResponse(
            json.dumps({"success": False, "message": f"Error fetching PMD subtasks: {str(e)}"}),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="pmd-subtasks/{integration_id}", methods=["PUT"])
def update_pmd_subtasks(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Function endpoint to update PMD subtasks for an integration.
    """
    integration_id = req.route_params.get("integration_id")
    if not integration_id:
        return func.HttpResponse(
            json.dumps({"success": False, "message": "integration_id is required"}),
            mimetype="application/json",
            status_code=400
        )
    
    try:
        # Parse request body
        request_body = req.get_json()
        if not request_body or "subtasks" not in request_body:
            return func.HttpResponse(
                json.dumps({"success": False, "message": "Request body must contain 'subtasks' array"}),
                mimetype="application/json",
                status_code=400
            )
        
        subtasks_to_update = request_body["subtasks"]
        if not isinstance(subtasks_to_update, list):
            return func.HttpResponse(
                json.dumps({"success": False, "message": "'subtasks' must be an array"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Get repositories
        policy_repo = get_policy_table_repo()
        rule_repo = get_rule_table_repo()
        subtask_repo = get_pmd_subtask_table_repo()
        
        if not policy_repo or not rule_repo or not subtask_repo:
            return func.HttpResponse(
                json.dumps({"success": False, "message": "Database repositories not available"}),
                mimetype="application/json",
                status_code=500
            )
        
        # Find PMD policy and rule
        policies = policy_repo.query_entities(f"IntegrationId eq '{integration_id}' and Name eq 'Static Code Analysis (PMD)'")
        if not policies:
            return func.HttpResponse(
                json.dumps({"success": False, "message": "PMD policy not found for this integration"}),
                mimetype="application/json",
                status_code=404
            )
        
        policy_id = policies[0].get("PolicyId")
        rules = rule_repo.query_entities(f"PolicyId eq '{policy_id}' and TaskType eq 'pmd_apex_security'")
        if not rules:
            return func.HttpResponse(
                json.dumps({"success": False, "message": "PMD rule not found for this integration"}),
                mimetype="application/json",
                status_code=404
            )
        
        rule_id = rules[0].get("RuleId")
        
        # Update each subtask
        updated_count = 0
        errors = []
        
        for subtask_update in subtasks_to_update:
            subtask_id = subtask_update.get("subtask_id")
            enabled = subtask_update.get("enabled")
            
            if not subtask_id or enabled is None:
                errors.append(f"Invalid subtask update: {subtask_update}")
                continue
            
            # Get existing subtask
            existing_subtasks = subtask_repo.query_entities(f"SubtaskId eq '{subtask_id}' and RuleId eq '{rule_id}'")
            if not existing_subtasks:
                errors.append(f"Subtask {subtask_id} not found")
                continue
            
            existing_subtask = existing_subtasks[0]
            
            # Update the subtask
            existing_subtask["Enabled"] = int(enabled)
            
            if subtask_repo.update_entity(existing_subtask):
                updated_count += 1
                logger.info(f"Updated subtask {subtask_id} to enabled={enabled}")
            else:
                errors.append(f"Failed to update subtask {subtask_id}")
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "integration_id": integration_id,
                "updated_count": updated_count,
                "total_requested": len(subtasks_to_update),
                "errors": errors if errors else None
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error updating PMD subtasks for integration {integration_id}: {e}")
        return func.HttpResponse(
            json.dumps({"success": False, "message": f"Error updating PMD subtasks: {str(e)}"}),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="pmd-individual-rules/{integration_id}/{subtask_id}", methods=["GET"])
def get_pmd_individual_rules(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Function endpoint to get individual PMD rules for a specific subtask.
    """
    integration_id = req.route_params.get("integration_id")
    subtask_id = req.route_params.get("subtask_id")
    
    if not integration_id or not subtask_id:
        return func.HttpResponse(
            json.dumps({"success": False, "message": "integration_id and subtask_id are required"}),
            mimetype="application/json",
            status_code=400
        )
    
    try:
        db_client = get_db_client()
        
        # Get enabled individual PMD rules for this subtask
        individual_rules = db_client.get_enabled_pmd_rules(subtask_id)
        
        # Format response
        rules_data = []
        for rule in individual_rules:
            rules_data.append({
                "individual_rule_id": rule.get("individual_rule_id"),
                "rule_name": rule.get("rule_name"),
                "rule_description": rule.get("rule_description"),
                "enabled": rule.get("enabled", True),
                "created_at": rule.get("created_at")
            })
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "integration_id": integration_id,
                "subtask_id": subtask_id,
                "individual_rules": rules_data,
                "total_rules": len(rules_data),
                "enabled_rules": len([r for r in rules_data if r["enabled"]])
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error fetching individual PMD rules for subtask {subtask_id}: {e}")
        return func.HttpResponse(
            json.dumps({"success": False, "message": f"Error fetching individual PMD rules: {str(e)}"}),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="pmd-individual-rules/{integration_id}/{subtask_id}", methods=["PUT"])
def update_pmd_individual_rules(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Function endpoint to update individual PMD rules for a specific subtask.
    """
    integration_id = req.route_params.get("integration_id")
    subtask_id = req.route_params.get("subtask_id")
    
    if not integration_id or not subtask_id:
        return func.HttpResponse(
            json.dumps({"success": False, "message": "integration_id and subtask_id are required"}),
            mimetype="application/json",
            status_code=400
        )
    
    try:
        # Parse request body
        request_body = req.get_json()
        if not request_body or "individual_rules" not in request_body:
            return func.HttpResponse(
                json.dumps({"success": False, "message": "Request body must contain 'individual_rules' array"}),
                mimetype="application/json",
                status_code=400
            )
        
        rules_to_update = request_body["individual_rules"]
        if not isinstance(rules_to_update, list):
            return func.HttpResponse(
                json.dumps({"success": False, "message": "'individual_rules' must be an array"}),
                mimetype="application/json",
                status_code=400
            )
        
        # Get repository
        individual_rule_repo = get_pmd_individual_rule_table_repo()
        
        if not individual_rule_repo:
            return func.HttpResponse(
                json.dumps({"success": False, "message": "Database repository not available"}),
                mimetype="application/json",
                status_code=500
            )
        
        # Update each individual rule
        updated_count = 0
        errors = []
        
        for rule_update in rules_to_update:
            individual_rule_id = rule_update.get("individual_rule_id")
            enabled = rule_update.get("enabled")
            
            if not individual_rule_id or enabled is None:
                errors.append(f"Invalid rule update: {rule_update}")
                continue
            
            # Get existing rule
            existing_rules = individual_rule_repo.query_entities(f"IndividualRuleId eq '{individual_rule_id}' and SubtaskId eq '{subtask_id}'")
            if not existing_rules:
                errors.append(f"Individual rule {individual_rule_id} not found")
                continue
            
            existing_rule = existing_rules[0]
            
            # Update the rule
            existing_rule["Enabled"] = int(enabled)
            
            if individual_rule_repo.update_entity(existing_rule):
                updated_count += 1
                logger.info(f"Updated individual rule {individual_rule_id} to enabled={enabled}")
            else:
                errors.append(f"Failed to update individual rule {individual_rule_id}")
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "integration_id": integration_id,
                "subtask_id": subtask_id,
                "updated_count": updated_count,
                "total_requested": len(rules_to_update),
                "errors": errors if errors else None
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error updating individual PMD rules for subtask {subtask_id}: {e}")
        return func.HttpResponse(
            json.dumps({"success": False, "message": f"Error updating individual PMD rules: {str(e)}"}),
            mimetype="application/json",
            status_code=500
        )

@bp.route(route="pmd-configuration/{integration_id}", methods=["GET"])
def get_pmd_configuration(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Function endpoint to get complete PMD configuration for an integration.
    This includes all subtasks and individual rules with their enabled/disabled status.
    """
    integration_id = req.route_params.get("integration_id")
    if not integration_id:
        return func.HttpResponse(
            json.dumps({"success": False, "message": "integration_id is required"}),
            mimetype="application/json",
            status_code=400
        )
    
    try:
        db_client = get_db_client()
        
        # Get complete PMD configuration for this integration
        configuration = db_client.get_pmd_configuration(integration_id)
        
        if not configuration:
            return func.HttpResponse(
                json.dumps({"success": False, "message": "PMD configuration not found for this integration"}),
                mimetype="application/json",
                status_code=404
            )
        
        return func.HttpResponse(
            json.dumps({
                "success": True,
                "configuration": configuration
            }),
            mimetype="application/json",
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error fetching PMD configuration for integration {integration_id}: {e}")
        return func.HttpResponse(
            json.dumps({"success": False, "message": f"Error fetching PMD configuration: {str(e)}"}),
            mimetype="application/json",
            status_code=500
        )
