import logging
import json
import azure.functions as func
from datetime import datetime

# Import shared modules
from shared.config import is_local_dev
from shared.utils import create_json_response, handle_exception
from shared.cors_middleware import cors_middleware
from shared.auth_utils import require_auth, get_current_user
from shared.user_repository import (
    get_user_account_by_email, get_user_account_by_id,
    get_user_account_table_repo, get_user_account_sql_repo,
    get_user_login_by_username, get_user_login_table_repo, get_user_login_sql_repo,
    hash_password, verify_password, generate_salt
)

# Create blueprint
bp = func.Blueprint()

# Configure logging
logger = logging.getLogger('user_profile')
logger.setLevel(logging.INFO)


@bp.route(route="user/profile", methods=["GET"])
@require_auth
def get_user_profile(req: func.HttpRequest) -> func.HttpResponse:
    """
    Get user profile information

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response with user profile data
    """
    logger.info('Processing get user profile request...')

    try:
        # Get current user from request
        current_user = req.user
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Get user account
        user_account = get_user_account_by_email(current_user["email"])
        if not user_account:
            return func.HttpResponse(
                json.dumps(create_json_response("User not found", 404)),
                mimetype="application/json",
                status_code=404
            )

        # Create response data
        response_data = {
            "firstName": user_account.FirstName,
            "lastName": user_account.LastName,
            "email": user_account.Email,
            "contact": user_account.Contact or "",
            "jobTitle": "",  # Not stored in database, placeholder
            "company": user_account.Organization or "",
            "state": user_account.State or "",
            "country": user_account.Country or ""
        }

        # Return response
        response = func.HttpResponse(
            json.dumps(create_json_response(response_data)),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "get_user_profile")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)


@bp.route(route="user/profile", methods=["PUT"])
@require_auth
def update_user_profile(req: func.HttpRequest) -> func.HttpResponse:
    """
    Update user profile information

    Args:
        req: HTTP request with updated profile data

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing update user profile request...')

    try:
        # Get current user from request
        current_user = req.user
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Get request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps(create_json_response("Invalid request body", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Get user account
        user_account = get_user_account_by_email(current_user["email"])
        if not user_account:
            return func.HttpResponse(
                json.dumps(create_json_response("User not found", 404)),
                mimetype="application/json",
                status_code=404
            )

        # Update user account fields
        user_account.FirstName = req_body.get("firstName", user_account.FirstName)
        user_account.LastName = req_body.get("lastName", user_account.LastName)
        user_account.Contact = req_body.get("contact", user_account.Contact)
        user_account.Organization = req_body.get("company", user_account.Organization)
        user_account.State = req_body.get("state", user_account.State)
        user_account.Country = req_body.get("country", user_account.Country)

        # Save changes
        if is_local_dev():
            # Use Azure Table Storage for local development
            user_repo = get_user_account_table_repo()
            if not user_repo:
                logger.error("User account table repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to update profile", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Get the user entity
            filter_query = f"RowKey eq '{user_account.Email}'"
            entities = user_repo.query_entities(filter_query)

            if not entities:
                logger.warning(f"User not found: {user_account.Email}")
                return func.HttpResponse(
                    json.dumps(create_json_response("User not found", 404)),
                    mimetype="application/json",
                    status_code=404
                )

            user_entity = entities[0]

            # Update fields
            user_entity["FirstName"] = user_account.FirstName
            user_entity["LastName"] = user_account.LastName
            user_entity["Contact"] = user_account.Contact
            user_entity["Organization"] = user_account.Organization
            user_entity["State"] = user_account.State
            user_entity["Country"] = user_account.Country
            user_entity["LastUpdated"] = datetime.now().isoformat()

            success = user_repo.update_entity(user_entity)
            if not success:
                logger.error(f"Failed to update user profile: {user_account.Email}")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to update profile", 500)),
                    mimetype="application/json",
                    status_code=500
                )
        else:
            # Use SQL Database for production
            user_repo = get_user_account_sql_repo()
            if not user_repo:
                logger.error("User account SQL repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to update profile", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Update user in SQL
            query = """
                UPDATE User_Account
                SET FirstName = ?, LastName = ?, Contact = ?, Organization = ?, State = ?, Country = ?, LastUpdated = ?
                WHERE Email = ?
            """
            params = (
                user_account.FirstName,
                user_account.LastName,
                user_account.Contact,
                user_account.Organization,
                user_account.State,
                user_account.Country,
                datetime.now().isoformat(),
                user_account.Email
            )

            success = user_repo.execute_non_query(query, params)
            if not success:
                logger.error(f"Failed to update user profile: {user_account.Email}")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to update profile", 500)),
                    mimetype="application/json",
                    status_code=500
                )

        # Create response data
        response_data = {
            "firstName": user_account.FirstName,
            "lastName": user_account.LastName,
            "email": user_account.Email,
            "contact": user_account.Contact or "",
            "jobTitle": req_body.get("jobTitle", ""),  # Not stored in database
            "company": user_account.Organization or "",
            "state": user_account.State or "",
            "country": user_account.Country or ""
        }

        # Return response
        response = func.HttpResponse(
            json.dumps(create_json_response(response_data)),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "update_user_profile")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)


@bp.route(route="user/password", methods=["POST"])
@require_auth
def change_password(req: func.HttpRequest) -> func.HttpResponse:
    """
    Change user password

    Args:
        req: HTTP request with current and new password

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing change password request...')

    try:
        # Get current user from request
        current_user = req.user
        if not current_user:
            return func.HttpResponse(
                json.dumps(create_json_response("Unauthorized", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Get request body
        req_body = req.get_json()
        if not req_body:
            return func.HttpResponse(
                json.dumps(create_json_response("Invalid request body", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Validate required fields
        required_fields = ['currentPassword', 'newPassword']
        for field in required_fields:
            if field not in req_body:
                return func.HttpResponse(
                    json.dumps(create_json_response(f"Missing required field: {field}", 400)),
                    mimetype="application/json",
                    status_code=400
                )

        current_password = req_body.get('currentPassword')
        new_password = req_body.get('newPassword')

        # Get user login
        user_login = get_user_login_by_username(current_user["email"])
        if not user_login:
            return func.HttpResponse(
                json.dumps(create_json_response("User login not found", 404)),
                mimetype="application/json",
                status_code=404
            )

        # Verify current password
        if not verify_password(
            current_password,
            user_login.PasswordHash,
            user_login.PasswordSalt,
            user_login.HashAlgorithmId
        ):
            return func.HttpResponse(
                json.dumps(create_json_response("Current password is incorrect", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Generate new salt and hash new password
        new_salt = generate_salt()
        new_hash = hash_password(new_password, new_salt, user_login.HashAlgorithmId)

        # Update password
        if is_local_dev():
            # Use Azure Table Storage for local development
            login_repo = get_user_login_table_repo()
            if not login_repo:
                logger.error("User login table repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to update password", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Get the user login entity
            filter_query = f"RowKey eq '{current_user['email']}'"
            entities = login_repo.query_entities(filter_query)

            if not entities:
                logger.warning(f"User login not found: {current_user['email']}")
                return func.HttpResponse(
                    json.dumps(create_json_response("User login not found", 404)),
                    mimetype="application/json",
                    status_code=404
                )

            login_entity = entities[0]

            # Update password fields
            login_entity["PasswordHash"] = new_hash
            login_entity["PasswordSalt"] = new_salt
            login_entity["LastUpdated"] = datetime.now().isoformat()

            success = login_repo.update_entity(login_entity)
            if not success:
                logger.error(f"Failed to update password: {current_user['email']}")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to update password", 500)),
                    mimetype="application/json",
                    status_code=500
                )
        else:
            # Use SQL Database for production
            login_repo = get_user_login_sql_repo()
            if not login_repo:
                logger.error("User login SQL repository not available")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to update password", 500)),
                    mimetype="application/json",
                    status_code=500
                )

            # Update password in SQL
            query = """
                UPDATE App_User_Login
                SET PasswordHash = ?, PasswordSalt = ?, LastUpdated = ?
                WHERE Username = ?
            """
            params = (
                new_hash,
                new_salt,
                datetime.now().isoformat(),
                current_user["email"]
            )

            success = login_repo.execute_non_query(query, params)
            if not success:
                logger.error(f"Failed to update password: {current_user['email']}")
                return func.HttpResponse(
                    json.dumps(create_json_response("Failed to update password", 500)),
                    mimetype="application/json",
                    status_code=500
                )

        # Return success response
        response = func.HttpResponse(
            json.dumps(create_json_response({"message": "Password updated successfully"})),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "change_password")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
