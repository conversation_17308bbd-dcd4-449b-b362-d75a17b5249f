"""
Profile System Permissions Blueprint

This module provides functions for retrieving and managing Salesforce profile and permission set system permissions.

Best practices implemented:
- Async functions for better performance
- Proper error handling and logging
- Repository pattern for data access
- Centralized configuration
- Lazy initialization of repositories
"""

import logging
import azure.functions as func
import aiohttp
import json
from datetime import datetime
from typing import List, Dict, Any, Optional

# Import shared modules
from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.utils import get_salesforce_access_token, execute_salesforce_query, handle_exception, create_json_response

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Lazy-initialized repositories
_profile_permissions_repo = None
_permission_set_repo = None

def get_profile_permissions_repo():
    """Lazy initialize the profile permissions repository"""
    global _profile_permissions_repo
    if _profile_permissions_repo is None:
        try:
            if is_local_dev():
                _profile_permissions_repo = TableStorageRepository(table_name="ProfileSystemPermissions")
            else:
                _profile_permissions_repo = SqlDatabaseRepository(table_name="App_ProfileSystemPermissions")
            logger.info("Initialized profile system permissions repository")
        except Exception as e:
            logger.error(f"Failed to initialize profile system permissions repository: {str(e)}")
            _profile_permissions_repo = None
    return _profile_permissions_repo

def get_permission_set_repo():
    """Lazy initialize the permission set repository"""
    global _permission_set_repo
    if _permission_set_repo is None:
        try:
            if is_local_dev():
                _permission_set_repo = TableStorageRepository(table_name="PermissionSetSystemPermissions")
            else:
                _permission_set_repo = SqlDatabaseRepository(table_name="App_PermissionSetSystemPermissions")
            logger.info("Initialized permission set system permissions repository")
        except Exception as e:
            logger.error(f"Failed to initialize permission set system permissions repository: {str(e)}")
            _permission_set_repo = None
    return _permission_set_repo

async def fetch_profile_system_permissions(access_token: str, instance_url: str) -> List[Dict[str, Any]]:
    """
    Fetch system permissions for all profiles.

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL

    Returns:
        List[Dict[str, Any]]: List of profile system permissions
    """
    profile_query = (
        "SELECT Name, "
        "PermissionsModifyAllData, "
        "PermissionsViewAllData, "
        "PermissionsResetPasswords, "
        "PermissionsDataExport, "
        "PermissionsManageUsers, "
        "PermissionsManageProfilesPermissionsets, "
        "PermissionsManageRoles, "
        "PermissionsManageSharing, "
        "PermissionsEditReadonlyFields, "
        "PermissionsViewSetup, "
        "PermissionsAuthorApex, "
        "PermissionsViewAllUsers, "
        "PermissionsManageEncryptionKeys, "
        "PermissionsManageTwoFactor, "
        "PermissionsModifyMetadata, "
        "PermissionsCustomizeApplication, "
        "PermissionsManageIpAddresses "
        "FROM Profile"
    )

    response = execute_salesforce_query(profile_query, access_token, instance_url)

    if isinstance(response, dict) and "records" in response:
        profiles = []
        for record in response["records"]:
            logger.debug(f"Processing Profile Record: {record}")
            profiles.append(record)
        return profiles
    else:
        logger.error(f"Unexpected response format for profiles: {response}")
        return []

def fetch_permission_set_system_permissions(access_token: str, instance_url: str) -> List[Dict[str, Any]]:
    """
    Fetch system permissions for all permission sets.

    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL

    Returns:
        List[Dict[str, Any]]: List of permission set system permissions
    """
    permission_set_query = (
        "SELECT Name, "
        "PermissionsModifyAllData, "
        "PermissionsViewAllData, "
        "PermissionsResetPasswords, "
        "PermissionsDataExport, "
        "PermissionsManageUsers, "
        "PermissionsManageProfilesPermissionsets, "
        "PermissionsManageRoles, "
        "PermissionsManageSharing, "
        "PermissionsEditReadonlyFields, "
        "PermissionsViewSetup, "
        "PermissionsAuthorApex, "
        "PermissionsViewAllUsers, "
        "PermissionsManageEncryptionKeys, "
        "PermissionsManageTwoFactor, "
        "PermissionsModifyMetadata, "
        "PermissionsCustomizeApplication, "
        "PermissionsManageIpAddresses "
        "FROM PermissionSet"
    )

    response = execute_salesforce_query(permission_set_query, access_token, instance_url)

    if isinstance(response, dict) and "records" in response:
        permission_sets = []
        for record in response["records"]:
            logger.debug(f"Processing Permission Set Record: {record}")
            permission_sets.append(record)
        return permission_sets
    else:
        logger.error(f"Unexpected response format for permission sets: {response}")
        return []

def save_profile_system_permissions(profiles: List[Dict[str, Any]], integration_id: str) -> Dict[str, Any]:
    """
    Save profile system permissions to the database.

    Args:
        profiles: List of profile system permissions
        integration_id: Integration ID

    Returns:
        Dict[str, Any]: Operation statistics
    """
    stats = {
        "total": len(profiles),
        "inserted": 0,
        "failed": 0
    }

    repo = get_profile_permissions_repo()
    if not repo:
        logger.error("Profile permissions repository not available")
        stats["error"] = "Repository not available"
        return stats

    if is_local_dev():
        # For local development, use Azure Table Storage
        for profile in profiles:
            entity = {
                'PartitionKey': integration_id,
                'RowKey': f"{datetime.now().strftime('%Y%m%d%H%M%S%f')}-{profile.get('name', 'unknown')}",
                'Name': profile.get('name', ''),
                'ModifyAllData': str(profile.get('permissionsmodifyalldata', False)),
                'ViewAllData': str(profile.get('permissionsviewalldata', False)),
                'ResetPasswords': str(profile.get('permissionsresetpasswords', False)),
                'DataExport': str(profile.get('permissionsdataexport', False)),
                'ManageUsers': str(profile.get('permissionsmanageusers', False)),
                'ManageProfilesPermissionsets': str(profile.get('permissionsmanageprofilespermissionsets', False)),
                'ManageRoles': str(profile.get('permissionsmanageroles', False)),
                'ManageSharing': str(profile.get('permissionsmanagesharing', False)),
                'EditReadonlyFields': str(profile.get('permissionseditreadonlyfields', False)),
                'ViewSetup': str(profile.get('permissionsviewsetup', False)),
                'AuthorApex': str(profile.get('permissionsauthorapex', False)),
                'ViewAllUsers': str(profile.get('permissionsviewallusers', False)),
                'ManageEncryptionKeys': str(profile.get('permissionsmanageencryptionkeys', False)),
                'ManageTwoFactor': str(profile.get('permissionsmanagetwofactor', False)),
                'ModifyMetadata': str(profile.get('permissionsmodifymetadata', False)),
                'CustomizeApplication': str(profile.get('permissionscustomizeapplication', False)),
                'ManageIpAddresses': str(profile.get('permissionsmanageipaddresses', False)),
                'LastUpdated': datetime.now().isoformat()
            }

            if repo.insert_entity(entity):
                stats["inserted"] += 1
            else:
                stats["failed"] += 1
    else:
        # For production, use SQL Database
        query = """
            INSERT INTO App_ProfileSystemPermissions (
                OrgId, Name, ModifyAllData, ViewAllData, ResetPasswords, DataExport,
                ManageUsers, ManageProfilesPermissionsets, ManageRoles, ManageSharing,
                EditReadonlyFields, ViewSetup, AuthorApex, ViewAllUsers,
                ManageEncryptionKeys, ManageTwoFactor, ModifyMetadata,
                CustomizeApplication, ManageIpAddresses, LastUpdated
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        for profile in profiles:
            params = (
                integration_id,
                profile.get('name', ''),
                profile.get('permissionsmodifyalldata', False),
                profile.get('permissionsviewalldata', False),
                profile.get('permissionsresetpasswords', False),
                profile.get('permissionsdataexport', False),
                profile.get('permissionsmanageusers', False),
                profile.get('permissionsmanageprofilespermissionsets', False),
                profile.get('permissionsmanageroles', False),
                profile.get('permissionsmanagesharing', False),
                profile.get('permissionseditreadonlyfields', False),
                profile.get('permissionsviewsetup', False),
                profile.get('permissionsauthorapex', False),
                profile.get('permissionsviewallusers', False),
                profile.get('permissionsmanageencryptionkeys', False),
                profile.get('permissionsmanagetwofactor', False),
                profile.get('permissionsmodifymetadata', False),
                profile.get('permissionscustomizeapplication', False),
                profile.get('permissionsmanageipaddresses', False),
                datetime.now().isoformat()
            )

            if repo.execute_non_query(query, params):
                stats["inserted"] += 1
            else:
                stats["failed"] += 1

    return stats

def save_permission_set_system_permissions(permission_sets: List[Dict[str, Any]], integration_id: str) -> Dict[str, Any]:
    """
    Save permission set system permissions to the database.

    Args:
        permission_sets: List of permission set system permissions
        integration_id: Integration ID

    Returns:
        Dict[str, Any]: Operation statistics
    """
    stats = {
        "total": len(permission_sets),
        "inserted": 0,
        "failed": 0
    }

    repo = get_permission_set_repo()
    if not repo:
        logger.error("Permission set repository not available")
        stats["error"] = "Repository not available"
        return stats

    if is_local_dev():
        # For local development, use Azure Table Storage
        for perm_set in permission_sets:
            entity = {
                'PartitionKey': integration_id,
                'RowKey': f"{datetime.now().strftime('%Y%m%d%H%M%S%f')}-{perm_set.get('name', 'unknown')}",
                'Name': perm_set.get('name', ''),
                'ModifyAllData': str(perm_set.get('permissionsmodifyalldata', False)),
                'ViewAllData': str(perm_set.get('permissionsviewalldata', False)),
                'ResetPasswords': str(perm_set.get('permissionsresetpasswords', False)),
                'DataExport': str(perm_set.get('permissionsdataexport', False)),
                'ManageUsers': str(perm_set.get('permissionsmanageusers', False)),
                'ManageProfilesPermissionsets': str(perm_set.get('permissionsmanageprofilespermissionsets', False)),
                'ManageRoles': str(perm_set.get('permissionsmanageroles', False)),
                'ManageSharing': str(perm_set.get('permissionsmanagesharing', False)),
                'EditReadonlyFields': str(perm_set.get('permissionseditreadonlyfields', False)),
                'ViewSetup': str(perm_set.get('permissionsviewsetup', False)),
                'AuthorApex': str(perm_set.get('permissionsauthorapex', False)),
                'ViewAllUsers': str(perm_set.get('permissionsviewallusers', False)),
                'ManageEncryptionKeys': str(perm_set.get('permissionsmanageencryptionkeys', False)),
                'ManageTwoFactor': str(perm_set.get('permissionsmanagetwofactor', False)),
                'ModifyMetadata': str(perm_set.get('permissionsmodifymetadata', False)),
                'CustomizeApplication': str(perm_set.get('permissionscustomizeapplication', False)),
                'ManageIpAddresses': str(perm_set.get('permissionsmanageipaddresses', False)),
                'LastUpdated': datetime.now().isoformat()
            }

            if repo.insert_entity(entity):
                stats["inserted"] += 1
            else:
                stats["failed"] += 1
    else:
        # For production, use SQL Database
        query = """
            INSERT INTO App_PermissionSetSystemPermissions (
                OrgId, Name, ModifyAllData, ViewAllData, ResetPasswords, DataExport,
                ManageUsers, ManageProfilesPermissionsets, ManageRoles, ManageSharing,
                EditReadonlyFields, ViewSetup, AuthorApex, ViewAllUsers,
                ManageEncryptionKeys, ManageTwoFactor, ModifyMetadata,
                CustomizeApplication, ManageIpAddresses, LastUpdated
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        for perm_set in permission_sets:
            params = (
                integration_id,
                perm_set.get('name', ''),
                perm_set.get('permissionsmodifyalldata', False),
                perm_set.get('permissionsviewalldata', False),
                perm_set.get('permissionsresetpasswords', False),
                perm_set.get('permissionsdataexport', False),
                perm_set.get('permissionsmanageusers', False),
                perm_set.get('permissionsmanageprofilespermissionsets', False),
                perm_set.get('permissionsmanageroles', False),
                perm_set.get('permissionsmanagesharing', False),
                perm_set.get('permissionseditreadonlyfields', False),
                perm_set.get('permissionsviewsetup', False),
                perm_set.get('permissionsauthorapex', False),
                perm_set.get('permissionsviewallusers', False),
                perm_set.get('permissionsmanageencryptionkeys', False),
                perm_set.get('permissionsmanagetwofactor', False),
                perm_set.get('permissionsmodifymetadata', False),
                perm_set.get('permissionscustomizeapplication', False),
                perm_set.get('permissionsmanageipaddresses', False),
                datetime.now().isoformat()
            )

            if repo.execute_non_query(query, params):
                stats["inserted"] += 1
            else:
                stats["failed"] += 1

    return stats

def get_profile_system_permissions(integration_id: str) -> List[Dict[str, Any]]:
    """
    Get profile system permissions from the database.

    Args:
        integration_id: Integration ID

    Returns:
        List[Dict[str, Any]]: List of profile system permissions
    """
    repo = get_profile_permissions_repo()
    if not repo:
        logger.error("Profile permissions repository not available")
        return []

    if is_local_dev():
        # For local development, use Azure Table Storage
        filter_query = f"PartitionKey eq '{integration_id}'"
        entities = repo.query_entities(filter_query)
        
        if not entities:
            return []
        
        profiles = []
        for entity in entities:
            # Convert string values to boolean
            profile = {
                'name': entity.get('Name', ''),
                'modifyAllData': entity.get('ModifyAllData', 'False').lower() == 'true',
                'viewAllData': entity.get('ViewAllData', 'False').lower() == 'true',
                'resetPasswords': entity.get('ResetPasswords', 'False').lower() == 'true',
                'dataExport': entity.get('DataExport', 'False').lower() == 'true',
                'manageUsers': entity.get('ManageUsers', 'False').lower() == 'true',
                'manageProfilesPermissionsets': entity.get('ManageProfilesPermissionsets', 'False').lower() == 'true',
                'manageRoles': entity.get('ManageRoles', 'False').lower() == 'true',
                'manageSharing': entity.get('ManageSharing', 'False').lower() == 'true',
                'editReadonlyFields': entity.get('EditReadonlyFields', 'False').lower() == 'true',
                'viewSetup': entity.get('ViewSetup', 'False').lower() == 'true',
                'authorApex': entity.get('AuthorApex', 'False').lower() == 'true',
                'viewAllUsers': entity.get('ViewAllUsers', 'False').lower() == 'true',
                'manageEncryptionKeys': entity.get('ManageEncryptionKeys', 'False').lower() == 'true',
                'manageTwoFactor': entity.get('ManageTwoFactor', 'False').lower() == 'true',
                'modifyMetadata': entity.get('ModifyMetadata', 'False').lower() == 'true',
                'customizeApplication': entity.get('CustomizeApplication', 'False').lower() == 'true',
                'manageIpAddresses': entity.get('ManageIpAddresses', 'False').lower() == 'true',
                'lastUpdated': entity.get('LastUpdated', datetime.now().isoformat())
            }
            profiles.append(profile)
        
        return profiles
    else:
        # For production, use SQL Database
        query = """
            SELECT Name, ModifyAllData, ViewAllData, ResetPasswords, DataExport,
                   ManageUsers, ManageProfilesPermissionsets, ManageRoles, ManageSharing,
                   EditReadonlyFields, ViewSetup, AuthorApex, ViewAllUsers,
                   ManageEncryptionKeys, ManageTwoFactor, ModifyMetadata,
                   CustomizeApplication, ManageIpAddresses, LastUpdated
            FROM App_ProfileSystemPermissions
            WHERE OrgId = ?
        """
        
        results = repo.execute_query(query, (integration_id,))
        if not results:
            return []
        
        profiles = []
        for row in results:
            profile = {
                'name': row[0],
                'modifyAllData': row[1],
                'viewAllData': row[2],
                'resetPasswords': row[3],
                'dataExport': row[4],
                'manageUsers': row[5],
                'manageProfilesPermissionsets': row[6],
                'manageRoles': row[7],
                'manageSharing': row[8],
                'editReadonlyFields': row[9],
                'viewSetup': row[10],
                'authorApex': row[11],
                'viewAllUsers': row[12],
                'manageEncryptionKeys': row[13],
                'manageTwoFactor': row[14],
                'modifyMetadata': row[15],
                'customizeApplication': row[16],
                'manageIpAddresses': row[17],
                'lastUpdated': row[18]
            }
            profiles.append(profile)
        
        return profiles

def get_permission_set_system_permissions(integration_id: str) -> List[Dict[str, Any]]:
    """
    Get permission set system permissions from the database.

    Args:
        integration_id: Integration ID

    Returns:
        List[Dict[str, Any]]: List of permission set system permissions
    """
    repo = get_permission_set_repo()
    if not repo:
        logger.error("Permission set repository not available")
        return []

    if is_local_dev():
        # For local development, use Azure Table Storage
        filter_query = f"PartitionKey eq '{integration_id}'"
        entities = repo.query_entities(filter_query)
        
        if not entities:
            return []
        
        permission_sets = []
        for entity in entities:
            # Convert string values to boolean
            perm_set = {
                'name': entity.get('Name', ''),
                'modifyAllData': entity.get('ModifyAllData', 'False').lower() == 'true',
                'viewAllData': entity.get('ViewAllData', 'False').lower() == 'true',
                'resetPasswords': entity.get('ResetPasswords', 'False').lower() == 'true',
                'dataExport': entity.get('DataExport', 'False').lower() == 'true',
                'manageUsers': entity.get('ManageUsers', 'False').lower() == 'true',
                'manageProfilesPermissionsets': entity.get('ManageProfilesPermissionsets', 'False').lower() == 'true',
                'manageRoles': entity.get('ManageRoles', 'False').lower() == 'true',
                'manageSharing': entity.get('ManageSharing', 'False').lower() == 'true',
                'editReadonlyFields': entity.get('EditReadonlyFields', 'False').lower() == 'true',
                'viewSetup': entity.get('ViewSetup', 'False').lower() == 'true',
                'authorApex': entity.get('AuthorApex', 'False').lower() == 'true',
                'viewAllUsers': entity.get('ViewAllUsers', 'False').lower() == 'true',
                'manageEncryptionKeys': entity.get('ManageEncryptionKeys', 'False').lower() == 'true',
                'manageTwoFactor': entity.get('ManageTwoFactor', 'False').lower() == 'true',
                'modifyMetadata': entity.get('ModifyMetadata', 'False').lower() == 'true',
                'customizeApplication': entity.get('CustomizeApplication', 'False').lower() == 'true',
                'manageIpAddresses': entity.get('ManageIpAddresses', 'False').lower() == 'true',
                'lastUpdated': entity.get('LastUpdated', datetime.now().isoformat())
            }
            permission_sets.append(perm_set)
        
        return permission_sets
    else:
        # For production, use SQL Database
        query = """
            SELECT Name, ModifyAllData, ViewAllData, ResetPasswords, DataExport,
                   ManageUsers, ManageProfilesPermissionsets, ManageRoles, ManageSharing,
                   EditReadonlyFields, ViewSetup, AuthorApex, ViewAllUsers,
                   ManageEncryptionKeys, ManageTwoFactor, ModifyMetadata,
                   CustomizeApplication, ManageIpAddresses, LastUpdated
            FROM App_PermissionSetSystemPermissions
            WHERE OrgId = ?
        """
        
        results = repo.execute_query(query, (integration_id,))
        if not results:
            return []
        
        permission_sets = []
        for row in results:
            perm_set = {
                'name': row[0],
                'modifyAllData': row[1],
                'viewAllData': row[2],
                'resetPasswords': row[3],
                'dataExport': row[4],
                'manageUsers': row[5],
                'manageProfilesPermissionsets': row[6],
                'manageRoles': row[7],
                'manageSharing': row[8],
                'editReadonlyFields': row[9],
                'viewSetup': row[10],
                'authorApex': row[11],
                'viewAllUsers': row[12],
                'manageEncryptionKeys': row[13],
                'manageTwoFactor': row[14],
                'modifyMetadata': row[15],
                'customizeApplication': row[16],
                'manageIpAddresses': row[17],
                'lastUpdated': row[18]
            }
            permission_sets.append(perm_set)
        
        return permission_sets

@bp.route(route="fetch_system_permissions")
async def fetch_system_permissions(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Function to fetch system permissions for profiles and permission sets.

    Args:
        req: HTTP request

    Returns:
        func.HttpResponse: HTTP response with JSON data
    """
    logger.info('Processing request to fetch system permissions...')

    try:
        # Get integration ID from query parameters
        integration_id = req.params.get('integration_id')
        if not integration_id:
            return func.HttpResponse(
                json.dumps({"error": "Missing integration_id parameter"}),
                mimetype="application/json",
                status_code=400
            )

        # Check if refresh is requested
        refresh = req.params.get('refresh', 'false').lower() == 'true'
        logger.info(f"Fetching system permissions for integration {integration_id}, refresh={refresh}")

        # Check if we already have data in the database
        existing_profiles = get_profile_system_permissions(integration_id)
        existing_permission_sets = get_permission_set_system_permissions(integration_id)
        
        # Only fetch from Salesforce if refresh=true or no data exists
        if refresh or not existing_profiles or not existing_permission_sets:
            # Get Salesforce access token
            access_token, instance_url = get_salesforce_access_token()
            if not access_token:
                return func.HttpResponse(
                    json.dumps({"error": "Failed to obtain Salesforce access token"}),
                    mimetype="application/json",
                    status_code=500
                )

            # Fetch profiles and permission sets
            profiles = fetch_profile_system_permissions(access_token, instance_url)
            permission_sets = fetch_permission_set_system_permissions(access_token, instance_url)
            
            # Save to database
            profile_stats = save_profile_system_permissions(profiles, integration_id)
            permission_set_stats = save_permission_set_system_permissions(permission_sets, integration_id)
            
            logger.info(f"Saved {profile_stats['inserted']} profiles and {permission_set_stats['inserted']} permission sets")
            
            # Get updated data from database
            existing_profiles = get_profile_system_permissions(integration_id)
            existing_permission_sets = get_permission_set_system_permissions(integration_id)

        # Prepare response
        response_data = {
            "dataStatus": "available" if (existing_profiles or existing_permission_sets) else "empty",
            "profiles": existing_profiles,
            "permissionSets": existing_permission_sets,
            "lastUpdated": datetime.now().isoformat()
        }

        return func.HttpResponse(
            json.dumps(create_json_response(response_data)),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        error_message = f"Error fetching system permissions: {str(e)}"
        logger.error(error_message)
        return func.HttpResponse(
            json.dumps({"error": error_message}),
            mimetype="application/json",
            status_code=500
        )
