"""
Authentication Blueprint

This module provides authentication endpoints for user registration, login, and token refresh.

Best practices implemented:
- JWT-based authentication
- Secure password hashing with salt
- Proper error handling and logging
- Input validation
- Database storage of user data according to schema
"""

import logging
import azure.functions as func
import json
import secrets
import jwt
import os
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional

# Import shared modules
from shared.config import is_local_dev, get_jwt_config
from shared.utils import create_json_response, handle_exception
from shared.cors_middleware import cors_middleware
from shared.user_repository import (
    create_user, get_user_account_by_email, authenticate_user, update_last_login,
    get_user_account_table_repo, get_user_login_table_repo, get_user_account_sql_repo, get_user_login_sql_repo,
    hash_password
)
from shared.auth_utils import get_jwt_secret, get_jwt_algorithm

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Get JWT configuration
jwt_config = get_jwt_config()
ACCESS_TOKEN_EXPIRE_MINUTES = jwt_config.get("access_token_expire_minutes", 30)
REFRESH_TOKEN_EXPIRE_DAYS = jwt_config.get("refresh_token_expire_days", 7)

def get_user(email: str) -> Optional[Dict[str, Any]]:
    """
    Get a user by email - wrapper for backward compatibility

    Args:
        email: User email

    Returns:
        Dict: User entity or None if not found
    """
    user_account = get_user_account_by_email(email)
    if not user_account:
        return None

    # Convert to dictionary format expected by existing code
    return {
        "Email": user_account.Email,
        "Name": f"{user_account.FirstName} {user_account.LastName}".strip(),
        "DateOfBirth": user_account.DoB.isoformat() if user_account.DoB else "",
        "Contact": user_account.Contact or "",
        "State": user_account.State,
        "Country": user_account.Country,
        "UserId": user_account.UserId
    }

def create_access_token(data: Dict[str, Any], expires_delta: timedelta = None) -> str:
    """
    Create a JWT access token

    Args:
        data: Data to encode in the token
        expires_delta: Token expiration time

    Returns:
        str: JWT token
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})

    encoded_jwt = jwt.encode(to_encode, get_jwt_secret(), algorithm=get_jwt_algorithm())
    return encoded_jwt

def create_refresh_token() -> str:
    """
    Create a refresh token

    Returns:
        str: Refresh token
    """
    return secrets.token_hex(32)

def store_refresh_token(email: str, refresh_token: str) -> bool:
    """
    Store a refresh token for a user

    Args:
        email: User email
        refresh_token: Refresh token

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            user_repo = get_user_login_table_repo()
            if not user_repo:
                logger.error("User login table repository not available")
                return False

            # Create refresh token entity
            token_entity = {
                "PartitionKey": "refresh_token",
                "RowKey": refresh_token,
                "Email": email,
                "CreatedAt": datetime.now().isoformat(),
                "ExpiresAt": (datetime.now() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)).isoformat()
            }

            return user_repo.insert_entity(token_entity)
        else:
            # Use SQL Database for production
            user_repo = get_user_login_sql_repo()
            if not user_repo:
                logger.error("User login SQL repository not available")
                return False

            # Store refresh token in SQL
            query = """
            INSERT INTO App_User_Token (Email, RefreshToken, CreatedAt, ExpiresAt)
            VALUES (?, ?, ?, ?)
            """
            params = (
                email,
                refresh_token,
                datetime.now().isoformat(),
                (datetime.now() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)).isoformat()
            )

            return user_repo.execute_non_query(query, params)
    except Exception as e:
        logger.error(f"Error storing refresh token: {str(e)}")
        return False

def verify_refresh_token(refresh_token: str) -> Optional[str]:
    """
    Verify a refresh token and return the associated email

    Args:
        refresh_token: Refresh token to verify

    Returns:
        str: User email or None if invalid
    """
    try:
        if is_local_dev():
            # Use Azure Table Storage for local development
            user_repo = get_user_login_table_repo()
            if not user_repo:
                logger.error("User login table repository not available")
                return None

            # Query by refresh token (RowKey)
            filter_query = f"RowKey eq '{refresh_token}' and PartitionKey eq 'refresh_token'"
            entities = user_repo.query_entities(filter_query)

            if not entities:
                logger.warning(f"Refresh token not found: {refresh_token}")
                return None

            token_entity = entities[0]

            # Check if token is expired
            expires_at = datetime.fromisoformat(token_entity["ExpiresAt"])
            if expires_at < datetime.now():
                logger.warning(f"Refresh token expired: {refresh_token}")
                return None

            return token_entity["Email"]
        else:
            # Use SQL Database for production
            user_repo = get_user_login_sql_repo()
            if not user_repo:
                logger.error("User login SQL repository not available")
                return None

            # Query by refresh token
            query = """
            SELECT Email, ExpiresAt FROM App_User_Token
            WHERE RefreshToken = ? AND ExpiresAt > ?
            """
            params = (refresh_token, datetime.now().isoformat())

            results = user_repo.execute_query(query, params)

            if not results:
                logger.warning(f"Refresh token not found or expired: {refresh_token}")
                return None

            return results[0][0]  # Email
    except Exception as e:
        logger.error(f"Error verifying refresh token: {str(e)}")
        return None



@bp.route(route="auth/signup", methods=["POST"])
def signup(req: func.HttpRequest) -> func.HttpResponse:
    """
    User registration endpoint

    Args:
        req: HTTP request with user registration data

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing signup request...')

    try:
        # Parse request body
        req_body = req.get_json()

        # Validate required fields
        if not req_body or not all(k in req_body for k in ["email", "password"]):
            return func.HttpResponse(
                json.dumps(create_json_response("Email and password are required", 400)),
                mimetype="application/json",
                status_code=400
            )

        email = req_body.get("email")
        password = req_body.get("password")

        # Parse name into components
        full_name = req_body.get("name", "")
        name_parts = full_name.split(" ", 2)
        first_name = name_parts[0] if len(name_parts) > 0 else ""
        middle_name = ""
        last_name = ""

        if len(name_parts) == 2:
            last_name = name_parts[1]
        elif len(name_parts) > 2:
            middle_name = name_parts[1]
            last_name = name_parts[2]

        dob = req_body.get("dob", "")
        contact = req_body.get("contact", "")
        state = req_body.get("state", "")
        country = req_body.get("country", "")
        organization = req_body.get("organization", "")

        # Check if user already exists
        existing_user = get_user(email)
        if existing_user:
            return func.HttpResponse(
                json.dumps(create_json_response("User already exists", 400)),
                mimetype="application/json",
                status_code=400
            )

        # Create user with new repository
        user_id = create_user(
            email=email,
            password=password,
            first_name=first_name,
            middle_name=middle_name,
            last_name=last_name,
            dob=dob,
            contact=contact,
            state=state,
            country=country,
            organization=organization,
            algorithm_id=1  # Use SHA-256 for now
        )

        if not user_id:
            return func.HttpResponse(
                json.dumps(create_json_response("Failed to create user", 500)),
                mimetype="application/json",
                status_code=500
            )

        # Get the name for response
        full_name = f"{first_name} {middle_name} {last_name}".replace("  ", " ").strip()

        # Create access token
        access_token = create_access_token({"sub": email})

        # Create refresh token
        refresh_token = create_refresh_token()
        store_refresh_token(email, refresh_token)

        # Return tokens and user info
        response_data = {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "user": {
                "email": email,
                "name": full_name
            }
        }

        response = func.HttpResponse(
            json.dumps(create_json_response(response_data)),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "signup")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

@bp.route(route="auth/login", methods=["POST"])
def login(req: func.HttpRequest) -> func.HttpResponse:
    """
    User login endpoint

    Args:
        req: HTTP request with user login data

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing login request...')

    try:
        # Parse request body
        req_body = req.get_json()

        # Validate required fields
        if not req_body or not all(k in req_body for k in ["email", "password"]):
            return func.HttpResponse(
                json.dumps(create_json_response("Email and password are required", 400)),
                mimetype="application/json",
                status_code=400
            )

        email = req_body.get("email")
        password = req_body.get("password")

        # Get user
        user = get_user(email)
        if not user:
            return func.HttpResponse(
                json.dumps(create_json_response("Invalid email or password", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Authenticate user with new repository
        user_id = authenticate_user(email, password)
        if not user_id:
            return func.HttpResponse(
                json.dumps(create_json_response("Invalid password. Please try again.", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Update last login
        update_last_login(email)

        # Create access token
        access_token = create_access_token({"sub": email})

        # Create refresh token
        refresh_token = create_refresh_token()
        store_refresh_token(email, refresh_token)

        # Return tokens and user info
        response_data = {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "user": {
                "email": email,
                "name": user.get("Name", "")
            }
        }

        # Create the response
        response = func.HttpResponse(
            json.dumps(create_json_response(response_data)),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "login")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)

@bp.route(route="auth/token/refresh", methods=["POST"])
def refresh_token(req: func.HttpRequest) -> func.HttpResponse:
    """
    Token refresh endpoint

    Args:
        req: HTTP request with refresh token

    Returns:
        func.HttpResponse: HTTP response
    """
    logger.info('Processing token refresh request...')

    try:
        # Parse request body
        req_body = req.get_json()

        # Validate required fields
        if not req_body or "refresh_token" not in req_body:
            return func.HttpResponse(
                json.dumps(create_json_response("Refresh token is required", 400)),
                mimetype="application/json",
                status_code=400
            )

        refresh_token_str = req_body.get("refresh_token")

        # Verify refresh token
        email = verify_refresh_token(refresh_token_str)
        if not email:
            return func.HttpResponse(
                json.dumps(create_json_response("Invalid or expired refresh token", 401)),
                mimetype="application/json",
                status_code=401
            )

        # Create new access token
        access_token = create_access_token({"sub": email})

        # Create new refresh token
        new_refresh_token = create_refresh_token()
        store_refresh_token(email, new_refresh_token)

        # Return new tokens
        response_data = {
            "access_token": access_token,
            "refresh_token": new_refresh_token,
            "token_type": "bearer"
        }

        response = func.HttpResponse(
            json.dumps(create_json_response(response_data)),
            mimetype="application/json",
            status_code=200
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
    except Exception as e:
        error_response = handle_exception(e, "refresh_token")
        response = func.HttpResponse(
            json.dumps(error_response),
            mimetype="application/json",
            status_code=500
        )

        # Apply CORS middleware
        return cors_middleware(req, response)
