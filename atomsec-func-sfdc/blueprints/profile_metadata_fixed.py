"""
Profile Metadata Blueprint

This module provides functions for retrieving and managing Salesforce profile metadata.

Best practices implemented:
- Async functions for better performance
- Proper error handling and logging
- Repository pattern for data access
- Centralized configuration
- Lazy initialization of repositories
"""

import logging
import azure.functions as func
import pandas as pd
import asyncio
import aiohttp
import json
from datetime import datetime
from typing import List, Tuple, Dict, Any, Optional

# Import shared modules
from shared.azure_services import is_local_dev
from shared.data_access import BlobStorageRepository, TableStorageRepository, SqlDatabaseRepository
from shared.utils import get_salesforce_access_token, execute_salesforce_query, handle_exception

# Configure module-level logger
logger = logging.getLogger(__name__)

# Create blueprint
bp = func.Blueprint()

# Lazy-initialized repositories
_blob_repo = None
_table_repo = None
_sql_repo = None

def get_blob_repo():
    """Lazy initialize the blob repository"""
    global _blob_repo
    if _blob_repo is None:
        try:
            _blob_repo = BlobStorageRepository(container_name="profile-metadata")
        except Exception as e:
            logger.warning(f"Failed to initialize blob repository: {str(e)}")
            _blob_repo = None
    return _blob_repo

def get_table_repo():
    """Lazy initialize the table repository"""
    global _table_repo
    if _table_repo is None:
        try:
            _table_repo = TableStorageRepository(table_name="ProfileMetadata")
        except Exception as e:
            logger.warning(f"Failed to initialize table repository: {str(e)}")
            _table_repo = None
    return _table_repo

def get_sql_repo():
    """Lazy initialize the SQL repository"""
    global _sql_repo
    if _sql_repo is None:
        try:
            _sql_repo = SqlDatabaseRepository(table_name="ProfileMetadata")
        except Exception as e:
            logger.warning(f"Failed to initialize SQL repository: {str(e)}")
            _sql_repo = None
    return _sql_repo

async def fetch_profile_permissions(profile_id: str, access_token: str, instance_url: str, session: aiohttp.ClientSession) -> List[Tuple]:
    """
    Fetch permissions for a specific profile and format the results.

    Args:
        profile_id: Salesforce Profile ID
        access_token: Salesforce access token
        instance_url: Salesforce instance URL
        session: aiohttp ClientSession

    Returns:
        List[Tuple]: List of permission records
    """
    permissions_query = (
        f"SELECT Parent.Profile.name, SObjectType, PermissionsRead, PermissionsCreate, PermissionsEdit, "
        f"PermissionsDelete, PermissionsViewAllRecords, PermissionsModifyAllRecords, Parent.Name "
        f"FROM ObjectPermissions WHERE Parent.ProfileId = '{profile_id}' AND Parent.Name != null AND Parent.Profile.Name != null"
    )

    response = await execute_salesforce_query(permissions_query, access_token, instance_url)

    if isinstance(response, dict) and "records" in response:
        permissions = []
        for record in response["records"]:
            logger.debug(f"Processing Record: {record}")

            permissions_data = (
                record.get("parent", {}).get("name", "Unknown Profile"),  # ParentName
                record.get("sobjecttype", "Unknown Object"),  # SObjectType
                record.get("permissionsread", False),
                record.get("permissionscreate", False),
                record.get("permissionsedit", False),
                record.get("permissionsdelete", False),
                record.get("permissionsviewallrecords", False),
                record.get("permissionsmodifyallrecords", False),
            )
            permissions.append(permissions_data)

        return permissions
    else:
        logger.error(f"Unexpected response format: {response}")
        return []

async def fetch_all_profile_permissions(profiles: List[Dict[str, Any]], access_token: str, instance_url: str) -> List[List[Tuple]]:
    """
    Fetch permissions for all profiles concurrently.

    Args:
        profiles: List of Salesforce profiles
        access_token: Salesforce access token
        instance_url: Salesforce instance URL

    Returns:
        List[List[Tuple]]: List of permission records for each profile
    """
    async with aiohttp.ClientSession() as session:
        tasks = []
        for profile in profiles:
            profile_id = profile["Id"]
            tasks.append(fetch_profile_permissions(profile_id, access_token, instance_url, session))

        responses = await asyncio.gather(*tasks)
        return responses

def batch_insert_profile_records(records: List[Tuple]) -> Dict[str, int]:
    """
    Insert new profile metadata records into the database or Azure Table Storage.

    Args:
        records: List of profile metadata records

    Returns:
        Dict[str, int]: Dictionary with counts of operations performed
    """
    # Initialize result statistics
    stats = {
        "total_records": len(records),
        "inserted": 0,
        "failed": 0,
        "profiles_count": len(set([r[0] for r in records])),
        "objects_count": len(set([r[1] for r in records]))
    }

    # Count permissions by type
    read_count = sum(1 for r in records if r[2])
    create_count = sum(1 for r in records if r[3])
    edit_count = sum(1 for r in records if r[4])
    delete_count = sum(1 for r in records if r[5])
    view_all_count = sum(1 for r in records if r[6])
    modify_all_count = sum(1 for r in records if r[7])

    # Log detailed statistics
    logger.info(f"Processing {stats['total_records']} records for {stats['profiles_count']} profiles and {stats['objects_count']} objects")
    logger.info(f"Permission counts - Read: {read_count}, Create: {create_count}, Edit: {edit_count}, Delete: {delete_count}, ViewAll: {view_all_count}, ModifyAll: {modify_all_count}")

    if is_local_dev():
        # Use Azure Table Storage for local development
        table_repo = get_table_repo()
        blob_repo = get_blob_repo()

        # Check if repositories are available
        if table_repo is None and blob_repo is None:
            logger.warning("No storage repositories available. Make sure Azurite is running.")
            stats["error"] = "No storage repositories available"
            return stats

        # Insert into table storage if available
        if table_repo is not None:
            start_time = datetime.now()
            for record in records:
                # Convert the record tuple to a dictionary for Table Storage
                entity = {
                    'PartitionKey': datetime.now().strftime("%Y%m%d"),
                    'RowKey': f"{datetime.now().strftime('%H%M%S%f')}-{stats['inserted']}",
                    'ParentName': record[0],
                    'SObjectType': record[1],
                    'PermissionsRead': str(record[2]),  # Convert boolean to string
                    'PermissionsCreate': str(record[3]),
                    'PermissionsEdit': str(record[4]),
                    'PermissionsDelete': str(record[5]),
                    'PermissionsViewAllRecords': str(record[6]),
                    'PermissionsModifyAllRecords': str(record[7])
                }

                if table_repo.insert_entity(entity):
                    stats["inserted"] += 1
                else:
                    stats["failed"] += 1

            elapsed_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"Table Storage: Inserted {stats['inserted']} records, Failed {stats['failed']} records in {elapsed_time:.2f} seconds")

        # Also save to blob storage for backup if available
        if blob_repo is not None:
            try:
                start_time = datetime.now()
                blob_name = f"profile-metadata-{datetime.now().strftime('%Y%m%d-%H%M%S')}.json"
                blob_data = [{
                    'ParentName': record[0],
                    'SObjectType': record[1],
                    'PermissionsRead': record[2],
                    'PermissionsCreate': record[3],
                    'PermissionsEdit': record[4],
                    'PermissionsDelete': record[5],
                    'PermissionsViewAllRecords': record[6],
                    'PermissionsModifyAllRecords': record[7]
                } for record in records]

                blob_repo.save_json(blob_name, blob_data)
                elapsed_time = (datetime.now() - start_time).total_seconds()
                logger.info(f"Blob Storage: Saved {len(blob_data)} records to {blob_name} in {elapsed_time:.2f} seconds")
                stats["blob_saved"] = len(blob_data)
                stats["blob_name"] = blob_name
            except Exception as e:
                logger.warning(f"Failed to save to blob storage: {str(e)}")
                stats["blob_error"] = str(e)

        return stats
    else:
        # Use SQL Database in production
        sql_repo = get_sql_repo()

        # Check if SQL repository is available
        if sql_repo is None:
            logger.warning("SQL repository is not available.")
            stats["error"] = "SQL repository is not available"
            return stats

        query = """
            INSERT INTO ProfileMetadata (
                ParentName, SObjectType, PermissionsRead, PermissionsCreate, PermissionsEdit,
                PermissionsDelete, PermissionsViewAllRecords, PermissionsModifyAllRecords
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """

        # Execute batch insert using SqlDatabaseRepository
        start_time = datetime.now()
        for record in records:
            if sql_repo.execute_non_query(query, record):
                stats["inserted"] += 1
            else:
                stats["failed"] += 1

        elapsed_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"SQL Database: Inserted {stats['inserted']} records, Failed {stats['failed']} records in {elapsed_time:.2f} seconds")
        return stats

def get_all_existing_profile_records(profile_filter: Optional[str] = None, object_filter: Optional[str] = None) -> List[Tuple]:
    """
    Fetch all records from the ProfileMetadata table in the database or Azure Table Storage.

    Args:
        profile_filter: Optional filter for profile name
        object_filter: Optional filter for object name

    Returns:
        List[Tuple]: List of profile metadata records
    """
    if is_local_dev():
        # Use Azure Table Storage for local development
        table_repo = get_table_repo()

        # Check if table repository is available
        if table_repo is None:
            logger.warning("Table repository is not available. Make sure Azurite is running.")
            return []

        try:
            # Prepare field filters if provided
            field_filter = {}
            if profile_filter:
                field_filter['ParentName'] = profile_filter
            if object_filter:
                field_filter['SObjectType'] = object_filter

            # Try to query entities with filters
            try:
                if field_filter:
                    logger.info(f"Querying with field filters: {field_filter}")
                    entities = table_repo.query_entities(field_filter=field_filter)
                else:
                    entities = table_repo.query_entities()
            except TypeError as te:
                # If TypeError occurs, try with an empty string filter
                logger.warning(f"TypeError in query_entities: {str(te)}. Trying with empty filter.")
                try:
                    entities = table_repo.query_entities("")

                    # Apply filters manually if needed
                    if field_filter:
                        filtered_entities = []
                        for entity in entities:
                            include = True
                            for field, value in field_filter.items():
                                if field in entity:
                                    entity_value = str(entity[field]).lower()
                                    filter_value = str(value).lower()
                                    if filter_value not in entity_value:
                                        include = False
                                        break
                            if include:
                                filtered_entities.append(entity)
                        entities = filtered_entities

                except Exception as inner_e:
                    logger.error(f"Failed to query entities with empty filter: {str(inner_e)}")
                    return []

            # Convert Table Storage entities to the same format as SQL records
            records = []
            for entity in entities:
                # Convert string representations back to appropriate types
                permissions_read = entity.get('PermissionsRead', 'False').lower() == 'true'
                permissions_create = entity.get('PermissionsCreate', 'False').lower() == 'true'
                permissions_edit = entity.get('PermissionsEdit', 'False').lower() == 'true'
                permissions_delete = entity.get('PermissionsDelete', 'False').lower() == 'true'
                permissions_view_all = entity.get('PermissionsViewAllRecords', 'False').lower() == 'true'
                permissions_modify_all = entity.get('PermissionsModifyAllRecords', 'False').lower() == 'true'

                record = (
                    entity.get('ParentName', ''),
                    entity.get('SObjectType', ''),
                    permissions_read,
                    permissions_create,
                    permissions_edit,
                    permissions_delete,
                    permissions_view_all,
                    permissions_modify_all
                )
                records.append(record)

            return records
        except Exception as e:
            logger.warning(f"Error querying table storage: {str(e)}")
            return []
    else:
        # Use SQL Database in production
        sql_repo = get_sql_repo()

        # Check if SQL repository is available
        if sql_repo is None:
            logger.warning("SQL repository is not available.")
            return []

        try:
            # Build SQL query with filters if provided
            if profile_filter or object_filter:
                query = "SELECT * FROM ProfileMetadata WHERE "
                conditions = []
                params = []

                if profile_filter:
                    conditions.append("ParentName LIKE ?")
                    params.append(f"%{profile_filter}%")

                if object_filter:
                    conditions.append("SObjectType LIKE ?")
                    params.append(f"%{object_filter}%")

                query += " AND ".join(conditions)
                return sql_repo.execute_query(query, tuple(params))
            else:
                query = "SELECT * FROM ProfileMetadata"
                return sql_repo.execute_query(query)
        except Exception as e:
            logger.warning(f"Error querying SQL database: {str(e)}")
            return []

# The profile_metadata function has been removed as it's redundant
# This functionality is now provided by the /api/api/integration/{tenant_url}/profiles endpoint
# in the integration_tabs.py blueprint

# @bp.route(route="profile_metadata")
# async def profile_metadata(req: func.HttpRequest) -> func.HttpResponse:
# Function implementation removed as it's redundant

# The add_profile_metadata endpoint has been removed as requested
# This functionality is now provided by the /api/api/integration/{tenant_url}/profiles endpoint
# in the integration_tabs.py blueprint
