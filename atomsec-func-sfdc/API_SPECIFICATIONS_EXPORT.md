# API Specifications Export

## 🔌 Complete API Reference

### Base URLs
- **SFDC Service**: `http://localhost:7071/api`
- **DB Service**: `http://localhost:7072/api`
- **Auth Service**: `http://localhost:7073/api`

## 📋 Task Management API

### Create Task
```http
POST /api/tasks
Content-Type: application/json

{
  "task_type": "metadata_extraction",
  "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
  "user_id": "2123",
  "params": {
    "access_token": "00D5j000006P6ly!AQgAQM9ts3lBUQ31FN780NESscn0OQyfQfFuK_sV60pEIbUjqWKjgQfhuf0XGluiqDvnx1s27RG_kICYPkYCXoo5ioLFq7UQ",
    "instance_url": "https://mtx2-dev-ed.my.salesforce.com/services/data/v59.0/",
    "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "environment": "production"
  },
  "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0"
}

Response: 201 Created
{
  "success": true,
  "task_id": "183c990b-c389-4ce5-900f-e13ddd1e0f0a",
  "message": "Task created successfully"
}
```

### Get Tasks
```http
GET /api/tasks?org_id={org_id}&status={status}&task_type={task_type}&limit={limit}

Parameters:
- org_id (required): Organization ID
- status (optional): Task status filter (pending, running, completed, failed)
- task_type (optional): Task type filter
- limit (optional): Maximum number of tasks to return (default: 100)

Response: 200 OK
{
  "success": true,
  "data": [
    {
      "task_id": "183c990b-c389-4ce5-900f-e13ddd1e0f0a",
      "task_type": "metadata_extraction",
      "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
      "user_id": "2123",
      "status": "completed",
      "priority": "high",
      "progress": 100,
      "retry_count": 0,
      "created_at": "2025-07-07T19:16:17.275408",
      "updated_at": "2025-07-07T19:16:17.275411",
      "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0",
      "params": {
        "access_token": "00D5j000006P6ly!AQgAQM9ts3lBUQ31FN780NESscn0OQyfQfFuK_sV60pEIbUjqWKjgQfhuf0XGluiqDvnx1s27RG_kICYPkYCXoo5ioLFq7UQ",
        "instance_url": "https://mtx2-dev-ed.my.salesforce.com/services/data/v59.0/",
        "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
        "environment": "production"
      },
      "message": "Task completed successfully",
      "result": "{\"blob_path\": \"/path/to/metadata\"}"
    }
  ]
}
```

### Get Task by ID
```http
GET /api/tasks/{task_id}

Response: 200 OK
{
  "success": true,
  "data": {
    "task_id": "183c990b-c389-4ce5-900f-e13ddd1e0f0a",
    "task_type": "metadata_extraction",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "user_id": "2123",
    "status": "completed",
    "priority": "high",
    "progress": 100,
    "retry_count": 0,
    "created_at": "2025-07-07T19:16:17.275408",
    "updated_at": "2025-07-07T19:16:17.275411",
    "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0",
    "params": {...},
    "message": "Task completed successfully",
    "result": "{\"blob_path\": \"/path/to/metadata\"}"
  }
}

Response: 404 Not Found
{
  "success": false,
  "error": "Task not found"
}
```

### Get Latest Task by Type
```http
GET /api/tasks/latest?org_id={org_id}&task_type={task_type}&status={status}&execution_log_id={execution_log_id}

Parameters:
- org_id (required): Organization ID
- task_type (required): Task type
- status (optional): Task status filter (default: any status)
- execution_log_id (optional): Execution log ID filter

Response: 200 OK
{
  "success": true,
  "data": {
    "task_id": "183c990b-c389-4ce5-900f-e13ddd1e0f0a",
    "task_type": "metadata_extraction",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "user_id": "2123",
    "status": "completed",
    "execution_log_id": "e5cc4b65-d0ce-4570-9425-9619af407db0",
    "created_at": "2025-07-07T19:16:17.275408"
  }
}

Response: 404 Not Found
{
  "success": false,
  "error": "No task found"
}
```

### Update Task Status
```http
PUT /api/tasks/{task_id}/status
Content-Type: application/json

{
  "status": "completed",
  "progress": 100,
  "message": "Task completed successfully",
  "result": "{\"blob_path\": \"/path/to/metadata\"}",
  "error": null
}

Response: 200 OK
{
  "success": true,
  "message": "Task status updated successfully"
}

Response: 404 Not Found
{
  "success": false,
  "error": "Task not found"
}
```

### Delete Task
```http
DELETE /api/tasks/{task_id}

Response: 200 OK
{
  "success": true,
  "message": "Task deleted successfully"
}

Response: 404 Not Found
{
  "success": false,
  "error": "Task not found"
}
```

## 🏥 Health Check API

### Get Service Health
```http
GET /api/health

Response: 200 OK
{
  "status": "healthy",
  "service": "atomsec-func-sfdc",
  "environment": "local",
  "version": "1.0.0",
  "timestamp": "2025-07-07T19:16:17.275408",
  "checks": {
    "database": "connected",
    "service_bus": "connected",
    "key_vault": "connected",
    "sfdc_service": "connected"
  },
  "metrics": {
    "pending_tasks": 5,
    "completed_tasks": 150,
    "failed_tasks": 2,
    "total_tasks": 157
  },
  "uptime": "2h 15m 30s",
  "memory_usage": "45.2%",
  "cpu_usage": "12.8%"
}
```

### Get Detailed Health
```http
GET /api/health/detailed

Response: 200 OK
{
  "status": "healthy",
  "service": "atomsec-func-sfdc",
  "environment": "local",
  "version": "1.0.0",
  "timestamp": "2025-07-07T19:16:17.275408",
  "checks": {
    "database": {
      "status": "connected",
      "response_time": "45ms",
      "last_check": "2025-07-07T19:16:17.275408"
    },
    "service_bus": {
      "status": "connected",
      "response_time": "23ms",
      "last_check": "2025-07-07T19:16:17.275408"
    },
    "key_vault": {
      "status": "connected",
      "response_time": "67ms",
      "last_check": "2025-07-07T19:16:17.275408"
    },
    "sfdc_service": {
      "status": "connected",
      "response_time": "89ms",
      "last_check": "2025-07-07T19:16:17.275408"
    }
  },
  "metrics": {
    "task_processing": {
      "total_tasks_processed": 157,
      "tasks_per_minute": 2.3,
      "average_processing_time": "45.2s",
      "dependency_check_time": "1.2s"
    },
    "task_status": {
      "pending_tasks": 5,
      "running_tasks": 2,
      "completed_tasks": 150,
      "failed_tasks": 2,
      "retry_count": 3
    },
    "system_health": {
      "db_service_available": true,
      "storage_available": true,
      "service_bus_available": true,
      "last_poll_time": "2025-07-07T19:16:17.275408"
    },
    "errors": {
      "dependency_check_failures": 0,
      "task_processing_errors": 2,
      "service_communication_errors": 0
    }
  },
  "uptime": "2h 15m 30s",
  "memory_usage": "45.2%",
  "cpu_usage": "12.8%",
  "disk_usage": "23.1%"
}
```

## 📊 Policy Management API

### Get Policies
```http
GET /api/policies?integration_id={integration_id}&user_id={user_id}&enabled={enabled}

Parameters:
- integration_id (optional): Filter by integration ID
- user_id (optional): Filter by user ID
- enabled (optional): Filter by enabled status (true/false)

Response: 200 OK
{
  "success": true,
  "data": [
    {
      "policy_id": "policy-123",
      "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
      "user_id": "2123",
      "name": "Security Policy",
      "description": "Default security policy for Salesforce orgs",
      "enabled": true,
      "created_at": "2025-07-07T19:16:17.275408",
      "updated_at": "2025-07-07T19:16:17.275411"
    }
  ]
}
```

### Create Policy
```http
POST /api/policies
Content-Type: application/json

{
  "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
  "user_id": "2123",
  "name": "Security Policy",
  "description": "Default security policy for Salesforce orgs",
  "enabled": true
}

Response: 201 Created
{
  "success": true,
  "policy_id": "policy-123",
  "message": "Policy created successfully"
}
```

### Update Policy
```http
PUT /api/policies/{policy_id}
Content-Type: application/json

{
  "name": "Updated Security Policy",
  "description": "Updated description",
  "enabled": false
}

Response: 200 OK
{
  "success": true,
  "message": "Policy updated successfully"
}
```

### Delete Policy
```http
DELETE /api/policies/{policy_id}

Response: 200 OK
{
  "success": true,
  "message": "Policy deleted successfully"
}
```

## 🔧 Rules Management API

### Get Rules
```http
GET /api/rules?policy_id={policy_id}&task_type={task_type}&enabled={enabled}

Parameters:
- policy_id (optional): Filter by policy ID
- task_type (optional): Filter by task type
- enabled (optional): Filter by enabled status (true/false)

Response: 200 OK
{
  "success": true,
  "data": [
    {
      "rule_id": "rule-123",
      "policy_id": "policy-123",
      "task_type": "metadata_extraction",
      "enabled": true,
      "conditions": {
        "max_retries": 3,
        "timeout": 300
      },
      "created_at": "2025-07-07T19:16:17.275408",
      "updated_at": "2025-07-07T19:16:17.275411"
    }
  ]
}
```

### Create Rule
```http
POST /api/rules
Content-Type: application/json

{
  "policy_id": "policy-123",
  "task_type": "metadata_extraction",
  "enabled": true,
  "conditions": {
    "max_retries": 3,
    "timeout": 300
  }
}

Response: 201 Created
{
  "success": true,
  "rule_id": "rule-123",
  "message": "Rule created successfully"
}
```

### Update Rule
```http
PUT /api/rules/{rule_id}
Content-Type: application/json

{
  "enabled": false,
  "conditions": {
    "max_retries": 5,
    "timeout": 600
  }
}

Response: 200 OK
{
  "success": true,
  "message": "Rule updated successfully"
}
```

### Delete Rule
```http
DELETE /api/rules/{rule_id}

Response: 200 OK
{
  "success": true,
  "message": "Rule deleted successfully"
}
```

## 🔍 Integration Management API

### Get Integrations
```http
GET /api/integrations?type={type}&environment={environment}&is_active={is_active}

Parameters:
- type (optional): Filter by integration type
- environment (optional): Filter by environment
- is_active (optional): Filter by active status (true/false)

Response: 200 OK
{
  "success": true,
  "data": [
    {
      "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
      "name": "Salesforce Org",
      "type": "Salesforce",
      "tenant_url": "mtx2-dev-ed.my.salesforce.com",
      "environment": "production",
      "is_active": true,
      "health_score": 85,
      "last_scan": "2025-07-07T19:16:17.275408",
      "last_health_check_scan": "2025-07-07T19:16:17.275408",
      "last_metadata_scan_time": "2025-07-07T19:16:17.275408",
      "created_at": "2025-07-07T19:16:17.275408",
      "updated_at": "2025-07-07T19:16:17.275411"
    }
  ]
}
```

### Create Integration
```http
POST /api/integrations
Content-Type: application/json

{
  "name": "Salesforce Org",
  "type": "Salesforce",
  "tenant_url": "mtx2-dev-ed.my.salesforce.com",
  "environment": "production",
  "is_active": true
}

Response: 201 Created
{
  "success": true,
  "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
  "message": "Integration created successfully"
}
```

### Update Integration
```http
PUT /api/integrations/{integration_id}
Content-Type: application/json

{
  "name": "Updated Salesforce Org",
  "environment": "sandbox",
  "is_active": false
}

Response: 200 OK
{
  "success": true,
  "message": "Integration updated successfully"
}
```

### Delete Integration
```http
DELETE /api/integrations/{integration_id}

Response: 200 OK
{
  "success": true,
  "message": "Integration deleted successfully"
}
```

## 📈 Metrics API

### Get Task Metrics
```http
GET /api/metrics/tasks?org_id={org_id}&time_range={time_range}

Parameters:
- org_id (optional): Filter by organization ID
- time_range (optional): Time range for metrics (1h, 24h, 7d, 30d)

Response: 200 OK
{
  "success": true,
  "data": {
    "total_tasks": 157,
    "pending_tasks": 5,
    "running_tasks": 2,
    "completed_tasks": 150,
    "failed_tasks": 2,
    "success_rate": 95.5,
    "average_processing_time": "45.2s",
    "tasks_per_minute": 2.3,
    "by_task_type": {
      "metadata_extraction": {
        "total": 25,
        "completed": 24,
        "failed": 1,
        "success_rate": 96.0
      },
      "health_check": {
        "total": 30,
        "completed": 29,
        "failed": 1,
        "success_rate": 96.7
      }
    },
    "by_status": {
      "pending": 5,
      "running": 2,
      "completed": 150,
      "failed": 2
    }
  }
}
```

### Get System Metrics
```http
GET /api/metrics/system

Response: 200 OK
{
  "success": true,
  "data": {
    "uptime": "2h 15m 30s",
    "memory_usage": "45.2%",
    "cpu_usage": "12.8%",
    "disk_usage": "23.1%",
    "active_connections": 15,
    "requests_per_minute": 45.2,
    "average_response_time": "234ms",
    "error_rate": "1.2%",
    "service_health": {
      "database": "healthy",
      "service_bus": "healthy",
      "key_vault": "healthy",
      "sfdc_service": "healthy"
    }
  }
}
```

## 🚨 Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": "Error message",
  "error_code": "ERROR_CODE",
  "timestamp": "2025-07-07T19:16:17.275408",
  "request_id": "req-123",
  "details": {
    "field": "Additional error details"
  }
}
```

### Common Error Codes
| Error Code | HTTP Status | Description |
|------------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `NOT_FOUND` | 404 | Resource not found |
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Access denied |
| `CONFLICT` | 409 | Resource conflict (e.g., duplicate) |
| `INTERNAL_ERROR` | 500 | Internal server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

### Example Error Responses

#### Validation Error
```http
Response: 400 Bad Request
{
  "success": false,
  "error": "Validation failed",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2025-07-07T19:16:17.275408",
  "request_id": "req-123",
  "details": {
    "task_type": "Invalid task type: invalid_type",
    "org_id": "Invalid UUID format"
  }
}
```

#### Not Found Error
```http
Response: 404 Not Found
{
  "success": false,
  "error": "Task not found",
  "error_code": "NOT_FOUND",
  "timestamp": "2025-07-07T19:16:17.275408",
  "request_id": "req-123"
}
```

#### Service Unavailable Error
```http
Response: 503 Service Unavailable
{
  "success": false,
  "error": "Database service unavailable",
  "error_code": "SERVICE_UNAVAILABLE",
  "timestamp": "2025-07-07T19:16:17.275408",
  "request_id": "req-123"
}
```

## 🔐 Authentication & Authorization

### API Key Authentication
```http
GET /api/tasks
Authorization: Bearer your-api-key
```

### JWT Token Authentication
```http
GET /api/tasks
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Role-Based Access Control
```json
{
  "user_id": "2123",
  "roles": ["admin", "analyst"],
  "permissions": [
    "tasks:read",
    "tasks:write",
    "policies:read",
    "policies:write"
  ],
  "organizations": [
    "d432f9f2-c257-49c9-a5d0-1f11784d0f75"
  ]
}
```

## 📝 API Versioning

### Version Header
```http
GET /api/tasks
Accept: application/json
API-Version: v1
```

### URL Versioning
```http
GET /api/v1/tasks
GET /api/v2/tasks
```

## 🧪 Testing Examples

### cURL Examples
```bash
# Health Check
curl -s "http://localhost:7071/api/health" | jq .

# Get Tasks
curl -s "http://localhost:7071/api/tasks?org_id=d432f9f2-c257-49c9-a5d0-1f11784d0f75" | jq .

# Create Task
curl -X POST "http://localhost:7071/api/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "metadata_extraction",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "user_id": "2123",
    "params": {
      "access_token": "your-token",
      "instance_url": "your-instance",
      "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75"
    },
    "execution_log_id": "test-execution-123"
  }' | jq .

# Update Task Status
curl -X PUT "http://localhost:7071/api/tasks/TASK_ID/status" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "completed",
    "progress": 100,
    "message": "Task completed successfully"
  }' | jq .
```

### Python Examples
```python
import requests
import json

# Base URL
base_url = "http://localhost:7071/api"

# Health Check
response = requests.get(f"{base_url}/health")
print(json.dumps(response.json(), indent=2))

# Get Tasks
params = {
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "status": "pending"
}
response = requests.get(f"{base_url}/tasks", params=params)
print(json.dumps(response.json(), indent=2))

# Create Task
task_data = {
    "task_type": "metadata_extraction",
    "org_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75",
    "user_id": "2123",
    "params": {
        "access_token": "your-token",
        "instance_url": "your-instance",
        "integration_id": "d432f9f2-c257-49c9-a5d0-1f11784d0f75"
    },
    "execution_log_id": "test-execution-123"
}
response = requests.post(f"{base_url}/tasks", json=task_data)
print(json.dumps(response.json(), indent=2))
```

### JavaScript Examples
```javascript
// Base URL
const baseUrl = 'http://localhost:7071/api';

// Health Check
fetch(`${baseUrl}/health`)
  .then(response => response.json())
  .then(data => console.log(data));

// Get Tasks
const params = new URLSearchParams({
  org_id: 'd432f9f2-c257-49c9-a5d0-1f11784d0f75',
  status: 'pending'
});

fetch(`${baseUrl}/tasks?${params}`)
  .then(response => response.json())
  .then(data => console.log(data));

// Create Task
const taskData = {
  task_type: 'metadata_extraction',
  org_id: 'd432f9f2-c257-49c9-a5d0-1f11784d0f75',
  user_id: '2123',
  params: {
    access_token: 'your-token',
    instance_url: 'your-instance',
    integration_id: 'd432f9f2-c257-49c9-a5d0-1f11784d0f75'
  },
  execution_log_id: 'test-execution-123'
};

fetch(`${baseUrl}/tasks`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(taskData)
})
  .then(response => response.json())
  .then(data => console.log(data));
```

---

This API specifications export provides a complete reference for implementing and consuming the AtomSec Salesforce Integration APIs in different architectural models.
