# Queue Setup for Background Tasks

This document explains how to set up the required Azure Storage Queues for the background task processor.

## Overview

The application uses Azure Storage Queues to manage background tasks. There are four queues:

1. `task-queue` - Default queue for tasks without a specific priority
2. `task-queue-high` - Queue for high-priority tasks
3. `task-queue-medium` - Queue for medium-priority tasks
4. `task-queue-low` - Queue for low-priority tasks

## Setup Instructions

### Automatic Setup

The easiest way to set up the queues is to use the provided `start.sh` script:

```bash
./start.sh
```

This script will:
1. Check if <PERSON><PERSON><PERSON> is running and start it if needed
2. Create all required queues
3. Start the function app

### Manual Setup

If you prefer to set up the queues manually, follow these steps:

1. Ensure Azurite is running:
   ```bash
   azurite
   ```

2. Create the required queues:
   ```bash
   python create_queues.py
   ```

3. Start the function app:
   ```bash
   func start
   ```

## Troubleshooting

If you encounter issues with the background tasks, check the following:

1. Ensure Azurite is running:
   ```bash
   nc -z localhost 10000 && echo "Blob service is running" || echo "Blob service is not running"
   nc -z localhost 10001 && echo "Queue service is running" || echo "Queue service is not running"
   nc -z localhost 10002 && echo "Table service is running" || echo "Table service is not running"
   ```

2. Verify the queues exist:
   ```bash
   python create_queues.py
   ```
   This will list all existing queues.

3. Check the logs for any queue-related errors.

## Queue Configuration

The queue configuration is defined in the `shared/background_processor.py` file. The following constants are used:

```python
# Task priorities
TASK_PRIORITY_HIGH = "high"
TASK_PRIORITY_MEDIUM = "medium"
TASK_PRIORITY_LOW = "low"

# Queue names for different priorities
QUEUE_PRIORITY_HIGH = "task-queue-high"
QUEUE_PRIORITY_MEDIUM = "task-queue-medium"
QUEUE_PRIORITY_LOW = "task-queue-low"
```

Each task type is mapped to a priority in the `TASK_TYPE_PRIORITY_MAP` dictionary.

## Task Types

The following task types are defined:

- `TASK_TYPE_OVERVIEW` - Overview data
- `TASK_TYPE_HEALTH_CHECK` - Security health check
- `TASK_TYPE_PROFILES` - Profile permissions
- `TASK_TYPE_GUEST_USER_RISKS` - Guest user risks
- `TASK_TYPE_PMD_ISSUES` - PMD issues
- `TASK_TYPE_DATA_EXPORT` - Data export
- `TASK_TYPE_REPORT_GENERATION` - Report generation
- `TASK_TYPE_SCHEDULED_SCAN` - Scheduled scan
- `TASK_TYPE_NOTIFICATION` - Notification
- `TASK_TYPE_METADATA_EXTRACTION` - Metadata extraction

## Task Priority Mapping

Tasks are automatically assigned a priority based on their type:

- High priority: `TASK_TYPE_HEALTH_CHECK`, `TASK_TYPE_GUEST_USER_RISKS`, `TASK_TYPE_NOTIFICATION`, `TASK_TYPE_METADATA_EXTRACTION`
- Medium priority: `TASK_TYPE_PROFILES`, `TASK_TYPE_PMD_ISSUES`, `TASK_TYPE_OVERVIEW`, `TASK_TYPE_SCHEDULED_SCAN`
- Low priority: `TASK_TYPE_DATA_EXPORT`, `TASK_TYPE_REPORT_GENERATION`
