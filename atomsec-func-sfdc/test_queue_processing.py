#!/usr/bin/env python3
"""
Test Queue Processing Script

This script tests if the message encoding fix works by sending a test message
to the queue and monitoring if it gets processed correctly.
"""

import os
import json
import time
import uuid
from datetime import datetime
from azure.storage.queue import QueueServiceClient

def get_connection_string():
    """Get the storage connection string"""
    return "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;"

def create_test_message():
    """Create a test task message"""
    task_id = f"test-{uuid.uuid4().hex[:8]}"
    execution_log_id = str(uuid.uuid4())
    
    test_message = {
        "task_id": task_id,
        "task_type": "sfdc_authenticate",
        "org_id": "test-org-id",
        "user_id": "test-user",
        "priority": "High",
        "params": {
            "integration_id": "test-integration-id",
            "tenant_url": "test.salesforce.com",
            "environment": "production",
            "scan_type": "manual"
        },
        "execution_log_id": execution_log_id,
        "created_at": datetime.now().isoformat()
    }
    
    return test_message

def send_test_message(queue_name="task-queue-high"):
    """Send a test message to the specified queue"""
    connection_string = get_connection_string()
    queue_service = QueueServiceClient.from_connection_string(connection_string)
    queue_client = queue_service.get_queue_client(queue_name)
    
    # Create test message
    test_message = create_test_message()
    message_content = json.dumps(test_message)
    
    print(f"📤 Sending test message to queue: {queue_name}")
    print(f"Task ID: {test_message['task_id']}")
    print(f"Message content: {message_content[:100]}...")
    
    try:
        # Send message using text encoding (no special encoding policy)
        queue_client.send_message(message_content)
        print("✅ Message sent successfully!")
        return test_message['task_id']
    except Exception as e:
        print(f"❌ Error sending message: {e}")
        return None

def monitor_queue_processing(task_id, timeout_seconds=60):
    """Monitor queue processing to see if the message gets processed or goes to poison queue"""
    connection_string = get_connection_string()
    queue_service = QueueServiceClient.from_connection_string(connection_string)
    
    # Queue clients to monitor
    main_queue = queue_service.get_queue_client("task-queue-high")
    poison_queue = queue_service.get_queue_client("task-queue-high-poison")
    
    print(f"\n🔍 Monitoring queue processing for task: {task_id}")
    print(f"Timeout: {timeout_seconds} seconds")
    
    start_time = time.time()
    
    while time.time() - start_time < timeout_seconds:
        try:
            # Check main queue
            main_props = main_queue.get_queue_properties()
            main_count = main_props.approximate_message_count
            
            # Check poison queue
            poison_props = poison_queue.get_queue_properties()
            poison_count = poison_props.approximate_message_count
            
            print(f"⏱️  {int(time.time() - start_time)}s - Main queue: {main_count} messages, Poison queue: {poison_count} messages")
            
            # Check if message appeared in poison queue
            if poison_count > 0:
                # Peek at poison messages to see if our test message is there
                poison_messages = poison_queue.peek_messages(max_messages=32)
                for msg in poison_messages:
                    try:
                        msg_data = json.loads(msg.content)
                        if msg_data.get('task_id') == task_id:
                            print(f"❌ Test message found in POISON queue!")
                            print(f"   This indicates the encoding fix did NOT work.")
                            return False
                    except:
                        pass
            
            # If main queue is empty and no poison messages, the message was likely processed
            if main_count == 0:
                print(f"✅ Main queue is empty - message likely processed successfully!")
                print(f"   This indicates the encoding fix WORKED.")
                return True
                
            time.sleep(2)
            
        except Exception as e:
            print(f"Error monitoring queues: {e}")
            time.sleep(2)
    
    print(f"⏰ Timeout reached. Final status:")
    try:
        main_props = main_queue.get_queue_properties()
        poison_props = poison_queue.get_queue_properties()
        print(f"   Main queue: {main_props.approximate_message_count} messages")
        print(f"   Poison queue: {poison_props.approximate_message_count} messages")
    except Exception as e:
        print(f"   Error getting final status: {e}")
    
    return None

def main():
    """Main function"""
    print("🧪 Queue Processing Test")
    print("=" * 50)
    print("This test will:")
    print("1. Send a test message to the high-priority queue")
    print("2. Monitor if it gets processed or goes to poison queue")
    print("3. Determine if the encoding fix worked")
    print()
    
    # Check if SFDC service is running
    print("⚠️  Make sure the SFDC service is running before proceeding!")
    proceed = input("Is the SFDC service running? (y/N): ").strip().lower()
    if proceed != 'y':
        print("Please start the SFDC service first.")
        return
    
    # Send test message
    task_id = send_test_message()
    if not task_id:
        print("Failed to send test message. Exiting.")
        return
    
    # Monitor processing
    result = monitor_queue_processing(task_id, timeout_seconds=30)
    
    print("\n" + "=" * 50)
    if result is True:
        print("🎉 SUCCESS: Message encoding fix appears to be working!")
        print("   The test message was processed without going to poison queue.")
    elif result is False:
        print("❌ FAILURE: Message encoding fix did not work.")
        print("   The test message ended up in the poison queue.")
        print("   Check the SFDC service logs for more details.")
    else:
        print("❓ UNCLEAR: Test timed out or inconclusive.")
        print("   Check the SFDC service logs and queue status manually.")
    
    print("\n📋 Next steps:")
    print("1. Check SFDC service logs for any error messages")
    print("2. Verify the host.json messageEncoding setting is 'none'")
    print("3. Test with a real integration scan if this test passed")

if __name__ == "__main__":
    main()
