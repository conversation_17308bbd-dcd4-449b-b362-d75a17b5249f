import os
import subprocess
import sys
import time
import json
import platform
import shutil

def check_npm_installed():
    """Check if npm is installed"""
    npm_cmd = 'npm.cmd' if platform.system() == 'Windows' else 'npm'

    try:
        # Check if npm is in PATH
        npm_path = shutil.which(npm_cmd)
        if npm_path:
            result = subprocess.run([npm_cmd, '--version'],
                                  stdout=subprocess.PIPE,
                                  stderr=subprocess.PIPE,
                                  text=True)
            if result.returncode == 0:
                print(f"✅ npm is installed: {result.stdout.strip()}")
                return True

        print("❌ npm is not installed or not in PATH")
        print("Please install Node.js from https://nodejs.org/")
        return False
    except Exception as e:
        print(f"❌ Error checking npm: {str(e)}")
        return False

def check_azurite_installed():
    """Check if Azurite is installed globally"""
    try:
        # First check if npm is installed
        if not check_npm_installed():
            return False

        # Then check for azurite
        azurite_cmd = 'azurite.cmd' if platform.system() == 'Windows' else 'azurite'
        azurite_path = shutil.which(azurite_cmd)

        if azurite_path:
            result = subprocess.run([azurite_cmd, '--version'],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                text=True)
            if result.returncode == 0:
                print(f"✅ Azurite is installed: {result.stdout.strip()}")
                return True

        print("❌ Azurite is not installed or not in PATH")
        return False
    except Exception as e:
        print(f"❌ Error checking Azurite: {str(e)}")
        return False

def install_azurite():
    """Install Azurite globally"""
    print("Installing Azurite globally...")
    try:
        # Use the correct npm command based on the OS
        npm_cmd = 'npm.cmd' if platform.system() == 'Windows' else 'npm'

        # Run the install command
        process = subprocess.run(
            [npm_cmd, 'install', '-g', 'azurite'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False  # Don't raise exception, we'll handle errors
        )

        if process.returncode == 0:
            print("✅ Azurite installed successfully")
            return True
        else:
            print(f"❌ Failed to install Azurite: {process.stderr}")

            # Check for common permission errors
            if "EACCES" in process.stderr or "permission denied" in process.stderr.lower():
                print("\nPermission error detected. Try running with sudo or as administrator:")
                print("  Windows: Run PowerShell as Administrator")
                print("  Linux/macOS: sudo npm install -g azurite")

            return False
    except Exception as e:
        print(f"❌ Failed to install Azurite: {str(e)}")
        return False

def check_ports_available():
    """Check if the required ports are available"""
    ports = [10100, 10101, 10102]
    unavailable_ports = []

    for port in ports:
        try:
            # Try to bind to the port to see if it's available
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()

            # If result is 0, the port is in use
            if result == 0:
                unavailable_ports.append(port)
        except Exception:
            # If we can't check, assume it might be in use
            unavailable_ports.append(port)

    if unavailable_ports:
        print(f"❌ The following ports are already in use: {unavailable_ports}")
        print("Azurite requires these ports to be available.")
        print("Please stop any services using these ports and try again.")
        return False

    return True

def start_azurite():
    """Start Azurite in the background"""
    # First check if ports are available
    if not check_ports_available():
        return False

    # Create the azurite directory
    azurite_dir = os.path.join(os.getcwd(), '.azurite')
    os.makedirs(azurite_dir, exist_ok=True)

    print(f"Starting Azurite with data directory: {azurite_dir}")
    try:
        # Use the correct azurite command based on the OS
        azurite_cmd = 'azurite.cmd' if platform.system() == 'Windows' else 'azurite'

        # Start Azurite in the background
        process = subprocess.Popen(
            [azurite_cmd, '--silent', '--location', azurite_dir,
             '--blobPort', '10100', '--queuePort', '10101', '--tablePort', '10102'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        # Give it a moment to start
        time.sleep(2)

        # Check if it's running
        if process.poll() is None:
            print("✅ Azurite started successfully")
            return True
        else:
            _, stderr = process.communicate()
            error_msg = stderr.decode() if stderr else "Unknown error"
            print(f"❌ Azurite failed to start: {error_msg}")
            return False
    except Exception as e:
        print(f"❌ Failed to start Azurite: {str(e)}")
        return False

def setup_local_settings():
    """Set up local.settings.json for Azure Functions"""
    settings_path = os.path.join(os.getcwd(), 'local.settings.json')

    # Default settings
    settings = {
        "IsEncrypted": False,
        "Values": {
            "AzureWebJobsStorage": "UseDevelopmentStorage=true",
            "FUNCTIONS_WORKER_RUNTIME": "python",
            "AzureWebJobsFeatureFlags": "EnableWorkerIndexing",
            # Note: AZURE_FUNCTIONS_ENVIRONMENT is intentionally omitted to avoid conflicts
            # with the Azure Functions Core Tools
            "KEY_VAULT_URL": "https://akv-atomsec-dev.vault.azure.net/",
            "USE_LOCAL_STORAGE": "true"
        }
    }

    # Check if file exists and update it
    if os.path.exists(settings_path):
        try:
            with open(settings_path, 'r') as f:
                existing_settings = json.load(f)

            # Update existing settings
            for key, value in settings["Values"].items():
                existing_settings["Values"][key] = value

            # Remove problematic environment variable if it exists
            if "AZURE_FUNCTIONS_ENVIRONMENT" in existing_settings["Values"]:
                del existing_settings["Values"]["AZURE_FUNCTIONS_ENVIRONMENT"]
                print("⚠️ Removed AZURE_FUNCTIONS_ENVIRONMENT to avoid conflicts")

            settings = existing_settings
        except Exception as e:
            print(f"Warning: Could not read existing settings: {str(e)}")

    # Write settings
    try:
        with open(settings_path, 'w') as f:
            json.dump(settings, f, indent=2)
        print(f"✅ Updated {settings_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to write settings: {str(e)}")
        return False

def setup_vscode_tasks():
    """Set up VS Code tasks for Azurite"""
    vscode_dir = os.path.join(os.getcwd(), '.vscode')
    os.makedirs(vscode_dir, exist_ok=True)

    tasks_path = os.path.join(vscode_dir, 'tasks.json')

    # Default tasks
    tasks = {
        "version": "2.0.0",
        "tasks": [
            {
                "type": "func",
                "label": "func: host start",
                "command": "host start",
                "problemMatcher": "$func-python-watch",
                "isBackground": True,
                "dependsOn": "pip install (functions)"
            },
            {
                "label": "pip install (functions)",
                "type": "shell",
                "osx": {
                    "command": "${config:azureFunctions.pythonVenv}/bin/python -m pip install -r requirements.txt"
                },
                "windows": {
                    "command": "${config:azureFunctions.pythonVenv}\\Scripts\\python -m pip install -r requirements.txt"
                },
                "linux": {
                    "command": "${config:azureFunctions.pythonVenv}/bin/python -m pip install -r requirements.txt"
                },
                "problemMatcher": []
            },
            {
                "label": "Start Azurite",
                "type": "shell",
                "command": "azurite --location .azurite --silent --blobPort 10100 --queuePort 10101 --tablePort 10102",
                "isBackground": True,
                "problemMatcher": []
            },
            {
                "label": "Start Function App with Azurite",
                "dependsOn": ["Start Azurite", "func: host start"],
                "dependsOrder": "sequence",
                "problemMatcher": []
            }
        ]
    }

    # Check if file exists and update it
    if os.path.exists(tasks_path):
        try:
            with open(tasks_path, 'r') as f:
                existing_tasks = json.load(f)

            # Check if Azurite tasks already exist
            azurite_task_exists = any(task.get("label") == "Start Azurite" for task in existing_tasks.get("tasks", []))

            if not azurite_task_exists:
                # Add Azurite tasks
                azurite_tasks = [task for task in tasks["tasks"] if task["label"] in ["Start Azurite", "Start Function App with Azurite"]]
                existing_tasks["tasks"].extend(azurite_tasks)
                tasks = existing_tasks
        except Exception as e:
            print(f"Warning: Could not read existing tasks: {str(e)}")

    # Write tasks
    try:
        with open(tasks_path, 'w') as f:
            json.dump(tasks, f, indent=2)
        print(f"✅ Updated {tasks_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to write tasks: {str(e)}")
        return False

def check_func_core_tools():
    """Check if Azure Functions Core Tools are installed"""
    try:
        # Check for func command
        func_cmd = 'func.cmd' if platform.system() == 'Windows' else 'func'
        func_path = shutil.which(func_cmd)

        if func_path:
            result = subprocess.run(
                [func_cmd, '--version'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            if result.returncode == 0:
                print(f"✅ Azure Functions Core Tools installed: {result.stdout.strip()}")
                return True

        print("❌ Azure Functions Core Tools not found")
        print("Please install Azure Functions Core Tools:")
        print("  https://learn.microsoft.com/en-us/azure/azure-functions/functions-run-local")
        return False
    except Exception as e:
        print(f"❌ Error checking Azure Functions Core Tools: {str(e)}")
        return False

def create_config_template():
    """Create config.py.template if it doesn't exist"""
    template_path = os.path.join(os.getcwd(), 'config.py.template')

    # Skip if the file already exists
    if os.path.exists(template_path):
        return True

    template_content = """# config.py - Template for Salesforce credentials
# Copy this file to config.py and fill in your credentials
# DO NOT commit config.py to source control

# Salesforce OAuth credentials
client_id = "your_salesforce_client_id"
client_secret = "your_salesforce_client_secret"
username = "your_salesforce_username"
password = "your_salesforce_password"
auth_url = "https://login.salesforce.com/services/oauth2/token"

# Additional configuration settings can be added here
"""

    try:
        with open(template_path, 'w') as f:
            f.write(template_content)
        print(f"✅ Created {template_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create config template: {str(e)}")
        return False

def main():
    """Main setup function"""
    print("Setting up local development environment for Azure Functions with Azurite...")
    print("\n=== Checking Prerequisites ===\n")

    # Check prerequisites
    prereqs_ok = True

    # Check Azure Functions Core Tools
    if not check_func_core_tools():
        prereqs_ok = False

    # Check and install Azurite if needed
    if not check_azurite_installed():
        if not install_azurite():
            print("\nPlease install Azurite manually: npm install -g azurite")
            prereqs_ok = False

    if not prereqs_ok:
        print("\n❌ Some prerequisites are missing. Please install them and try again.")
        return False

    print("\n=== Setting Up Configuration ===\n")

    # Set up local settings
    setup_local_settings()

    # Set up VS Code tasks
    setup_vscode_tasks()

    # Create config.py template
    create_config_template()

    print("\n=== Starting Services ===\n")

    # Start Azurite
    azurite_started = start_azurite()

    print("\n=== Setup Summary ===\n")

    if azurite_started:
        print("✅ Azurite is running in the background")
    else:
        print("❌ Azurite could not be started automatically")
        print("  You'll need to start it manually before running the Functions host")

    print("\n✅ Setup complete! Next steps:")
    print("\n1. If you need to work with Salesforce:")
    print("   - Copy config.py.template to config.py")
    print("   - Edit config.py with your Salesforce credentials")

    print("\n2. Start the Functions host:")
    print("   - In VS Code: Use the 'Start Function App with Azurite' task")
    print("   - Or run: func start")

    print("\n3. Test the application:")
    print("   - Open http://localhost:7071/api/home in your browser")

    return True

if __name__ == "__main__":
    sys.exit(0 if main() else 1)
