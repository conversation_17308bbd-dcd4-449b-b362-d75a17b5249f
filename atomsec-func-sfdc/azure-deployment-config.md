# Azure Deployment Configuration for AtomSec SFDC Function App

This guide provides step-by-step instructions for deploying and configuring the AtomSec SFDC Function App in Azure.

## Prerequisites

1. Azure subscription with appropriate permissions
2. Azure CLI installed and configured
3. Azure Function App created
4. Azure Key Vault created and configured
5. DB Service (atomsec-func-db-r) deployed and running

## Azure Function App Configuration

### 1. Application Settings

Configure the following application settings in your Azure Function App:

#### Core Configuration
```bash
# Azure AD Configuration
AZURE_AD_CLIENT_ID=<your_azure_ad_client_id>
AZURE_AD_CLIENT_SECRET=<your_azure_ad_client_secret>
AZURE_AD_TENANT_ID=<your_azure_ad_tenant_id>
AZURE_AD_REDIRECT_URI=https://your-frontend-app.azurewebsites.net

# Key Vault Configuration
KEY_VAULT_URL=https://your-key-vault.vault.azure.net/

# Storage Configuration (if not using Key Vault)
AZURE_STORAGE_CONNECTION_STRING=<your_storage_connection_string>

# SQL Database Configuration (if not using Key Vault)
SQL_CONNECTION_STRING=<your_sql_connection_string>

# Frontend URL
FRONTEND_URL=https://your-frontend-app.azurewebsites.net

# API Base URL
base_url=https://your-api-gateway.azure-api.net
```

#### Database Service Configuration
```bash
# DB Service URL (if not using Key Vault)
DB_SERVICE_URL=https://atomsec-func-db-r.azurewebsites.net/api/db

# DB Service Connection Settings
DB_SERVICE_TIMEOUT=30
DB_SERVICE_RETRY_ATTEMPTS=3
DB_SERVICE_RETRY_DELAY=1

# DB Service Authentication (if not using Key Vault)
DB_SERVICE_API_KEY=<your_db_service_api_key>

# DB Service Customization
DB_SERVICE_USER_AGENT=atomsec-func-sfdc/1.0.0-prod
```

#### PMD Configuration
```bash
# PMD General Settings
PMD_ENABLED=true

# PMD Scan Configuration
PMD_DEFAULT_CATEGORIES=security,performance,codestyle,design

# PMD Performance Settings
PMD_MAX_FINDINGS=10000
PMD_SCAN_TIMEOUT=300
PMD_TEMP_DIR=/tmp
```

### 2. Key Vault Integration

For production deployments, it's recommended to store sensitive configuration in Azure Key Vault:

#### Required Key Vault Secrets
```bash
# Core Secrets
jwt-secret=<your_jwt_secret>
storage-connection-string=<your_storage_connection_string>
sql-connection-string=<your_sql_connection_string>
frontend-url=https://your-frontend-app.azurewebsites.net

# DB Service Secrets
db-service-url=https://atomsec-func-db-r.azurewebsites.net/api/db
db-service-api-key=<your_db_service_api_key>
```

#### Key Vault Access Policy
Ensure your Function App has access to Key Vault by:

1. **Using Managed Identity** (Recommended):
   ```bash
   # Enable managed identity
   az functionapp identity assign --name your-function-app-name --resource-group your-resource-group
   
   # Grant Key Vault access
   az keyvault set-policy --name your-key-vault-name --object-id <managed-identity-object-id> --secret-permissions get list
   ```

2. **Using Service Principal**:
   ```bash
   # Create service principal
   az ad sp create-for-rbac --name "atomsec-func-sfdc" --role contributor
   
   # Grant Key Vault access
   az keyvault set-policy --name your-key-vault-name --spn <service-principal-id> --secret-permissions get list
   ```

### 3. Network Configuration

#### Virtual Network Integration (Optional)
If your Function App needs to access resources in a VNet:

```bash
# Integrate with VNet
az functionapp vnet-integration add --name your-function-app-name --resource-group your-resource-group --vnet your-vnet-name --subnet your-subnet-name
```

#### Private Endpoints (Optional)
For enhanced security, use private endpoints for:

- Azure Storage
- SQL Database
- Key Vault

### 4. CORS Configuration

Configure CORS for your Function App:

```bash
# Allow your frontend domain
az functionapp cors add --name your-function-app-name --resource-group your-resource-group --allowed-origins https://your-frontend-app.azurewebsites.net

# Allow local development
az functionapp cors add --name your-function-app-name --resource-group your-resource-group --allowed-origins http://localhost:3000
```

## Deployment Scripts

### 1. Azure CLI Deployment Script

Create a deployment script (`deploy.sh`):

```bash
#!/bin/bash

# Configuration
RESOURCE_GROUP="atomsec-rg"
FUNCTION_APP_NAME="atomsec-func-sfdc"
KEY_VAULT_NAME="akv-atomsec-dev"
LOCATION="East US"

# Deploy Function App
echo "Deploying Function App..."
az functionapp deployment source config-zip \
    --resource-group $RESOURCE_GROUP \
    --name $FUNCTION_APP_NAME \
    --src ./deployment-package.zip

# Configure application settings
echo "Configuring application settings..."
az functionapp config appsettings set \
    --resource-group $RESOURCE_GROUP \
    --name $FUNCTION_APP_NAME \
    --settings \
    KEY_VAULT_URL="https://$KEY_VAULT_NAME.vault.azure.net/" \
    FRONTEND_URL="https://app-atomsec-dev01.azurewebsites.net" \
    base_url="https://apim-atomsec-dev.azure-api.net" \
    DB_SERVICE_TIMEOUT="30" \
    DB_SERVICE_RETRY_ATTEMPTS="3" \
    DB_SERVICE_RETRY_DELAY="1" \
    PMD_ENABLED="true" \
    PMD_DEFAULT_CATEGORIES="security,performance,codestyle" \
    PMD_MAX_FINDINGS="10000" \
    PMD_SCAN_TIMEOUT="300"

# Configure CORS
echo "Configuring CORS..."
az functionapp cors add \
    --resource-group $RESOURCE_GROUP \
    --name $FUNCTION_APP_NAME \
    --allowed-origins "https://app-atomsec-dev01.azurewebsites.net" "http://localhost:3000"

echo "Deployment completed!"
```

### 2. Azure DevOps Pipeline

Create an Azure DevOps pipeline (`azure-pipelines.yml`):

```yaml
trigger:
- main

pool:
  vmImage: 'ubuntu-latest'

variables:
  functionAppName: 'atomsec-func-sfdc'
  resourceGroupName: 'atomsec-rg'
  keyVaultName: 'akv-atomsec-dev'

steps:
- task: UsePythonVersion@0
  inputs:
    versionSpec: '3.9'

- script: |
    python -m pip install --upgrade pip
    pip install -r requirements.txt
  displayName: 'Install dependencies'

- script: |
    python -m pytest tests/ --doctest-modules --junitxml=junit/test-results.xml
  displayName: 'Run tests'

- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
    replaceExistingArchive: true

- task: AzureFunctionApp@1
  inputs:
    azureSubscription: 'Your-Azure-Subscription'
    appName: '$(functionAppName)'
    resourceGroupName: '$(resourceGroupName)'
    package: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'

- task: AzureCLI@2
  inputs:
    azureSubscription: 'Your-Azure-Subscription'
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      # Configure application settings
      az functionapp config appsettings set \
        --resource-group $(resourceGroupName) \
        --name $(functionAppName) \
        --settings \
        KEY_VAULT_URL="https://$(keyVaultName).vault.azure.net/" \
        FRONTEND_URL="https://app-atomsec-dev01.azurewebsites.net" \
        base_url="https://apim-atomsec-dev.azure-api.net" \
        DB_SERVICE_TIMEOUT="30" \
        DB_SERVICE_RETRY_ATTEMPTS="3" \
        DB_SERVICE_RETRY_DELAY="1" \
        PMD_ENABLED="true" \
        PMD_DEFAULT_CATEGORIES="security,performance,codestyle" \
        PMD_MAX_FINDINGS="10000" \
        PMD_SCAN_TIMEOUT="300"
```

## Monitoring and Troubleshooting

### 1. Application Insights

Enable Application Insights for monitoring:

```bash
# Create Application Insights
az monitor app-insights component create \
    --app atomsec-func-sfdc-insights \
    --location "East US" \
    --resource-group atomsec-rg \
    --application-type web

# Enable for Function App
az monitor app-insights component connect-function-app \
    --app atomsec-func-sfdc-insights \
    --function-app atomsec-func-sfdc \
    --resource-group atomsec-rg
```

### 2. Log Analytics

Configure Log Analytics workspace for centralized logging:

```bash
# Create Log Analytics workspace
az monitor log-analytics workspace create \
    --resource-group atomsec-rg \
    --workspace-name atomsec-logs

# Enable diagnostic settings
az monitor diagnostic-settings create \
    --resource /subscriptions/<subscription-id>/resourceGroups/atomsec-rg/providers/Microsoft.Web/sites/atomsec-func-sfdc \
    --workspace atomsec-logs \
    --name atomsec-func-sfdc-diagnostics \
    --logs '[{"category": "FunctionAppLogs", "enabled": true}]'
```

### 3. Health Checks

Implement health checks to monitor service dependencies:

```bash
# Test DB Service connectivity
curl -X GET "https://atomsec-func-sfdc.azurewebsites.net/api/health"

# Test PMD configuration
curl -X GET "https://atomsec-func-sfdc.azurewebsites.net/api/info"
```

### 4. Common Issues and Solutions

#### Issue: DB Service Connection Failures
**Symptoms**: 500 errors when making database operations
**Solutions**:
- Verify `DB_SERVICE_URL` is correct
- Check network connectivity between services
- Verify API key authentication if configured

#### Issue: PMD Scan Failures
**Symptoms**: PMD tasks failing with timeout or permission errors
**Solutions**:
- Increase `PMD_SCAN_TIMEOUT` for large codebases
- Verify `PMD_TEMP_DIR` is writable
- Check if `PMD_ENABLED` is set to "true"

#### Issue: Key Vault Access Denied
**Symptoms**: Configuration retrieval failures
**Solutions**:
- Verify managed identity is enabled
- Check Key Vault access policies
- Ensure secrets exist in Key Vault

## Security Best Practices

1. **Use Managed Identity**: Prefer managed identity over service principals
2. **Key Vault Integration**: Store all secrets in Key Vault
3. **Network Security**: Use VNet integration and private endpoints
4. **CORS Configuration**: Restrict CORS to specific domains
5. **Monitoring**: Enable Application Insights and Log Analytics
6. **Regular Updates**: Keep dependencies and runtime updated

## Cost Optimization

1. **Consumption Plan**: Use consumption plan for development/testing
2. **Premium Plan**: Use premium plan for production with VNet integration
3. **Auto-scaling**: Configure auto-scaling rules appropriately
4. **Monitoring**: Monitor usage and optimize resource allocation 