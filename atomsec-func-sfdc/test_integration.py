#!/usr/bin/env python3
"""
Integration Test for Dev Branch Changes

This script tests the key functionality that was integrated from the dev branch:
1. Best practices XML loading
2. Policies results processing
3. ExecutionLogId generation
4. Health check risk comparison
"""

import sys
import uuid
import json
from datetime import datetime
from typing import Dict, List, Any

# Add current directory to path
sys.path.append('.')

def test_best_practices_loading():
    """Test loading best practices from XML files"""
    print("=== Testing Best Practices Loading ===")
    
    try:
        from blueprints.security_analysis import load_best_practices
        
        # Test SecurityHealthCheck best practices
        security_practices = load_best_practices('best_practices/SecurityHealthCheck-BestPractice.xml')
        print(f"✓ Loaded {len(security_practices)} security health check practices")
        
        # Test Profiles best practices
        profile_practices = load_best_practices('best_practices/Profiles_PermissionSetRisks-BestPractice.xml')
        print(f"✓ Loaded {len(profile_practices)} profile/permission set practices")
        
        # Verify some key practices exist
        assert 'Expired Certificate' in security_practices
        assert 'Modify All Data' in profile_practices
        print("✓ Key practices found in XML files")
        
        return True
    except Exception as e:
        print(f"✗ Error loading best practices: {str(e)}")
        return False

def test_policies_results_processing():
    """Test policies results processing logic"""
    print("\n=== Testing Policies Results Processing ===")
    
    try:
        from blueprints.security_analysis import load_best_practices
        
        # Load best practices
        best_practices = load_best_practices()
        
        # Sample health check risks (simulating Salesforce data)
        sample_risks = [
            {
                'Setting': 'Expired Certificate',
                'OrgValue': '1',  # Organization has 1 expired certificate
                'StandardValue': '0'  # Should be 0
            },
            {
                'Setting': 'Maximum invalid login attempts',
                'OrgValue': '10',  # Organization allows 10 attempts
                'StandardValue': '3'  # Should be 3
            },
            {
                'Setting': 'Session Timeout',
                'OrgValue': '30 minutes',  # Organization has 30 min timeout
                'StandardValue': '15 minutes'  # Should be 15 minutes
            }
        ]
        
        # Process each risk
        processed_results = []
        for risk in sample_risks:
            setting = risk.get('Setting')
            org_value = risk.get('OrgValue')
            
            best_practice = best_practices.get(setting)
            if best_practice:
                standard_value = best_practice.get('StandardValue', '')
                org_value_str = str(org_value).lower()
                standard_value_str = str(standard_value).lower()
                
                has_weakness = org_value_str != standard_value_str
                
                result = {
                    'Setting': setting,
                    'OrgValue': org_value,
                    'StandardValue': standard_value,
                    'Weakness': 'Yes' if has_weakness else 'No',
                    'OWASPCategory': best_practice.get('OWASPCategory', ''),
                    'Severity': best_practice.get('Severity', ''),
                    'IssueDescription': best_practice.get('Description', ''),
                    'Recommendations': f"Set {setting} to {standard_value} to comply with best practices."
                }
                processed_results.append(result)
                
                print(f"✓ Processed {setting}: Weakness={result['Weakness']}, Severity={result['Severity']}")
        
        print(f"✓ Successfully processed {len(processed_results)} policies results")
        return True
        
    except Exception as e:
        print(f"✗ Error processing policies results: {str(e)}")
        return False

def test_execution_log_id_generation():
    """Test ExecutionLogId generation"""
    print("\n=== Testing ExecutionLogId Generation ===")
    
    try:
        # Simulate ExecutionLogId generation (as done in background_processor.py)
        execution_log_id = str(uuid.uuid4())
        print(f"✓ Generated ExecutionLogId: {execution_log_id}")
        
        # Verify it's a valid UUID
        uuid.UUID(execution_log_id)
        print("✓ ExecutionLogId is a valid UUID")
        
        # Test task data structure
        task_data = {
            "task_id": f"health_check-test-{uuid.uuid4().hex[:8]}",
            "task_type": "health_check",
            "org_id": "test-integration-1",
            "user_id": "test-user",
            "params": {},
            "created_at": datetime.now().isoformat(),
            "status": "pending",
            "priority": "medium",
            "retry_count": 0,
            "execution_log_id": execution_log_id
        }
        
        print("✓ Task data structure includes ExecutionLogId")
        return True
        
    except Exception as e:
        print(f"✗ Error with ExecutionLogId generation: {str(e)}")
        return False

def test_api_endpoint_structure():
    """Test API endpoint structure"""
    print("\n=== Testing API Endpoint Structure ===")
    
    try:
        # Test policies result data structure
        sample_policies_result = {
            "success": True,
            "data": [
                {
                    "setting": "Expired Certificate",
                    "org_value": "1",
                    "standard_value": "0",
                    "weakness": "Yes",
                    "owasp_category": "A02:2021-Cryptographic Failures",
                    "severity": "High",
                    "recommendations": "Set Expired Certificate to 0 to comply with best practices.",
                    "issue_description": "Expired certificates undermine HTTPS and TLS integrity.",
                    "created_at": datetime.now().isoformat(),
                    "task_status_id": str(uuid.uuid4())
                }
            ]
        }
        
        # Verify JSON serialization works
        json_str = json.dumps(sample_policies_result)
        parsed_back = json.loads(json_str)
        
        print("✓ API response structure is valid JSON")
        print(f"✓ Sample response contains {len(parsed_back['data'])} policies results")
        
        return True
        
    except Exception as e:
        print(f"✗ Error with API endpoint structure: {str(e)}")
        return False

def main():
    """Run all integration tests"""
    print("🚀 Starting Dev Branch Integration Tests\n")
    
    tests = [
        test_best_practices_loading,
        test_policies_results_processing,
        test_execution_log_id_generation,
        test_api_endpoint_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print("❌ Test failed!")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed! Dev branch changes successfully integrated.")
        return 0
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
