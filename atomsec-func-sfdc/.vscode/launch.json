{"version": "0.2.0", "configurations": [{"name": "Attach to Python Functions", "type": "debugpy", "request": "attach", "connect": {"host": "localhost", "port": 9091}, "preLaunchTask": "Start Function App (Alternative)"}, {"name": "FastAPI", "type": "debugpy", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["app:app", "--reload", "--port", "7071"], "jinja": true, "justMyCode": true, "env": {"IS_LOCAL_DEV": "true", "USE_LOCAL_STORAGE": "true"}}, {"name": "FastAPI with run_fastapi.py", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/run_fastapi.py", "console": "integratedTerminal", "justMyCode": true, "env": {"IS_LOCAL_DEV": "true", "USE_LOCAL_STORAGE": "true"}}]}