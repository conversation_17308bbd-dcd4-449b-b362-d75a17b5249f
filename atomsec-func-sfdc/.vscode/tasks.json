{"version": "2.0.0", "tasks": [{"type": "func", "label": "func: host start", "command": "host start", "problemMatcher": "$func-python-watch", "isBackground": true, "dependsOn": "pip install (functions)"}, {"label": "pip install (functions)", "type": "shell", "osx": {"command": "${config:azureFunctions.pythonVenv}/bin/python -m pip install -r requirements.txt"}, "windows": {"command": "${config:azureFunctions.pythonVenv}\\Scripts\\python -m pip install -r requirements.txt"}, "linux": {"command": "${config:azureFunctions.pythonVenv}/bin/python -m pip install -r requirements.txt"}, "problemMatcher": []}, {"label": "Start Azurite", "type": "shell", "command": "azurite --location .azurite --silent --blobPort 10000 --queuePort 10001 --tablePort 10002", "isBackground": true, "problemMatcher": []}, {"label": "Start Function App with Azurite", "dependsOn": ["Start Azurite", "func: host start"], "dependsOrder": "sequence", "problemMatcher": []}, {"label": "Start Function App (Alternative)", "type": "shell", "command": "${config:azureFunctions.pythonVenv}\\Scripts\\activate && .\\start-func.ps1", "dependsOn": ["Start Azurite", "pip install (functions)"], "dependsOrder": "sequence", "isBackground": true, "problemMatcher": "$func-python-watch"}, {"label": "Start FastAPI", "type": "shell", "command": "python run_fastapi.py", "isBackground": true, "problemMatcher": []}, {"label": "Start FastAPI with Azurite", "dependsOn": ["Start Azurite", "Start FastAPI"], "dependsOrder": "sequence", "problemMatcher": []}]}