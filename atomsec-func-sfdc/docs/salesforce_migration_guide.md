# Salesforce Integration Migration Guide

This guide explains how to migrate from the custom Salesforce API implementation to the new simple-salesforce implementation.

## Overview

The new implementation uses the [simple-salesforce](https://github.com/simple-salesforce/simple-salesforce) library, which provides a more robust and maintained way to interact with Salesforce APIs. The migration involves:

1. Installing the simple-salesforce package
2. Using the new SalesforceClient class for direct interactions
3. Using the utility functions as drop-in replacements for existing functions

## Benefits of Using simple-salesforce

- **Maintained Library**: simple-salesforce is actively maintained and updated for new Salesforce API versions
- **Robust Error Handling**: Better error handling and retry mechanisms
- **Simplified Authentication**: Supports multiple authentication methods
- **Type Hints**: Better IDE support and code completion
- **Comprehensive API Coverage**: Supports all Salesforce APIs (REST, SOAP, Bulk, Metadata, etc.)
- **Performance Optimizations**: Connection pooling and other optimizations

## Installation

The simple-salesforce package has been added to the requirements.txt file:

```
simple-salesforce>=1.12.6  # Salesforce API client
```

To install it in your development environment:

```bash
pip install simple-salesforce
```

## Migration Steps

### 1. Direct Replacement of Utility Functions

The simplest way to migrate is to use the new utility functions as direct replacements for the existing ones:

| Old Function | New Function | Notes |
|--------------|--------------|-------|
| `execute_salesforce_query` | `shared.salesforce_utils.execute_salesforce_query` | Drop-in replacement |
| `execute_salesforce_tooling_query` | `shared.salesforce_utils.execute_salesforce_tooling_query` | Drop-in replacement |
| `get_salesforce_access_token` | `shared.salesforce_utils.get_salesforce_access_token` | Drop-in replacement |
| `test_salesforce_connection_jwt` | `shared.salesforce_utils.test_salesforce_connection` | Drop-in replacement |

Example of replacing an existing function call:

```python
# Old code
from shared.utils import execute_salesforce_query

result = execute_salesforce_query(query, access_token, instance_url)

# New code
from shared.salesforce_utils import execute_salesforce_query

result = execute_salesforce_query(query, access_token, instance_url)
```

### 2. Using the SalesforceClient Directly

For more advanced use cases, you can use the SalesforceClient class directly:

```python
from shared.salesforce_client import SalesforceClient

# Initialize with access token
client = SalesforceClient(
    instance_url="https://your-instance.salesforce.com",
    access_token="your-access-token"
)

# Or initialize with client credentials
client = SalesforceClient(
    instance_url="https://your-instance.salesforce.com",
    client_id="your-client-id",
    client_secret="your-client-secret",
    is_sandbox=False
)

# Execute a query
result = client.query("SELECT Id, Name FROM User LIMIT 5")

# Execute a tooling query
result = client.tooling_query("SELECT Id, Name FROM Profile LIMIT 5")
```

### 3. Advanced Features

The SalesforceClient provides access to all simple-salesforce features:

```python
# Access SObject API
accounts = client.sf.Account.list()

# Create a record
new_account = client.sf.Account.create({
    'Name': 'New Account'
})

# Update a record
client.sf.Account.update('001xx000003DGT2AAO', {
    'Name': 'Updated Account Name'
})

# Delete a record
client.sf.Account.delete('001xx000003DGT2AAO')

# Execute a SOSL search
search_results = client.sf.search('FIND {Smith}')

# Execute Apex REST
apex_result = client.sf.apexecute('CustomApexEndpoint', method='GET')
```

## Migration Examples

### Example 1: Fetching Profiles

```python
# Old code
async def fetch_profiles(access_token: str, instance_url: str) -> List[Dict[str, Any]]:
    query = "SELECT Id, Name, Description, UserType, CreatedDate, LastModifiedDate FROM Profile"
    result = await execute_salesforce_query(query, access_token, instance_url)
    
    if result and "records" in result:
        return result["records"]
    return []

# New code
async def fetch_profiles(access_token: str, instance_url: str) -> List[Dict[str, Any]]:
    from shared.salesforce_utils import execute_salesforce_query
    
    query = "SELECT Id, Name, Description, UserType, CreatedDate, LastModifiedDate FROM Profile"
    result = await execute_salesforce_query(query, access_token, instance_url)
    
    if result and "records" in result:
        return result["records"]
    return []
```

### Example 2: Security Health Check

```python
# Old code
def get_security_health_check_score(access_token: str, instance_url: str) -> Optional[int]:
    query = "SELECT Score FROM SecurityHealthCheck"
    result = execute_salesforce_tooling_query(query, access_token, instance_url)
    
    if result and "records" in result and len(result["records"]) > 0:
        return result["records"][0].get("Score")
    return None

# New code
def get_security_health_check_score(access_token: str, instance_url: str) -> Optional[int]:
    from shared.salesforce_utils import execute_salesforce_tooling_query
    
    query = "SELECT Score FROM SecurityHealthCheck"
    result = execute_salesforce_tooling_query(query, access_token, instance_url)
    
    if result and "records" in result and len(result["records"]) > 0:
        return result["records"][0].get("Score")
    return None
```

## Testing

A comprehensive test suite is provided in `tests/test_salesforce_client.py`. Run the tests to verify that the new implementation works correctly:

```bash
python -m pytest tests/test_salesforce_client.py -v
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Ensure that your Connected App in Salesforce has the correct OAuth scopes and settings.

2. **API Version Compatibility**: The simple-salesforce library uses the latest API version by default. If you need to use a specific version, you can specify it when creating the client:

   ```python
   client = SalesforceClient(
       instance_url="https://your-instance.salesforce.com",
       access_token="your-access-token",
       version="58.0"  # Specify API version
   )
   ```

3. **Rate Limiting**: Salesforce has API rate limits. The simple-salesforce library handles rate limiting better than the custom implementation, but you should still be aware of these limits.

### Getting Help

If you encounter any issues during migration, refer to:

- [simple-salesforce documentation](https://simple-salesforce.readthedocs.io/)
- [Salesforce API documentation](https://developer.salesforce.com/docs/atlas.en-us.api_rest.meta/api_rest/intro_what_is_rest_api.htm)
- The examples in `examples/salesforce_client_example.py`
