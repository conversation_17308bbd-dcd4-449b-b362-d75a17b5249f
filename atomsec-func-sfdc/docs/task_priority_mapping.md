# Task Priority Mapping

This document explains the task priority mapping system implemented in the AtomSec Salesforce Security application.

## Priority Levels

The system uses three priority levels for tasks:

1. **High Priority** - Critical security tasks that should be processed first
2. **Medium Priority** - Important tasks that are not as time-critical
3. **Low Priority** - Useful tasks that can be processed when system resources are available

## Task Type to Priority Mapping

Tasks are automatically assigned priorities based on their type:

### High Priority Tasks

These tasks are critical for security monitoring and should be processed first:

| Task Type | Description | Rationale |
|-----------|-------------|-----------|
| `health_check` | Security Health Check | Provides critical security health score and immediate security posture information |
| `guest_user_risks` | Guest User Risks | External users represent a significant security risk that should be monitored closely |
| `notification` | Notifications | Time-sensitive alerts that users need to see immediately |

### Medium Priority Tasks

These tasks are important but not as time-critical:

| Task Type | Description | Rationale |
|-----------|-------------|-----------|
| `profiles` | Profiles & Permissions | Profile and permission analysis is important for security but not as time-sensitive as health checks |
| `pmd_issues` | PMD Issues | Code quality and security issues that should be addressed but aren't typically urgent |
| `overview` | Overview | General system overview that provides useful but not time-critical information |
| `scheduled_scan` | Scheduled Scan | Regular scheduled scans that trigger other tasks |

### Low Priority Tasks

These tasks are useful but can be processed when system resources are available:

| Task Type | Description | Rationale |
|-----------|-------------|-----------|
| `data_export` | Data Export | Exporting data for analysis is useful but not time-critical |
| `report_generation` | Report Generation | Creating reports can be resource-intensive but isn't time-sensitive |

## Implementation Details

The priority mapping is implemented in the following components:

1. **Backend**: The `BackgroundProcessor` class in `shared/background_processor.py` contains a `TASK_TYPE_PRIORITY_MAP` dictionary that maps task types to priorities. The `enqueue_task` method uses this mapping to automatically assign priorities based on task type if a priority is not explicitly provided.

2. **Frontend**: The `TaskManagement.jsx` component contains a similar mapping and automatically updates the priority dropdown when a user selects a task type.

## Queue Processing

Tasks are processed from three separate queues based on their priority:

- `task-queue-high` - High priority tasks
- `task-queue-medium` - Medium priority tasks
- `task-queue-low` - Low priority tasks

The Azure Functions runtime processes messages from these queues in parallel, but the system is designed to ensure that high-priority tasks are processed first when resources are limited.

## Overriding Priorities

Users can manually override the recommended priority when scheduling tasks through the UI. This allows for flexibility in special cases where a task might need a different priority than the default.
