{"swagger": "2.0", "info": {"title": "func-atomsec-sfdc-dev2", "version": "1.0", "description": "Import from \"func-atomsec-sfdc-dev\" Function App"}, "host": "apim-atomsec-dev.azure-api.net", "basePath": "/api", "schemes": ["https"], "securityDefinitions": {"apiKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ocp-Apim-Subscription-Key", "in": "header"}, "apiKeyQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "subscription-key", "in": "query"}}, "security": [{"apiKeyHeader": []}, {"apiKeyQuery": []}], "paths": {"/disconnect-org": {"post": {"operationId": "post-disconnect-org", "summary": "disconnect_org", "responses": {"200": {"description": ""}}}}, "/auth/signup": {"post": {"operationId": "post-signup", "summary": "signup", "responses": {"200": {"description": ""}}}}, "/rescan-org": {"post": {"operationId": "post-rescan-org", "summary": "rescan_org", "responses": {"200": {"description": ""}}}}, "/integration/test-connection": {"post": {"operationId": "post-test-connection", "summary": "test_connection", "responses": {"200": {"description": ""}}}}, "/integration/scan/{id}": {"post": {"operationId": "post-scan-integration", "summary": "scan_integration", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}}, "/user/password": {"post": {"operationId": "post-change-password", "summary": "change_password", "responses": {"200": {"description": ""}}}}, "/tasks/{action}": {"post": {"operationId": "post-task-management-api", "summary": "task_management_api", "parameters": [{"name": "action", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}, "get": {"operationId": "get-task-management-api", "summary": "task_management_api", "parameters": [{"name": "action", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}}, "/info": {"post": {"operationId": "system-info", "summary": "system_info", "responses": {"200": {"description": ""}}}}, "/user/profile": {"put": {"operationId": "put-update-user-profile", "summary": "update_user_profile", "responses": {"200": {"description": ""}}}, "get": {"operationId": "get-get-user-profile", "summary": "get_user_profile", "responses": {"200": {"description": ""}}}}, "/api/integration/{integration_id}/guest-user-risks": {"get": {"operationId": "get-get-integration-guest-user-risks", "summary": "get_integration_guest_user_risks", "parameters": [{"name": "integration_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}}, "/api/integration/{integration_id}/health-check": {"get": {"operationId": "get-get-integration-health-check", "summary": "get_integration_health_check", "parameters": [{"name": "integration_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}}, "/integration/connect": {"post": {"operationId": "post-connect-integration", "summary": "connect_integration", "responses": {"200": {"description": ""}}}}, "/auth/azure/login": {"get": {"operationId": "get-azure-login", "summary": "azure_login", "responses": {"200": {"description": ""}}}}, "/auth/token/refresh": {"post": {"operationId": "post-refresh-token", "summary": "refresh_token", "responses": {"200": {"description": ""}}}}, "/connect-org": {"post": {"operationId": "post-connect-org", "summary": "connect_org", "responses": {"200": {"description": ""}}}}, "/integrations": {"get": {"operationId": "get-get-integrations", "summary": "get_integrations", "responses": {"200": {"description": ""}}}}, "/integration/{id}": {"delete": {"operationId": "delete-delete-integration", "summary": "delete_integration", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}}, "/auth/azure/me": {"get": {"operationId": "get-azure-me", "summary": "azure_me", "responses": {"200": {"description": ""}}}}, "/{path}": {"get": {"operationId": "get-api-router", "summary": "api_router", "parameters": [{"name": "path", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}, "post": {"operationId": "post-api-router", "summary": "api_router", "parameters": [{"name": "path", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}, "delete": {"operationId": "delete-api-router", "summary": "api_router", "parameters": [{"name": "path", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}, "put": {"operationId": "put-api-router", "summary": "api_router", "parameters": [{"name": "path", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}, "options": {"operationId": "options-api-router", "summary": "api_router", "parameters": [{"name": "path", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}}, "/auth/login": {"post": {"operationId": "post-login", "summary": "login", "responses": {"200": {"description": ""}}}}, "/org-details": {"get": {"operationId": "get-get-org-details", "summary": "get_org_details", "responses": {"200": {"description": ""}}}, "options": {"operationId": "options-get-org-details", "summary": "get_org_details", "responses": {"200": {"description": ""}}}}, "/api/integration/{integration_id}/pmd-issues": {"get": {"operationId": "get-get-integration-pmd-issues", "summary": "get_integration_pmd_issues", "parameters": [{"name": "integration_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}}, "/api/integration/{integration_id}/overview": {"get": {"operationId": "get-get-integration-overview", "summary": "get_integration_overview", "parameters": [{"name": "integration_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}}, "/api/integration/{integration_id}/profiles": {"get": {"operationId": "get-get-integration-profiles", "summary": "get_integration_profiles", "parameters": [{"name": "integration_id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": ""}}}}, "/home": {"post": {"operationId": "home", "summary": "home", "responses": {"200": {"description": ""}}}}, "/orgs": {"get": {"operationId": "get-get-orgs", "summary": "get_orgs", "responses": {"200": {"description": ""}}}, "options": {"operationId": "options-get-orgs", "summary": "get_orgs", "responses": {"200": {"description": ""}}}}, "/auth/azure/callback": {"get": {"operationId": "get-azure-callback", "summary": "azure_callback", "responses": {"200": {"description": ""}}}}}, "tags": []}