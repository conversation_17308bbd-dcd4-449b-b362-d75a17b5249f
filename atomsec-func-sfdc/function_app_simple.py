"""
Simplified AtomSec SFDC Function App

This is a simplified version with minimal dependencies for testing deployment.
"""

import logging
import json
import azure.functions as func
from typing import Dict, Any
from datetime import datetime
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Log Azure Functions environment information
logger.info("=== Simplified Azure Functions Environment Info ===")
logger.info(f"Python version: {sys.version}")
logger.info(f"Azure Functions version: {os.environ.get('FUNCTIONS_WORKER_RUNTIME_VERSION', 'Unknown')}")
logger.info(f"Function directory: {os.environ.get('AzureWebJobsScriptRoot', 'Unknown')}")
logger.info(f"Current working directory: {os.getcwd()}")
logger.info("==================================================")

# Create the Function App
app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

# Test function to verify V2 function discovery works
@app.route(route="test", methods=["GET"])
def test_function(req: func.HttpRequest) -> func.HttpResponse:
    """Test function to verify V2 function discovery"""
    logger.info("Test function called - V2 function discovery is working")
    return func.HttpResponse(
        json.dumps({
            "message": "Test function works", 
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "service": "atomsec-func-sfdc-simple"
        }),
        mimetype="application/json"
    )

# Health check endpoint
@app.route(route="health", methods=["GET"])
def health_check(req: func.HttpRequest) -> func.HttpResponse:
    """
    Health check endpoint for the SFDC function app

    Returns:
        JSON response with health status
    """
    try:
        health_status = {
            "status": "healthy",
            "service": "atomsec-func-sfdc-simple",
            "environment": "production",
            "timestamp": datetime.now().isoformat(),
            "checks": {
                "function_app": "running",
                "basic_imports": "successful"
            }
        }

        status_code = 200

        return func.HttpResponse(
            json.dumps(health_status),
            mimetype="application/json",
            status_code=status_code
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }),
            mimetype="application/json",
            status_code=503
        )

# Info endpoint
@app.route(route="info", methods=["GET"])
def info(req: func.HttpRequest) -> func.HttpResponse:
    """
    Information endpoint for the SFDC function app

    Returns:
        JSON response with service information
    """
    info_data = {
        "service": "atomsec-func-sfdc-simple",
        "version": "1.0.0",
        "description": "Simplified AtomSec SFDC Service - Salesforce integration and security scanning",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "test": {
                "url": "/api/test",
                "method": "GET",
                "description": "Test endpoint for V2 function discovery"
            },
            "health": {
                "url": "/api/health",
                "method": "GET",
                "description": "Health check endpoint"
            },
            "info": {
                "url": "/api/info",
                "method": "GET",
                "description": "Service information endpoint"
            }
        },
        "features": [
            "Basic HTTP endpoints",
            "V2 function discovery",
            "Health monitoring",
            "Service information"
        ]
    }

    return func.HttpResponse(
        json.dumps(info_data),
        mimetype="application/json"
    )

logger.info("Simplified function app initialization completed successfully") 