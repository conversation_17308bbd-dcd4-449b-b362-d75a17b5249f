#!/usr/bin/env python3
"""
Test script to verify the function app starts without Service Bus errors

This script tests that the local development configuration works properly
without Service Bus connection issues.
"""

import sys
import os
import logging

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work without Service Bus connection errors"""
    try:
        print("Testing imports...")
        
        # Test Service Bus client import
        from shared.service_bus_client import get_service_bus_task_client
        print("✓ Service Bus client import successful")
        
        # Test mock Service Bus import
        from shared.mock_service_bus import get_mock_service_bus_client
        print("✓ Mock Service Bus client import successful")
        
        # Test that we get the mock client in local development
        client = get_service_bus_task_client()
        print(f"✓ Got client: {type(client).__name__}")
        
        # Test sending a mock message
        test_task = {
            "task_id": "test-task-123",
            "task_type": "health_check",
            "org_id": "test-org",
            "user_id": "test-user",
            "params": {}
        }
        
        result = client.send_task_message(test_task, priority="medium")
        print(f"✓ Mock message send result: {result}")
        
        print("All imports and basic functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Error during import test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_function_app_structure():
    """Test that the function app structure is correct"""
    try:
        print("\nTesting function app structure...")
        
        # Test that function_app.py can be imported
        import function_app
        print("✓ Function app import successful")
        
        # Check that the app object exists
        if hasattr(function_app, 'app'):
            print("✓ Function app object exists")
        else:
            print("✗ Function app object not found")
            return False
            
        print("Function app structure tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Error during function app test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("=== Local Development Startup Test ===\n")
    
    # Set local development environment
    os.environ['IS_LOCAL_DEV'] = 'true'
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Run tests
    tests_passed = 0
    total_tests = 2
    
    if test_imports():
        tests_passed += 1
    
    if test_function_app_structure():
        tests_passed += 1
    
    # Summary
    print(f"\n=== Test Results ===")
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ All tests passed! Local development setup is working correctly.")
        return 0
    else:
        print("✗ Some tests failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
