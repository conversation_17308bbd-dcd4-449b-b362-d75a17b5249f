# Pipeline Backups Directory

## Overview
This directory contains legacy pipeline files that have been replaced by the new staging slot deployment pipeline.

## Files
- `pipeline-func-sfdc-dev-legacy.yml` - Original direct production deployment pipeline (deprecated)

## Migration Status
✅ **Completed**: Staging slot deployment pipeline created
- **New Pipeline**: `../pipeline-func-sfdc-dev-staging.yml`
- **Guide**: `../DEPLOYMENT_STAGING_GUIDE.md`

## Change Summary
- **Before**: Direct production deployment (risk of downtime)
- **After**: Staging slot deployment with zero-downtime swap operations

## Usage
The legacy file is preserved for:
- Rollback scenarios
- Reference/comparison
- Historical tracking

## Next Steps
1. Update Azure DevOps pipeline to use `pipeline-func-sfdc-dev-staging.yml`
2. Test new staging deployment process
3. Monitor initial deployments
4. Remove legacy file after successful migration period