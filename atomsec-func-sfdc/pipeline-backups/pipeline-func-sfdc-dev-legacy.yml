# Azure DevOps Pipeline for deploying SFDC Function App with Staging Slot
# Deploys func-atomsec-sfdc-dev02 using staging slot and swap operations
# Updated to support zero-downtime deployments

trigger:

- dev-db-working-pipeline

pool:
  vmImage: ubuntu-latest

variables:
  # Service connection for SFDC service deployment
  sfdcServiceConnection: 'sc-atomsec-dev-backend'     # For atomsec-dev-backend resource group
  functionAppName: 'func-atomsec-sfdc-dev02'
  resourceGroupName: 'atomsec-dev-backend'
  stagingSlotName: 'stage'

# Note: DB service (func-atomsec-dbconnect-dev) is deployed separately via its own pipeline
# This pipeline only handles the SFDC microservice deployment to func-atomsec-sfdc-dev02

jobs:
- job: DeploySFDCService
  displayName: 'Deploy SFDC Function App with Staging Slot'
  steps:
  - task: UsePythonVersion@0
    inputs:
      versionSpec: '3.12'
    displayName: 'Set up Python 3.12 for SFDC Service'

  - script: |
      python --version
      python -m pip install --upgrade pip setuptools wheel

      echo 'Installing system dependencies for Python packages...'
      sudo apt-get update
      sudo apt-get install -y build-essential python3-dev pkg-config

      echo 'Installing ODBC driver for SQL Server on the build agent...'
      # Ensure curl is present, apt-get update will run after adding the repo
      sudo apt-get install -y curl

      # Add Microsoft GPG key
      curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -

      # Add Microsoft repository for Ubuntu
      # $(lsb_release -rs) will get the version of Ubuntu on the agent (e.g., 20.04, 22.04)
      sudo curl -fsSL https://packages.microsoft.com/config/ubuntu/$(lsb_release -rs)/prod.list -o /etc/apt/sources.list.d/mssql-release.list

      # Update package list again and install driver & dev package
      sudo apt-get update
      sudo ACCEPT_EULA=Y apt-get install -y msodbcsql17
      sudo ACCEPT_EULA=Y apt-get install -y unixodbc-dev

      echo 'ODBC driver installation attempt on build agent complete.'

      echo 'Installing Python dependencies for SFDC service...'
      if [ -f requirements.txt ]; then
        head requirements.txt
        
        # Ensure we have modern build tools compatible with Python 3.12
        pip install --upgrade pip
        pip install --upgrade setuptools>=68.0.0 wheel>=0.40.0 build>=1.0.0
        
        # Install dependencies using the same approach as working DB function app
        pip install --no-cache-dir -r requirements.txt -t .
      else
        echo 'No requirements.txt found'
        exit 1
      fi
    displayName: 'Install ODBC driver and Python dependencies for SFDC'

  - script: |
      echo "=== PRE-DEPLOYMENT VERIFICATION ==="
      echo "Checking function app structure..."
      ls -la
      echo "Checking for function_app.py..."
      if [ -f function_app.py ]; then
        echo "✓ function_app.py found"
        echo "First 20 lines of function_app.py:"
        head -20 function_app.py
      else
        echo "✗ function_app.py not found"
        exit 1
      fi
      
      echo "Checking host.json..."
      if [ -f host.json ]; then
        echo "✓ host.json found"
        cat host.json
      else
        echo "✗ host.json not found"
        exit 1
      fi
      
      echo "Checking requirements.txt..."
      if [ -f requirements.txt ]; then
        echo "✓ requirements.txt found"
        echo "Dependencies:"
        cat requirements.txt
      else
        echo "✗ requirements.txt not found"
        exit 1
      fi
      
      echo "Checking for __init__.py..."
      if [ -f __init__.py ]; then
        echo "✓ __init__.py found"
      else
        echo "✗ __init__.py not found"
      fi
      
      echo "Checking for api directory..."
      if [ -d api ]; then
        echo "✓ api directory found"
        ls -la api/
      else
        echo "✗ api directory not found"
      fi
      
      echo "Checking for shared directory..."
      if [ -d shared ]; then
        echo "✓ shared directory found"
        ls -la shared/
      else
        echo "✗ shared directory not found"
      fi
      
      echo "=== BASIC FUNCTION APP STRUCTURE VERIFICATION COMPLETE ==="
    displayName: 'Pre-deployment verification'
    continueOnError: true

  - script: |
      echo "=== POST-DEPENDENCY INSTALLATION VERIFICATION ==="
      echo "Testing build environment after dependencies are installed..."
      
      # Run the build environment verification script
      if [ -f scripts/verify_build_environment.py ]; then
        echo "Running build environment verification..."
        python scripts/verify_build_environment.py || echo "Build environment verification completed"
      else
        echo "Build environment verification script not found, skipping"
      fi
      
      # Run the function app startup test
      if [ -f scripts/test_function_app.py ]; then
        echo "Running function app startup test..."
        python scripts/test_function_app.py || echo "Function app startup test completed"
      else
        echo "Function app startup test script not found, skipping"
      fi
      
      echo "=== POST-DEPENDENCY INSTALLATION VERIFICATION COMPLETE ==="
    displayName: 'Verify build environment after dependency installation'
    continueOnError: true

  - script: |
      echo "=== PREPARING DEPLOYMENT ==="
      echo "Creating backup of original function_app.py..."
      cp function_app.py function_app_backup.py
      
      echo "Testing simplified function app..."
      if [ -f function_app_simple.py ]; then
        echo "✓ Simplified function app exists"
      else
        echo "✗ Simplified function app not found"
      fi
      
      echo "=== DEPLOYMENT PREPARATION COMPLETE ==="
    displayName: 'Prepare deployment with fallback'
    continueOnError: true

  - task: ArchiveFiles@2
    inputs:
      rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)/sfdc-build-$(Build.BuildNumber).zip'
      replaceExistingArchive: true
    displayName: 'Archive SFDC Function App'

  - task: AzureCLI@2
    displayName: 'Create/Verify Staging Slot'
    inputs:
      azureSubscription: '$(sfdcServiceConnection)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        echo "=== CREATING/VERIFYING STAGING SLOT ==="
        
        # Check if staging slot exists
        echo "Checking for existing staging slot..."
        slot_exists=$(az webapp deployment slot list --name $(functionAppName) --resource-group $(resourceGroupName) --query "[?name=='$(stagingSlotName)']" --output tsv)
        
        if [ -z "$slot_exists" ]; then
          echo "Creating staging slot..."
          az webapp deployment slot create \
            --name $(functionAppName) \
            --resource-group $(resourceGroupName) \
            --slot $(stagingSlotName) \
            --configuration-source $(functionAppName)
          echo "✅ Staging slot created successfully"
        else
          echo "✅ Staging slot already exists"
        fi
        
        # Verify staging slot status
        echo "Verifying staging slot status..."
        az webapp show --name $(functionAppName) --resource-group $(resourceGroupName) --slot $(stagingSlotName) --query "{name:name,state:state,hostNames:defaultHostName}" --output table
        
        echo "=== STAGING SLOT READY ==="

  - task: AzureAppServiceManage@0
    displayName: 'Stop Staging Slot Before Deployment'
    inputs:
      azureSubscription: '$(sfdcServiceConnection)'
      Action: 'Stop Azure App Service'
      WebAppName: '$(functionAppName)'
      ResourceGroupName: '$(resourceGroupName)'
      SpecifySlotOrASE: true
      Slot: '$(stagingSlotName)'

  - task: AzureCLI@2
    displayName: 'Deploy to Staging Slot'
    inputs:
      azureSubscription: '$(sfdcServiceConnection)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        echo "=== DEPLOYING TO STAGING SLOT ==="
        
        # Reset staging slot if needed
        echo "Resetting staging slot..."
        python scripts/force_reset_slot.py $(functionAppName) $(resourceGroupName) $(stagingSlotName) || echo "Staging slot reset completed"
        
        # Deploy to staging slot
        echo "Deploying to staging slot..."
        az webapp deployment source config-zip \
          --resource-group $(resourceGroupName) \
          --name $(functionAppName) \
          --slot $(stagingSlotName) \
          --src "$(Build.ArtifactStagingDirectory)/sfdc-build-$(Build.BuildNumber).zip" \
          --timeout 1800
        
        if [ $? -eq 0 ]; then
          echo "✅ Staging deployment successful"
        else
          echo "❌ Staging deployment failed"
          exit 1
        fi
        
        echo "=== STAGING DEPLOYMENT COMPLETE ==="

  - task: AzureAppServiceManage@0
    displayName: 'Start Staging Slot After Deployment'
    inputs:
      azureSubscription: '$(sfdcServiceConnection)'
      Action: 'Start Azure App Service'
      WebAppName: '$(functionAppName)'
      ResourceGroupName: '$(resourceGroupName)'
      SpecifySlotOrASE: true
      Slot: '$(stagingSlotName)'

  - task: AzureCLI@2
    displayName: 'Configure Staging Slot Settings'
    inputs:
      azureSubscription: '$(sfdcServiceConnection)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        echo "=== CONFIGURING STAGING SLOT SETTINGS ==="
        
        # Core Azure Functions settings for staging
        echo "Setting core Azure Functions settings for staging..."
        az webapp config appsettings set \
          --name $(functionAppName) \
          --resource-group $(resourceGroupName) \
          --slot $(stagingSlotName) \
          --settings \
            FUNCTIONS_WORKER_RUNTIME=python \
            FUNCTIONS_EXTENSION_VERSION=~4 \
            WEBSITE_RUN_FROM_PACKAGE=1 \
            SCM_DO_BUILD_DURING_DEPLOYMENT=false \
            AzureWebJobsFeatureFlags=EnableWorkerIndexing \
          || echo "Failed to set core settings"

        # Application-specific settings for staging
        echo "Setting application-specific settings for staging..."
        az webapp config appsettings set \
          --name $(functionAppName) \
          --resource-group $(resourceGroupName) \
          --slot $(stagingSlotName) \
          --settings \
            KEY_VAULT_URL="https://akv-atomsec-dev.vault.azure.net/" \
            DB_SERVICE_URL="https://func-atomsec-dbconnect-dev.azurewebsites.net/api/db" \
            DB_SERVICE_TIMEOUT="30" \
            DB_SERVICE_RETRY_ATTEMPTS="3" \
            DB_SERVICE_RETRY_DELAY="1" \
            FRONTEND_URL="https://app-atomsec-dev01.azurewebsites.net" \
            PMD_ENABLED="true" \
          || echo "Failed to set application settings"

        echo "=== STAGING SLOT SETTINGS CONFIGURED ==="

  - script: |
      echo "=== VALIDATING STAGING SLOT ==="
      echo "Waiting for staging slot to fully start..."
      sleep 60
      
      echo "Checking staging slot health..."
      python scripts/manage_slots.py $(functionAppName) $(resourceGroupName) $(stagingSlotName) health || echo "Health check completed"
      
      echo "Testing staging slot endpoints..."
      staging_url="https://$(functionAppName)-$(stagingSlotName).azurewebsites.net"
      
      echo "Testing basic connectivity to staging..."
      curl -v --max-time 30 "$staging_url" || echo "Basic connectivity test completed"
      
      echo "Testing staging health endpoint..."
      curl -v --max-time 30 "$staging_url/api/health" || echo "Health endpoint test completed"
      
      echo "Running staging deployment verification..."
      python scripts/verify_azure_functions.py "$staging_url" || echo "Staging verification completed"
      
      echo "=== STAGING VALIDATION COMPLETE ==="
    displayName: 'Validate Staging Slot Health'
    continueOnError: false

  - task: AzureAppServiceManage@0
    displayName: 'Swap Staging to Production'
    inputs:
      azureSubscription: '$(sfdcServiceConnection)'
      Action: 'Swap Slots'
      WebAppName: '$(functionAppName)'
      ResourceGroupName: '$(resourceGroupName)'
      SourceSlot: '$(stagingSlotName)'
      TargetSlot: 'production'

  - script: |
      echo "=== POST-SWAP VERIFICATION ==="
      echo "Waiting for swap to complete..."
      sleep 60
      
      echo "Verifying production slot after swap..."
      production_url="https://$(functionAppName).azurewebsites.net"
      
      echo "Testing production connectivity..."
      curl -v --max-time 30 "$production_url" || echo "Production connectivity test completed"
      
      echo "Testing production health endpoint..."
      curl -v --max-time 30 "$production_url/api/health" || echo "Production health test completed"
      
      echo "Running final production verification..."
      python scripts/verify_azure_functions.py "$production_url"
      
      if [ $? -ne 0 ]; then
        echo "❌ Production verification failed after swap"
        echo "Consider rolling back if necessary"
        exit 1
      else
        echo "✅ Production verification successful after swap"
      fi
      
      echo "=== PRODUCTION VERIFICATION COMPLETE ==="
    displayName: 'Verify Production After Swap'
    continueOnError: false

  - script: |
      echo "=== CLEANUP STAGING SLOT ==="
      echo "Stopping staging slot after successful deployment..."
      az webapp stop --name $(functionAppName) --resource-group $(resourceGroupName) --slot $(stagingSlotName) || echo "Staging slot stop completed"
      
      echo "=== DEPLOYMENT PIPELINE COMPLETE ==="
      echo "✅ Zero-downtime deployment completed successfully"
      echo "✅ Application deployed via staging slot and swapped to production"
    displayName: 'Cleanup Staging Slot'
    continueOnError: true

  - script: |
      echo "=== FINAL VERIFICATION ==="
      echo "Verifying functions in production after staging deployment..."
      python scripts/verify_azure_functions.py "https://$(functionAppName).azurewebsites.net"
      
      if [ $? -ne 0 ]; then
        echo "❌ Final function verification failed"
        echo "Check the logs and configuration for issues"
        exit 1
      else
        echo "✅ Final function verification successful - full function app is working!"
      fi
    displayName: 'Final Production Verification'
    continueOnError: false

  - task: PublishBuildArtifacts@1
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)/sfdc-build-$(Build.BuildNumber).zip'
      ArtifactName: 'sfdc-drop'
      publishLocation: 'Container'
    displayName: 'Publish SFDC Artifacts'