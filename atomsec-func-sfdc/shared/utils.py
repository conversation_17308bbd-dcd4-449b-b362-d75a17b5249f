"""
Utilities Module

This module provides common utility functions used across the application.

Best practices implemented:
- Centralized error handling
- Consistent logging
- Reusable HTTP helpers
- Salesforce API integration (via salesforce_utils module)
"""

import logging
import json
import os
import requests
import asyncio
import aiohttp
from typing import Dict, Any, Tuple, List, Optional, Union
from datetime import datetime
import re
import azure.functions as func

# Import Salesforce utilities
from shared.salesforce_utils import (
    execute_salesforce_query,
    execute_salesforce_tooling_query,
    get_salesforce_access_token,
    test_salesforce_connection
)

# Configure module-level logger
logger = logging.getLogger(__name__)

# HTTP Response helpers
def create_json_response(data: Any, status_code: int = 200) -> Dict[str, Any]:
    """
    Create a standardized JSON response

    Args:
        data: Data to include in the response
        status_code: HTTP status code

    Returns:
        Dict: Standardized response dictionary
    """
    success = 200 <= status_code < 300

    response = {
        "success": success,
        "statusCode": status_code,
        "timestamp": datetime.now().isoformat(),
        "data": data
    }

    if not success:
        response["error"] = data if isinstance(data, str) else "An error occurred"
        response["data"] = None

    return response

# Error handling
def handle_exception(e: Exception, context: str = "") -> Dict[str, Any]:
    """
    Handle an exception and return a standardized error response

    Args:
        e: Exception to handle
        context: Context where the exception occurred

    Returns:
        Dict: Standardized error response
    """
    error_message = f"{type(e).__name__}: {str(e)}"
    logger.error(f"Error in {context}: {error_message}")

    return create_json_response(error_message, 500)

# Salesforce API integration is now provided by the salesforce_utils module
# The following functions are imported from salesforce_utils:
# - execute_salesforce_query
# - execute_salesforce_tooling_query
# - get_salesforce_access_token
# - test_salesforce_connection

# Date and time helpers
def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Format a datetime object as a string

    Args:
        dt: Datetime to format
        format_str: Format string

    Returns:
        str: Formatted datetime string
    """
    return dt.strftime(format_str)

def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
    """
    Parse a string into a datetime object

    Args:
        dt_str: Datetime string to parse
        format_str: Format string

    Returns:
        datetime: Parsed datetime or None if error
    """
    try:
        return datetime.strptime(dt_str, format_str)
    except Exception as e:
        logger.error(f"Error parsing datetime {dt_str}: {str(e)}")
        return None

def parse_json_body(req: func.HttpRequest) -> Dict[str, Any]:
    """
    Parse JSON body from HTTP request

    Args:
        req: HTTP request

    Returns:
        Dict: Parsed JSON body or empty dict if error
    """
    try:
        body = req.get_body().decode('utf-8')
        if not body:
            logger.warning("Empty request body")
            return {}

        return json.loads(body)
    except ValueError as e:
        logger.error(f"Error parsing JSON body: {str(e)}")
        return {}
    except Exception as e:
        logger.error(f"Unexpected error parsing request body: {str(e)}")
        return {}
