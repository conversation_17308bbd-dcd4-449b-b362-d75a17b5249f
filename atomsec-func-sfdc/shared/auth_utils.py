"""
Authentication Utilities Module

This module provides utilities for authentication and authorization.

Best practices implemented:
- JWT-based authentication
- Proper error handling and logging
- Reusable authentication functions
"""

import logging
import jwt
import requests
import os
from typing import Dict, Any, Optional
import azure.functions as func
from fastapi import Request

# Import shared modules
from shared.config import get_jwt_config
from shared.user_repository import get_user_account_by_email

# Configure module-level logger
logger = logging.getLogger(__name__)

# JWT configuration
_jwt_config = None

def get_jwt_secret():
    """Get JWT secret from configuration"""
    global _jwt_config
    if _jwt_config is None:
        _jwt_config = get_jwt_config()
        logger.info(f"[JWT DEBUG] Loaded JWT config: algorithm={_jwt_config.get('algorithm')}, secret_length={len(_jwt_config.get('secret', ''))}")
    return _jwt_config["secret"]

def get_jwt_algorithm():
    """Get JWT algorithm from configuration"""
    global _jwt_config
    if _jwt_config is None:
        _jwt_config = get_jwt_config()
    return _jwt_config["algorithm"]

def decode_azure_ad_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Decode and validate Azure AD RS256 JWT token
    
    Args:
        token: JWT token string
        
    Returns:
        Dict: Token payload or None if invalid
    """
    try:
        # Get Azure AD tenant ID from environment or use common
        tenant_id = os.environ.get("AZURE_AD_TENANT_ID", "41b676db-bf6f-46ae-a354-a83a1362533f")
        
        # Get the signing key from Azure AD
        unverified_header = jwt.get_unverified_header(token)
        kid = unverified_header.get('kid')
        
        if not kid:
            logger.warning("No 'kid' found in Azure AD token header")
            return None
            
        # Get public keys from Azure AD
        jwks_url = f"https://login.microsoftonline.com/{tenant_id}/discovery/v2.0/keys"
        logger.debug(f"Fetching Azure AD public keys from: {jwks_url}")
        
        response = requests.get(jwks_url, timeout=10)
        response.raise_for_status()
        jwks = response.json()
        
        # Find the correct key
        signing_key = None
        for key in jwks.get('keys', []):
            if key.get('kid') == kid:
                # Convert JWK to PEM format for verification
                from cryptography.hazmat.primitives import serialization
                from cryptography.hazmat.primitives.asymmetric import rsa
                import base64
                
                # Decode the public key components
                n = base64.urlsafe_b64decode(key['n'] + '==')
                e = base64.urlsafe_b64decode(key['e'] + '==')
                
                # Convert to integers
                n_int = int.from_bytes(n, 'big')
                e_int = int.from_bytes(e, 'big')
                
                # Create RSA public key
                public_key = rsa.RSAPublicNumbers(e_int, n_int).public_key()
                
                # Convert to PEM format
                signing_key = public_key.public_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                )
                break
                
        if not signing_key:
            logger.warning(f"No matching key found for kid: {kid}")
            return None
            
        # Verify the token
        payload = jwt.decode(
            token, 
            signing_key, 
            algorithms=['RS256'],
            # Note: Azure AD tokens may have different audience values
            # We'll validate the payload structure instead of strict audience checking
            options={"verify_aud": False}
        )
        
        logger.debug("Successfully validated Azure AD token")
        return payload
        
    except requests.RequestException as e:
        logger.error(f"Failed to fetch Azure AD public keys: {str(e)}")
        return None
    except ImportError as e:
        logger.error(f"Missing cryptography library for Azure AD token validation: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error validating Azure AD token: {str(e)}")
        return None

def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Decode and validate JWT token
    Supports both custom HS256 tokens and Azure AD RS256 tokens

    Args:
        token: JWT token to decode

    Returns:
        Dict: Decoded token payload or None if invalid
    """
    try:
        # First, try to decode the token without verification to check the algorithm
        unverified_payload = jwt.decode(token, options={"verify_signature": False})
        unverified_header = jwt.get_unverified_header(token)
        
        algorithm = unverified_header.get('alg', 'HS256')
        logger.debug(f"Token algorithm detected: {algorithm}")
        
        if algorithm == 'HS256':
            # Custom token signed with our secret
            jwt_secret = get_jwt_secret()
            jwt_algorithm = get_jwt_algorithm()
            logger.info(f"[JWT DEBUG] Using JWT secret: {jwt_secret[:10]}... (length: {len(jwt_secret)})")
            logger.info(f"[JWT DEBUG] Using JWT algorithm: {jwt_algorithm}")

            payload = jwt.decode(token, jwt_secret, algorithms=[jwt_algorithm])
            logger.info(f"[JWT DEBUG] Successfully decoded HS256 token payload: {payload}")
            
        elif algorithm == 'RS256':
            # Azure AD token - validate using Azure AD public keys
            payload = decode_azure_ad_token(token)
            if not payload:
                logger.warning("Failed to validate Azure AD token")
                return None
            logger.debug("Successfully decoded RS256 token (Azure AD)")
            
        else:
            logger.warning(f"Unsupported token algorithm: {algorithm}")
            return None

        return payload
        
    except jwt.ExpiredSignatureError:
        logger.warning("Token expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid token: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error decoding token: {str(e)}")
        return None

def extract_user_from_payload(payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Extract user information from JWT payload
    Handles both custom tokens and Azure AD tokens
    
    Args:
        payload: JWT token payload
        
    Returns:
        Dict: User information or None if invalid
    """
    # For Azure AD tokens, email is typically in 'upn' or 'email' field
    # For custom tokens, email is typically in 'sub' field
    email = (payload.get("upn") or 
             payload.get("email") or 
             payload.get("sub") or 
             payload.get("preferred_username"))
    
    if not email:
        logger.warning("No email/user identifier found in token payload")
        logger.debug(f"Available payload fields: {list(payload.keys())}")
        return None
    
    # Extract name from various possible fields
    name = (payload.get("name") or 
            payload.get("given_name", "") + " " + payload.get("family_name", "") or
            "").strip()
    
    # Extract user ID
    user_id = (payload.get("user_id") or 
               payload.get("oid") or  # Azure AD object ID
               payload.get("sub"))
    
    # Extract roles (custom tokens) or Azure AD roles
    roles = payload.get("roles", [])
    if not roles and "roles" in payload:
        roles = payload["roles"] if isinstance(payload["roles"], list) else []
    
    # Check admin status
    is_admin = payload.get("isAdmin", False)
    if not is_admin and roles:
        # Check if any role indicates admin status
        admin_roles = ["admin", "administrator", "owner", "global_admin"]
        is_admin = any(role.lower() in admin_roles for role in roles if isinstance(role, str))
    
    user_info = {
        "email": email,
        "user_id": user_id,
        "name": name,
        "roles": roles,
        "isAdmin": is_admin
    }
    
    logger.debug(f"Extracted user info: email={email}, user_id={user_id}, roles={roles}")
    return user_info

def get_token_from_header(req: func.HttpRequest) -> Optional[str]:
    """
    Extract token from Authorization header

    Args:
        req: HTTP request

    Returns:
        str: Token or None if not found
    """
    auth_header = req.headers.get("Authorization")
    if not auth_header:
        return None

    parts = auth_header.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        return None

    return parts[1]

def get_current_user(req: func.HttpRequest) -> Optional[Dict[str, Any]]:
    """
    Get current user from request

    Args:
        req: HTTP request

    Returns:
        Dict: User information or None if not authenticated
    """
    # Get token from header
    token = get_token_from_header(req)
    logger.info(f"[AUTH DEBUG] Token from header: {token}")
    if not token:
        logger.warning("No token found in request")
        return None

    # Decode token
    payload = decode_token(token)
    logger.info(f"[AUTH DEBUG] Decoded payload: {payload}")
    if not payload:
        logger.warning("Invalid token")
        return None

    # Extract user information from token
    user_info = extract_user_from_payload(payload)
    if not user_info:
        return None

    email = user_info["email"]
    logger.info(f"[AUTH DEBUG] Email from token: {email}")

    # Fetch user from DB to get user ID
    user_account = get_user_account_by_email(email)
    logger.info(f"[AUTH DEBUG] User account from DB: {user_account}")
    if not user_account:
        logger.warning("No user found in DB for email")
        return None

    # Update user info with database ID
    user_info["id"] = user_account.UserId  # UserId is the unique user identifier
    logger.info(f"[AUTH DEBUG] Returning user info: {user_info}")
    return user_info

def get_user_id_from_request(req: func.HttpRequest) -> Optional[str]:
    """
    Extract user ID from authenticated request

    Args:
        req: HTTP request

    Returns:
        str: User ID or None if not authenticated
    """
    user = get_current_user(req)
    if not user:
        return None

    # Try to get user_id from token first
    user_id = user.get("id")
    if user_id:
        logger.debug(f"Found user_id in token: {user_id}")
        return str(user_id)

    # Fallback to email as user identifier
    email = user.get("email")
    if email:
        logger.debug(f"Using email as user identifier: {email}")
        return email

    logger.warning("No user identifier found in token")
    return None

def get_user_from_request_or_default(req: func.HttpRequest, default_user_id: str = "system") -> str:
    """
    Get user ID from request or return default

    Args:
        req: HTTP request
        default_user_id: Default user ID to use if authentication fails

    Returns:
        str: User ID or default value
    """
    user_id = get_user_id_from_request(req)
    if user_id:
        return user_id

    logger.debug(f"No authenticated user found, using default: {default_user_id}")
    return default_user_id

def require_auth(original_func):
    """
    Decorator to require authentication

    Args:
        original_func: Function to decorate

    Returns:
        Function: Decorated function
    """
    def wrapper(req):
        # Import here to avoid circular imports
        from shared.common import is_local_dev

        # In local development mode, create a mock user for testing
        local_dev_status = is_local_dev()
        logger.info(f"[AUTH DEBUG] is_local_dev() returned: {local_dev_status}")

        if local_dev_status:
            logger.info("[AUTH DEBUG] Local development mode detected, using mock authentication")
            mock_user = {
                "email": "<EMAIL>",
                "id": "dev-user-id"
            }
            setattr(req, "user", mock_user)
            logger.info("[AUTH DEBUG] Mock user set, calling original function")
            return original_func(req)

        # Get current user for production
        logger.info("[AUTH DEBUG] Production mode, attempting to get current user")
        current_user = get_current_user(req)
        logger.info(f"[AUTH DEBUG] get_current_user returned: {current_user}")
        if not current_user:
            logger.warning("[AUTH DEBUG] No current user found, returning 401")
            # Import here to avoid circular imports
            import azure.functions as func_module
            import json
            return func_module.HttpResponse(
                json.dumps({"success": False, "statusCode": 401, "error": "Unauthorized"}),
                mimetype="application/json",
                status_code=401
            )

        # Add user to request
        setattr(req, "user", current_user)

        # Call original function
        return original_func(req)

    # Copy function attributes to wrapper
    import functools
    functools.update_wrapper(wrapper, original_func)

    return wrapper
