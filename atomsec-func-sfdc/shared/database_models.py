"""
Database Models Module

This module defines the database models for the application, providing a structured
representation of the database schema for use in data access operations.

Models are defined as Python classes with attributes corresponding to database columns.
"""

from dataclasses import dataclass
from datetime import datetime, date
from typing import Optional, List, Dict, Any


@dataclass
class UserAccount:
    """Model for dbo.User_Account table"""
    UserId: Optional[int] = None  # PK, auto-increment
    FirstName: str = ""
    MiddleName: str = ""
    LastName: str = ""
    DoB: Optional[date] = None
    Email: str = ""
    Contact: Optional[str] = None
    State: str = ""
    Country: str = ""
    Organization: str = ""  # Added Organization field

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserAccount':
        """Create a UserAccount instance from a dictionary"""
        return cls(
            UserId=data.get('UserId'),
            FirstName=data.get('FirstName', ''),
            MiddleName=data.get('MiddleName', ''),
            LastName=data.get('LastName', ''),
            DoB=data.get('DoB'),
            Email=data.get('Email', ''),
            Contact=data.get('Contact'),
            State=data.get('State', ''),
            Country=data.get('Country', '')
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'UserId': self.UserId,
            'FirstName': self.FirstName,
            'MiddleName': self.MiddleName,
            'LastName': self.LastName,
            'DoB': self.DoB,
            'Email': self.Email,
            'Contact': self.Contact,
            'State': self.State,
            'Country': self.Country
        }

    def to_table_entity(self) -> Dict[str, Any]:
        """Convert to Azure Table Storage entity"""
        return {
            'PartitionKey': 'user_account',
            'RowKey': self.Email,
            'FirstName': self.FirstName,
            'MiddleName': self.MiddleName,
            'LastName': self.LastName,
            'DoB': self.DoB.isoformat() if self.DoB else '',
            'Email': self.Email,
            'Contact': str(self.Contact) if self.Contact else '',
            'State': self.State,
            'Country': self.Country
        }


@dataclass
class HashingAlgorithm:
    """Model for dbo.HashingAlgorithm table"""
    HashAlgorithmId: Optional[int] = None  # PK, auto-increment
    AlgorithmName: str = ""

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HashingAlgorithm':
        """Create a HashingAlgorithm instance from a dictionary"""
        return cls(
            HashAlgorithmId=data.get('HashAlgorithmId'),
            AlgorithmName=data.get('AlgorithmName', '')
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'HashAlgorithmId': self.HashAlgorithmId,
            'AlgorithmName': self.AlgorithmName
        }

    def to_table_entity(self) -> Dict[str, Any]:
        """Convert to Azure Table Storage entity"""
        return {
            'PartitionKey': 'hashing_algorithm',
            'RowKey': str(self.HashAlgorithmId) if self.HashAlgorithmId else str(hash(self.AlgorithmName)),
            'AlgorithmName': self.AlgorithmName
        }


@dataclass
class UserLogin:
    """Model for dbo.App_User_Login table"""
    UserId: int  # PK, FK to User_Account
    Username: str
    PasswordHash: str
    PasswordSalt: str
    HashAlgorithmId: int  # FK to HashingAlgorithm

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserLogin':
        """Create a UserLogin instance from a dictionary"""
        return cls(
            UserId=data['UserId'],
            Username=data.get('Username', ''),
            PasswordHash=data.get('PasswordHash', ''),
            PasswordSalt=data.get('PasswordSalt', ''),
            HashAlgorithmId=data.get('HashAlgorithmId', 1)  # Default to algorithm ID 1
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'UserId': self.UserId,
            'Username': self.Username,
            'PasswordHash': self.PasswordHash,
            'PasswordSalt': self.PasswordSalt,
            'HashAlgorithmId': self.HashAlgorithmId
        }

    def to_table_entity(self) -> Dict[str, Any]:
        """Convert to Azure Table Storage entity"""
        return {
            'PartitionKey': 'user_login',
            'RowKey': self.Username,
            'UserId': self.UserId,
            'PasswordHash': self.PasswordHash,
            'PasswordSalt': self.PasswordSalt,
            'HashAlgorithmId': self.HashAlgorithmId
        }


@dataclass
class Role:
    """Model for dbo.Role table"""
    RoleId: Optional[int] = None  # PK, auto-increment
    Rolename: str = ""
    Description: str = ""
    CreatedDate: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Role':
        """Create a Role instance from a dictionary"""
        return cls(
            RoleId=data.get('RoleId'),
            Rolename=data.get('Rolename', ''),
            Description=data.get('Description', ''),
            CreatedDate=data.get('CreatedDate', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'RoleId': self.RoleId,
            'Rolename': self.Rolename,
            'Description': self.Description,
            'CreatedDate': self.CreatedDate
        }


@dataclass
class UserRole:
    """Model for dbo.App_User_Roles table"""
    UserId: int  # FK to User_Account
    RoleId: int  # FK to Role
    CreatedDate: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserRole':
        """Create a UserRole instance from a dictionary"""
        return cls(
            UserId=data['UserId'],
            RoleId=data['RoleId'],
            CreatedDate=data.get('CreatedDate', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'UserId': self.UserId,
            'RoleId': self.RoleId,
            'CreatedDate': self.CreatedDate
        }


@dataclass
class Permission:
    """Model for dbo.Permission table"""
    PermissionId: Optional[int] = None  # PK, auto-increment
    PermissionLabel: str = ""
    Action: str = ""
    Description: str = ""
    CreatedDate: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Permission':
        """Create a Permission instance from a dictionary"""
        return cls(
            PermissionId=data.get('PermissionId'),
            PermissionLabel=data.get('PermissionLabel', ''),
            Action=data.get('Action', ''),
            Description=data.get('Description', ''),
            CreatedDate=data.get('CreatedDate', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'PermissionId': self.PermissionId,
            'PermissionLabel': self.PermissionLabel,
            'Action': self.Action,
            'Description': self.Description,
            'CreatedDate': self.CreatedDate
        }


@dataclass
class RolePermission:
    """Model for dbo.Role_Permissions table"""
    RoleId: int  # FK to Role
    PermissionId: int  # FK to Permission
    CreatedDate: datetime = datetime.now()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RolePermission':
        """Create a RolePermission instance from a dictionary"""
        return cls(
            RoleId=data['RoleId'],
            PermissionId=data['PermissionId'],
            CreatedDate=data.get('CreatedDate', datetime.now())
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations"""
        return {
            'RoleId': self.RoleId,
            'PermissionId': self.PermissionId,
            'CreatedDate': self.CreatedDate
        }
