"""
Mock Service Bus for Local Development

This module provides a mock Service Bus implementation for local development
that simulates the Service Bus behavior using HTTP calls to trigger functions.
"""

import logging
import json
import requests
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class MockServiceBusClient:
    """Mock Service Bus client for local development"""

    def __init__(self):
        """Initialize the mock Service Bus client"""
        self.base_url = "http://localhost:7071"  # Default Azure Functions local URL
        self.enabled = True
        logger.info("Mock Service Bus client initialized for local development")

    def send_task_message(self, task_data: Dict[str, Any], priority: str = 'medium', 
                         scheduled_time: Optional[datetime] = None) -> bool:
        """
        Send a task message by directly calling the appropriate function endpoint
        
        Args:
            task_data: Task data to send
            priority: Task priority (high, medium, low)
            scheduled_time: Optional scheduled execution time (ignored in mock)
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.enabled:
            logger.warning("Mock Service Bus client is disabled")
            return False
            
        try:
            
            # Call the HTTP endpoint for the priority level
            endpoint_url = f"{self.base_url}/api/task/{priority}"

            logger.info(f"Sending HTTP request to {endpoint_url} for task {task_data.get('task_id')}")

            try:
                # Make HTTP POST request to the function endpoint
                response = requests.post(
                    endpoint_url,
                    json=task_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )

                if response.status_code == 200:
                    logger.info(f"Successfully sent task {task_data.get('task_id')} to {endpoint_url}")
                    return True
                else:
                    logger.error(f"HTTP request failed with status {response.status_code}: {response.text}")
                    return False

            except requests.exceptions.ConnectionError:
                logger.warning(f"Could not connect to {endpoint_url}. Function app may not be running.")
                # Fall back to direct processing for local development
                try:
                    from function_app import process_service_bus_task_message

                    # Create a mock ServiceBusMessage
                    class MockServiceBusMessage:
                        def __init__(self, body: str):
                            self._body = body.encode('utf-8')

                        def get_body(self):
                            return self._body

                    mock_sb_message = MockServiceBusMessage(json.dumps(task_data))

                    # Call the processor directly
                    process_service_bus_task_message(mock_sb_message, priority, f"sfdc-service-{priority}")

                    logger.info(f"Successfully processed task {task_data.get('task_id')} via direct call")
                    return True

                except ImportError as import_error:
                    logger.warning(f"Could not import function processor: {import_error}")
                    logger.info(f"Mock Service Bus: Task {task_data.get('task_id')} would be processed")
                    return True

            except Exception as http_error:
                logger.error(f"HTTP request error: {str(http_error)}")
                return False
                
        except Exception as e:
            logger.error(f"Error in mock Service Bus send: {str(e)}")
            return False

    def create_subscriptions_if_not_exist(self) -> bool:
        """
        Mock subscription creation - always returns True for local development
        
        Returns:
            bool: Always True for mock implementation
        """
        logger.info("Mock Service Bus: Subscriptions would be created in real Service Bus")
        return True

    def get_subscription_name_for_priority(self, priority: str) -> str:
        """Get subscription name for a given priority"""
        priority_subscriptions = {
            'high': 'sfdc-service-high',
            'medium': 'sfdc-service-medium', 
            'low': 'sfdc-service-low'
        }
        return priority_subscriptions.get(priority, priority_subscriptions['medium'])

# Global mock client instance
_mock_service_bus_client = None

def get_mock_service_bus_client() -> MockServiceBusClient:
    """Get the global mock Service Bus client instance"""
    global _mock_service_bus_client
    if _mock_service_bus_client is None:
        _mock_service_bus_client = MockServiceBusClient()
    return _mock_service_bus_client
