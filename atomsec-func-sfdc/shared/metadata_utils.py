"""
Utility functions for working with Salesforce metadata
"""

import logging
import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple

# Create logger
logger = logging.getLogger(__name__)

def get_salesforce_metadata(access_token: str, instance_url: str, blob_repo, blob_prefix: str) -> Tuple[bool, str]:
    """
    Fetch metadata from Salesforce and store it in blob storage
    
    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL
        blob_repo: Blob storage repository
        blob_prefix: Prefix for blob storage
        
    Returns:
        Tuple of (success, error_message)
    """
    try:
        # Verify blob_repo is properly initialized
        if not blob_repo:
            logger.error("Blob repository is not initialized")
            return False, "Blob repository is not initialized"
            
        logger.info(f"Starting metadata retrieval for instance: {instance_url}")
        logger.info(f"Using blob prefix: {blob_prefix}")
        logger.info(f"Blob repository type: {type(blob_repo)}")
        
        # Create package.xml for metadata retrieval
        package_xml = """<?xml version="1.0" encoding="UTF-8"?>
<Package xmlns="http://soap.sforce.com/2006/04/metadata">
    <types>
        <members>*</members>
        <n>Profile</n>
    </types>
    <types>
        <members>*</members>
        <n>PermissionSet</n>
    </types>
    <types>
        <members>*</members>
        <n>CustomObject</n>
    </types>
    <types>
        <members>*</members>
        <n>ApexClass</n>
    </types>
    <types>
        <members>*</members>
        <n>ApexTrigger</n>
    </types>
    <version>62.0</version>
</Package>"""

        # Save package.xml to blob storage
        try:
            package_xml_blob_name = f"{blob_prefix}/package.xml"
            logger.info(f"Attempting to save package.xml to blob: {package_xml_blob_name}")
            # Ensure blob_repo has container_client attribute or adjust accordingly
            if hasattr(blob_repo, 'container_client'):
                blob_client = blob_repo.container_client.get_blob_client(package_xml_blob_name)
                blob_client.upload_blob(package_xml.encode('utf-8'), overwrite=True, content_settings={'contentType': 'application/xml'})
                logger.info(f"Successfully saved package.xml (XML string) to blob: {package_xml_blob_name}")
            else:
                # Fallback or error if container_client is not available
                # This might indicate blob_repo is a different kind of object than in metadata_extraction.py
                # For now, log a warning. A more robust solution might require knowing blob_repo's type.
                logger.warning(f"blob_repo in metadata_utils.py does not have container_client. Cannot save package.xml as raw XML string via upload_blob. Attempting save_json as fallback (original problematic behavior).")
                blob_repo.save_json(package_xml_blob_name, {"xml_content": package_xml}) # Encapsulate to avoid direct string-is-JSON issue
            
        except Exception as blob_e:
            logger.error(f"Error saving package.xml to blob: {str(blob_e)}")
            return False, f"Error saving package.xml to blob: {str(blob_e)}"

        # Metadata API endpoint
        metadata_url = f"{instance_url}/services/data/v62.0/tooling/sobjects"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        # Get list of available objects
        try:
            logger.info(f"Fetching metadata objects from: {metadata_url}")
            response = requests.get(metadata_url, headers=headers)
            response.raise_for_status()
            
            # Save the metadata to blob storage
            metadata_blob_name = f"{blob_prefix}/metadata_objects.json"
            blob_repo.save_json(metadata_blob_name, response.json())
            logger.info(f"Saved metadata objects to blob: {metadata_blob_name}")
        except Exception as meta_e:
            logger.error(f"Error fetching or saving metadata objects: {str(meta_e)}")
            return False, f"Error fetching or saving metadata objects: {str(meta_e)}"
        
        # Get profiles
        try:
            profiles_url = f"{instance_url}/services/data/v62.0/tooling/query/?q=SELECT+Id,Name,UserLicense.Name+FROM+Profile"
            logger.info(f"Fetching profiles from: {profiles_url}")
            profiles_response = requests.get(profiles_url, headers=headers)
            profiles_response.raise_for_status()
            
            # Save profiles to blob storage
            profiles_blob_name = f"{blob_prefix}/profiles.json"
            blob_repo.save_json(profiles_blob_name, profiles_response.json())
            logger.info(f"Saved profiles to blob: {profiles_blob_name}")
        except Exception as prof_e:
            logger.error(f"Error fetching or saving profiles: {str(prof_e)}")
            return False, f"Error fetching or saving profiles: {str(prof_e)}"
        
        # Get permission sets
        try:
            permsets_url = f"{instance_url}/services/data/v62.0/tooling/query/?q=SELECT+Id,Name,Label+FROM+PermissionSet"
            logger.info(f"Fetching permission sets from: {permsets_url}")
            permsets_response = requests.get(permsets_url, headers=headers)
            permsets_response.raise_for_status()
            
            # Save permission sets to blob storage
            permsets_blob_name = f"{blob_prefix}/permission_sets.json"
            blob_repo.save_json(permsets_blob_name, permsets_response.json())
            logger.info(f"Saved permission sets to blob: {permsets_blob_name}")
        except Exception as perm_e:
            logger.error(f"Error fetching or saving permission sets: {str(perm_e)}")
            return False, f"Error fetching or saving permission sets: {str(perm_e)}"
        
        # Get apex classes
        try:
            apex_classes_url = f"{instance_url}/services/data/v62.0/tooling/query/?q=SELECT+Id,Name,Body+FROM+ApexClass"
            logger.info(f"Fetching apex classes from: {apex_classes_url}")
            apex_classes_response = requests.get(apex_classes_url, headers=headers)
            apex_classes_response.raise_for_status()
            
            # Save apex classes to blob storage
            apex_classes_blob_name = f"{blob_prefix}/apex_classes.json"
            blob_repo.save_json(apex_classes_blob_name, apex_classes_response.json())
            logger.info(f"Saved apex classes to blob: {apex_classes_blob_name}")
        except Exception as apex_e:
            logger.error(f"Error fetching or saving apex classes: {str(apex_e)}")
            return False, f"Error fetching or saving apex classes: {str(apex_e)}"
        
        # Get apex triggers
        try:
            apex_triggers_url = f"{instance_url}/services/data/v62.0/tooling/query/?q=SELECT+Id,Name,Body+FROM+ApexTrigger"
            logger.info(f"Fetching apex triggers from: {apex_triggers_url}")
            apex_triggers_response = requests.get(apex_triggers_url, headers=headers)
            apex_triggers_response.raise_for_status()
            
            # Save apex triggers to blob storage
            apex_triggers_blob_name = f"{blob_prefix}/apex_triggers.json"
            blob_repo.save_json(apex_triggers_blob_name, apex_triggers_response.json())
            logger.info(f"Saved apex triggers to blob: {apex_triggers_blob_name}")
        except Exception as trig_e:
            logger.error(f"Error fetching or saving apex triggers: {str(trig_e)}")
            return False, f"Error fetching or saving apex triggers: {str(trig_e)}"
        
        logger.info("Successfully completed metadata retrieval and storage")
        return True, ""
    except Exception as e:
        error_message = f"Error fetching metadata: {str(e)}"
        logger.error(error_message)
        return False, error_message
