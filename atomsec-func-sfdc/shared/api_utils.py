"""
API Utilities Module

This module provides utilities for standardized API responses and documentation.

Best practices implemented:
- Standardized API response format
- Consistent error handling
- API documentation helpers
"""

import json
import logging
import azure.functions as func
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

# Configure module-level logger
logger = logging.getLogger(__name__)

def create_json_response(
    data: Any = None, 
    message: str = None, 
    status_code: int = 200, 
    error: str = None,
    metadata: Dict[str, Any] = None
) -> func.HttpResponse:
    """
    Create a standardized JSON response
    
    Args:
        data: Response data
        message: Response message
        status_code: HTTP status code
        error: Error message (if any)
        metadata: Additional metadata
        
    Returns:
        func.HttpResponse: HTTP response with JSON content
    """
    response_body = {
        "success": 200 <= status_code < 300,
        "timestamp": datetime.now().isoformat(),
    }
    
    if message:
        response_body["message"] = message
        
    if error:
        response_body["error"] = error
        
    if data is not None:
        response_body["data"] = data
        
    if metadata:
        response_body["metadata"] = metadata
        
    return func.HttpResponse(
        json.dumps(response_body, default=str),
        mimetype="application/json",
        status_code=status_code
    )

def create_error_response(
    error_message: str, 
    status_code: int = 500, 
    error_code: str = None,
    details: Any = None
) -> func.HttpResponse:
    """
    Create a standardized error response
    
    Args:
        error_message: Error message
        status_code: HTTP status code
        error_code: Error code (if any)
        details: Additional error details
        
    Returns:
        func.HttpResponse: HTTP response with JSON content
    """
    error_body = {
        "message": error_message
    }
    
    if error_code:
        error_body["code"] = error_code
        
    if details:
        error_body["details"] = details
        
    return create_json_response(
        message="Error occurred",
        error=error_body,
        status_code=status_code
    )

def handle_api_exception(e: Exception, status_code: int = 500) -> func.HttpResponse:
    """
    Handle API exceptions and return standardized error response
    
    Args:
        e: Exception
        status_code: HTTP status code
        
    Returns:
        func.HttpResponse: HTTP response with JSON content
    """
    error_message = str(e)
    logger.error(f"API error: {error_message}")
    
    return create_error_response(
        error_message=error_message,
        status_code=status_code
    )

# API Documentation Helpers

class ApiEndpoint:
    """API Endpoint documentation class"""
    
    def __init__(
        self,
        path: str,
        method: str,
        summary: str,
        description: str = None,
        request_params: List[Dict[str, Any]] = None,
        request_body: Dict[str, Any] = None,
        responses: Dict[int, Dict[str, Any]] = None,
        tags: List[str] = None
    ):
        self.path = path
        self.method = method.upper()
        self.summary = summary
        self.description = description or summary
        self.request_params = request_params or []
        self.request_body = request_body
        self.responses = responses or {
            200: {
                "description": "Success",
                "content": {
                    "application/json": {
                        "schema": {
                            "type": "object",
                            "properties": {
                                "success": {"type": "boolean", "example": True},
                                "timestamp": {"type": "string", "example": datetime.now().isoformat()},
                                "data": {"type": "object"}
                            }
                        }
                    }
                }
            }
        }
        self.tags = tags or []
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Swagger UI"""
        result = {
            "path": self.path,
            "method": self.method,
            "summary": self.summary,
            "description": self.description,
            "tags": self.tags,
            "parameters": self.request_params,
            "responses": self.responses
        }
        
        if self.request_body:
            result["requestBody"] = {
                "content": {
                    "application/json": {
                        "schema": self.request_body
                    }
                }
            }
            
        return result

class ApiDocumentation:
    """API Documentation class for Swagger UI"""
    
    def __init__(
        self,
        title: str,
        description: str,
        version: str,
        endpoints: List[ApiEndpoint] = None
    ):
        self.title = title
        self.description = description
        self.version = version
        self.endpoints = endpoints or []
        
    def add_endpoint(self, endpoint: ApiEndpoint) -> None:
        """Add an endpoint to the documentation"""
        self.endpoints.append(endpoint)
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to OpenAPI specification dictionary"""
        paths = {}
        
        for endpoint in self.endpoints:
            endpoint_dict = endpoint.to_dict()
            path = endpoint_dict.pop("path")
            method = endpoint_dict.pop("method").lower()
            
            if path not in paths:
                paths[path] = {}
                
            paths[path][method] = endpoint_dict
            
        return {
            "openapi": "3.0.0",
            "info": {
                "title": self.title,
                "description": self.description,
                "version": self.version
            },
            "paths": paths
        }
        
    def to_json(self) -> str:
        """Convert to OpenAPI specification JSON"""
        return json.dumps(self.to_dict(), default=str)
