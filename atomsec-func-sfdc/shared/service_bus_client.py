"""
Service Bus Client for Task Processing

This module provides a unified Service Bus client that replaces Azure Storage Queues
for task processing. It implements priority-based routing and proper error handling.
"""

import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from azure.servicebus import ServiceBusClient, ServiceBusMessage
from azure.servicebus.exceptions import ServiceBusError
from shared.common import is_local_dev

logger = logging.getLogger(__name__)

class ServiceBusEventPublisher:
    """Client for publishing events to Azure Service Bus"""
    
    def __init__(self):
        """Initialize the Service Bus client"""
        self.connection_string = self._get_connection_string()
        self.topic_name = "atomsec-events"
        self.client = None
        
        if self.connection_string:
            try:
                self.client = ServiceBusClient.from_connection_string(self.connection_string)
                logger.info("Service Bus client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Service Bus client: {str(e)}")
                self.client = None
    
    def _get_connection_string(self) -> Optional[str]:
        """Get Service Bus connection string from environment"""
        return os.environ.get('AzureServiceBusConnectionString')
    
    def publish_event(self, event_type: str, event_data: Dict[str, Any], 
                     user_id: Optional[str] = None, integration_id: Optional[str] = None) -> bool:
        """
        Publish an event to Service Bus
        
        Args:
            event_type: Type of event (e.g., 'TaskCreated', 'IntegrationUpdated')
            event_data: Event data payload
            user_id: Optional user ID for user-specific events
            integration_id: Optional integration ID for integration-specific events
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.client:
            logger.warning("Service Bus client not available, skipping event publishing")
            return False
        
        try:
            # Create event message
            event_message = {
                "eventType": event_type,
                "eventId": f"{event_type}_{datetime.now().isoformat()}",
                "timestamp": datetime.now().isoformat(),
                "userId": user_id,
                "integrationId": integration_id,
                "data": event_data
            }
            
            # Create Service Bus message
            message = ServiceBusMessage(
                body=json.dumps(event_message),
                content_type="application/json"
            )
            
            # Add custom properties
            message.application_properties = {
                "eventType": event_type,
                "userId": user_id or "",
                "integrationId": integration_id or "",
                "timestamp": event_message["timestamp"]
            }
            
            # Publish to topic
            with self.client.get_topic_sender(topic_name=self.topic_name) as sender:
                sender.send_messages(message)
            
            logger.info(f"Successfully published {event_type} event")
            return True
            
        except ServiceBusError as e:
            logger.error(f"Service Bus error publishing {event_type} event: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error publishing {event_type} event: {str(e)}")
            return False
    
    def publish_task_event(self, task_id: str, task_type: str, org_id: str, 
                          user_id: str, status: str, priority: str = "medium",
                          params: Optional[Dict[str, Any]] = None) -> bool:
        """
        Publish task-related events
        
        Args:
            task_id: Task ID
            task_type: Type of task
            org_id: Organization ID
            user_id: User ID
            status: Task status
            priority: Task priority
            params: Task parameters
            
        Returns:
            bool: True if successful, False otherwise
        """
        event_data = {
            "task_id": task_id,
            "task_type": task_type,
            "org_id": org_id,
            "user_id": user_id,
            "status": status,
            "priority": priority,
            "params": params or {}
        }
        
        return self.publish_event("TaskCreated", event_data, user_id, org_id)
    
    def publish_integration_event(self, integration_id: str, event_type: str,
                                 user_id: str, integration_data: Dict[str, Any]) -> bool:
        """
        Publish integration-related events
        
        Args:
            integration_id: Integration ID
            event_type: Type of event (IntegrationCreated, IntegrationUpdated, etc.)
            user_id: User ID
            integration_data: Integration data
            
        Returns:
            bool: True if successful, False otherwise
        """
        event_data = {
            "integration_id": integration_id,
            "integration_data": integration_data
        }
        
        return self.publish_event(event_type, event_data, user_id, integration_id)
    
    def publish_user_event(self, user_id: str, event_type: str, user_data: Dict[str, Any]) -> bool:
        """
        Publish user-related events
        
        Args:
            user_id: User ID
            event_type: Type of event (UserCreated, UserUpdated, etc.)
            user_data: User data
            
        Returns:
            bool: True if successful, False otherwise
        """
        event_data = {
            "user_id": user_id,
            "user_data": user_data
        }
        
        return self.publish_event(event_type, event_data, user_id)
    
    def publish_security_event(self, integration_id: str, event_type: str,
                              user_id: str, security_data: Dict[str, Any]) -> bool:
        """
        Publish security-related events
        
        Args:
            integration_id: Integration ID
            event_type: Type of event (SecurityScanCompleted, etc.)
            user_id: User ID
            security_data: Security scan data
            
        Returns:
            bool: True if successful, False otherwise
        """
        event_data = {
            "integration_id": integration_id,
            "security_data": security_data
        }
        
        return self.publish_event(event_type, event_data, user_id, integration_id)

class ServiceBusTaskClient:
    """Service Bus client for task processing with priority routing"""

    def __init__(self):
        """Initialize the Service Bus task client"""
        self.connection_string = os.getenv('AZURE_SERVICE_BUS_CONNECTION_STRING')
        self.topic_name = os.getenv('AZURE_SERVICE_BUS_TOPIC_NAME', 'atomsec-tasks')
        self.client = None
        
        # Priority to subscription mapping
        self.priority_subscriptions = {
            'high': 'sfdc-service-high',
            'medium': 'sfdc-service-medium', 
            'low': 'sfdc-service-low'
        }
        
        if self.connection_string:
            try:
                self.client = ServiceBusClient.from_connection_string(self.connection_string)
                logger.info("Service Bus task client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Service Bus task client: {str(e)}")
                self.client = None
        else:
            logger.warning("Service Bus connection string not found. Task client will not be available.")

    def send_task_message(self, task_data: Dict[str, Any], priority: str = 'medium', 
                         scheduled_time: Optional[datetime] = None) -> bool:
        """
        Send a task message to Service Bus with priority routing
        
        Args:
            task_data: Task data to send
            priority: Task priority (high, medium, low)
            scheduled_time: Optional scheduled execution time
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.client:
            logger.error("Service Bus client not available")
            return False
            
        try:
            # Create the message
            message_body = json.dumps(task_data)
            message = ServiceBusMessage(
                body=message_body,
                content_type="application/json"
            )
            
            # Add message properties for routing and filtering
            message.application_properties = {
                "messageType": "TaskMessage",
                "priority": priority,
                "taskType": task_data.get("task_type", "unknown"),
                "orgId": task_data.get("org_id", "unknown"),
                "taskId": task_data.get("task_id", "unknown")
            }
            
            # Add correlation ID for tracking
            message.correlation_id = task_data.get("task_id", "unknown")
            
            # Set scheduled enqueue time if provided
            if scheduled_time:
                message.scheduled_enqueue_time_utc = scheduled_time
                
            # Send to topic
            with self.client.get_topic_sender(topic_name=self.topic_name) as sender:
                sender.send_messages(message)
                
            logger.info(f"Sent task message {task_data.get('task_id')} with priority {priority}")
            return True
            
        except ServiceBusError as e:
            logger.error(f"Service Bus error sending task message: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending task message: {str(e)}")
            return False

    def create_subscriptions_if_not_exist(self) -> bool:
        """
        Create priority-based subscriptions if they don't exist

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.client:
            logger.error("Service Bus client not available")
            return False

        try:
            from azure.servicebus.management import ServiceBusAdministrationClient

            admin_client = ServiceBusAdministrationClient.from_connection_string(self.connection_string)

            # Create subscriptions for each priority level
            for priority, subscription_name in self.priority_subscriptions.items():
                try:
                    # Check if subscription exists
                    admin_client.get_subscription(self.topic_name, subscription_name)
                    logger.info(f"Subscription {subscription_name} already exists")
                except:
                    # Create subscription with priority filter
                    from azure.servicebus.management import CreateSubscriptionOptions, SqlRuleFilter

                    subscription_options = CreateSubscriptionOptions()
                    subscription_options.max_delivery_count = 3
                    subscription_options.lock_duration = timedelta(minutes=5)

                    admin_client.create_subscription(
                        topic_name=self.topic_name,
                        subscription_name=subscription_name,
                        options=subscription_options
                    )

                    # Create rule to filter by priority
                    rule_filter = SqlRuleFilter(f"priority = '{priority}'")
                    admin_client.create_rule(
                        topic_name=self.topic_name,
                        subscription_name=subscription_name,
                        rule_name=f"priority-{priority}-filter",
                        filter=rule_filter
                    )

                    logger.info(f"Created subscription {subscription_name} with priority filter")

            return True

        except Exception as e:
            logger.error(f"Error creating subscriptions: {str(e)}")
            return False

    def get_subscription_name_for_priority(self, priority: str) -> str:
        """Get subscription name for a given priority"""
        return self.priority_subscriptions.get(priority, self.priority_subscriptions['medium'])

# Global client instances
_service_bus_client = None
_service_bus_task_client = None

def get_service_bus_client() -> Optional[ServiceBusEventPublisher]:
    """Get or create Service Bus client instance"""
    global _service_bus_client
    if _service_bus_client is None:
        _service_bus_client = ServiceBusEventPublisher()
    return _service_bus_client

def get_service_bus_task_client():
    """Get the appropriate Service Bus task client instance (real or mock)"""
    if is_local_dev():
        # Use mock Service Bus for local development
        from shared.mock_service_bus import get_mock_service_bus_client
        return get_mock_service_bus_client()
    else:
        # Use real Service Bus for production
        global _service_bus_task_client
        if _service_bus_task_client is None:
            _service_bus_task_client = ServiceBusTaskClient()
        return _service_bus_task_client
