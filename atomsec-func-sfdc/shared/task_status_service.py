"""
Task Status Service Module

This module provides centralized management of task status for tracking
asynchronous and long-running tasks throughout their lifecycle.

Features:
- Create and manage task status records
- Track task progress and completion
- Support for task retry mechanisms
- Integration with execution logs
- Support for both local development and production environments
- Proper error handling and logging
- Clean code principles and best practices

Best practices implemented:
- Single responsibility principle
- Dependency injection
- Environment-aware configuration
- Comprehensive error handling
- Consistent logging patterns
- Type hints for better code clarity
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Union

# Import shared modules
from shared.azure_services import is_local_dev
from shared.data_access import TableStorageRepository, SqlDatabaseRepository
from shared.database_models_new import Task
from shared.common import is_local_dev

# Configure module-level logger
logger = logging.getLogger(__name__)

# Task status constants
TASK_STATUS_PENDING = "pending"
TASK_STATUS_RUNNING = "running"
TASK_STATUS_COMPLETED = "completed"
TASK_STATUS_FAILED = "failed"
TASK_STATUS_RETRY = "retry"
TASK_STATUS_CANCELLED = "cancelled"

# Task priority constants
TASK_PRIORITY_LOW = "low"
TASK_PRIORITY_MEDIUM = "medium"
TASK_PRIORITY_HIGH = "high"
TASK_PRIORITY_CRITICAL = "critical"

# Global repository instances (lazy initialized)
_task_status_table_repo = None
_task_status_sql_repo = None


class TaskStatusService:
    """
    Service class for managing task status and lifecycle
    
    This service provides a clean interface for creating, updating, and querying
    task status while abstracting the underlying storage mechanism.
    """
    
    def __init__(self):
        """Initialize the task status service"""
        self.table_repo = None
        self.sql_repo = None
        self._initialize_repositories()
    
    def _initialize_repositories(self) -> None:
        """Initialize the appropriate repository based on environment"""
        try:
            if is_local_dev():
                # Use Table Storage for local development
                self.table_repo = self._get_table_repository()
                logger.info("Initialized task status service with Table Storage for local development")
            else:
                # Use SQL Database for production
                self.sql_repo = self._get_sql_repository()
                logger.info("Initialized task status service with SQL Database for production")
        except Exception as e:
            logger.error(f"Error initializing task status service repositories: {str(e)}")
    
    def _get_table_repository(self) -> Optional[TableStorageRepository]:
        """Get or create Table Storage repository"""
        global _task_status_table_repo
        
        if _task_status_table_repo is None:
            try:
                _task_status_table_repo = TableStorageRepository("TaskStatus")
                logger.debug("Created Table Storage repository for task status")
            except Exception as e:
                logger.error(f"Failed to create Table Storage repository: {str(e)}")
                return None
        
        return _task_status_table_repo
    
    def _get_sql_repository(self) -> Optional[SqlDatabaseRepository]:
        """Get or create SQL Database repository"""
        global _task_status_sql_repo
        
        if _task_status_sql_repo is None:
            try:
                _task_status_sql_repo = SqlDatabaseRepository("Tasks")
                logger.debug("Created SQL Database repository for task status")
            except Exception as e:
                logger.error(f"Failed to create SQL Database repository: {str(e)}")
                return None
        
        return _task_status_sql_repo
    
    def create_task(
        self,
        task_type: str,
        org_id: Union[str, int],
        user_id: Union[str, int],
        priority: str = TASK_PRIORITY_MEDIUM,
        execution_log_id: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
        scheduled_time: Optional[datetime] = None
    ) -> Optional[str]:
        """
        Create a new task
        
        Args:
            task_type: Type of task (e.g., "Integration_Scan", "Data_Export")
            org_id: Organization ID
            user_id: User ID who created the task
            priority: Task priority (low, medium, high, critical)
            execution_log_id: Optional execution log ID to associate with
            params: Optional parameters for the task
            scheduled_time: Optional scheduled execution time
        
        Returns:
            str: Task ID if successful, None otherwise
        """
        try:
            # Generate unique task ID
            task_id = str(uuid.uuid4())
            
            # Convert IDs to appropriate types
            org_id_str = str(org_id)
            user_id_str = str(user_id)
            
            # Create task object
            task = Task(
                TaskId=task_id,
                TaskType=task_type,
                OrgId=org_id_str,
                UserId=user_id_str,
                Status=TASK_STATUS_PENDING,
                Priority=priority,
                Progress=0,
                Message="Task created",
                CreatedAt=datetime.now(),
                UpdatedAt=datetime.now(),
                ExecutionLogId=execution_log_id,
                Params=params,
                ScheduledTime=scheduled_time
            )
            
            if is_local_dev():
                return self._create_task_table(task)
            else:
                return self._create_task_sql(task)
                
        except Exception as e:
            logger.error(f"Error creating task: {str(e)}")
            return None
    
    def _create_task_table(self, task: Task) -> Optional[str]:
        """Create task in Table Storage"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return None
            
            # Create entity for Table Storage
            entity = {
                "PartitionKey": f"task_{task.OrgId}",
                "RowKey": task.TaskId,
                "TaskId": task.TaskId,
                "TaskType": task.TaskType,
                "OrgId": task.OrgId,
                "UserId": task.UserId,
                "Status": task.Status,
                "Priority": task.Priority,
                "Progress": task.Progress,
                "Message": task.Message,
                "Result": task.Result,
                "CreatedAt": task.CreatedAt.isoformat(),
                "UpdatedAt": task.UpdatedAt.isoformat(),
                "CompletedAt": task.CompletedAt.isoformat() if task.CompletedAt else None,
                "ScheduledTime": task.ScheduledTime.isoformat() if task.ScheduledTime else None,
                "RetryCount": task.RetryCount,
                "ExecutionLogId": task.ExecutionLogId,
                "Params": str(task.Params) if task.Params else None
            }
            
            success = self.table_repo.insert_entity(entity)
            if success:
                logger.info(f"Created task in Table Storage: {task.TaskId}")
                return task.TaskId
            else:
                logger.error(f"Failed to create task in Table Storage: {task.TaskId}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating task in Table Storage: {str(e)}")
            return None
    
    def _create_task_sql(self, task: Task) -> Optional[str]:
        """Create task in SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return None
            
            # Note: For SQL, we'll use a Tasks table that matches the Task model
            # This assumes the table exists with appropriate schema
            query = """
            INSERT INTO Tasks (TaskId, TaskType, OrgId, UserId, Status, Priority, Progress, 
                             Message, Result, CreatedAt, UpdatedAt, CompletedAt, ScheduledTime, 
                             RetryCount, ExecutionLogId, Params)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                task.TaskId,
                task.TaskType,
                task.OrgId,
                task.UserId,
                task.Status,
                task.Priority,
                task.Progress,
                task.Message,
                task.Result,
                task.CreatedAt.isoformat(),
                task.UpdatedAt.isoformat(),
                task.CompletedAt.isoformat() if task.CompletedAt else None,
                task.ScheduledTime.isoformat() if task.ScheduledTime else None,
                task.RetryCount,
                task.ExecutionLogId,
                str(task.Params) if task.Params else None
            )
            
            success = self.sql_repo.execute_non_query(query, params)
            if success:
                logger.info(f"Created task in SQL Database: {task.TaskId}")
                return task.TaskId
            else:
                logger.error(f"Failed to create task in SQL Database: {task.TaskId}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating task in SQL Database: {str(e)}")
            return None

    def update_task_status(
        self,
        task_id: str,
        status: str,
        progress: Optional[int] = None,
        message: Optional[str] = None,
        result: Optional[str] = None,
        error: Optional[str] = None
    ) -> bool:
        """
        Update task status and related fields

        Args:
            task_id: ID of the task to update
            status: New status
            progress: Optional progress percentage (0-100)
            message: Optional status message
            result: Optional result data
            error: Optional error message

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            completed_at = None
            if status in [TASK_STATUS_COMPLETED, TASK_STATUS_FAILED, TASK_STATUS_CANCELLED]:
                completed_at = datetime.now()
            if is_local_dev():
                # Query all entities and filter by RowKey since we don't know the org_id
                # Azure Table Storage doesn't support startswith in OData queries
                all_entities = list(self.table_repo.query_entities())
                
                # Find the entity with matching RowKey (task_id)
                target_entity = None
                for entity in all_entities:
                    if entity.get('RowKey') == task_id:
                        target_entity = entity
                        break
                
                if not target_entity:
                    logger.warning(f"Task not found: {task_id}")
                    return False
                
                target_entity['Status'] = status
                if progress is not None:
                    target_entity['Progress'] = progress
                if message:
                    target_entity['Message'] = message
                if result:
                    target_entity['Result'] = result
                if error:
                    target_entity['ErrorMessage'] = error
                if completed_at:
                    target_entity['CompletedAt'] = completed_at.isoformat()
                return self.table_repo.update_entity(target_entity)
            else:
                return self._update_task_status_sql(task_id, status, progress, message, result, error, completed_at)
        except Exception as e:
            logger.error(f"Error updating task status: {str(e)}")
            return False

    def _update_task_status_sql(
        self,
        task_id: str,
        status: str,
        progress: Optional[int],
        message: Optional[str],
        result: Optional[str],
        error: Optional[str],
        completed_at: Optional[datetime]
    ) -> bool:
        """Update task status in SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return False

            # Build dynamic update query based on provided parameters
            update_fields = ["Status = ?", "UpdatedAt = ?"]
            params = [status, datetime.now().isoformat()]

            if progress is not None:
                update_fields.append("Progress = ?")
                params.append(progress)
            if message is not None:
                update_fields.append("Message = ?")
                params.append(message)
            if result is not None:
                update_fields.append("Result = ?")
                params.append(result)
            if error is not None:
                # Append error to existing message
                current_message = message or ""
                error_message = f"{current_message}\nError: {error}".strip()
                if "Message = ?" not in update_fields:
                    update_fields.append("Message = ?")
                    params.append(error_message)
                else:
                    # Replace the last message parameter with the error-appended version
                    params[-1] = error_message
            if completed_at is not None:
                update_fields.append("CompletedAt = ?")
                params.append(completed_at.isoformat())

            # Add task_id as the last parameter for WHERE clause
            params.append(task_id)

            query = f"""
            UPDATE Tasks
            SET {', '.join(update_fields)}
            WHERE TaskId = ?
            """

            success = self.sql_repo.execute_non_query(query, tuple(params))
            if success:
                logger.info(f"Updated task status in SQL Database: {task_id} -> {status}")
                return True
            else:
                logger.error(f"Failed to update task status in SQL Database: {task_id}")
                return False

        except Exception as e:
            logger.error(f"Error updating task status in SQL Database: {str(e)}")
            return False

    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a task by ID

        Args:
            task_id: ID of the task to retrieve

        Returns:
            Dict[str, Any]: Task data if found, None otherwise
        """
        try:
            if is_local_dev():
                # Query all entities and filter by RowKey since we don't know the org_id
                # Azure Table Storage doesn't support startswith in OData queries
                all_entities = list(self.table_repo.query_entities())
                
                # Find the entity with matching RowKey (task_id)
                target_entity = None
                for entity in all_entities:
                    if entity.get('RowKey') == task_id:
                        target_entity = entity
                        break
                
                if target_entity:
                    logger.debug(f"Retrieved task from Table Storage: {task_id}")
                    return dict(target_entity)
                else:
                    logger.warning(f"Task not found in Table Storage: {task_id}")
                    return None
            else:
                return self._get_task_sql(task_id)
        except Exception as e:
            logger.error(f"Error getting task from Table Storage: {str(e)}")
            return None

    def _get_task_sql(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task from SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return None

            query = """
            SELECT TaskId, TaskType, OrgId, UserId, Status, Priority, Progress,
                   Message, Result, CreatedAt, UpdatedAt, CompletedAt, ScheduledTime,
                   RetryCount, ExecutionLogId, Params
            FROM Tasks
            WHERE TaskId = ?
            """
            params = (task_id,)

            results = self.sql_repo.execute_query(query, params)
            if results and len(results) > 0:
                row = results[0]
                task = {
                    "TaskId": row[0],
                    "TaskType": row[1],
                    "OrgId": row[2],
                    "UserId": row[3],
                    "Status": row[4],
                    "Priority": row[5],
                    "Progress": row[6],
                    "Message": row[7],
                    "Result": row[8],
                    "CreatedAt": row[9],
                    "UpdatedAt": row[10],
                    "CompletedAt": row[11],
                    "ScheduledTime": row[12],
                    "RetryCount": row[13],
                    "ExecutionLogId": row[14],
                    "Params": row[15]
                }
                logger.debug(f"Retrieved task from SQL Database: {task_id}")
                return task
            else:
                logger.warning(f"Task not found in SQL Database: {task_id}")
                return None

        except Exception as e:
            logger.error(f"Error getting task from SQL Database: {str(e)}")
            return None

    def get_tasks_by_org(
        self,
        org_id: Union[str, int],
        status_filter: Optional[str] = None,
        limit: int = 50,
        include_completed: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Get tasks for a specific organization

        Args:
            org_id: Organization ID
            status_filter: Optional status filter
            limit: Maximum number of tasks to return
            include_completed: Whether to include completed tasks

        Returns:
            List[Dict[str, Any]]: List of tasks
        """
        try:
            org_id_str = str(org_id)

            if is_local_dev():
                filter_query = f"PartitionKey eq 'task_{org_id}'"
                if status_filter:
                    filter_query += f" and Status eq '{status_filter}'"
                elif not include_completed:
                    filter_query += f" and Status ne '{TASK_STATUS_COMPLETED}' and Status ne '{TASK_STATUS_FAILED}' and Status ne '{TASK_STATUS_CANCELLED}'"
                entities = list(self.table_repo.query_entities(filter_query))
                sorted_entities = sorted(entities, key=lambda x: x.get("CreatedAt", ""), reverse=True)[:limit]
                logger.debug(f"Retrieved {len(sorted_entities)} tasks for org {org_id}")
                return [dict(entity) for entity in sorted_entities]
            else:
                return self._get_tasks_by_org_sql(org_id_str, status_filter, limit, include_completed)

        except Exception as e:
            logger.error(f"Error getting tasks by org: {str(e)}")
            return []

    def _get_tasks_by_org_sql(
        self,
        org_id: str,
        status_filter: Optional[str],
        limit: int,
        include_completed: bool
    ) -> List[Dict[str, Any]]:
        """Get tasks by organization from SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return []

            where_conditions = ["OrgId = ?"]
            params = [org_id]

            if status_filter:
                where_conditions.append("Status = ?")
                params.append(status_filter)
            elif not include_completed:
                where_conditions.append("Status NOT IN (?, ?, ?)")
                params.extend([TASK_STATUS_COMPLETED, TASK_STATUS_FAILED, TASK_STATUS_CANCELLED])

            where_clause = " AND ".join(where_conditions)

            query = f"""
            SELECT TOP {limit} TaskId, TaskType, OrgId, UserId, Status, Priority, Progress,
                   Message, Result, CreatedAt, UpdatedAt, CompletedAt, ScheduledTime,
                   RetryCount, ExecutionLogId, Params
            FROM Tasks
            WHERE {where_clause}
            ORDER BY CreatedAt DESC
            """

            results = self.sql_repo.execute_query(query, tuple(params))
            tasks = []

            for row in results:
                task = {
                    "TaskId": row[0],
                    "TaskType": row[1],
                    "OrgId": row[2],
                    "UserId": row[3],
                    "Status": row[4],
                    "Priority": row[5],
                    "Progress": row[6],
                    "Message": row[7],
                    "Result": row[8],
                    "CreatedAt": row[9],
                    "UpdatedAt": row[10],
                    "CompletedAt": row[11],
                    "ScheduledTime": row[12],
                    "RetryCount": row[13],
                    "ExecutionLogId": row[14],
                    "Params": row[15]
                }
                tasks.append(task)

            logger.debug(f"Retrieved {len(tasks)} tasks for org {org_id}")
            return tasks

        except Exception as e:
            logger.error(f"Error getting tasks from SQL Database: {str(e)}")
            return []

    def retry_task(self, task_id: str, max_retries: int = 3) -> bool:
        """
        Retry a failed task

        Args:
            task_id: ID of the task to retry
            max_retries: Maximum number of retries allowed

        Returns:
            bool: True if retry was initiated, False otherwise
        """
        try:
            # Get current task
            task = self.get_task(task_id)
            if not task:
                logger.warning(f"Task not found for retry: {task_id}")
                return False

            # Check if task can be retried
            current_retry_count = task.get("RetryCount", 0)
            if current_retry_count >= max_retries:
                logger.warning(f"Task {task_id} has exceeded maximum retries ({max_retries})")
                return False

            # Update task for retry
            new_retry_count = current_retry_count + 1
            retry_message = f"Retry attempt {new_retry_count}/{max_retries}"

            success = self.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RETRY,
                progress=0,
                message=retry_message
            )

            if success:
                # Update retry count separately
                if is_local_dev():
                    self._update_retry_count_table(task_id, new_retry_count)
                else:
                    self._update_retry_count_sql(task_id, new_retry_count)

                logger.info(f"Task {task_id} marked for retry (attempt {new_retry_count})")
                return True
            else:
                logger.error(f"Failed to update task status for retry: {task_id}")
                return False

        except Exception as e:
            logger.error(f"Error retrying task: {str(e)}")
            return False

    def _update_retry_count_table(self, task_id: str, retry_count: int) -> bool:
        """Update retry count in Table Storage"""
        try:
            if not self.table_repo:
                return False

            filter_query = f"RowKey eq '{task_id}'"
            entities = list(self.table_repo.query_entities(filter_query))

            if entities:
                entity = entities[0]
                entity["RetryCount"] = retry_count
                return self.table_repo.update_entity(entity)

            return False
        except Exception as e:
            logger.error(f"Error updating retry count in Table Storage: {str(e)}")
            return False

    def _update_retry_count_sql(self, task_id: str, retry_count: int) -> bool:
        """Update retry count in SQL Database"""
        try:
            if not self.sql_repo:
                return False

            query = "UPDATE Tasks SET RetryCount = ? WHERE TaskId = ?"
            params = (retry_count, task_id)

            return self.sql_repo.execute_non_query(query, params)
        except Exception as e:
            logger.error(f"Error updating retry count in SQL Database: {str(e)}")
            return False

    def cancel_task(self, task_id: str, reason: str = "Cancelled by user") -> bool:
        """
        Cancel a task

        Args:
            task_id: ID of the task to cancel
            reason: Reason for cancellation

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            return self.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_CANCELLED,
                message=reason
            )
        except Exception as e:
            logger.error(f"Error cancelling task: {str(e)}")
            return False

    def delete_task(self, task_id: str) -> bool:
        """
        Delete a task

        Args:
            task_id: ID of the task to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if is_local_dev():
                return self._delete_task_table(task_id)
            else:
                return self._delete_task_sql(task_id)

        except Exception as e:
            logger.error(f"Error deleting task: {str(e)}")
            return False

    def _delete_task_table(self, task_id: str) -> bool:
        """Delete task from Table Storage"""
        try:
            if not self.table_repo:
                logger.error("Table repository not available")
                return False

            # Find the entity first to get the partition key
            filter_query = f"RowKey eq '{task_id}'"
            entities = list(self.table_repo.query_entities(filter_query))

            if not entities:
                logger.warning(f"Task not found: {task_id}")
                return False

            entity = entities[0]
            success = self.table_repo.delete_entity(entity["PartitionKey"], entity["RowKey"])

            if success:
                logger.info(f"Deleted task from Table Storage: {task_id}")
                return True
            else:
                logger.error(f"Failed to delete task from Table Storage: {task_id}")
                return False

        except Exception as e:
            logger.error(f"Error deleting task from Table Storage: {str(e)}")
            return False

    def _delete_task_sql(self, task_id: str) -> bool:
        """Delete task from SQL Database"""
        try:
            if not self.sql_repo:
                logger.error("SQL repository not available")
                return False

            query = "DELETE FROM Tasks WHERE TaskId = ?"
            params = (task_id,)

            success = self.sql_repo.execute_non_query(query, params)

            if success:
                logger.info(f"Deleted task from SQL Database: {task_id}")
                return True
            else:
                logger.error(f"Failed to delete task from SQL Database: {task_id}")
                return False

        except Exception as e:
            logger.error(f"Error deleting task from SQL Database: {str(e)}")
            return False


# Global service instance
_task_status_service = None


def get_task_status_service() -> TaskStatusService:
    """
    Get the global task status service instance

    Returns:
        TaskStatusService: The service instance
    """
    global _task_status_service

    if _task_status_service is None:
        _task_status_service = TaskStatusService()
        logger.debug("Created global task status service instance")

    return _task_status_service
