"""
Azure Services Module

This module provides centralized access to Azure services with proper configuration
for both local development and production environments.

Best practices implemented:
- Environment-aware configuration
- Centralized credential management
- Proper error handling and logging
- Support for local development with Azurite
- Key Vault integration for secrets
"""

import os
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

# Import Azure modules with error handling
try:
    from azure.identity import DefaultAzureCredential, AzureCliCredential
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("Azure Identity package not installed. Install with: pip install azure-identity")
    # Create dummy classes to prevent errors
    class DefaultAzureCredential:
        def __init__(self, *args, **kwargs):
            pass
    class AzureCliCredential:
        def __init__(self, *args, **kwargs):
            pass

try:
    from azure.keyvault.secrets import SecretClient
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("Azure KeyVault Secrets package not installed. Install with: pip install azure-keyvault-secrets")
    # Create dummy class
    class SecretClient:
        def __init__(self, *args, **kwargs):
            pass
        def get_secret(self, *args, **kwargs):
            return None
        def set_secret(self, *args, **kwargs):
            return None

# Optional Key Vault Management functionality
KEY_VAULT_MGMT_AVAILABLE = False
try:
    from azure.mgmt.keyvault import KeyVaultManagementClient
    from azure.mgmt.keyvault.models import (VaultCreateOrUpdateParameters, VaultProperties,
                                        Sku, SkuName, AccessPolicyEntry, Permissions,
                                        SecretPermissions, KeyPermissions, CertificatePermissions,
                                        StoragePermissions)
    KEY_VAULT_MGMT_AVAILABLE = True
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("Azure KeyVault Management package not installed. Install with: pip install azure-mgmt-keyvault")
    # Create dummy classes
    class KeyVaultManagementClient:
        def __init__(self, *args, **kwargs):
            pass

try:
    from azure.storage.blob import BlobServiceClient
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("Azure Storage Blob package not installed. Install with: pip install azure-storage-blob")
    # Create dummy class
    class BlobServiceClient:
        @classmethod
        def from_connection_string(cls, *args, **kwargs):
            return cls()
        def __init__(self, *args, **kwargs):
            pass

try:
    from azure.storage.queue import QueueServiceClient
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("Azure Storage Queue package not installed. Install with: pip install azure-storage-queue")
    # Create dummy class
    class QueueServiceClient:
        @classmethod
        def from_connection_string(cls, *args, **kwargs):
            return cls()
        def __init__(self, *args, **kwargs):
            pass

try:
    from azure.data.tables import TableServiceClient
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("Azure Data Tables package not installed. Install with: pip install azure-data-tables")
    # Create dummy class
    class TableServiceClient:
        @classmethod
        def from_connection_string(cls, *args, **kwargs):
            return cls()
        def __init__(self, *args, **kwargs):
            pass

# No need for Cosmos DB as we're using MS SQL DB

# Configure module-level logger
logger = logging.getLogger(__name__)

# Import common functions
from shared.common import is_local_dev, is_test_env

# Global client instances
_key_vault_client = None

# Credential management
def get_credential():
    """Get the appropriate credential based on environment"""
    if is_local_dev():
        # For local development, use AzureCliCredential or a mock credential
        logger.info("Using development credential for local environment")

        # For Azurite, we don't need a real credential
        if os.environ.get("USE_LOCAL_STORAGE") == "true":
            # Create a simple mock credential for Azurite
            from unittest.mock import MagicMock
            mock_credential = MagicMock()
            mock_credential.get_token = lambda *args, **kwargs: MagicMock(token="mock_token")
            return mock_credential

        # Otherwise try AzureCliCredential
        try:
            return AzureCliCredential()
        except Exception as e:
            logger.warning(f"Failed to get AzureCliCredential: {str(e)}. Falling back to DefaultAzureCredential")
            return DefaultAzureCredential()
    else:
        # For production, use DefaultAzureCredential
        logger.info("Using DefaultAzureCredential for production")
        return DefaultAzureCredential()

def get_key_vault_client():
    """
    Get Azure Key Vault client
    
    Returns:
        SecretClient or None
    """
    global _key_vault_client
    
    if _key_vault_client is None:
        try:
            key_vault_url = os.environ.get('KEY_VAULT_URL')
            if key_vault_url:
                credential = get_credential()
                _key_vault_client = SecretClient(vault_url=key_vault_url, credential=credential)
                logger.info("Key Vault client initialized successfully")
            else:
                logger.warning("KEY_VAULT_URL environment variable not found")
        except Exception as e:
            logger.error(f"Failed to initialize Key Vault client: {str(e)}")
    
    return _key_vault_client

# Key Vault Management
def get_keyvault_mgmt_client():
    """Get a Key Vault Management client for creating and managing Key Vaults"""
    if not KEY_VAULT_MGMT_AVAILABLE:
        logger.error("Key Vault Management functionality is not available. Install with: pip install azure-mgmt-keyvault")
        raise ImportError("azure-mgmt-keyvault package is required for this operation")

    credential = get_credential()
    subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
    if not subscription_id:
        logger.warning("AZURE_SUBSCRIPTION_ID not found in environment variables")
        # Try to get from Azure CLI
        try:
            import subprocess
            import json
            result = subprocess.run(
                ["az", "account", "show", "--query", "id", "-o", "tsv"],
                capture_output=True,
                text=True,
                check=True
            )
            subscription_id = result.stdout.strip()
            logger.info(f"Retrieved subscription ID from Azure CLI: {subscription_id}")
        except Exception as e:
            logger.error(f"Failed to get subscription ID from Azure CLI: {str(e)}")
            raise ValueError("AZURE_SUBSCRIPTION_ID not found and could not be retrieved from Azure CLI")

    return KeyVaultManagementClient(credential, subscription_id)

def create_key_vault_if_not_exists(vault_name, resource_group_name, location="eastus"):
    """Create a Key Vault if it doesn't exist

    Args:
        vault_name: Name of the Key Vault
        resource_group_name: Name of the resource group
        location: Azure region (default: eastus)

    Returns:
        str: Key Vault URI
    """
    if not KEY_VAULT_MGMT_AVAILABLE:
        logger.error("Key Vault Management functionality is not available. Install with: pip install azure-mgmt-keyvault")
        raise ImportError("azure-mgmt-keyvault package is required for this operation")

    try:
        # Get Key Vault Management client
        client = get_keyvault_mgmt_client()

        # Check if Key Vault exists
        try:
            vault = client.vaults.get(resource_group_name, vault_name)
            logger.info(f"Key Vault '{vault_name}' already exists")
            return f"https://{vault_name}.vault.azure.net/"
        except Exception:
            logger.info(f"Key Vault '{vault_name}' does not exist, creating...")

        # Get object ID of the current user/service principal
        object_id = get_current_object_id()

        # Create Key Vault parameters
        parameters = VaultCreateOrUpdateParameters(
            location=location,
            properties=VaultProperties(
                tenant_id=os.environ.get("AZURE_TENANT_ID"),
                sku=Sku(name=SkuName.standard),
                access_policies=[AccessPolicyEntry(
                    tenant_id=os.environ.get("AZURE_TENANT_ID"),
                    object_id=object_id,
                    permissions=Permissions(
                        secrets=[SecretPermissions.get, SecretPermissions.list,
                                SecretPermissions.set, SecretPermissions.delete],
                        keys=[KeyPermissions.get, KeyPermissions.list,
                             KeyPermissions.create, KeyPermissions.delete],
                        certificates=[CertificatePermissions.get, CertificatePermissions.list,
                                     CertificatePermissions.create, CertificatePermissions.delete],
                        storage=[StoragePermissions.get, StoragePermissions.list,
                                StoragePermissions.set, StoragePermissions.delete]
                    )
                )],
                enabled_for_deployment=True,
                enabled_for_disk_encryption=True,
                enabled_for_template_deployment=True
            )
        )

        # Create Key Vault
        vault = client.vaults.begin_create_or_update(
            resource_group_name,
            vault_name,
            parameters
        ).result()

        logger.info(f"Key Vault '{vault_name}' created successfully")
        return f"https://{vault_name}.vault.azure.net/"
    except Exception as e:
        logger.error(f"Error creating Key Vault '{vault_name}': {str(e)}")
        raise

def get_current_object_id():
    """Get the object ID of the current user/service principal"""
    try:
        # For local development, return a mock object ID
        if is_local_dev():
            return "00000000-0000-0000-0000-000000000000"
        
        # In production, get from Azure CLI or environment
        try:
            import subprocess
            import json
            result = subprocess.run(
                ["az", "ad", "signed-in-user", "show", "--query", "id", "-o", "tsv"],
                capture_output=True,
                text=True,
                check=True
            )
            object_id = result.stdout.strip()
            logger.info(f"Retrieved object ID from Azure CLI: {object_id}")
            return object_id
        except Exception as e:
            logger.error(f"Failed to get object ID from Azure CLI: {str(e)}")
            # Try to get from environment variable
            object_id = os.environ.get("AZURE_OBJECT_ID")
            if object_id:
                return object_id
            raise ValueError("Could not retrieve object ID from Azure CLI or environment")
            
    except Exception as e:
        logger.error(f"Error getting current object ID: {str(e)}")
        raise

def get_current_user_id(req) -> Optional[str]:
    """
    Extract current user ID from request headers
    
    Args:
        req: HTTP request object
        
    Returns:
        str: User ID or None if not found
    """
    try:
        # Try to get user ID from Authorization header (JWT token)
        auth_header = req.headers.get('Authorization', '')
        if auth_header.startswith('Bearer '):
            token = auth_header[7:]  # Remove 'Bearer ' prefix
            # In a real implementation, you would decode and validate the JWT token
            # For now, we'll extract user ID from a custom header
            return req.headers.get('X-User-ID')
        
        # Try to get user ID from custom header
        user_id = req.headers.get('X-User-ID')
        if user_id:
            return user_id
        
        # Try to get user ID from query parameters (for testing)
        user_id = req.params.get('user_id')
        if user_id:
            return user_id
        
        # For local development, return a default user ID
        if is_local_dev():
            return "local-dev-user"
        
        return None
        
    except Exception as e:
        logger.error(f"Error extracting user ID: {str(e)}")
        return None

def get_user_organization_id(req) -> Optional[str]:
    """
    Extract user's organization ID from request headers
    
    Args:
        req: HTTP request object
        
    Returns:
        str: Organization ID or None if not found
    """
    try:
        # Try to get organization ID from custom header
        org_id = req.headers.get('X-Organization-ID')
        if org_id:
            return org_id
        
        # Try to get organization ID from query parameters (for testing)
        org_id = req.params.get('org_id')
        if org_id:
            return org_id
        
        # For local development, return a default organization ID
        if is_local_dev():
            return "local-dev-org"
        
        return None
        
    except Exception as e:
        logger.error(f"Error extracting organization ID: {str(e)}")
        return None

def validate_user_permission(user_id: str, resource_type: str, resource_id: str, permission: str) -> bool:
    """
    Validate user permission for a specific resource
    
    Args:
        user_id: User ID
        resource_type: Type of resource (e.g., 'integration', 'account')
        resource_id: Resource ID
        permission: Permission to check (e.g., 'read', 'write', 'delete')
        
    Returns:
        bool: True if user has permission, False otherwise
    """
    try:
        # In a real implementation, you would check user permissions in the database
        # For now, we'll implement a simple permission check
        
        # For local development, allow all permissions
        if is_local_dev():
            return True
        
        # TODO: Implement proper permission checking logic
        # This would involve:
        # 1. Getting user's role and permissions from database
        # 2. Checking if the role has the required permission for the resource type
        # 3. Checking if the user has access to the specific resource
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating user permission: {str(e)}")
        return False

def add_access_policy(vault_name, resource_group_name, object_id, tenant_id=None):
    """Add an access policy to a Key Vault

    Args:
        vault_name: Name of the Key Vault
        resource_group_name: Name of the resource group
        object_id: Object ID of the user or service principal
        tenant_id: Tenant ID (default: from environment)

    Returns:
        bool: True if successful, False otherwise
    """
    if not KEY_VAULT_MGMT_AVAILABLE:
        logger.error("Key Vault Management functionality is not available. Install with: pip install azure-mgmt-keyvault")
        raise ImportError("azure-mgmt-keyvault package is required for this operation")

    try:
        # Get Key Vault Management client
        client = get_keyvault_mgmt_client()

        # Get current vault
        vault = client.vaults.get(resource_group_name, vault_name)

        # Use environment tenant ID if not provided
        if not tenant_id:
            tenant_id = os.environ.get("AZURE_TENANT_ID")

        # Create access policy
        access_policy = AccessPolicyEntry(
            tenant_id=tenant_id,
            object_id=object_id,
            permissions=Permissions(
                secrets=[SecretPermissions.get, SecretPermissions.list,
                        SecretPermissions.set, SecretPermissions.delete],
                keys=[KeyPermissions.get, KeyPermissions.list,
                     KeyPermissions.create, KeyPermissions.delete],
                certificates=[CertificatePermissions.get, CertificatePermissions.list,
                             CertificatePermissions.create, CertificatePermissions.delete],
                storage=[StoragePermissions.get, StoragePermissions.list,
                        StoragePermissions.set, StoragePermissions.delete]
            )
        )

        # Add access policy to vault
        vault.properties.access_policies.append(access_policy)

        # Update vault
        parameters = VaultCreateOrUpdateParameters(
            location=vault.location,
            properties=vault.properties
        )

        client.vaults.begin_create_or_update(
            resource_group_name,
            vault_name,
            parameters
        ).result()

        logger.info(f"Access policy added for object ID '{object_id}' to Key Vault '{vault_name}'")
        return True
    except Exception as e:
        logger.error(f"Error adding access policy to Key Vault '{vault_name}': {str(e)}")
        return False

# Key Vault Secret Client
def get_keyvault_client(vault_url=None):
    """Get a Key Vault client with appropriate configuration"""
    if not vault_url:
        from shared.config import get_key_vault_url
        vault_url = get_key_vault_url()

    credential = get_credential()
    return SecretClient(vault_url=vault_url, credential=credential)

def get_secret(secret_name, vault_url=None):
    """Get a secret from Key Vault or local storage

    Args:
        secret_name: Name of the secret
        vault_url: Optional Key Vault URL

    Returns:
        str: Secret value or None if not found
    """
    try:
        logger.info(f"Attempting to retrieve secret: {secret_name}")

        # Check if we're in local development mode
        if is_local_dev():
            logger.info(f"Local development environment detected, retrieving secret from local storage: {secret_name}")

            # For local development, retrieve from Azure Table Storage
            try:
                # Parse the secret name to get the service name and secret type
                # Format is typically: {service_name}-{secret_type}
                # For Salesforce credentials, the format is: salesforce-{integration_id}-client-id or salesforce-{integration_id}-client-secret

                # Check if this is a Salesforce credential
                if secret_name.startswith('salesforce-'):
                    # For Salesforce credentials, the partition key is salesforce-{integration_id}
                    # and the row key is client-id, client-secret, username, or private-key
                    if '-client-id' in secret_name:
                        service_name = secret_name.replace('-client-id', '')
                        secret_type = 'client-id'
                    elif '-client-secret' in secret_name:
                        service_name = secret_name.replace('-client-secret', '')
                        secret_type = 'client-secret'
                    elif '-username' in secret_name:
                        service_name = secret_name.replace('-username', '')
                        secret_type = 'username'
                    elif '-private-key' in secret_name:
                        service_name = secret_name.replace('-private-key', '')
                        secret_type = 'private-key'
                    else:
                        # For other Salesforce secrets, use the standard parsing
                        parts = secret_name.rsplit('-', 1)
                        if len(parts) == 2:
                            service_name = parts[0]
                            secret_type = parts[1]
                        else:
                            service_name = secret_name
                            secret_type = 'client-id'  # Default
                else:
                    # For other types of secrets, use the standard parsing
                    parts = secret_name.rsplit('-', 1)
                    if len(parts) == 2:
                        service_name = parts[0]
                        secret_type = parts[1]  # Usually 'client-id' or 'client-secret'
                    else:
                        service_name = secret_name
                        secret_type = 'client-id'  # Default

                logger.info(f"Parsed secret name into service_name={service_name}, secret_type={secret_type}")

                # Get table service client
                from azure.data.tables import TableServiceClient
                from shared.config import get_storage_connection_string

                connection_string = get_storage_connection_string()
                if not connection_string:
                    logger.error("Failed to get storage connection string")
                    return None

                logger.info("Got storage connection string, creating TableServiceClient")
                table_service = TableServiceClient.from_connection_string(connection_string)

                # Get credentials table
                credentials_table_name = "Credentials"
                logger.info(f"Getting table client for {credentials_table_name}")

                # Check if table exists
                tables = [table.name for table in table_service.list_tables()]
                if credentials_table_name not in tables:
                    logger.error(f"Table {credentials_table_name} does not exist. Available tables: {tables}")
                    return None

                table_client = table_service.get_table_client(credentials_table_name)

                # Query for the secret - try different formats
                try:
                    # First try the format used in the store_client_credentials function
                    try:
                        logger.info(f"Trying to get entity with PartitionKey={service_name}, RowKey={secret_type}")
                        entity = table_client.get_entity(service_name, secret_type)
                        logger.info(f"Successfully retrieved secret for {service_name} from local storage")
                        return entity.get("Value")
                    except Exception as e1:
                        logger.info(f"Secret not found with format (service_name={service_name}, secret_type={secret_type}), trying alternative format: {str(e1)}")

                        # Try the format where partition key is the service name and row key is client-id or client-secret
                        # This is the format actually used in the database according to the logs
                        if secret_type == 'client-id' or secret_type == 'client-secret':
                            # The secret name is already in the correct format
                            row_key = secret_type
                        else:
                            # Convert id to client-id and secret to client-secret
                            row_key = f"client-{secret_type}" if secret_type in ['id', 'secret'] else secret_type

                        try:
                            logger.info(f"Trying format: PartitionKey={service_name}, RowKey={row_key}")
                            entity = table_client.get_entity(service_name, row_key)
                            logger.info(f"Successfully retrieved secret using format (service_name={service_name}, row_key={row_key})")
                            return entity.get("Value")
                        except Exception as e2:
                            logger.warning(f"Secret not found with format (service_name={service_name}, row_key={row_key}): {str(e2)}")

                        # Try alternative format where partition key includes '-client'
                        if secret_type == 'id' or secret_type == 'secret':
                            alt_partition_key = f"{service_name}-client"
                            alt_row_key = secret_type
                            try:
                                logger.info(f"Trying alternative format: PartitionKey={alt_partition_key}, RowKey={alt_row_key}")
                                entity = table_client.get_entity(alt_partition_key, alt_row_key)
                                logger.info(f"Successfully retrieved secret for {alt_partition_key} from local storage")
                                return entity.get("Value")
                            except Exception as e3:
                                logger.warning(f"Secret not found in alternative format either: {str(e3)}")

                        # Try listing all entities to see what's available
                        try:
                            logger.info("Listing all entities in the Credentials table to find the secret")
                            entities = list(table_client.query_entities(f"PartitionKey eq '{service_name}'"))
                            if entities:
                                logger.info(f"Found {len(entities)} entities with PartitionKey={service_name}")
                                for entity in entities:
                                    logger.info(f"Entity: PartitionKey={entity.get('PartitionKey')}, RowKey={entity.get('RowKey')}")
                                    if secret_type in entity.get('RowKey', ''):
                                        logger.info(f"Found matching entity: {entity}")
                                        return entity.get("Value")
                            else:
                                logger.warning(f"No entities found with PartitionKey={service_name}")

                                # Try a broader search
                                all_entities = list(table_client.list_entities())
                                logger.info(f"Found {len(all_entities)} total entities in the table")
                                for entity in all_entities:
                                    pk = entity.get('PartitionKey', '')
                                    rk = entity.get('RowKey', '')
                                    logger.info(f"Entity: PartitionKey={pk}, RowKey={rk}")
                                    if service_name in pk and secret_type in rk:
                                        logger.info(f"Found potential match: {entity}")
                                        return entity.get("Value")
                        except Exception as e4:
                            logger.warning(f"Error listing entities: {str(e4)}")

                        # If all attempts fail, raise the original exception
                        raise e1
                except Exception as e:
                    logger.warning(f"Secret not found in local storage: {str(e)}")
                    return None
            except Exception as local_e:
                logger.error(f"Error retrieving secret from local storage: {str(local_e)}")
                return None
        else:
            # For production, retrieve from Key Vault
            logger.info(f"Production environment detected, retrieving secret from Key Vault: {secret_name}")
            client = get_keyvault_client(vault_url)
            secret_value = client.get_secret(secret_name).value
            logger.info(f"Successfully retrieved secret from Key Vault: {secret_name}")
            return secret_value
    except Exception as e:
        logger.error(f"Error retrieving secret '{secret_name}': {str(e)}")
        return None

def set_secret(secret_name, secret_value, vault_url=None, content_type=None, tags=None):
    """Set a secret in Key Vault with enhanced security

    Args:
        secret_name: Name of the secret
        secret_value: Value of the secret
        vault_url: Optional Key Vault URL
        content_type: Optional content type for the secret
        tags: Optional tags for the secret

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        client = get_keyvault_client(vault_url)

        # Add default content type if not provided
        if not content_type:
            content_type = "text/plain"

        # Add default tags if not provided
        if not tags:
            tags = {
                "created_by": "atomsec-application",
                "created_at": datetime.now().isoformat(),
                "purpose": "application-secret"
            }

        # Set the secret with content type and tags
        client.set_secret(secret_name, secret_value, content_type=content_type, tags=tags)
        logger.info(f"Successfully set secret '{secret_name}' in Key Vault")
        return True
    except Exception as e:
        logger.error(f"Error setting secret '{secret_name}': {str(e)}")
        return False

def store_client_credentials(client_id, client_secret=None, service_name="default", vault_url=None, use_jwt=False, username=None, private_key=None, user_id=None):
    """Store client credentials in Key Vault with enhanced security

    Args:
        client_id: Client ID
        client_secret: Client secret (for client credentials flow)
        service_name: Service name (default: default)
        vault_url: Key Vault URL (optional)
        use_jwt: Whether to use JWT flow (default: False)
        username: Username for JWT flow
        private_key: Private key for JWT flow
        user_id: User ID for security validation

    Returns:
        Dict: Result of the operation
    """
    try:
        if is_local_dev():
            logger.info(f"Local development environment detected, storing credentials in local storage for {service_name}")

            # For local development, store credentials in Azure Table Storage
            try:
                # Extract integration ID from service name (format: salesforce-{integration_id})
                integration_id = service_name.split('-', 1)[1] if '-' in service_name else service_name

                # Create a table storage repository for credentials
                from azure.data.tables import TableServiceClient, TableClient
                from shared.config import get_storage_connection_string

                # Get table service client
                connection_string = get_storage_connection_string()
                table_service = TableServiceClient.from_connection_string(connection_string)

                # Create credentials table if it doesn't exist
                credentials_table_name = "Credentials"
                table_service.create_table_if_not_exists(credentials_table_name)
                table_client = table_service.get_table_client(credentials_table_name)

                # Store client ID with user ID
                client_id_entity = {
                    "PartitionKey": service_name,
                    "RowKey": "client-id",
                    "Value": client_id,
                    "CreatedAt": datetime.now().isoformat(),
                    "AuthFlow": "jwt" if use_jwt else "client_credentials",
                    "UserId": user_id  # Store user ID for security
                }
                table_client.upsert_entity(client_id_entity)

                # Store client secret if provided
                if client_secret:
                    client_secret_entity = {
                        "PartitionKey": service_name,
                        "RowKey": "client-secret",
                        "Value": client_secret,
                        "CreatedAt": datetime.now().isoformat(),
                        "UserId": user_id  # Store user ID for security
                    }
                    table_client.upsert_entity(client_secret_entity)

                # Store JWT credentials if using JWT flow
                if use_jwt and username and private_key:
                    jwt_username_entity = {
                        "PartitionKey": service_name,
                        "RowKey": "jwt-username",
                        "Value": username,
                        "CreatedAt": datetime.now().isoformat(),
                        "UserId": user_id  # Store user ID for security
                    }
                    table_client.upsert_entity(jwt_username_entity)

                    jwt_private_key_entity = {
                        "PartitionKey": service_name,
                        "RowKey": "jwt-private-key",
                        "Value": private_key,
                        "CreatedAt": datetime.now().isoformat(),
                        "UserId": user_id  # Store user ID for security
                    }
                    table_client.upsert_entity(jwt_private_key_entity)

                # Create result dictionary
                result = {
                    "success": True,
                    "client_id_secret_name": f"{service_name}-client-id",
                    "message": f"Client credentials for '{service_name}' stored successfully in local storage",
                    "storage_type": "local_table",
                    "auth_flow": "jwt" if use_jwt else "client_credentials",
                    "user_id": user_id  # Include user ID in result
                }

                return result

            except Exception as e:
                logger.error(f"Error storing credentials in local storage: {str(e)}")
                return {
                    "success": False,
                    "error": str(e),
                    "storage_type": "local_table"
                }
        else:
            # For production, store in Key Vault
            logger.info(f"Production environment detected, storing credentials in Key Vault for {service_name}")

            # Create secret names with service name prefix
            client_id_secret_name = f"{service_name}-client-id"

            # Create tags with metadata
            auth_flow = "jwt" if use_jwt else "client-credentials"
            tags = {
                "service": service_name,
                "created_by": "atomsec-application",
                "created_at": datetime.now().isoformat(),
                "secret_type": auth_flow,
                "auth_flow": auth_flow
            }

            # Store client ID with appropriate content type and tags
            client_id_success = set_secret(
                client_id_secret_name,
                client_id,
                vault_url,
                content_type="application/x-client-id",
                tags=tags
            )

            # Create result dictionary
            result = {
                "success": True,
                "client_id_secret_name": client_id_secret_name,
                "message": f"Client credentials for '{service_name}' stored successfully in Key Vault",
                "storage_type": "key_vault",
                "auth_flow": auth_flow
            }

            # Track success of all operations
            all_success = client_id_success
            failed_operations = [] if client_id_success else ["client_id"]

            if use_jwt:
                # Store username for JWT flow
                if username:
                    username_secret_name = f"{service_name}-username"
                    username_success = set_secret(
                        username_secret_name,
                        username,
                        vault_url,
                        content_type="application/x-username",
                        tags=tags
                    )
                    all_success = all_success and username_success
                    if username_success:
                        result["username_secret_name"] = username_secret_name
                    else:
                        failed_operations.append("username")

                # Store private key for JWT flow
                if private_key:
                    private_key_secret_name = f"{service_name}-private-key"
                    private_key_success = set_secret(
                        private_key_secret_name,
                        private_key,
                        vault_url,
                        content_type="application/x-private-key",
                        tags=tags
                    )
                    all_success = all_success and private_key_success
                    if private_key_success:
                        result["private_key_secret_name"] = private_key_secret_name
                    else:
                        failed_operations.append("private_key")
            else:
                # Store client secret for Client Credentials flow
                if client_secret:
                    client_secret_secret_name = f"{service_name}-client-secret"
                    client_secret_success = set_secret(
                        client_secret_secret_name,
                        client_secret,
                        vault_url,
                        content_type="application/x-client-secret",
                        tags=tags
                    )
                    all_success = all_success and client_secret_success
                    if client_secret_success:
                        result["client_secret_secret_name"] = client_secret_secret_name
                    else:
                        failed_operations.append("client_secret")

            if all_success:
                return result
            else:
                # Try to clean up if some operations succeeded but others failed
                try:
                    client = get_keyvault_client(vault_url)

                    # Clean up client ID if it succeeded but other operations failed
                    if client_id_success and "client_id" not in failed_operations:
                        try:
                            client.begin_delete_secret(client_id_secret_name).result()
                            logger.info(f"Cleaned up client ID secret '{client_id_secret_name}' after failure")
                        except Exception as e:
                            logger.warning(f"Failed to clean up client ID secret '{client_id_secret_name}': {str(e)}")

                    # Clean up username if it succeeded but other operations failed
                    if use_jwt and "username" not in failed_operations and "username_secret_name" in result:
                        try:
                            client.begin_delete_secret(result["username_secret_name"]).result()
                            logger.info(f"Cleaned up username secret '{result['username_secret_name']}' after failure")
                        except Exception as e:
                            logger.warning(f"Failed to clean up username secret '{result['username_secret_name']}': {str(e)}")

                    # Clean up private key if it succeeded but other operations failed
                    if use_jwt and "private_key" not in failed_operations and "private_key_secret_name" in result:
                        try:
                            client.begin_delete_secret(result["private_key_secret_name"]).result()
                            logger.info(f"Cleaned up private key secret '{result['private_key_secret_name']}' after failure")
                        except Exception as e:
                            logger.warning(f"Failed to clean up private key secret '{result['private_key_secret_name']}': {str(e)}")

                    # Clean up client secret if it succeeded but other operations failed
                    if not use_jwt and "client_secret" not in failed_operations and "client_secret_secret_name" in result:
                        try:
                            client.begin_delete_secret(result["client_secret_secret_name"]).result()
                            logger.info(f"Cleaned up client secret '{result['client_secret_secret_name']}' after failure")
                        except Exception as e:
                            logger.warning(f"Failed to clean up client secret '{result['client_secret_secret_name']}': {str(e)}")
                except Exception as e:
                    logger.warning(f"Error during cleanup after failed credential storage: {str(e)}")

                return {
                    "success": False,
                    "message": f"Failed to store credentials for '{service_name}' in Key Vault. Failed operations: {', '.join(failed_operations)}"
                }
    except Exception as e:
        logger.error(f"Error in store_client_credentials: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# Blob Storage client
def get_blob_client(connection_string=None):
    """Get a Blob Storage client with appropriate configuration"""
    # Check if we're running in a test environment
    if is_test_env():
        # For tests, return a mock client
        logger.info("Running in test environment, returning mock BlobServiceClient")
        from unittest.mock import MagicMock
        mock_client = MagicMock()
        # Set up any necessary mock behaviors
        return mock_client
    elif not connection_string:
        # Get connection string from configuration
        from shared.config import get_storage_connection_string
        connection_string = get_storage_connection_string()
        if is_local_dev():
            # No need to modify the connection string as it already includes BlobEndpoint
            logger.info("Using Azurite for Blob Storage")

    return BlobServiceClient.from_connection_string(connection_string)

# Queue Storage client
def get_queue_client(connection_string=None):
    """Get a Queue Storage client with appropriate configuration"""
    # Check if we're running in a test environment
    if is_test_env():
        # For tests, return a mock client
        logger.info("Running in test environment, returning mock QueueServiceClient")
        from unittest.mock import MagicMock
        mock_client = MagicMock()
        # Set up any necessary mock behaviors
        return mock_client
    elif not connection_string:
        # Get connection string from configuration
        from shared.config import get_storage_connection_string
        connection_string = get_storage_connection_string()
        if is_local_dev():
            # No need to modify the connection string as it already includes QueueEndpoint
            logger.info("Using Azurite for Queue Storage")

    return QueueServiceClient.from_connection_string(connection_string)

# Table Storage client
def get_table_client(connection_string=None):
    """Get a Table Storage client with appropriate configuration"""
    # Check if we're running in a test environment
    if is_test_env():
        # For tests, return a mock client
        logger.info("Running in test environment, returning mock TableServiceClient")
        from unittest.mock import MagicMock
        mock_client = MagicMock()
        # Set up any necessary mock behaviors
        return mock_client
    elif not connection_string:
        # Get connection string from configuration
        from shared.config import get_storage_connection_string
        connection_string = get_storage_connection_string()
        if is_local_dev():
            # No need to modify the connection string as it already includes TableEndpoint
            logger.info("Using Azurite for Table Storage")

    return TableServiceClient.from_connection_string(connection_string)

# Cosmos DB client (mock implementation since we're using MS SQL DB)
def get_cosmos_client(endpoint=None, key=None, database_name=None):
    """Get a mock Cosmos DB client since we're using MS SQL DB"""
    logger.warning("Using mock Cosmos DB client (MS SQL DB is the primary database)")
    from unittest.mock import MagicMock
    return MagicMock()

# SQL Database connection
def get_sql_connection_string():
    """Get SQL Database connection string"""
    from shared.config import get_sql_connection_string as config_get_sql_connection_string
    return config_get_sql_connection_string()

def validate_credential_access(service_name: str, user_id: str) -> bool:
    """
    Validate if a user has access to the credentials for a service

    Args:
        service_name: Service name (format: salesforce-{integration_id})
        user_id: User ID to validate

    Returns:
        bool: True if user has access, False otherwise
    """
    try:
        if is_local_dev():
            # For local development, check Azure Table Storage
            from azure.data.tables import TableServiceClient
            from shared.config import get_storage_connection_string

            # Get table service client
            connection_string = get_storage_connection_string()
            table_service = TableServiceClient.from_connection_string(connection_string)

            # Get credentials table
            credentials_table_name = "Credentials"
            table_client = table_service.get_table_client(credentials_table_name)

            # Query for client ID entity to get the stored user ID
            filter_query = f"PartitionKey eq '{service_name}' and RowKey eq 'client-id'"
            entities = list(table_client.query_entities(query_filter=filter_query))

            if not entities:
                logger.warning(f"No credentials found for service {service_name}")
                return False

            stored_user_id = entities[0].get("UserId")
            if not stored_user_id:
                logger.warning(f"No user ID found in credentials for service {service_name}")
                return False

            # Check if the stored user ID matches the requesting user ID
            has_access = stored_user_id == user_id
            if not has_access:
                logger.warning(f"User {user_id} attempted to access credentials for service {service_name} owned by user {stored_user_id}")
            
            return has_access
        else:
            # Production code using Key Vault
            # ... (implement production validation) ...
            pass

    except Exception as e:
        logger.error(f"Error validating credential access: {str(e)}")
        return False

def get_client_credentials(service_name: str, user_id: str) -> Dict[str, Any]:
    """
    Get client credentials for a service, with user access validation

    Args:
        service_name: Service name (format: salesforce-{integration_id})
        user_id: User ID requesting the credentials

    Returns:
        Dict[str, Any]: Credentials if access is granted, None otherwise
    """
    try:
        # First validate user access
        if not validate_credential_access(service_name, user_id):
            logger.warning(f"Access denied for user {user_id} to credentials for service {service_name}")
            return None

        if is_local_dev():
            # For local development, get from Azure Table Storage
            from azure.data.tables import TableServiceClient
            from shared.config import get_storage_connection_string

            # Get table service client
            connection_string = get_storage_connection_string()
            table_service = TableServiceClient.from_connection_string(connection_string)

            # Get credentials table
            credentials_table_name = "Credentials"
            table_client = table_service.get_table_client(credentials_table_name)

            # Query for all credentials for this service
            filter_query = f"PartitionKey eq '{service_name}'"
            entities = list(table_client.query_entities(query_filter=filter_query))

            if not entities:
                logger.warning(f"No credentials found for service {service_name}")
                return None

            # Build credentials dictionary
            credentials = {}
            for entity in entities:
                row_key = entity.get("RowKey")
                value = entity.get("Value")
                if row_key and value:
                    credentials[row_key] = value

            return credentials
        else:
            # Production code using Key Vault
            # ... (implement production retrieval) ...
            pass

    except Exception as e:
        logger.error(f"Error getting client credentials: {str(e)}")
        return None
