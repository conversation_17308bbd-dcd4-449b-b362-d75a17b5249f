"""
Salesforce Metadata Extraction Module

This module provides functionality for extracting metadata from Salesforce orgs
and storing it in blob storage.
"""

import logging
import json
import io
import zipfile
import base64
import xml.dom.minidom
import xml.etree.ElementTree as ET
import requests
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple, BinaryIO
import re
import os
import threading
from azure.storage.blob import BlobClient
import uuid
from lxml import etree

from shared.data_access import BlobStorageRepository
from shared.common import is_local_dev

# Configure module-level logger
logger = logging.getLogger(__name__)

# Load metadata configuration from file if present
CONFIG_PATH = os.path.join(os.path.dirname(__file__), '..', 'config', 'metadata_config.json')
if os.path.exists(CONFIG_PATH):
    try:
        with open(CONFIG_PATH, 'r') as config_file:
            METADATA_CONFIG = json.load(config_file)
        logger.info(f"Loaded metadata configuration from {CONFIG_PATH}")
    except Exception as e:
        logger.error(f"Error loading metadata configuration: {e}")
else:
    logger.info(f"No metadata_config.json found at {CONFIG_PATH}, using default config")

INVALID_TYPES_LOG = os.path.join(os.path.dirname(__file__), '../invalid_metadata_types.txt')

# Add a module-level flag to prevent multiple executions
_is_extracting = False

# Add a lock to prevent multiple executions
_extraction_lock = threading.Lock()

# Distributed lock blob name
DISTRIBUTED_LOCK_BLOB = "metadata_extraction.lock"

# Add this at the top of the file, after imports
logger.info("[MODULE LOAD] metadata_extraction.py loaded (for orchestration debug)")

class MetadataClient:
    """Client for interacting with Salesforce Metadata API."""

    def __init__(self, access_token: str, instance_url: str, api_version: str = '58.0'):
        """
        Initialize the metadata client.

        Args:
            access_token: Salesforce access token
            instance_url: Salesforce instance URL
            api_version: Salesforce API version
        """
        self.access_token = access_token
        self.instance_url = instance_url
        self.api_version = api_version
        self.headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        self.soap_headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'text/xml; charset=UTF-8',
            'SOAPAction': '""',
            'Accept': 'text/xml'
        }

        # Base URLs
        self.rest_base_url = f"{instance_url}/services/data/v{api_version}"
        self.metadata_url = f"{instance_url}/services/Soap/m/{api_version}"
        self.tooling_url = f"{instance_url}/services/data/v{api_version}/tooling"

        # Log initialization
        logger.info(f"Initialized MetadataClient with instance URL: {instance_url}")
        logger.info(f"Metadata API URL: {self.metadata_url}")

    def get_metadata_types(self) -> List[Dict[str, Any]]:
        """
        Get all available metadata types.

        Returns:
            List of metadata type dictionaries
        """
        try:
            # First try using the SOAP Metadata API describe endpoint
            soap_envelope = f"""<?xml version="1.0" encoding="UTF-8"?>
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                              xmlns:met="http://soap.sforce.com/2006/04/metadata">
                <soapenv:Header>
                    <met:SessionHeader>
                        <met:sessionId>{self.access_token}</met:sessionId>
                    </met:SessionHeader>
                </soapenv:Header>
                <soapenv:Body>
                    <met:describeMetadata>
                        <met:asOfVersion>{self.api_version}</met:asOfVersion>
                    </met:describeMetadata>
                </soapenv:Body>
            </soapenv:Envelope>"""

            logger.info(f"Fetching metadata types from SOAP API: {self.metadata_url}")

            response = requests.post(
                self.metadata_url,
                headers=self.soap_headers,
                data=soap_envelope,
                timeout=30
            )

            if response.status_code == 200:
                logger.info(f"Successfully retrieved metadata types from SOAP Metadata API")

                # Parse the SOAP response
                import xml.etree.ElementTree as ET
                root = ET.fromstring(response.text)

                # Check for SOAP fault
                namespace = {'soapenv': 'http://schemas.xmlsoap.org/soap/envelope/'}
                fault = root.find('.//soapenv:Fault', namespace)
                if fault is not None:
                    fault_string = fault.find('faultstring')
                    if fault_string is not None and fault_string.text:
                        logger.error(f"SOAP fault when describing metadata: {fault_string.text}")
                        raise Exception(f"SOAP fault: {fault_string.text}")

                # Extract metadata objects
                namespace = {'ns': 'http://soap.sforce.com/2006/04/metadata'}
                metadata_objects = root.findall('.//ns:metadataObjects', namespace)

                metadata_types = []
                for obj in metadata_objects:
                    xml_name_elem = obj.find('ns:xmlName', namespace)
                    directory_name_elem = obj.find('ns:directoryName', namespace)

                    if xml_name_elem is not None and xml_name_elem.text:
                        metadata_types.append({
                            "xmlName": xml_name_elem.text,
                            "directoryName": directory_name_elem.text if directory_name_elem is not None else xml_name_elem.text.lower(),
                        })

                if metadata_types:
                    logger.info(f"Retrieved {len(metadata_types)} metadata types from SOAP API")
                    return metadata_types
                else:
                    logger.warning("No metadata types found in SOAP response, trying fallback")
            else:
                logger.warning(f"Failed to get metadata types from SOAP API: {response.status_code} - {response.text}")

        except Exception as e:
            logger.warning(f"Error fetching metadata types from SOAP API: {str(e)}")

        # Try using the REST API as a fallback
        try:
            url = f"{self.rest_base_url}/sobjects"
            logger.info(f"Trying REST API sobjects endpoint: {url}")

            response = requests.get(url, headers=self.headers, timeout=30)

            if response.status_code == 200:
                logger.info(f"Successfully retrieved sobjects from REST API")
                result = response.json()

                # Convert sobjects to metadata format - use common metadata types
                sobjects = result.get('sobjects', [])
                metadata_types = []

                # Filter for common metadata types that are useful for security analysis
                useful_types = {
                    'ApexClass', 'ApexTrigger', 'CustomObject', 'Layout', 'Profile',
                    'PermissionSet', 'CustomField', 'Flow', 'ValidationRule', 'WorkflowRule',
                    'CustomTab', 'CustomApplication', 'RemoteSiteSetting', 'ConnectedApp'
                }

                for sobject in sobjects:
                    name = sobject.get('name')
                    if name and name in useful_types:
                        metadata_types.append({
                            "xmlName": name,
                            "directoryName": name.lower(),
                        })

                if metadata_types:
                    logger.info(f"Retrieved {len(metadata_types)} useful metadata types from REST API")
                    return metadata_types
                else:
                    logger.warning("No useful metadata types found in REST response")
            else:
                logger.error(f"Failed to get sobjects from REST API: {response.status_code} - {response.text}")
        except Exception as e:
            logger.warning(f"Error fetching metadata types from REST API: {str(e)}")

        # Return a comprehensive list of metadata types as a last resort fallback
        logger.warning("Using comprehensive fallback metadata types list")
        return [
            {"xmlName": "ApexClass", "directoryName": "classes"},
            {"xmlName": "ApexTrigger", "directoryName": "triggers"},
            {"xmlName": "CustomObject", "directoryName": "objects"},
            {"xmlName": "Layout", "directoryName": "layouts"},
            {"xmlName": "CustomField", "directoryName": "fields"},
            {"xmlName": "Profile", "directoryName": "profiles"},
            {"xmlName": "PermissionSet", "directoryName": "permissionsets"},
            {"xmlName": "Flow", "directoryName": "flows"},
            {"xmlName": "ValidationRule", "directoryName": "validationRules"},
            {"xmlName": "WorkflowRule", "directoryName": "workflows"},
            {"xmlName": "CustomTab", "directoryName": "tabs"},
            {"xmlName": "CustomApplication", "directoryName": "applications"},
            {"xmlName": "RemoteSiteSetting", "directoryName": "remoteSiteSettings"},
            {"xmlName": "ConnectedApp", "directoryName": "connectedApps"}
        ]

    def list_metadata(self, metadata_type: str) -> List[Dict[str, Any]]:
        """
        List metadata of a specific type.

        Args:
            metadata_type: Type of metadata to list

        Returns:
            List of metadata items
        """
        try:
            # Create SOAP envelope for listMetadata
            soap_envelope = f"""
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                              xmlns:met="http://soap.sforce.com/2006/04/metadata">
                <soapenv:Header>
                    <met:SessionHeader>
                        <met:sessionId>{self.access_token}</met:sessionId>
                    </met:SessionHeader>
                </soapenv:Header>
                <soapenv:Body>
                    <met:listMetadata>
                        <met:queries>
                            <met:type>{metadata_type}</met:type>
                        </met:queries>
                    </met:listMetadata>
                </soapenv:Body>
            </soapenv:Envelope>
            """

            response = requests.post(
                self.metadata_url,
                headers=self.soap_headers,
                data=soap_envelope,
                timeout=30  # Add timeout to prevent hanging
            )

            if response.status_code != 200:
                # Check for specific error types
                if "INVALID_TYPE" in response.text:
                    logger.warning(f"Invalid metadata type: {metadata_type}")
                    raise ValueError(f"Invalid metadata type: {metadata_type}")
                elif "NOT_FOUND" in response.text:
                    logger.warning(f"Metadata type not found: {metadata_type}")
                    raise ValueError(f"Metadata type not found: {metadata_type}")
                else:
                    logger.error(f"Failed to list metadata: {response.text}")
                    raise Exception(f"Failed to list metadata: {response.status_code}")

            # Parse the XML response
            root = ET.fromstring(response.text)

            # Check for SOAP fault
            namespace = {'soapenv': 'http://schemas.xmlsoap.org/soap/envelope/'}
            fault = root.find('.//soapenv:Fault', namespace)
            if fault is not None:
                fault_string = fault.find('faultstring')
                if fault_string is not None and fault_string.text:
                    error_message = fault_string.text
                    logger.warning(f"SOAP fault when listing {metadata_type}: {error_message}")
                    raise ValueError(f"SOAP fault: {error_message}")

            # Extract the result elements
            namespace = {'ns': 'http://soap.sforce.com/2006/04/metadata'}
            result_elements = root.findall('.//ns:result', namespace)

            if not result_elements:
                logger.info(f"No results found for metadata type: {metadata_type}")
                return []

            # Convert XML elements to dictionaries
            results = []
            for elem in result_elements:
                item = {}
                for child in elem:
                    tag = child.tag.split('}')[-1]  # Remove namespace
                    item[tag] = child.text
                results.append(item)

            logger.info(f"Successfully retrieved {len(results)} items for metadata type: {metadata_type}")
            return results

        except ET.ParseError as e:
            logger.error(f"XML parsing error for metadata type {metadata_type}: {str(e)}")
            logger.error(f"Response content: {response.text[:500]}...")
            return []
        except requests.exceptions.Timeout:
            logger.error(f"Request timeout when listing metadata type: {metadata_type}")
            return []
        except ValueError as e:
            # These are expected errors like INVALID_TYPE, just propagate them
            raise
        except Exception as e:
            logger.error(f"Unexpected error listing metadata type {metadata_type}: {str(e)}")
            return []

def create_package_xml(metadata_items: Dict[str, List[str]], api_version: str = '58.0') -> str:
    """
    Build a package.xml file from metadata items.

    Args:
        metadata_items: Dictionary mapping metadata types to lists of component names
        api_version: Salesforce API version

    Returns:
        String containing the package.xml content
    """
    # Create the root element
    root = ET.Element('Package')
    root.set('xmlns', 'http://soap.sforce.com/2006/04/metadata')

    # Process metadata types
    for metadata_type, members in sorted(metadata_items.items()):
        # Skip special package keys
        if metadata_type.startswith('_package_') or metadata_type.startswith('_'):
            continue

        if not members:
            continue

        types_elem = ET.SubElement(root, 'types')

        # Add member elements
        for member in sorted(members):
            member_elem = ET.SubElement(types_elem, 'members')
            member_elem.text = member

        # Add name element
        name_elem = ET.SubElement(types_elem, 'name')
        name_elem.text = metadata_type

    # Add version element
    version_elem = ET.SubElement(root, 'version')
    version_elem.text = api_version

    # Write XML to string with declaration, no pretty print
    buf = io.BytesIO()
    tree = ET.ElementTree(root)
    tree.write(buf, encoding='utf-8', xml_declaration=True)
    xml_str = buf.getvalue().decode('utf-8')
    return xml_str

def extract_xml_section(xml_content: str, section_name: str) -> str:
    """
    Extract a specific XML section from a larger XML document.
    Returns the section as a string, or empty string if not found.
    """
    try:
        import re
        pattern = f"<{section_name}>(.*?)</{section_name}>"
        match = re.search(pattern, xml_content, re.DOTALL)
        if match:
            return f"<{section_name}>{match.group(1)}</{section_name}>"
        return ""
    except Exception as e:
        logger.error(f"Error extracting {section_name} section: {e}")
        return ""

def poll_and_extract_metadata(client, access_token, blob_repo, blob_prefix, package_xml, max_attempts=30, poll_interval=5):
    """
    Polls Salesforce for retrieve completion, then extracts and uploads all files (including special Profile sections) to blob storage.
    Returns (success, error_message, blob_prefix)
    """
    import base64
    import io
    import zipfile
    import time
    # Parse the package.xml to get the structure
    try:
        package_root = etree.fromstring(package_xml.encode('utf-8'))
    except Exception as e:
        logger.error(f"Failed to parse package.xml for retrieve: {e}")
        return False, f"Failed to parse package.xml: {e}", None

    # Build SOAP envelope for retrieve using lxml
    envelope = etree.Element("{http://schemas.xmlsoap.org/soap/envelope/}Envelope")
    header = etree.SubElement(envelope, "{http://schemas.xmlsoap.org/soap/envelope/}Header")
    session_header = etree.SubElement(header, "{http://soap.sforce.com/2006/04/metadata}SessionHeader")
    session_id = etree.SubElement(session_header, "{http://soap.sforce.com/2006/04/metadata}sessionId")
    session_id.text = access_token
    body = etree.SubElement(envelope, "{http://schemas.xmlsoap.org/soap/envelope/}Body")
    retrieve = etree.SubElement(body, "{http://soap.sforce.com/2006/04/metadata}retrieve")
    retrieve_request = etree.SubElement(retrieve, "{http://soap.sforce.com/2006/04/metadata}retrieveRequest")
    api_version_elem = etree.SubElement(retrieve_request, "{http://soap.sforce.com/2006/04/metadata}apiVersion")
    api_version_elem.text = client.api_version
    single_package = etree.SubElement(retrieve_request, "{http://soap.sforce.com/2006/04/metadata}singlePackage")
    single_package.text = "true"
    unpackaged = etree.SubElement(retrieve_request, "{http://soap.sforce.com/2006/04/metadata}unpackaged")
    # Add all types from the package.xml
    for types_elem in package_root.findall('.//{http://soap.sforce.com/2006/04/metadata}types'):
        types_node = etree.SubElement(unpackaged, "{http://soap.sforce.com/2006/04/metadata}types")
        for member_elem in types_elem.findall('.//{http://soap.sforce.com/2006/04/metadata}members'):
            member_node = etree.SubElement(types_node, "{http://soap.sforce.com/2006/04/metadata}members")
            member_node.text = member_elem.text
        name_elem = types_elem.find('.//{http://soap.sforce.com/2006/04/metadata}name')
        if name_elem is not None:
            name_node = etree.SubElement(types_node, "{http://soap.sforce.com/2006/04/metadata}name")
            name_node.text = name_elem.text
    # Add the version
    version_elem = package_root.find('.//{http://soap.sforce.com/2006/04/metadata}version')
    if version_elem is not None:
        version_node = etree.SubElement(unpackaged, "{http://soap.sforce.com/2006/04/metadata}version")
        version_node.text = version_elem.text
    # Convert the XML to a string
    soap_envelope = etree.tostring(envelope, encoding='utf-8', xml_declaration=True).decode('utf-8')
    logger.info(f"Generated SOAP envelope (first 200 chars): {soap_envelope[:200]}...")
    # Send retrieve request
    logger.info(f"Sending retrieve request to: {client.metadata_url}")
    response = requests.post(
        client.metadata_url,
        headers=client.soap_headers,
        data=soap_envelope
    )
    logger.info(f"Retrieve response status code: {response.status_code}")
    logger.info(f"Retrieve response content (first 500 chars): {response.text[:500]}...")
    if response.status_code != 200:
        logger.error(f"Failed to retrieve metadata: {response.text}")
        error_message = f"Salesforce API error (status code: {response.status_code})"
        try:
            root = ET.fromstring(response.text)
            namespace = {'soapenv': 'http://schemas.xmlsoap.org/soap/envelope/'}
            fault = root.find('.//soapenv:Fault', namespace)
            if fault is not None:
                fault_string = fault.find('faultstring')
                if fault_string is not None and fault_string.text:
                    error_message = fault_string.text
        except Exception as e:
            logger.warning(f"Failed to parse error response: {e}")
        return False, f"Failed to retrieve metadata: {error_message}", None
    # Parse the XML response
    root = ET.fromstring(response.text)
    namespace = {'ns': 'http://soap.sforce.com/2006/04/metadata'}
    id_element = root.find('.//ns:id', namespace)
    if id_element is None:
        logger.error("No async ID found in retrieve response")
        return False, "No async ID found in retrieve response", None
    async_id = id_element.text
    logger.info(f"Retrieve async ID: {async_id}")
    attempt = 0
    last_status = None
    last_lease_renewal = time.time()
    while attempt < max_attempts:
        attempt += 1
        logger.info(f"Checking retrieve status (attempt {attempt}/{max_attempts})")

        # Create SOAP envelope for checkRetrieveStatus
        check_envelope = f'''<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:met="http://soap.sforce.com/2006/04/metadata">
    <soapenv:Header>
        <met:SessionHeader>
            <met:sessionId>{access_token}</met:sessionId>
        </met:SessionHeader>
    </soapenv:Header>
    <soapenv:Body>
        <met:checkRetrieveStatus>
            <met:asyncProcessId>{async_id}</met:asyncProcessId>
        </met:checkRetrieveStatus>
    </soapenv:Body>
</soapenv:Envelope>'''
        response = requests.post(
            client.metadata_url,
            headers=client.soap_headers,
            data=check_envelope
        )
        logger.info(f"Check status response code: {response.status_code}")
        if response.status_code != 200:
            logger.error(f"Failed to check retrieve status: {response.text}")
            time.sleep(poll_interval)
            continue
        root = ET.fromstring(response.text)
        status_element = root.find('.//ns:status', namespace)
        status = status_element.text if status_element is not None and status_element.text else "Unknown"
        if status != last_status:
            logger.info(f"Retrieve operation status: {status}")
            last_status = status
        if status == 'Succeeded':
            zip_file_element = root.find('.//ns:zipFile', namespace)
            if zip_file_element is None:
                logger.error("No ZIP file found in retrieve response")
                return False, "No ZIP file found in retrieve response", None
            zip_bytes = base64.b64decode(zip_file_element.text)
            try:
                with zipfile.ZipFile(io.BytesIO(zip_bytes)) as zip_file:
                    file_list = zip_file.namelist()
                    logger.info(f"ZIP file contains {len(file_list)} files: {file_list}")
                    for file_info in zip_file.infolist():
                        if file_info.filename.endswith('/'):
                            continue  # skip directories
                        with zip_file.open(file_info) as file:
                            try:
                                content = file.read().decode('utf-8', errors='replace')
                                blob_path = f"{blob_prefix}/{file_info.filename}"
                                blob_client = blob_repo.container_client.get_blob_client(blob_path)
                                blob_client.upload_blob(content, overwrite=True)
                                logger.info(f"Uploaded {file_info.filename} to blob storage")
                                if file_info.filename.endswith('.profile') and '<Profile' in content:
                                    profile_name = file_info.filename.split('/')[-1].replace('.profile', '')
                                    profile_config = METADATA_CONFIG.get("special_handling_types", {}).get("Profile", {})
                                    extract_sections = profile_config.get("extract_sections", [])
                                    for section_config in extract_sections:
                                        section_name = section_config.get("section_name")
                                        output_type = section_config.get("output_type")
                                        if section_name and output_type and f"<{section_name}>" in content:
                                            section_content = extract_xml_section(content, section_name)
                                            if section_content:
                                                section_blob_path = f"{blob_prefix}/{output_type}/{profile_name}.{output_type}"
                                                section_blob_client = blob_repo.container_client.get_blob_client(section_blob_path)
                                                section_blob_client.upload_blob(section_content, overwrite=True)
                                                logger.info(f"Extracted and uploaded {output_type} for {profile_name}")
                            except UnicodeDecodeError:
                                file.seek(0)
                                binary_content = file.read()
                                blob_path = f"{blob_prefix}/{file_info.filename}"
                                blob_client = blob_repo.container_client.get_blob_client(blob_path)
                                blob_client.upload_blob(binary_content, overwrite=True)
                                logger.info(f"Uploaded binary file {file_info.filename} to blob storage")
                    package_xml_blob_name = f"{blob_prefix}/package.xml"
                    blob_client = blob_repo.container_client.get_blob_client(package_xml_blob_name)
                    blob_client.upload_blob(package_xml, overwrite=True)
                    logger.info(f"[PACKAGE.XML SAVED] Blob: {package_xml_blob_name}")
                    logger.info(f"[ZIP EXTRACTION] Successfully extracted and uploaded metadata ZIP for blob prefix: {blob_prefix}")
                return True, None, blob_prefix
            except Exception as blob_error:
                logger.error(f"Failed to extract/upload files from metadata ZIP: {blob_error}")
                return False, f"Failed to extract/upload files from metadata ZIP: {str(blob_error)}", None
        elif status in ('InProgress', 'Pending'):
            logger.info(f"Retrieve operation still {status}. Waiting {poll_interval} seconds before next check.")
            time.sleep(poll_interval)
            continue
        elif status in ('Failed', 'Error'):
            logger.error(f"Retrieve operation failed with status: {status}")
            logger.error(f"[ZIP EXTRACTION] Failed to extract/upload metadata ZIP. Last status: {status}")
            return False, f"Retrieve operation failed with status: {status}", None
        else:
            logger.warning(f"Unknown retrieve status: {status}. Waiting {poll_interval} seconds before next check.")
            time.sleep(poll_interval)
    logger.error(f"Retrieve operation timed out after {max_attempts} attempts")
    return False, f"Retrieve operation timed out after {max_attempts} attempts", None

# Add helper function for deduplicated logging of invalid metadata types
def log_invalid_metadata_type(metadata_type: str, log_path: str):
    try:
        # Read existing types
        if os.path.exists(log_path):
            with open(log_path, 'r') as f:
                existing = set(line.strip() for line in f if line.strip())
        else:
            existing = set()
        # Only append if not already present
        if metadata_type not in existing:
            with open(log_path, 'a') as f:
                f.write(f"{metadata_type}\n")
            logger.info(f"Logged invalid metadata type: {metadata_type}")
    except Exception as log_e:
        logger.error(f"Failed to log invalid metadata type: {log_e}")

# Optional helper to deduplicate the invalid types log file
def deduplicate_invalid_types_log(log_path: str):
    try:
        if os.path.exists(log_path):
            with open(log_path, 'r') as f:
                lines = set(line.strip() for line in f if line.strip())
            with open(log_path, 'w') as f:
                for line in sorted(lines):
                    f.write(f"{line}\n")
    except Exception as e:
        logger.error(f"Failed to deduplicate invalid metadata types log: {e}")

def acquire_distributed_lock(blob_repo, timeout=60, run_id=None):
    """
    Acquire a distributed lock using Azure Blob Lease. Returns the lease object if acquired, else None.
    """
    try:
        lock_blob_client = blob_repo.container_client.get_blob_client(DISTRIBUTED_LOCK_BLOB)
        # Ensure the lock blob exists
        try:
            lock_blob_client.get_blob_properties()
        except Exception:
            lock_blob_client.upload_blob(b"lock", overwrite=True)
        lease = lock_blob_client.acquire_lease(lease_duration=timeout)
        logger.info(f"[DISTRIBUTED LOCK] Acquired distributed lock for metadata extraction. Lease ID: {lease.id}, Blob: {DISTRIBUTED_LOCK_BLOB}, Lease Duration: {timeout}s run_id={run_id}")
        return lease
    except Exception as e:
        logger.warning(f"[DISTRIBUTED LOCK] Could not acquire distributed lock: {e}")
        return None

def release_distributed_lock(blob_repo, lease, run_id=None):
    try:
        lease.release()
        logger.info(f"[DISTRIBUTED LOCK] Released distributed lock for metadata extraction. Lease ID: {lease.id}, Blob: {DISTRIBUTED_LOCK_BLOB} run_id={run_id}")
    except Exception as e:
        logger.warning(f"[DISTRIBUTED LOCK] Could not release distributed lock: {e}")

def extract_salesforce_metadata(
    access_token: str,
    instance_url: str,
    blob_repo: BlobStorageRepository,
    integration_id: str,
    org_name: str,
    orchestration_context: str = None,
    task_id: str = None,
    execution_log_id: str = None
) -> Tuple[bool, Optional[str], Optional[str]]:
    # Use execution_log_id for isolation instead of global locks
    run_id = execution_log_id or str(uuid.uuid4())
    process_id = os.getpid()
    thread_id = threading.get_ident()
    logger.info(f"[EXTRACT ENTRY] run_id={run_id} pid={process_id} tid={thread_id} org={org_name} integration_id={integration_id} context={orchestration_context} task_id={task_id} execution_log_id={execution_log_id}")

    # No global locks needed - each execution is isolated by execution_log_id
    try:
        logger.info(f"[FUNCTION ENTRY] run_id={run_id} extract_salesforce_metadata called")
        logger.info(f"START: extract_salesforce_metadata run_id={run_id}")
        logger.info(f"Extracting metadata from Salesforce org: {instance_url} run_id={run_id}")

        # Validate inputs
        if not access_token:
            error_msg = "Access token is required for metadata extraction"
            logger.error(error_msg)
            return False, error_msg, None

        if not instance_url:
            error_msg = "Instance URL is required for metadata extraction"
            logger.error(error_msg)
            return False, error_msg, None

        if not blob_repo:
            error_msg = "Blob repository is required for metadata extraction"
            logger.error(error_msg)
            return False, error_msg, None

        # Ensure instance_url is just the base FQDN, without trailing slashes or /services paths
        parsed_url = re.match(r"(https?://[^/]+)", instance_url)
        if parsed_url:
            base_instance_url = parsed_url.group(1)
        else:
            base_instance_url = instance_url.rstrip('/')  # Fallback if regex fails
        logger.info(f"Using base instance URL for metadata operations: {base_instance_url}")

        # Create blob prefix using execution_log_id for isolation
        sanitized_org_name = (org_name or "unknown").replace(" ", "_").lower()
        if execution_log_id:
            blob_prefix = f"{sanitized_org_name}/{integration_id}/{execution_log_id}"
        elif task_id:
            blob_prefix = f"{sanitized_org_name}/{integration_id}/{task_id}"
        else:
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            blob_prefix = f"{sanitized_org_name}/{integration_id}/{timestamp}"

        logger.info(f"Using blob prefix for isolation: {blob_prefix}")

        # Initialize metadata client
        client = MetadataClient(access_token, base_instance_url)

        # Get metadata types
        metadata_types = client.get_metadata_types()
        logger.info(f"Retrieved {len(metadata_types)} metadata types")

        # Read invalid metadata types from file
        invalid_types = set()
        if os.path.exists(INVALID_TYPES_LOG):
            with open(INVALID_TYPES_LOG, 'r') as f:
                invalid_types = set(line.strip() for line in f if line.strip())
        logger.info(f"Loaded {len(invalid_types)} invalid metadata types from log file.")

        # Build metadata items dictionary
        metadata_items = {}
        wildcard_types = set(METADATA_CONFIG["wildcard_metadata_types"])

        for metadata_type_info in metadata_types:
            metadata_type = metadata_type_info.get("xmlName")
            if not metadata_type or metadata_type in invalid_types:
                continue
            if metadata_type in wildcard_types:
                metadata_items[metadata_type] = ["*"]
                logger.info(f"Added wildcard entry for {metadata_type}")
            else:
                try:
                    logger.info(f"Listing metadata for type: {metadata_type}")
                    components = client.list_metadata(metadata_type)
                    # Filter out None, 'null', or empty member names
                    component_names = [comp.get('fullName') for comp in components if comp.get('fullName') and comp.get('fullName') not in (None, '', 'null') and not str(comp.get('fullName')).startswith('null_')]
                    if component_names:
                        logger.info(f"Found {len(component_names)} components for type {metadata_type}")
                        metadata_items[metadata_type] = component_names
                    else:
                        logger.info(f"No components found for type {metadata_type}")
                except Exception as e:
                    logger.warning(f"Error listing metadata for type {metadata_type}: {e}")
                    # No longer logging invalid types to file

        # Ensure all wildcard types from config are present in metadata_items
        for wildcard_type in wildcard_types:
            if wildcard_type not in metadata_items:
                metadata_items[wildcard_type] = ["*"]
                logger.info(f"Added config wildcard entry for {wildcard_type} at end of package.xml build.")

        # Create package.xml
        package_xml = create_package_xml(metadata_items)

        # Save package.xml to blob storage
        package_xml_blob_name = f"{blob_prefix}/package.xml"
        try:
            logger.info(f"[PACKAGE.XML PREVIEW] First 200 chars: {package_xml[:200]}")
            blob_client = blob_repo.container_client.get_blob_client(package_xml_blob_name)
            blob_client.upload_blob(package_xml, overwrite=True)
            logger.info(f"[PACKAGE.XML SAVED] Blob: {package_xml_blob_name}")
        except Exception as blob_e:
            logger.warning(f"Failed to save package.xml to blob: {str(blob_e)}. Continuing with metadata retrieval.")

        # Extract metadata using package.xml
        success, error_message, blob_path = poll_and_extract_metadata(client, access_token, blob_repo, blob_prefix, package_xml)
        if success:
            # Optional: Deduplicate the invalid types log file after successful extraction
            deduplicate_invalid_types_log(INVALID_TYPES_LOG)
            logger.info(f"[EXTRACT EXIT] run_id={run_id} pid={process_id} tid={thread_id} org={org_name} integration_id={integration_id} context={orchestration_context} success=True error=None")
            return True, None, blob_path
        else:
            logger.info(f"[EXTRACT EXIT] run_id={run_id} pid={process_id} tid={thread_id} org={org_name} integration_id={integration_id} context={orchestration_context} success=False error={error_message}")
            return False, error_message, None

    except Exception as e:
        error_msg = f"Error extracting metadata: {str(e)}"
        logger.error(f"[EXTRACT ERROR] run_id={run_id} pid={process_id} tid={thread_id} org={org_name} integration_id={integration_id} context={orchestration_context} error={str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        logger.info(f"[EXTRACT EXIT] run_id={run_id} pid={process_id} tid={thread_id} org={org_name} integration_id={integration_id} context={orchestration_context} success=False error={error_msg}")
        return False, error_msg, None