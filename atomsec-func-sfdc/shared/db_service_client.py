"""
Database Service Client

This module provides a client for making HTTP requests to the atomsec-func-db-r service.
It replaces direct database operations with API calls to the centralized database service.

Features:
- HTTP client for database service API calls
- Environment-aware endpoint configuration
- Error handling and retry logic
- Consistent response parsing
- PMD-specific operations support
"""

import logging
import json
import requests
from typing import Dict, List, Any, Optional
import os

logger = logging.getLogger(__name__)

class DatabaseServiceClient:
    """Client for interacting with the atomsec-func-db-r service"""

    def __init__(self):
        """Initialize the database service client"""
        # Get configuration from config module
        from shared.config import get_db_service_config
        config = get_db_service_config()
        
        # Configure base URL and settings
        self.base_url = config["base_url"]
        self.timeout = config["timeout"]
        self.retry_attempts = config["retry_attempts"]
        self.retry_delay = config["retry_delay"]
        self.session = requests.Session()

        # Set default headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': config.get("user_agent", "atomsec-func-sfdc/1.0.0")
        })
        
        # Add API key if configured
        if config.get("api_key"):
            self.session.headers.update({
                'X-API-Key': config["api_key"]
            })

        logger.info(f"Initialized DatabaseServiceClient with base URL: {self.base_url}")

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None,
                     params: Optional[Dict] = None, is_internal_task_enqueue: bool = False) -> Optional[Dict[str, Any]]:
        """
        Make HTTP request to the database service with retry logic

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint (without /api prefix)
            data: Request body data
            params: Query parameters
            is_internal_task_enqueue: If True, add X-Internal-Task-Enqueue header

        Returns:
            Response data or None if request failed
        """
        for attempt in range(self.retry_attempts + 1):
            try:
                url = f"{self.base_url}/{endpoint.lstrip('/')}"

                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1}/{self.retry_attempts + 1})")

                # Prepare headers
                headers = self.session.headers.copy()
                if is_internal_task_enqueue:
                    headers['X-Internal-Task-Enqueue'] = 'true'

                response = self.session.request(
                    method=method,
                    url=url,
                    json=data,
                    params=params,
                    timeout=self.timeout,
                    headers=headers
                )

                if response.status_code >= 200 and response.status_code < 300:
                    try:
                        return response.json()
                    except json.JSONDecodeError:
                        logger.warning(f"Response is not valid JSON: {response.text}")
                        return {"success": True, "data": response.text}
                else:
                    logger.error(f"Request failed with status {response.status_code}: {response.text}")
                    if attempt < self.retry_attempts:
                        logger.info(f"Retrying request in {self.retry_delay} seconds...")
                        import time
                        time.sleep(self.retry_delay)
                        continue
                    return None

            except requests.exceptions.Timeout:
                logger.error(f"Request to {endpoint} timed out (attempt {attempt + 1})")
                if attempt < self.retry_attempts:
                    logger.info(f"Retrying request in {self.retry_delay} seconds...")
                    import time
                    time.sleep(self.retry_delay)
                    continue
                return None
            except requests.exceptions.ConnectionError:
                logger.error(f"Connection error when calling {endpoint} (attempt {attempt + 1})")
                if attempt < self.retry_attempts:
                    logger.info(f"Retrying request in {self.retry_delay} seconds...")
                    import time
                    time.sleep(self.retry_delay)
                    continue
                return None
            except Exception as e:
                logger.error(f"Unexpected error calling {endpoint}: {str(e)}")
                if "Object of type datetime is not JSON serializable" in str(e):
                    logger.error(f"JSON serialization error - data may contain datetime objects: {data}")
                return None

        return None

    # ===== PMD OPERATIONS =====

    def get_enabled_pmd_subtasks(self, integration_id: str) -> List[Dict[str, Any]]:
        """
        Get enabled PMD subtasks for a specific integration
        
        Args:
            integration_id: Integration ID
            
        Returns:
            List of enabled PMD subtasks
        """
        response = self._make_request('GET', f'pmd/subtasks/{integration_id}')
        if response and response.get('success'):
            return response.get('data', [])
        logger.warning(f"No enabled PMD subtasks found for integration {integration_id}")
        return []

    def get_enabled_pmd_rules(self, subtask_id: str) -> List[Dict[str, Any]]:
        """
        Get enabled individual PMD rules for a specific subtask
        
        Args:
            subtask_id: Subtask ID
            
        Returns:
            List of enabled individual PMD rules
        """
        response = self._make_request('GET', f'pmd/rules/{subtask_id}')
        if response and response.get('success'):
            return response.get('data', [])
        logger.warning(f"No enabled PMD rules found for subtask {subtask_id}")
        return []

    def get_pmd_configuration(self, integration_id: str) -> Optional[Dict[str, Any]]:
        """
        Get complete PMD configuration for a specific integration
        
        Args:
            integration_id: Integration ID
            
        Returns:
            Complete PMD configuration or None
        """
        response = self._make_request('GET', f'pmd/configuration/{integration_id}')
        if response and response.get('success'):
            return response.get('data')
        logger.warning(f"No PMD configuration found for integration {integration_id}")
        return None

    def store_pmd_findings(self, findings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Store PMD scan findings
        
        Args:
            findings: List of PMD findings to store
            
        Returns:
            Dictionary containing processing results
        """
        data = {'findings': findings}
        response = self._make_request('POST', 'pmd/findings', data=data)
        if response and response.get('success'):
            return response.get('data', {})
        logger.error(f"Failed to store PMD findings: {response}")
        return {
            "processed_count": 0,
            "error_count": len(findings),
            "total_findings": len(findings)
        }

    def get_pmd_findings(self, integration_id: str, limit: int = 100, offset: int = 0,
                        severity: Optional[str] = None, category: Optional[str] = None) -> Dict[str, Any]:
        """
        Get PMD findings for a specific integration
        
        Args:
            integration_id: Integration ID
            limit: Maximum number of findings to return
            offset: Number of findings to skip
            severity: Filter by severity
            category: Filter by category
            
        Returns:
            Dictionary containing PMD findings and metadata
        """
        params = {
            'limit': limit,
            'offset': offset
        }
        if severity:
            params['severity'] = severity
        if category:
            params['category'] = category

        response = self._make_request('GET', f'pmd/findings/{integration_id}', params=params)
        if response and response.get('success'):
            return response.get('data', {})
        logger.warning(f"No PMD findings found for integration {integration_id}")
        return {
            "findings": [],
            "total_count": 0,
            "limit": limit,
            "offset": offset,
            "has_more": False
        }

    # ===== INTEGRATION OPERATIONS =====

    def get_integration_by_id(self, integration_id: str) -> Optional[Dict[str, Any]]:
        """Get integration by ID"""
        response = self._make_request('GET', f'integrations/{integration_id}')
        return response.get('data') if response and response.get('success') else None

    def create_integration(self, integration_data: Dict[str, Any]) -> Optional[str]:
        """Create new integration"""
        response = self._make_request('POST', 'integrations', data=integration_data)
        if response and response.get('success'):
            return response.get('data', {}).get('integration_id')
        return None

    def update_integration(self, integration_id: str, update_data: Dict[str, Any]) -> bool:
        """Update integration"""
        response = self._make_request('PUT', f'integrations/{integration_id}', data=update_data)
        return response and response.get('success', False)

    def get_integrations(self, user_email: str = None, user_id: int = None,
                        account_id: int = None, include_inactive: bool = False,
                        integration_type: str = None) -> List[Dict[str, Any]]:
        """Get integrations with filtering"""
        params = {}
        if user_email:
            params['user_email'] = user_email
        if user_id:
            params['user_id'] = user_id
        if account_id:
            params['account_id'] = account_id
        if include_inactive:
            params['include_inactive'] = 'true'
        if integration_type:
            params['integration_type'] = integration_type

        response = self._make_request('GET', 'integrations', params=params)
        if response and response.get('success'):
            return response.get('data', {}).get('integrations', [])
        return []

    # ===== TASK OPERATIONS =====

    def create_task(self, task_data: Dict[str, Any]) -> Optional[str]:
        """Create new task"""
        # Mark as internal if not sfdc_authenticate
        is_internal = task_data.get('task_type') != 'sfdc_authenticate'
        response = self._make_request('POST', 'tasks', data=task_data, is_internal_task_enqueue=is_internal)
        if response and response.get('success'):
            return response.get('data', {}).get('task_id')
        return None

    def update_task_status(self, task_id: str, status: str, progress: int = None, message: str = None, result: str = None, error: str = None) -> bool:
        """Update task status"""
        update_data = {'status': status}
        if progress is not None:
            update_data['progress'] = progress
        if message:
            update_data['message'] = message
        if result:
            update_data['result'] = result
        if error:
            update_data['error'] = error

        response = self._make_request('PUT', f'tasks/{task_id}/status', data=update_data)
        return response and response.get('success', False)

    def get_task_by_id(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task by ID"""
        response = self._make_request('GET', f'tasks/{task_id}')
        return response.get('data') if response and response.get('success') else None

    def get_tasks_by_org(self, org_id: str) -> List[Dict[str, Any]]:
        """Get all tasks for an organization"""
        params = {'org_id': org_id}
        response = self._make_request('GET', 'tasks', params=params)
        return response.get('data', []) if response and response.get('success') else []

    # ===== SECURITY DATA OPERATIONS =====

    def store_health_check_data(self, org_id: str, execution_log_id: str,
                               risks_data: List[Dict[str, Any]]) -> bool:
        """Store security health check data"""
        data = {
            'org_id': org_id,
            'execution_log_id': execution_log_id,
            'risks_data': risks_data
        }
        response = self._make_request('POST', 'security/health-checks', data=data)
        return response and response.get('success', False)

    def store_profile_data(self, org_id: str, execution_log_id: str,
                          profile_data: List[Dict[str, Any]]) -> bool:
        """Store profile permission data"""
        data = {
            'org_id': org_id,
            'execution_log_id': execution_log_id,
            'profile_data': profile_data
        }
        response = self._make_request('POST', 'security/profiles', data=data)
        return response and response.get('success', False)

    def get_health_check_data(self, org_id: str, risk_type: str = None) -> List[Dict[str, Any]]:
        """Get health check data"""
        params = {'org_id': org_id}
        if risk_type:
            params['risk_type'] = risk_type

        response = self._make_request('GET', 'security/health-checks', params=params)
        return response.get('data', []) if response and response.get('success') else []

    # ===== ORGANIZATION OPERATIONS =====

    def create_organization(self, org_data: Dict[str, Any]) -> Optional[str]:
        """Create new organization"""
        response = self._make_request('POST', 'organizations', data=org_data)
        if response and response.get('success'):
            return response.get('data', {}).get('organization_id')
        return None

    def get_organization_by_id(self, org_id: str) -> Optional[Dict[str, Any]]:
        """Get organization by ID"""
        response = self._make_request('GET', f'organizations/{org_id}')
        return response.get('data') if response and response.get('success') else None

    def update_organization(self, org_id: str, update_data: Dict[str, Any]) -> bool:
        """Update organization"""
        response = self._make_request('PUT', f'organizations/{org_id}', data=update_data)
        return response and response.get('success', False)

    def get_organizations(self, include_inactive: bool = False, domain: str = None) -> List[Dict[str, Any]]:
        """Get organizations with filtering"""
        params = {}
        if include_inactive:
            params['include_inactive'] = 'true'
        if domain:
            params['domain'] = domain

        response = self._make_request('GET', 'organizations', params=params)
        if response and response.get('success'):
            return response.get('data', {}).get('organizations', [])
        return []

    # ===== USER OPERATIONS =====

    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email"""
        response = self._make_request('GET', f'users/email/{email}')
        return response.get('data') if response and response.get('success') else None

    def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        response = self._make_request('GET', f'users/{user_id}')
        return response.get('data') if response and response.get('success') else None

    def create_user(self, user_data: Dict[str, Any]) -> Optional[str]:
        """Create new user"""
        response = self._make_request('POST', 'users', data=user_data)
        if response and response.get('success'):
            return response.get('data', {}).get('user_id')
        return None

    def update_user(self, user_id: str, update_data: Dict[str, Any]) -> bool:
        """Update user"""
        response = self._make_request('PUT', f'users/{user_id}', data=update_data)
        return response and response.get('success', False)

    def verify_user_login(self, username: str, password_hash: str) -> Optional[int]:
        """
        Verify user login credentials
        
        Args:
            username: Username/email
            password_hash: Hashed password or plain text password
            
        Returns:
            User ID if valid, None otherwise
        """
        # Check if this is a plain text password (for login) or hashed password (for verification)
        if len(password_hash) < 64:  # Plain text password
            # This is a login attempt with plain text password
            data = {
                'email': username,
                'password': password_hash
            }
            response = self._make_request('POST', 'users/login', data=data)
            if response and response.get('success'):
                return response.get('data', {}).get('user_id')
        else:
            # This is a verification attempt with hashed password
            data = {
                'username': username,
                'password_hash': password_hash
            }
            response = self._make_request('POST', 'users/login/verify', data=data)
            if response and response.get('success'):
                return response.get('data', {}).get('user_id')
        return None

    def get_users(self, account_id: int = None, is_active: bool = None) -> List[Dict[str, Any]]:
        """
        Get users with optional filtering
        
        Args:
            account_id: Filter by account ID
            is_active: Filter by active status
            
        Returns:
            List of users
        """
        params = {}
        if account_id is not None:
            params['account_id'] = account_id
        if is_active is not None:
            params['is_active'] = is_active
            
        response = self._make_request('GET', 'users', params=params)
        if response and response.get('success'):
            return response.get('data', [])
        return []

    def delete_user(self, user_id: int) -> bool:
        """
        Delete a user (soft delete)
        
        Args:
            user_id: User ID
            
        Returns:
            True if successful, False otherwise
        """
        response = self._make_request('DELETE', f'users/{user_id}')
        return response and response.get('success', False)

    def create_user_login(self, user_id: int, username: str, password_hash: str, salt: str) -> bool:
        """
        Create login credentials for a user
        
        Args:
            user_id: User ID
            username: Username/email
            password_hash: Hashed password
            salt: Password salt
            
        Returns:
            True if successful, False otherwise
        """
        data = {
            'user_id': user_id,
            'username': username,
            'password_hash': password_hash,
            'salt': salt
        }
        response = self._make_request('POST', f'users/{user_id}/login', data=data)
        return response and response.get('success', False)

    def create_user_with_password(self, email: str, password: str, first_name: str = "", 
                                 middle_name: str = "", last_name: str = "", dob: str = "",
                                 contact: str = "", state: str = "", country: str = "",
                                 organization: str = "") -> Optional[int]:
        """
        Create a user with password
        
        Args:
            email: User email
            password: Plain text password
            first_name: First name
            middle_name: Middle name
            last_name: Last name
            dob: Date of birth
            contact: Contact number
            state: State
            country: Country
            organization: Organization
            
        Returns:
            User ID if successful, None otherwise
        """
        data = {
            'email': email,
            'password': password,
            'first_name': first_name,
            'middle_name': middle_name,
            'last_name': last_name,
            'dob': dob,
            'contact': contact,
            'state': state,
            'country': country,
            'organization': organization
        }
        response = self._make_request('POST', 'auth/signup', data=data)
        if response and response.get('success'):
            return response.get('data', {}).get('user_id')
        return None

    def update_user_password(self, user_id: int, password_hash: str, salt: str, updated_at: str) -> bool:
        """
        Update user password
        
        Args:
            user_id: User ID
            password_hash: New hashed password
            salt: New password salt
            updated_at: Update timestamp
            
        Returns:
            True if successful, False otherwise
        """
        data = {
            'password_hash': password_hash,
            'salt': salt,
            'updated_at': updated_at
        }
        response = self._make_request('PUT', f'users/{user_id}/password', data=data)
        return response and response.get('success', False)

    # ===== ACCOUNT OPERATIONS =====

    def get_accounts(self, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """Get accounts with filtering"""
        params = {}
        if include_inactive:
            params['include_inactive'] = 'true'

        response = self._make_request('GET', 'accounts', params=params)
        if response and response.get('success'):
            return response.get('data', {}).get('accounts', [])
        return []

    def get_account_by_id(self, account_id: str) -> Optional[Dict[str, Any]]:
        """Get account by ID"""
        response = self._make_request('GET', f'accounts/{account_id}')
        return response.get('data') if response and response.get('success') else None

    def create_account(self, account_data: Dict[str, Any]) -> Optional[str]:
        """Create new account"""
        response = self._make_request('POST', 'accounts', data=account_data)
        if response and response.get('success'):
            return response.get('data', {}).get('account_id')
        return None

    def update_account(self, account_id: str, update_data: Dict[str, Any]) -> bool:
        """Update account"""
        response = self._make_request('PUT', f'accounts/{account_id}', data=update_data)
        return response and response.get('success', False)

    # ===== INTEGRATION DATA OPERATIONS =====

    def get_integration_overview(self, integration_id: str) -> Optional[Dict[str, Any]]:
        """Get integration overview data"""
        response = self._make_request('GET', f'integrations/{integration_id}/overview')
        return response.get('data') if response and response.get('success') else None

    def get_integration_health_check(self, integration_id: str) -> Optional[Dict[str, Any]]:
        """Get integration health check data"""
        response = self._make_request('GET', f'integrations/{integration_id}/health-check')
        return response.get('data') if response and response.get('success') else None

    def get_integration_profiles(self, integration_id: str) -> Optional[Dict[str, Any]]:
        """Get integration profiles and permission sets"""
        response = self._make_request('GET', f'integrations/{integration_id}/profiles')
        return response.get('data') if response and response.get('success') else None

    def get_integration_credentials(self, integration_id: str) -> Optional[Dict[str, Any]]:
        """Get integration credentials"""
        response = self._make_request('GET', f'integrations/{integration_id}/credentials')
        return response.get('data') if response and response.get('success') else None

    def store_credential(self, credential_data: Dict[str, Any]) -> bool:
        """Store credential data"""
        response = self._make_request('POST', 'credentials', data=credential_data)
        return response and response.get('success', False)

    def store_overview_data(self, org_id: str, overview_data: Dict[str, Any]) -> bool:
        """Store overview data"""
        data = {
            'org_id': org_id,
            'overview_data': overview_data
        }
        response = self._make_request('POST', 'security/overview', data=data)
        return response and response.get('success', False)

    def store_policies_result_data(self, org_id: str, execution_log_id: str,
                                  policies_data: List[Dict[str, Any]]) -> bool:
        """Store policies result data"""
        data = {
            'org_id': org_id,
            'execution_log_id': execution_log_id,
            'policies_data': policies_data
        }
        response = self._make_request('POST', 'security/policies-result', data=data)
        return response and response.get('success', False)

    def store_profile_assignment_count_data(self, org_id: str, execution_log_id: str,
                                           assignment_data: List[Dict[str, Any]]) -> bool:
        """Store profile assignment count data"""
        data = {
            'org_id': org_id,
            'execution_log_id': execution_log_id,
            'assignment_data': assignment_data
        }
        response = self._make_request('POST', 'security/profile-assignment-counts', data=data)
        return response and response.get('success', False)

    def get_policies_result_data(self, org_id: str, profile_name: str = None,
                                permission_set_name: str = None) -> List[Dict[str, Any]]:
        """Get policies result data"""
        params = {'org_id': org_id}
        if profile_name:
            params['profile_name'] = profile_name
        if permission_set_name:
            params['permission_set_name'] = permission_set_name

        response = self._make_request('GET', 'security/policies-result', params=params)
        return response.get('data', []) if response and response.get('success') else []

    def get_profile_assignment_count_data(self, org_id: str, profile_name: str = None,
                                         permission_set_name: str = None) -> List[Dict[str, Any]]:
        """Get profile assignment count data"""
        params = {'org_id': org_id}
        if profile_name:
            params['profile_name'] = profile_name
        if permission_set_name:
            params['permission_set_name'] = permission_set_name

        response = self._make_request('GET', 'security/profile-assignment-counts', params=params)
        return response.get('data', []) if response and response.get('success') else []

    # ===== GENERIC TABLE OPERATIONS =====

    def store_table_entity(self, table_name: str, entity_data: Dict[str, Any]) -> bool:
        """Store entity in a specific table (for generic table operations)"""
        data = {
            'table_name': table_name,
            'entity_data': entity_data
        }
        response = self._make_request('POST', 'tables/entity', data=data)
        return response and response.get('success', False)

    def query_table_entities(self, table_name: str, filter_query: str = None) -> List[Dict[str, Any]]:
        """Query entities from a specific table"""
        params = {'table_name': table_name}
        if filter_query:
            params['filter'] = filter_query

        response = self._make_request('GET', 'tables/entities', params=params)
        return response.get('data', []) if response and response.get('success') else []

    # ===== TASK QUERY OPERATIONS =====

    def get_latest_task_by_type(self, org_id: str, task_type: str, status: str = None, execution_log_id: str = None) -> Optional[Dict[str, Any]]:
        """Get the latest task of a specific type for an organization"""
        params = {
            'org_id': org_id,
            'task_type': task_type
        }
        if status:
            params['status'] = status
        if execution_log_id:
            params['execution_log_id'] = execution_log_id

        response = self._make_request('GET', 'tasks/latest', params=params)
        if response and response.get('success'):
            return response.get('data')
        return None

    def get_task_by_execution_log_and_type(self, execution_log_id: str, task_type: str) -> Optional[Dict[str, Any]]:
        """Get task by execution log ID and task type"""
        params = {
            'execution_log_id': execution_log_id,
            'task_type': task_type
        }
        response = self._make_request('GET', 'tasks/by-execution-log-and-type', params=params)
        if response and response.get('success'):
            return response.get('data')
        return None

    def get_policies_by_execution_log(self, org_id: str, execution_log_id: str) -> List[Dict[str, Any]]:
        """Get policies by execution log ID"""
        params = {
            'org_id': org_id,
            'execution_log_id': execution_log_id
        }
        response = self._make_request('GET', 'security/policies-result', params=params)
        return response.get('data', []) if response and response.get('success') else []

    def get_policies_by_execution_log_and_type(self, org_id: str, execution_log_id: str, policy_type: str) -> List[Dict[str, Any]]:
        """Get policies by execution log ID and type"""
        params = {
            'org_id': org_id,
            'execution_log_id': execution_log_id,
            'type': policy_type
        }
        response = self._make_request('GET', 'security/policies-result', params=params)
        return response.get('data', []) if response and response.get('success') else []

    def get_assignments_by_execution_log_and_type(self, org_id: str, execution_log_id: str, assignment_type: str) -> List[Dict[str, Any]]:
        """Get assignments by execution log ID and type"""
        params = {
            'org_id': org_id,
            'execution_log_id': execution_log_id,
            'type': assignment_type
        }
        response = self._make_request('GET', 'security/profile-assignment-counts', params=params)
        return response.get('data', []) if response and response.get('success') else []

    def store_permission_sets_data(self, org_id: str, execution_log_id: str, permission_sets_data: List[Dict[str, Any]]) -> bool:
        """Store permission sets data"""
        data = {
            'org_id': org_id,
            'execution_log_id': execution_log_id,
            'permission_sets_data': permission_sets_data
        }
        response = self._make_request('POST', 'security/permission-sets', data=data)
        return response and response.get('success', False)

    def store_pmd_finding(self, org_id: str, pmd_finding: Dict[str, Any]) -> bool:
        """Store PMD finding via DB service"""
        data = {
            'org_id': org_id,
            'pmd_finding': pmd_finding
        }
        response = self._make_request('POST', 'security/pmd-findings', data=data)
        return response and response.get('success', False)

    def get_pmd_findings(self, org_id: str) -> List[Dict[str, Any]]:
        """Get PMD findings for an organization"""
        response = self._make_request('GET', f'security/pmd-findings/{org_id}')
        return response.get('data', []) if response and response.get('success') else []

    def get_pmd_subtasks(self, integration_id: str) -> List[Dict[str, Any]]:
        """Get enabled PMD subtasks for an integration"""
        response = self._make_request('GET', f'pmd/subtasks/{integration_id}')
        return response.get('data', []) if response and response.get('success') else []

    def get_pmd_rules(self, subtask_id: str) -> List[Dict[str, Any]]:
        """Get enabled PMD rules for a subtask"""
        response = self._make_request('GET', f'pmd/rules/{subtask_id}')
        return response.get('data', []) if response and response.get('success') else []

    def get_pmd_configuration(self, integration_id: str) -> Dict[str, Any]:
        """Get complete PMD configuration for an integration"""
        response = self._make_request('GET', f'pmd/configuration/{integration_id}')
        return response.get('data', {}) if response and response.get('success') else {}

    def toggle_pmd_subtask(self, subtask_id: str, enabled: bool) -> Dict[str, Any]:
        """Toggle enabled status of a PMD subtask"""
        data = {'enabled': enabled}
        response = self._make_request('PUT', f'pmd/subtasks/{subtask_id}/toggle', data=data)
        return response if response else {"success": False, "error": "Failed to toggle PMD subtask"}

    def toggle_pmd_rule(self, rule_id: str, enabled: bool) -> Dict[str, Any]:
        """Toggle enabled status of an individual PMD rule"""
        data = {'enabled': enabled}
        response = self._make_request('PUT', f'pmd/rules/{rule_id}/toggle', data=data)
        return response if response else {"success": False, "error": "Failed to toggle PMD rule"}

    # ===== POLICY AND RULE OPERATIONS =====

    def store_policy(self, policy_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store policy data"""
        response = self._make_request('POST', 'policies', data=policy_data)
        return response

    def get_policies_for_integration(self, integration_id: str) -> List[Dict[str, Any]]:
        """Get all policies for an integration"""
        params = {'integration_id': integration_id}
        response = self._make_request('GET', 'policies', params=params)
        return response.get('data', []) if response and response.get('success') else []

    def store_rule(self, rule_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store rule data"""
        response = self._make_request('POST', 'rules', data=rule_data)
        return response

    def get_rules_for_policy(self, policy_id: str) -> List[Dict[str, Any]]:
        """Get all rules for a policy"""
        params = {'policy_id': policy_id}
        response = self._make_request('GET', 'rules', params=params)
        return response.get('data', []) if response and response.get('success') else []

    def store_policy_rule_setting(self, setting_data: Dict[str, Any]) -> Dict[str, Any]:
        """Store policy rule setting data"""
        response = self._make_request('POST', 'policy-rule-settings', data=setting_data)
        return response

    def get_enabled_tasks_for_integration(self, integration_id: str, user_id: str = None) -> List[str]:
        """Get list of enabled task types for an integration"""
        params = {'integration_id': integration_id}
        if user_id:
            params['user_id'] = user_id
        response = self._make_request('GET', 'policy-rule-settings/enabled-tasks', params=params)
        return response.get('data', []) if response and response.get('success') else []

    def create_default_policies_and_rules(self, policy_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create default policies and rules for an integration"""
        response = self._make_request('POST', 'policies/create-defaults', data=policy_data)
        return response if response else {"success": False, "error": "Failed to create default policies and rules"}

    # ===== MIGRATION COMPATIBILITY METHODS =====

    def insert_pmd_scan(self, pmd_scan_data: Dict[str, Any]) -> bool:
        """Insert PMD scan data (compatibility method for migrated code)"""
        data = {
            'table_name': 'PMDScans',
            'entity_data': pmd_scan_data
        }
        response = self._make_request('POST', 'tables/entity', data=data)
        return response and response.get('success', False)

    def insert_policy(self, policy_data: Dict[str, Any]) -> bool:
        """Insert policy data (compatibility method for migrated code)"""
        response = self.store_policy(policy_data)
        return response and response.get('success', False)

    def insert_rule(self, rule_data: Dict[str, Any]) -> bool:
        """Insert rule data (compatibility method for migrated code)"""
        response = self.store_rule(rule_data)
        return response and response.get('success', False)

    def insert_policy_rule_setting(self, setting_data: Dict[str, Any]) -> bool:
        """Insert policy rule setting data (compatibility method for migrated code)"""
        response = self.store_policy_rule_setting(setting_data)
        return response and response.get('success', False)

    # ===== BLOB STORAGE OPERATIONS =====

    def list_blobs(self, container_name: str, name_starts_with: str = None) -> List[str]:
        """List blobs in a container"""
        params = {'container_name': container_name}
        if name_starts_with:
            params['name_starts_with'] = name_starts_with

        response = self._make_request('GET', 'blobs/list', params=params)
        return response.get('data', []) if response and response.get('success') else []

    def get_blob_bytes(self, container_name: str, blob_name: str) -> Optional[bytes]:
        """Get blob content as bytes"""
        params = {
            'container_name': container_name,
            'blob_name': blob_name
        }
        response = self._make_request('GET', 'blobs/content', params=params)
        if response and response.get('success'):
            # Convert base64 content back to bytes
            import base64
            content_b64 = response.get('data', {}).get('content', '')
            return base64.b64decode(content_b64) if content_b64 else None
        return None

    def upload_blob(self, container_name: str, blob_name: str, file_path: str) -> bool:
        """Upload a file to blob storage"""
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            import base64
            data = {
                'container_name': container_name,
                'blob_name': blob_name,
                'content': base64.b64encode(file_content).decode('utf-8')
            }
            response = self._make_request('POST', 'blobs/upload', data=data)
            return response and response.get('success', False)
        except Exception as e:
            logger.error(f"Error uploading blob {blob_name}: {e}")
            return False

    def get_blob_info(self, container_name: str, blob_prefix: str = None) -> Dict[str, Any]:
        """Get information about blobs in a container"""
        params = {'container_name': container_name}
        if blob_prefix:
            params['blob_prefix'] = blob_prefix

        response = self._make_request('GET', 'blobs/info', params=params)
        return response.get('data', {}) if response and response.get('success') else {}

    def get_pending_tasks(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get pending tasks from the database service

        Args:
            limit: Maximum number of tasks to retrieve

        Returns:
            List of pending task dictionaries
        """
        try:
            # Use lowercase "pending" to match the actual status values in the database
            params = {'limit': limit, 'status': 'pending'}
            response = self._make_request('GET', 'tasks', params=params)

            if response and response.get('success'):
                return response.get('data', [])
            else:
                logger.warning(f"Failed to get pending tasks: {response}")
                return []

        except Exception as e:
            logger.error(f"Error getting pending tasks: {e}")
            return []

    def create_execution_log(self, execution_log):
        """Client method to create a new execution log via DB service."""
        try:
            response = self._make_request('POST', 'execution-logs', data=execution_log)
            return response if response else {"success": False, "error": "Failed to create execution log"}
        except Exception as e:
            logger.error(f"Error creating execution log: {e}")
            return {"success": False, "error": str(e)}

    def update_execution_log(self, execution_log_id, update_data):
        """Client method to update an execution log via DB service."""
        try:
            params = {'execution_log_id': execution_log_id}
            response = self._make_request('PUT', 'execution-logs/update', data=update_data, params=params)
            return response if response else {"success": False, "error": "Failed to update execution log"}
        except Exception as e:
            logger.error(f"Error updating execution log {execution_log_id}: {e}")
            return {"success": False, "error": str(e)}

    def get_execution_log_by_id(self, execution_log_id):
        """Client method to get an execution log by ID via DB service."""
        try:
            params = {'execution_log_id': execution_log_id}
            response = self._make_request('GET', 'execution-logs/get', params=params)
            return response.get('data') if response and response.get('success') else None
        except Exception as e:
            logger.error(f"Error getting execution log {execution_log_id}: {e}")
            return None

    def list_execution_logs_by_org(self, org_id):
        """Client method to list all execution logs for an org via DB service."""
        try:
            params = {'org_id': org_id}
            response = self._make_request('GET', 'execution-logs', params=params)
            return response.get('data', []) if response and response.get('success') else []
        except Exception as e:
            logger.error(f"Error listing execution logs for org {org_id}: {e}")
            return []


# Global client instance
_db_client = None

def get_db_client() -> DatabaseServiceClient:
    """Get the global database service client instance"""
    global _db_client
    if _db_client is None:
        _db_client = DatabaseServiceClient()
    return _db_client

# Alias for backward compatibility
db_service_client = get_db_client() 