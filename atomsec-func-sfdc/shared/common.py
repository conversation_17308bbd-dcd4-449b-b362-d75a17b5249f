"""
Common Module

This module provides common functions and utilities used across the application.
It helps break circular imports between modules.
"""

import os
import logging

# Configure module-level logger
logger = logging.getLogger(__name__)

# Environment detection
def is_local_dev() -> bool:
    """Check if running in local development environment"""
    # First check for explicit local development flag
    if os.environ.get("IS_LOCAL_DEV") == "true":
        logger.info("Local development environment detected via IS_LOCAL_DEV flag")
        return True

    # Check if running locally based on environment variables
    if os.environ.get("FUNCTIONS_WORKER_RUNTIME_VERSION") is None:
        logger.info("Local development environment detected via FUNCTIONS_WORKER_RUNTIME_VERSION")
        return True

    # Check if USE_LOCAL_STORAGE is set
    if os.environ.get("USE_LOCAL_STORAGE") == "true":
        logger.info("Local development environment detected via USE_LOCAL_STORAGE flag")
        return True

    # Check if running on localhost
    if os.environ.get("WEBSITE_HOSTNAME", "").startswith("localhost"):
        logger.info("Local development environment detected via WEBSITE_HOSTNAME")
        return True

    logger.info("Production environment detected")
    return False

def is_test_env() -> bool:
    """Check if running in test environment"""
    return os.environ.get("ATOMSEC_TEST_ENV") == "true" or os.environ.get("PYTEST_RUNNING") == "true"
