"""
Task Processor Function

This function processes tasks from the task queue and updates their status.
"""

import json
import logging
import azure.functions as func
import requests
import urllib.parse
import traceback
import re
import xml.etree.ElementTree as ET
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import asyncio
import uuid

# Try to import fuzzywuzzy with fallback
try:
    from fuzzywuzzy import fuzz
    FUZZYWUZZY_AVAILABLE = True
except ImportError:
    FUZZYWUZZY_AVAILABLE = False
    # Note: logger is defined later, so we'll log this warning in the functions that need it

# Import shared modules
from shared.background_processor import (
    BackgroundProcessor,
    TASK_STATUS_PENDING,
    TASK_STATUS_RUNNING,
    TASK_STATUS_COMPLETED,
    TASK_STATUS_FAILED,
    TASK_STATUS_RETRY,
    TASK_STATUS_CANCELLED,
    TASK_TYPE_OVERVIEW,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_PROFILES,
    TASK_TYPE_PROFILES_PERMISSION_SETS,
    TASK_TYPE_DATA_EXPORT,
    TASK_TYPE_REPORT_GENERATION,
    TASK_TYPE_SCHEDULED_SCAN,
    TASK_TYPE_NOTIFICATION,
    TASK_TYPE_METADATA_EXTRACTION,
    TASK_TYPE_SFDC_AUTHENTICATE,
    TASK_TYPE_PERMISSION_SETS,
    TASK_TYPE_MFA_ENFORCEMENT,
    TASK_TYPE_DEVICE_ACTIVATION,
    TASK_TYPE_LOGIN_IP_RANGES,
    TASK_TYPE_LOGIN_HOURS,
    TASK_TYPE_SESSION_TIMEOUT,
    TASK_TYPE_API_WHITELISTING,
    TASK_TYPE_PASSWORD_POLICY,
    TASK_TYPE_PMD_APEX_SECURITY,
    TASK_PRIORITY_HIGH,
    TASK_PRIORITY_MEDIUM,
    TASK_PRIORITY_LOW
)

from shared.salesforce_utils import (
    execute_salesforce_query,
    execute_salesforce_tooling_query,
    get_salesforce_access_token,
    test_salesforce_connection,
    get_salesforce_client
)
from shared.auth_service import authenticate_salesforce_integration
from shared.data_access import BlobStorageRepository, get_table_storage_repository  # Still needed for blob storage and credentials operations
from shared.common import is_local_dev
from shared.azure_services import get_secret
from shared.metadata_extraction import extract_salesforce_metadata
from blueprints.security_analysis import fetch_security_health_check_risks, calculate_health_score, process_and_store_policies_results # For health check task
from shared.db_service_client import get_db_client
from fastapi import APIRouter

# Import task processors - temporarily disabled problematic imports
from .tasks.pmd_task import process_pmd_task
from .tasks.password_policy import process_password_policy_task
from .tasks.device_activation import process_device_activation_task
from .tasks.session_timeout import process_session_timeout_task
from .tasks.mfa_enforcement import process_mfa_enforcement_task
from .tasks.login_ip_ranges import process_login_ip_ranges_task
from .tasks.login_hours import process_login_hours_task
from .tasks.api_whitelisting import process_api_whitelisting_task
from .tasks.profiles_permission_sets import process_profiles_permission_sets_task
from .tasks.pmd_task import process_pmd_task

router = APIRouter()

# Configure module-level logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Task type mapping from string to constants
TASK_TYPE_MAPPING = {
    "sfdc_authenticate": TASK_TYPE_SFDC_AUTHENTICATE,
    "metadata_extraction": TASK_TYPE_METADATA_EXTRACTION,
    "health_check": TASK_TYPE_HEALTH_CHECK,
    "profiles": TASK_TYPE_PROFILES,
    "profiles_permission_sets": TASK_TYPE_PROFILES_PERMISSION_SETS,
    "permission_sets": TASK_TYPE_PERMISSION_SETS,
    "mfa_enforcement": TASK_TYPE_MFA_ENFORCEMENT,
    "device_activation": TASK_TYPE_DEVICE_ACTIVATION,
    "login_ip_ranges": TASK_TYPE_LOGIN_IP_RANGES,
    "login_hours": TASK_TYPE_LOGIN_HOURS,
    "session_timeout": TASK_TYPE_SESSION_TIMEOUT,
    "api_whitelisting": TASK_TYPE_API_WHITELISTING,
    "password_policy": TASK_TYPE_PASSWORD_POLICY,
    "pmd_apex_security": TASK_TYPE_PMD_APEX_SECURITY,
    "overview": TASK_TYPE_OVERVIEW,
    "data_export": TASK_TYPE_DATA_EXPORT,
    "report_generation": TASK_TYPE_REPORT_GENERATION,
    "scheduled_scan": TASK_TYPE_SCHEDULED_SCAN,
    "notification": TASK_TYPE_NOTIFICATION
}

# Task dependency definitions
TASK_DEPENDENCIES = {
    # Health check runs immediately after authentication to verify connection
    TASK_TYPE_HEALTH_CHECK: [TASK_TYPE_SFDC_AUTHENTICATE],

    # Overview and profiles tasks run immediately after authentication (no dependencies on metadata)
    TASK_TYPE_OVERVIEW: [TASK_TYPE_SFDC_AUTHENTICATE],
    TASK_TYPE_PROFILES: [TASK_TYPE_SFDC_AUTHENTICATE],

    # Metadata extraction runs after authentication (not after health check)
    TASK_TYPE_METADATA_EXTRACTION: [TASK_TYPE_SFDC_AUTHENTICATE],

    # All policy tasks depend on metadata extraction being completed
    TASK_TYPE_PROFILES_PERMISSION_SETS: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_MFA_ENFORCEMENT: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_DEVICE_ACTIVATION: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_LOGIN_IP_RANGES: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_LOGIN_HOURS: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_SESSION_TIMEOUT: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_API_WHITELISTING: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_PASSWORD_POLICY: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_PMD_APEX_SECURITY: [TASK_TYPE_METADATA_EXTRACTION],
    TASK_TYPE_PERMISSION_SETS: [TASK_TYPE_METADATA_EXTRACTION]
}

def check_task_dependencies_and_wait(processor: BackgroundProcessor, task_type: str, org_id: str, execution_log_id: str) -> tuple[bool, str]:
    """
    Check if all prerequisite tasks for a given task type have been completed.
    Implements sequential task execution with a 5-second delay before dependent tasks start.

    This replaces the retry-based approach with a cleaner sequential execution model:
    - sfdc_authenticate runs first (no dependencies)
    - health_check waits 5 seconds after authentication completion
    - metadata_extraction waits 5 seconds after health_check completion

    Args:
        processor: Background processor instance
        task_type: Type of task to check dependencies for (string or constant)
        org_id: Organization ID
        execution_log_id: Execution log ID for the current workflow

    Returns:
        tuple: (can_proceed, message)
    """
    import time

    try:
        # Convert string task type to constant if needed
        if task_type in TASK_TYPE_MAPPING:
            constant_task_type = TASK_TYPE_MAPPING[task_type]
            logger.debug(f"Mapped task type '{task_type}' to constant '{constant_task_type}'")
        else:
            # Assume it's already a constant
            constant_task_type = task_type

        # Get required dependencies for this task type
        required_deps = TASK_DEPENDENCIES.get(constant_task_type, [])

        if not required_deps:
            # No dependencies required (e.g., sfdc_authenticate)
            logger.info(f"Task {task_type} has no dependencies, proceeding immediately")
            return True, "No dependencies required"

        db_client = get_db_client()

        # Check if all dependencies are completed with retry mechanism for database consistency
        max_retries = 5  # Increased from 3 to 5
        retry_delay = 3  # Increased from 2 to 3 seconds
        
        for attempt in range(max_retries):
            all_deps_satisfied = True
            missing_deps = []

            for dep_task_type in required_deps:
                latest_task = None
                
                # Try DB service first
                if db_client:
                    try:
                        # Find the most recent completed prerequisite task for this org, task type, and execution_log_id
                        latest_task = db_client.get_latest_task_by_type(org_id, dep_task_type, "completed", execution_log_id)
                        logger.info(f"[DEPENDENCY CHECK DEBUG] org_id={org_id}, dep_task_type={dep_task_type}, execution_log_id={execution_log_id}, latest_task={latest_task}")
                        
                        # Enhanced logging for dependency checking
                        if latest_task:
                            logger.info(f"[DEPENDENCY CHECK SUCCESS] Found completed dependency task: {dep_task_type} (ID: {latest_task.get('task_id')})")
                        else:
                            logger.warning(f"[DEPENDENCY CHECK MISSING] Could not find completed dependency task: {dep_task_type}")
                            # Log all tasks for this org to help diagnose issues
                            all_org_tasks = db_client.get_tasks_by_org(org_id)
                            if all_org_tasks:
                                logger.info(f"[DEPENDENCY CHECK DETAIL] Found {len(all_org_tasks)} total tasks for org {org_id}")
                                for task in all_org_tasks:
                                    if task.get('task_type') == dep_task_type:
                                        logger.info(f"[DEPENDENCY CHECK DETAIL] Found task of type {dep_task_type}: ID={task.get('task_id')}, status={task.get('status')}, execution_log_id={task.get('execution_log_id')}")
                            else:
                                logger.warning(f"[DEPENDENCY CHECK DETAIL] No tasks found for org {org_id}")

                        if not latest_task:
                            # Try without status filter as fallback
                            latest_task = db_client.get_latest_task_by_type(org_id, dep_task_type, None, execution_log_id)
                            logger.info(f"[DEPENDENCY CHECK FALLBACK] org_id={org_id}, dep_task_type={dep_task_type}, execution_log_id={execution_log_id}, latest_task={latest_task}")
                            if latest_task:
                                logger.info(f"[DEPENDENCY CHECK FALLBACK] Found task with status '{latest_task.get('status')}' instead of 'completed'")
                    except Exception as db_error:
                        logger.warning(f"[DEPENDENCY CHECK] DB service error for {dep_task_type}: {str(db_error)}")
                        latest_task = None
                
                # If DB service failed or returned no result, try local repository
                if not latest_task:
                    try:
                        logger.info(f"[DEPENDENCY CHECK] Trying local repository for {dep_task_type}")
                        from shared.background_processor import BackgroundProcessor
                        local_processor = BackgroundProcessor()
                        
                        # Get tasks for the org and filter by type and status
                        org_tasks = local_processor.get_tasks_by_org(org_id, include_completed=True)
                        
                        # Filter by task type, execution_log_id, and completed status
                        matching_tasks = []
                        for task in org_tasks:
                            task_type_match = task.get('task_type') == dep_task_type
                            execution_log_match = task.get('execution_log_id') == execution_log_id
                            status_match = task.get('status', '').lower() == 'completed'
                            
                            if task_type_match and execution_log_match and status_match:
                                matching_tasks.append(task)
                        
                        if matching_tasks:
                            # Sort by created_at descending and take the first one
                            matching_tasks.sort(key=lambda x: x.get('created_at', ''), reverse=True)
                            latest_task = matching_tasks[0]
                            logger.info(f"[DEPENDENCY CHECK] Found completed task via local repository: {latest_task.get('task_id')}")
                        else:
                            logger.warning(f"[DEPENDENCY CHECK] No completed task found in local repository for {dep_task_type}")
                    except Exception as local_error:
                        logger.error(f"[DEPENDENCY CHECK] Local repository error for {dep_task_type}: {str(local_error)}")
                        latest_task = None
                
                # Check if we found a completed task
                if latest_task and latest_task.get('status', '').lower() == 'completed':
                    logger.info(f"[DEPENDENCY CHECK] Found completed task: {latest_task.get('task_id')}")
                else:
                    all_deps_satisfied = False
                    missing_deps.append(dep_task_type)

            if all_deps_satisfied:
                # All dependencies are satisfied - implement 5-second sequential delay
                logger.info(f"[DEPENDENCY CHECK] All dependencies satisfied for {task_type} on attempt {attempt + 1}. Implementing 5-second sequential delay before processing.")
                time.sleep(5.0)  # 5-second delay for sequential execution
                logger.info(f"Sequential delay completed for {task_type}. Proceeding with task processing.")
                return True, "All dependencies satisfied - sequential delay completed"
            
            # Dependencies not met on this attempt
            missing_dep_str = ', '.join(missing_deps)
            logger.warning(f"[DEPENDENCY CHECK] Task {task_type} dependencies not met on attempt {attempt + 1}: {missing_dep_str}")
            
            if attempt < max_retries - 1:
                logger.info(f"[DEPENDENCY CHECK] Retrying in {retry_delay} seconds... (attempt {attempt + 1}/{max_retries})")
                time.sleep(retry_delay)
            else:
                # Final attempt failed - try to proceed anyway for critical tasks
                if task_type in ['health_check', 'metadata_extraction', 'profiles_permission_sets', 'mfa_enforcement', 'login_ip_ranges']:
                    logger.warning(f"[DEPENDENCY CHECK] Task {task_type} dependencies not met after {max_retries} attempts, but proceeding anyway for critical task: {missing_dep_str}")
                    return True, f"Proceeding despite missing dependencies: {missing_dep_str}"
                else:
                    # Final attempt failed
                    logger.error(f"[DEPENDENCY CHECK] Task {task_type} dependencies not met after {max_retries} attempts: {missing_dep_str}")
                    return False, f"Prerequisite task '{missing_dep_str}' has not completed successfully after {max_retries} attempts"

    except Exception as e:
        logger.error(f"Error checking task dependencies for {task_type}: {str(e)}")
        # For critical tasks, proceed anyway
        if task_type in ['health_check', 'metadata_extraction', 'profiles_permission_sets', 'mfa_enforcement', 'login_ip_ranges']:
            logger.warning(f"Proceeding with {task_type} despite dependency check error: {str(e)}")
            return True, f"Proceeding despite dependency check error: {str(e)}"
        else:
            return False, f"Error checking dependencies: {str(e)}"

@router.get("/api/profiles-permissions")
def get_profiles_permissions(orgId: str):
    """
    Get profiles and permissions data for an organization (legacy API endpoint)

    Args:
        orgId: Organization ID

    Returns:
        Dict containing profiles and permissions data
    """
    try:
        db_client = get_db_client()

        # 1. Get latest profiles_permission_sets task
        latest_profiles_task = db_client.get_latest_task_by_type(orgId, "profiles_permission_sets", "completed")
        if not latest_profiles_task:
            return {"status": "processing", "message": "Profiles and permissions scan is still running."}

        execution_log_id_profiles = latest_profiles_task.get("execution_log_id")

        # 2. Get latest permission_sets task
        latest_psets_task = db_client.get_latest_task_by_type(orgId, "permission_sets", "completed")
        execution_log_id_psets = None
        if latest_psets_task:
            execution_log_id_psets = latest_psets_task.get("execution_log_id")

        # 3. Query for profile policies (all types for profiles_permission_sets)
        all_policies = db_client.get_policies_by_execution_log(orgId, execution_log_id_profiles)

        # 4. Query for permission set policies (Type = 'PermissionSetPermissions' for permission_sets)
        if execution_log_id_psets:
            permission_set_policies = db_client.get_policies_by_execution_log_and_type(
                orgId, execution_log_id_psets, "PermissionSetPermissions"
            )
            all_policies.extend(permission_set_policies)

        # 5. Also fetch PermissionSetPermissions records for profiles_permission_sets task (legacy support)
        permission_set_policies_profiles = db_client.get_policies_by_execution_log_and_type(
            orgId, execution_log_id_profiles, "PermissionSetPermissions"
        )
        all_policies.extend(permission_set_policies_profiles)

        # 6. Fetch assignments (for latest profiles_permission_sets task)
        profile_assignments = db_client.get_assignments_by_execution_log_and_type(
            orgId, execution_log_id_profiles, "ProfilePermissions"
        )
        permissionset_assignments = db_client.get_assignments_by_execution_log_and_type(
            orgId, execution_log_id_profiles, "ProfilePermissionSetAssignment"
        )

        # 7. Collect all unique permission setting names for dynamic columns
        all_settings = set()
        for policy in all_policies:
            settings_data = policy.get('Settings', '{}')
            if isinstance(settings_data, str):
                try:
                    import json
                    settings = json.loads(settings_data)
                    all_settings.update(settings.keys())
                except json.JSONDecodeError:
                    continue
            elif isinstance(settings_data, dict):
                all_settings.update(settings_data.keys())

        return {
            "status": "completed",
            "settings": sorted(list(all_settings)),
            "policies": all_policies,
            "profileAssignments": profile_assignments,
            "permissionSetAssignments": permissionset_assignments
        }

    except Exception as e:
        logger.error(f"Error getting profiles permissions for org {orgId}: {str(e)}")
        return {
            "status": "error",
            "message": f"Error retrieving profiles and permissions data: {str(e)}"
        }

def main(msg: func.QueueMessage) -> None:
    """
    Process a task from the queue

    Args:
        msg: Queue message
    """
    import traceback
    logger.info("[MAIN] Entered main function for task processing.")
    try:
        # Handle message content
        logger.info("[MAIN] Processing queue message...")

        # With messageEncoding="none" in host.json, Azure Functions provides the message as text
        # We just need to get the message body and parse it as JSON
        try:
            # Handle different message formats
            if isinstance(msg, dict):
                # Message is already a dict (direct JSON)
                task_data = msg
                logger.debug(f"[MAIN] Message is already a dict: {task_data}")
            elif hasattr(msg, 'get_body'):
                # Message is a queue message object
                message_body = msg.get_body().decode('utf-8')
                logger.debug(f"[MAIN] Message body: {message_body}")
                task_data = json.loads(message_body)
                logger.debug(f"[MAIN] Successfully parsed message as JSON: {task_data}")
            else:
                # Try to convert to string and parse
                message_body = str(msg)
                logger.debug(f"[MAIN] Converting message to string: {message_body}")
                task_data = json.loads(message_body)
                logger.debug(f"[MAIN] Successfully parsed message as JSON: {task_data}")
        except Exception as e:
            logger.error(f"[MAIN] Error processing message: {str(e)}")
            # Log the raw message for debugging
            logger.error(f"[MAIN] Message type: {type(msg)}")
            logger.error(f"[MAIN] Message content: {msg}")
            if hasattr(msg, 'get_body'):
                try:
                    body = msg.get_body()
                    logger.error(f"[MAIN] Message body: {body}")
                    logger.error(f"[MAIN] Message body type: {type(body)}")
                except Exception as body_error:
                    logger.error(f"[MAIN] Error getting message body: {str(body_error)}")
            logger.error(f"[MAIN] Traceback:\n{traceback.format_exc()}")
            raise ValueError(f"Could not process message: {str(e)}")

        # Log the parsed task data for debugging
        logger.debug(f"[MAIN] Parsed task data: {task_data}")

        # Extract task information
        task_id = task_data.get("task_id")
        task_type = task_data.get("task_type")
        org_id = task_data.get("org_id")
        user_id = task_data.get("user_id")
        params = task_data.get("params", {})
        execution_log_id = task_data.get("execution_log_id")

        # Normalize task_type to avoid whitespace/case issues
        task_type = (task_type or "").strip().lower()
        logger.info(f"[MAIN] Received task_type: '{task_type}'")

        logger.info(f"[MAIN] Processing task {task_id} of type {task_type} for organization {org_id}")
        logger.debug(f"[MAIN] Task parameters: {params}")

        # Initialize background processor
        processor = BackgroundProcessor()

        try:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=5,
                message="Task started - preventing duplicate processing"
            )
            logger.info(f"[MAIN] Updated task {task_id} status to RUNNING to prevent race conditions")
        except Exception as status_error:
            logger.error(f"[MAIN] Error updating task status: {str(status_error)}")
            # Continue processing even if status update fails

        # Update task status to running with generic message
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=10,
            message="Task started"
        )

        # Extract priority and retry count
        priority = task_data.get("priority", TASK_PRIORITY_MEDIUM)
        retry_count = task_data.get("retry_count", 0)

        # Log task details
        logger.info(f"[MAIN] Processing task {task_id} of type {task_type} for organization {org_id} with priority {priority} (retry {retry_count})")

        # Check if this is a recurring task that needs to be rescheduled
        is_recurring = params.get("is_recurring", False)
        if is_recurring and task_type == TASK_TYPE_SCHEDULED_SCAN:
            logger.info(f"[MAIN] Rescheduling recurring task {task_id}.")
            # Reschedule the task for the next occurrence
            schedule_type = params.get("schedule_type", "daily")
            processor.schedule_recurring_task(
                task_type=TASK_TYPE_SCHEDULED_SCAN,
                org_id=org_id,
                user_id=user_id,
                schedule_type=schedule_type,
                params=params,
                priority=priority
            )
            logger.info(f"[MAIN] Rescheduled recurring task {task_id} with schedule type {schedule_type}")

        # Check task dependencies and implement sequential delay before processing
        logger.info(f"[MAIN] Checking dependencies and implementing sequential delay for task {task_id} of type '{task_type}'")
        can_proceed, dependency_message = check_task_dependencies_and_wait(processor, task_type, org_id, execution_log_id)
        if not can_proceed:
            logger.warning(f"[MAIN] Task {task_id} cannot proceed: {dependency_message}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Dependencies not met: {dependency_message}"
            )
            return

        logger.info(f"[MAIN] Dependencies satisfied and sequential delay completed for task {task_id}: {dependency_message}")

        # Convert string task type to constant if needed
        if task_type in TASK_TYPE_MAPPING:
            constant_task_type = TASK_TYPE_MAPPING[task_type]
            logger.debug(f"[MAIN] Mapped task type '{task_type}' to constant '{constant_task_type}'")
        else:
            # Assume it's already a constant
            constant_task_type = task_type

        # Process task based on type
        logger.info(f"[MAIN] Branching to task type handler for {constant_task_type}")
        if constant_task_type == TASK_TYPE_OVERVIEW:
            logger.info(f"[MAIN] Calling process_overview_task for {task_id}")
            process_overview_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_HEALTH_CHECK:
            logger.info(f"[MAIN] Calling process_health_check_task for {task_id}")
            process_health_check_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PROFILES:
            logger.info(f"[MAIN] Calling process_profiles_task for {task_id}")
            process_profiles_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PROFILES_PERMISSION_SETS:
            logger.info(f"[MAIN] Calling process_profiles_permission_sets_task for {task_id}")
            process_profiles_permission_sets_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PERMISSION_SETS:
            logger.info(f"[MAIN] Calling process_permission_sets_task for {task_id}")
            process_permission_sets_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_MFA_ENFORCEMENT:
            logger.info(f"[MAIN] Calling process_mfa_enforcement_task for {task_id}")
            process_mfa_enforcement_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_DEVICE_ACTIVATION:
            logger.info(f"[MAIN] Calling process_device_activation_task for {task_id}")
            process_device_activation_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_LOGIN_IP_RANGES:
            logger.info(f"[MAIN] Calling process_login_ip_ranges_task for {task_id}")
            process_login_ip_ranges_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_LOGIN_HOURS:
            logger.info(f"[MAIN] Calling process_login_hours_task for {task_id}")
            process_login_hours_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_SESSION_TIMEOUT:
            logger.info(f"[MAIN] Calling process_session_timeout_task for {task_id}")
            process_session_timeout_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_API_WHITELISTING:
            logger.info(f"[MAIN] Calling process_api_whitelisting_task for {task_id}")
            process_api_whitelisting_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PASSWORD_POLICY:
            logger.info(f"[MAIN] Calling process_password_policy_task for {task_id}")
            process_password_policy_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_PMD_APEX_SECURITY:
            logger.info(f"[MAIN] Calling process_pmd_task for {task_id}")
            process_pmd_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_DATA_EXPORT:
            logger.info(f"[MAIN] Calling process_data_export_task for {task_id}")
            process_data_export_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_REPORT_GENERATION:
            logger.info(f"[MAIN] Calling process_report_generation_task for {task_id}")
            process_report_generation_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_SCHEDULED_SCAN:
            logger.info(f"[MAIN] Calling process_scheduled_scan_task for {task_id}")
            # For scheduled scans, run all the data collection tasks
            process_scheduled_scan_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_NOTIFICATION:
            logger.info(f"[MAIN] Calling process_notification_task for {task_id}")
            process_notification_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_METADATA_EXTRACTION:
            logger.info(f"[MAIN] Calling process_metadata_extraction_task for {task_id}")
            process_metadata_extraction_task(processor, task_id, org_id, user_id, params, execution_log_id)
        elif constant_task_type == TASK_TYPE_SFDC_AUTHENTICATE:
            logger.info(f"[MAIN] Calling process_sfdc_authenticate_task for {task_id}")
            process_sfdc_authenticate_task(processor, task_id, org_id, user_id, params, execution_log_id)
        else:
            logger.error(f"[MAIN] Unknown task type: {task_type} (mapped to: {constant_task_type})")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Unknown task type: {task_type}"
            )
        logger.info(f"[MAIN] Exiting main function for task {task_id}")
    except Exception as e:
        logger.error(f"[MAIN] Error processing task: {str(e)}")
        logger.error(f"[MAIN] Traceback:\n{traceback.format_exc()}")

        # Try to update task status if possible
        try:
            processor = BackgroundProcessor()
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Error processing task: {str(e)}"
            )
        except Exception as update_error:
            logger.error(f"[MAIN] Error updating task status: {str(update_error)}")
            logger.error(f"[MAIN] Traceback (update error):\n{traceback.format_exc()}")

def get_integration_by_id(org_id: str) -> Dict[str, Any]:
    """
    Get integration by ID

    Args:
        org_id: Organization ID

    Returns:
        Dict[str, Any]: Integration if found, None otherwise
    """
    try:
        db_client = get_db_client()
        integration = db_client.get_integration_by_id(org_id)

        if integration:
            logger.info(f"Found integration: {integration.get('name', 'Unknown')} (ID: {org_id})")
            return integration
        else:
            logger.warning(f"No integration found with ID: {org_id}")
            return None

    except Exception as e:
        logger.error(f"Error getting integration by ID {org_id}: {str(e)}")
        return None

# Salesforce API functions are now imported from shared.salesforce_utils

def process_overview_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process an overview task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        logger.info(f"Processing overview task {task_id} for organization {org_id}")
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Starting overview data collection")

        access_token = params.get("access_token")
        instance_url = params.get("instance_url")
        integration_id_from_params = params.get("integration_id") # Should be same as org_id

        if not all([access_token, instance_url, integration_id_from_params]):
            err_msg = "Missing access_token, instance_url, or integration_id in task parameters for overview."
            logger.error(f"[OverviewTask {task_id}] {err_msg}")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
            return

        if org_id != integration_id_from_params:
            logger.warning(f"[OverviewTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

        try:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=40,
                message="Fetching overview data from Salesforce using provided token."
            )

            # Fetch data from Salesforce
            # 1. Get total profiles
            profiles_query = "SELECT COUNT() FROM Profile"
            profiles_result = execute_salesforce_query(profiles_query, access_token, instance_url)
            total_profiles = profiles_result.get("totalSize", 0) if profiles_result else 0
            logger.info(f"Found {total_profiles} profiles")

            # 2. Get total permission sets
            permission_sets_query = "SELECT COUNT() FROM PermissionSet WHERE IsOwnedByProfile = false"
            permission_sets_result = execute_salesforce_query(permission_sets_query, access_token, instance_url)
            total_permission_sets = permission_sets_result.get("totalSize", 0) if permission_sets_result else 0
            logger.info(f"Found {total_permission_sets} permission sets")

            # Initialize risk counts
            total_risks = 0
            high_risks = 0
            medium_risks = 0
            low_risks = 0

            # Get health score directly from SecurityHealthCheck
            score_query = "SELECT Score FROM SecurityHealthCheck"
            score_result = execute_salesforce_tooling_query(score_query, access_token, instance_url, api_version="v58.0")

            if score_result and "records" in score_result and len(score_result["records"]) > 0:
                # Use the score directly from Salesforce
                health_score = score_result["records"][0].get("Score", 0)
                logger.info(f"Retrieved health score directly from Salesforce: {health_score}")

                # Get total risks count for reference
                risks_count_query = "SELECT COUNT() FROM SecurityHealthCheckRisks"
                risks_count_result = execute_salesforce_tooling_query(risks_count_query, access_token, instance_url)
                total_risks = risks_count_result.get("totalSize", 0) if risks_count_result else 0
                logger.info(f"Found {total_risks} security health check risks")
            else:
                logger.warning("Could not retrieve health score directly from SecurityHealthCheck, using fallback method")

                # 3. Get security health check data
                health_check_query = "SELECT COUNT() FROM SecurityHealthCheckRisks"
                health_check_result = execute_salesforce_tooling_query(health_check_query, access_token, instance_url)
                total_risks = health_check_result.get("totalSize", 0) if health_check_result else 0
                logger.info(f"Found {total_risks} security health check risks")

                # 4. Get risk counts by severity
                high_risks_query = "SELECT COUNT() FROM SecurityHealthCheckRisks WHERE RiskType = 'HIGH_RISK'"
                high_risks_result = execute_salesforce_tooling_query(high_risks_query, access_token, instance_url)
                high_risks = high_risks_result.get("totalSize", 0) if high_risks_result else 0
                logger.info(f"Found {high_risks} high risks")

                medium_risks_query = "SELECT COUNT() FROM SecurityHealthCheckRisks WHERE RiskType = 'MEDIUM_RISK'"
                medium_risks_result = execute_salesforce_tooling_query(medium_risks_query, access_token, instance_url)
                medium_risks = medium_risks_result.get("totalSize", 0) if medium_risks_result else 0
                logger.info(f"Found {medium_risks} medium risks")

                low_risks_query = "SELECT COUNT() FROM SecurityHealthCheckRisks WHERE RiskType = 'LOW_RISK'"
                low_risks_result = execute_salesforce_tooling_query(low_risks_query, access_token, instance_url)
                low_risks = low_risks_result.get("totalSize", 0) if low_risks_result else 0
                logger.info(f"Found {low_risks} low risks")

                # Calculate health score (simple algorithm: 100 - (high_risks * 5 + medium_risks * 2 + low_risks))
                health_score = 100 - (high_risks * 5 + medium_risks * 2 + low_risks)
                health_score = max(0, min(100, health_score))  # Ensure score is between 0 and 100
                logger.info(f"Calculated health score using fallback method: {health_score}")

            # Update task status
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=70,
                message="Saving overview data"
            )

            # Save overview data to database via DB service
            db_client = get_db_client()
            overview_data = {
                "health_score": health_score,
                "total_profiles": total_profiles,
                "total_permission_sets": total_permission_sets,
                "total_risks": total_risks,
                "high_risks": high_risks,
                "medium_risks": medium_risks,
                "low_risks": low_risks,
                "last_updated": datetime.now().isoformat(),
                "execution_log_id": execution_log_id
            }

            success = db_client.store_overview_data(org_id, overview_data)
            if success:
                logger.info(f"Saved overview data via DB service for organization {org_id}")
            else:
                logger.error(f"Failed to save overview data via DB service for organization {org_id}")

            # Update task status
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_COMPLETED,
                progress=100,
                message="Overview data fetched and saved successfully"
            )
        except Exception as e:
            logger.error(f"Error processing overview task: {str(e)}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Error processing overview task: {str(e)}"
            )
    except Exception as e:
        logger.error(f"Error processing overview task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing overview task: {str(e)}"
        )

def process_health_check_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str] = None) -> None:
    execution_log_id = get_execution_log_id_from_params(params, execution_log_id)
    if not execution_log_id:
        logger.error(f"[HealthCheckTask {task_id}] execution_log_id is missing. Refusing to enqueue downstream tasks.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "execution_log_id is missing.")
        return
    logger.info(f"[HealthCheckTask {task_id}] Using execution_log_id: {execution_log_id}")
    # Task-level idempotency check
    task_status = processor.get_task_status(task_id)
    if task_status and task_status.get("status") in (TASK_STATUS_COMPLETED, TASK_STATUS_FAILED, TASK_STATUS_CANCELLED):
        logger.info(f"[HealthCheckTask {task_id}] Task already {task_status.get('status')}, skipping processing.")
        return
    # Execution_log_id-level idempotency check
    if execution_log_id:
        db_client = get_db_client()
        if db_client:
            existing_health_check_records = db_client.get_policies_by_execution_log_and_type(
                org_id=str(org_id),
                execution_log_id=str(execution_log_id),
                policy_type='HealthCheck'
            )
            if existing_health_check_records:
                logger.info(f"[HealthCheckTask {task_id}] Health check data already exists for execution_log_id {execution_log_id}. Marking task as completed.")
                processor.update_task_status(
                    task_id=task_id,
                    status=TASK_STATUS_COMPLETED,
                    progress=100,
                    message=f"Health check already completed for {org_id} with execution_log_id {execution_log_id}",
                    result=json.dumps({"health_score": 0, "risks_count": len(existing_health_check_records)})
                )
                return
    # Immediate task status update to prevent race conditions
    try:
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=5,
            message="Health check task started - preventing duplicate processing"
        )
        logger.info(f"[HealthCheckTask {task_id}] Updated task status to RUNNING to prevent race conditions")
    except Exception as status_error:
        logger.error(f"[HealthCheckTask {task_id}] Error updating task status: {str(status_error)}")
    """
    Process a health check task
    """
    logger.info(f"[HealthCheckTask {task_id}] Received params: {params}")
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    integration_id_from_params = params.get("integration_id")
    if not all([access_token, instance_url]):
        err_msg = "Missing access_token or instance_url in task parameters for health check."
        logger.error(f"[HealthCheckTask {task_id}] {err_msg}")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
        return

    if org_id != integration_id_from_params:
        logger.warning(f"[HealthCheckTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id.")

    try:
        logger.info(f"[HealthCheckTask {task_id}] Fetching security health check risks for {org_id} using provided token.")

        # Handle async operations using asyncio.run to avoid event loop conflicts
        logger.info(f"[HealthCheckTask {task_id}] Running health check async operations")

        def run_async_health_check():
            async def async_health_check():
                risks = await fetch_security_health_check_risks(access_token, instance_url)
                health_score = await calculate_health_score(risks)
                return risks, health_score

            return asyncio.run(async_health_check())

        risks, health_score = run_async_health_check()

        logger.info(f"[HealthCheckTask {task_id}] Calculated health score for {org_id}: {health_score}")
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 70, f"Health score calculated: {health_score}")

        # Store raw health check data via DB service
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 75, "Storing health check data")
        
        # CRITICAL FIX: Remove duplicate health check storage - only store in PoliciesResult table
        # The old architecture only stored health check data in PoliciesResult table, not in HealthCheck table
        logger.info(f"[HealthCheckTask {task_id}] Skipping HealthCheck table storage - using PoliciesResult table only")

        # Process and store policies results (this is the correct storage location)
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 80, "Processing policies results")
        try:
            # Import the function from blueprints
            from blueprints.security_analysis import process_and_store_policies_results
            process_and_store_policies_results(risks, org_id, execution_log_id)
            logger.info(f"[HealthCheckTask {task_id}] Successfully processed and stored policies results for {org_id}")
        except Exception as policies_error:
            logger.error(f"[HealthCheckTask {task_id}] Error processing policies results: {str(policies_error)}")
            # Don't fail the entire task for this error, just log it

        # Update Integration record via DB service
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 90, "Updating integration record")
        db_client = get_db_client()
        now_iso = datetime.now().isoformat()

        update_data = {
            "health_score": str(health_score),
            "last_health_check_scan": now_iso
        }

        success = db_client.update_integration(org_id, update_data)
        if success:
            logger.info(f"[HealthCheckTask {task_id}] Updated integration {org_id} with health score {health_score} via DB service.")
        else:
            logger.error(f"[HealthCheckTask {task_id}] Failed to update integration {org_id} via DB service.")

        # After health check completes successfully, enqueue metadata extraction task
        logger.info(f"[HealthCheckTask {task_id}] Health check completed successfully.")


        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message=f"Health check completed for {org_id}. Score: {health_score}",
            result=json.dumps({"health_score": health_score, "risks_count": len(risks) if isinstance(risks, list) else 0})
        )
    except TypeError as te:
        logger.error(f"[HealthCheckTask {task_id}] TypeError processing health check task for {org_id}: {str(te)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"TypeError during health check: {str(te)}")
    except Exception as e:
        logger.error(f"[HealthCheckTask {task_id}] Error processing health check task for {org_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Error during health check: {str(e)}")

def process_profiles_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a profiles task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    logger.info(f"Processing profiles task {task_id} for organization {org_id}")
    processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Starting profiles data collection")

    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    integration_id_from_params = params.get("integration_id")

    if not all([access_token, instance_url, integration_id_from_params]):
        err_msg = "Missing access_token, instance_url, or integration_id in task parameters for profiles task."
        logger.error(f"[ProfilesTask {task_id}] {err_msg}")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
        return

    if org_id != integration_id_from_params:
        logger.warning(f"[ProfilesTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

    try:
        from blueprints.profile_system_permissions import (
            fetch_profile_system_permissions,
            fetch_permission_set_system_permissions,
            save_profile_system_permissions,
            save_permission_set_system_permissions
        )

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message="Fetching profile system permissions from Salesforce using provided token."
        )

        # Use the existing event loop instead of creating a new one
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # Handle async operations properly
        if loop.is_running():
            # If loop is already running, we need to use a separate thread
            import concurrent.futures

            def run_async_profiles_task():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    profiles = new_loop.run_until_complete(fetch_profile_system_permissions(access_token, instance_url))
                    return profiles
                finally:
                    new_loop.close()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_async_profiles_task)
                profiles = future.result()
        else:
            # If loop is not running, we can use run_until_complete
            profiles = loop.run_until_complete(fetch_profile_system_permissions(access_token, instance_url))

        logger.info(f"Fetched {len(profiles)} profiles with system permissions from Salesforce")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=60,
            message="Fetching permission set system permissions from Salesforce using provided token."
        )

        # fetch_permission_set_system_permissions is sync, call directly
        permission_sets = fetch_permission_set_system_permissions(access_token, instance_url)
        logger.info(f"Fetched {len(permission_sets)} permission sets with system permissions from Salesforce")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Saving profile system permissions via DB service"
        )

        # Store profiles data via DB service (maintaining microservice architecture)
        db_client = get_db_client()
        profile_success = db_client.store_profile_data(org_id, execution_log_id, profiles)
        if profile_success:
            logger.info(f"Saved {len(profiles)} profiles with system permissions via DB service")
        else:
            logger.error(f"Failed to save profiles data via DB service for organization {org_id}")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=85,
            message="Saving permission set system permissions via DB service"
        )

        # Store permission sets data via DB service (maintaining microservice architecture)
        permission_set_success = db_client.store_permission_sets_data(org_id, execution_log_id, permission_sets)
        if permission_set_success:
            logger.info(f"Saved {len(permission_sets)} permission sets with system permissions via DB service")
        else:
            logger.error(f"Failed to save permission sets data via DB service for organization {org_id}")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Profile and permission set system permissions fetched and saved successfully"
        )
    except TypeError as te: # Specific catch for TypeError
        logger.error(f"[ProfilesTask {task_id}] TypeError: {str(te)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"TypeError: {str(te)}")
    except NameError as ne: # Specific catch for NameError
        logger.error(f"[ProfilesTask {task_id}] NameError: {str(ne)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"NameError: {str(ne)}")
    except Exception as e:
        logger.error(f"[ProfilesTask {task_id}] Error processing profiles task: {str(e)}") # Corrected log task_id
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Error processing profiles task: {str(e)}")

def process_data_export_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a data export task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message="Preparing data for export"
        )

        # Get integration details
        integration = get_integration_by_id(org_id)
        if not integration:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Integration with ID {org_id} not found"
            )
            return

        # Extract export parameters
        export_format = params.get("format", "csv")
        data_type = params.get("data_type", "health_check")

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message=f"Generating {export_format.upper()} export for {data_type}"
        )

        # In a real implementation, you would fetch data and generate the export file
        # For now, we'll just simulate the process

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Saving export file"
        )

        # Generate a file name for the export
        file_name = f"{integration.get('name', 'export')}-{data_type}-{datetime.now().strftime('%Y%m%d%H%M%S')}.{export_format}"

        # In a real implementation, you would save the file to blob storage or a database
        # For now, we'll just simulate the process

        # Update task status with the result (file URL or download token)
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Export completed successfully",
            result=json.dumps({
                "file_name": file_name,
                "format": export_format,
                "data_type": data_type,
                "download_url": f"/api/download/{file_name}",
                "expiry": (datetime.now() + timedelta(days=7)).isoformat()
            })
        )
    except Exception as e:
        logger.error(f"Error processing data export task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing data export task: {str(e)}"
        )

def process_report_generation_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a report generation task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message="Preparing report data"
        )

        # Get integration details
        integration = get_integration_by_id(org_id)
        if not integration:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Integration with ID {org_id} not found"
            )
            return

        # Extract report parameters
        report_type = params.get("report_type", "security_summary")
        report_format = params.get("format", "pdf")

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message=f"Generating {report_type} report in {report_format.upper()} format"
        )

        # In a real implementation, you would fetch data and generate the report
        # For now, we'll just simulate the process

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Saving report file"
        )

        # Generate a file name for the report
        file_name = f"{integration.get('name', 'report')}-{report_type}-{datetime.now().strftime('%Y%m%d%H%M%S')}.{report_format}"

        # In a real implementation, you would save the file to blob storage or a database
        # For now, we'll just simulate the process

        # Update task status with the result (file URL or download token)
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Report generated successfully",
            result=json.dumps({
                "file_name": file_name,
                "report_type": report_type,
                "format": report_format,
                "download_url": f"/api/download/{file_name}",
                "expiry": (datetime.now() + timedelta(days=7)).isoformat()
            })
        )
    except Exception as e:
        logger.error(f"Error processing report generation task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing report generation task: {str(e)}"
        )

def process_scheduled_scan_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str = None) -> None:
    try:
        db_client = get_db_client()
        from datetime import datetime
        
        # Get execution_log_id from params if not provided directly
        execution_log_id = get_execution_log_id_from_params(params, execution_log_id)
        
        if not execution_log_id:
            logger.error(f"[ScheduledScan {task_id}] execution_log_id is missing. Refusing to proceed with scheduled scan.")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "execution_log_id is missing for scheduled scan.")
            return
            
        logger.info(f"[ScheduledScan {task_id}] Using execution_log_id: {execution_log_id}")
        
        # Store execution_log_id in params for all downstream tasks
        params = dict(params or {})
        params['execution_log_id'] = execution_log_id
        
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=10,
            message="Starting scheduled scan"
        )
        
        integration = get_integration_by_id(org_id)
        if not integration:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message=f"Integration with ID {org_id} not found"
            )
            return
            
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message=f"Enqueuing authentication task for scheduled scan"
        )
        
        auth_task_params = {
            "integration_id": org_id,
            "tenant_url": integration.get("tenant_url") or integration.get("TenantUrl"),
            "environment": integration.get("environment") or integration.get("Environment"),
            "execution_log_id": execution_log_id
        }
        
        logger.info(f"[ScheduledScan] Enqueuing sfdc_authenticate with execution_log_id: {execution_log_id}")
        auth_task_id = processor.enqueue_task(
            task_type=TASK_TYPE_SFDC_AUTHENTICATE,
            org_id=org_id,
            user_id=user_id,
            params=auth_task_params,
            priority=TASK_PRIORITY_HIGH,
            execution_log_id=execution_log_id
        )
        
        if auth_task_id:
            logger.info(f"Enqueued sfdc_authenticate task {auth_task_id} for scheduled scan {task_id} with execution_log_id: {execution_log_id}")
        else:
            logger.error(f"Failed to enqueue sfdc_authenticate task for scheduled scan {task_id}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=0,
                message="Failed to enqueue authentication task."
            )
            return
            
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message=f"Scheduled scan started. Enqueued authentication task.",
            result=json.dumps({
                "auth_task_id": auth_task_id
            })
        )
        
    except Exception as e:
        logger.error(f"Error processing scheduled scan task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing scheduled scan task: {str(e)}"
        )

def process_metadata_extraction_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str] = None) -> None:
    execution_log_id = get_execution_log_id_from_params(params, execution_log_id)
    if not execution_log_id:
        logger.error(f"[MetadataTask {task_id}] execution_log_id is missing. Refusing to enqueue downstream tasks.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "execution_log_id is missing.")
        return
    logger.info(f"[MetadataTask {task_id}] Using execution_log_id: {execution_log_id}")
    """
    Process metadata extraction task

    Args:
        processor: Background processor instance
        task_id: Task ID
        org_id: Organization ID (integration ID)
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    # Idempotency check: skip if already completed/failed/cancelled
    task_status = processor.get_task_status(task_id)
    if task_status and task_status["status"] in (TASK_STATUS_COMPLETED, TASK_STATUS_FAILED, TASK_STATUS_CANCELLED):
        logger.info(f"[Idempotency] Task {task_id} already {task_status['status']}, skipping processing.")
        return

    logger.info(f"[MetadataTask {task_id}] Received params: {params}")
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    integration_id_from_params = params.get("integration_id")
    if not all([access_token, instance_url, integration_id_from_params]):
        err_msg = "Missing access_token, instance_url, or integration_id in task parameters for metadata extraction."
        logger.error(f"[MetadataTask {task_id}] {err_msg}")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
        return

    if org_id != integration_id_from_params:
        logger.warning(f"[MetadataTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

    integration_id = org_id # Use org_id as the integration_id for clarity in this task

    try:
        # Get integration details for org name via DB service
        from shared.db_service_client import get_db_client
        metadata_db_service = get_db_client()
        integration_entity = metadata_db_service.get_integration_by_id(integration_id)

        if not integration_entity:
            error_message = f"Integration with ID {integration_id} not found for metadata task."
            logger.error(f"[MetadataTask {task_id}] {error_message}")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, error_message)
            return

        integration_type = integration_entity.get("Type", "Salesforce")
        # Try multiple fields for org name, with better fallback
        org_name = (
            integration_entity.get("Name") or
            integration_entity.get("name") or
            integration_entity.get("OrganizationName") or
            integration_entity.get("organization_name") or
            f"org-{integration_id[:8]}"  # Use first 8 chars of integration ID as fallback
        )

        # Get environment from integration entity or params
        environment = (
            integration_entity.get("Environment") or
            integration_entity.get("environment") or
            params.get("environment") or
            "production"  # Default fallback
        )

        logger.info(f"[MetadataTask {task_id}] Retrieved integration details: Type={integration_type}, Name={org_name}, Environment={environment}, ID={integration_id}")

        logger.info(f"[MetadataTask {task_id}] Extracting metadata for {integration_type} integration {integration_id} ({org_name}) using provided token.")

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=30,
            message="Extracting metadata from Salesforce using provided token."
        )

        # Extract metadata
        logger.info(f"[ORCHESTRATION] extract_salesforce_metadata called from process_metadata_extraction_task. Task ID: {task_id}, Execution Log ID: {execution_log_id}")
        blob_repo = BlobStorageRepository(container_name="salesforce-metadata")
        success, error_message, returned_blob_path = extract_salesforce_metadata(
            access_token=access_token,
            instance_url=instance_url,
            blob_repo=blob_repo,
            integration_id=integration_id,
            org_name=org_name,
            orchestration_context=f"process_metadata_extraction_task:{task_id}",
            task_id=task_id,  # Pass the task_id for consistent output folder
            execution_log_id=execution_log_id  # Pass execution_log_id for isolation
        )

        if not success:
            logger.error(f"[MetadataTask {task_id}] Failed to extract metadata from Salesforce: {error_message}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=100,
                message=f"Failed to extract metadata: {error_message}"
            )
            return

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Metadata successfully retrieved/extracted."
        )

        # Update integration with last scan timestamp via DB service
        update_data = {
            "last_metadata_scan_time": datetime.now().isoformat()
        }
        success = metadata_db_service.update_integration(integration_id, update_data)
        if success:
            logger.info(f"[MetadataTask {task_id}] Updated integration {integration_id} with last metadata scan timestamp via DB service.")
        else:
            logger.error(f"[MetadataTask {task_id}] Failed to update integration {integration_id} via DB service.")

        # NOTE: Policy creation removed - policies should only be created when a new integration is added
        # not on every scan. Policy creation is now handled in the integration creation flow.

        # CRITICAL FIX: Mark task as 100% completed BEFORE enqueueing dependent tasks
        # This prevents the race condition where dependent tasks check for completed metadata_extraction
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Metadata extraction completed successfully.",
            result=json.dumps({
                "blob_path": returned_blob_path,
                "timestamp": datetime.now().isoformat()
            })
        )

        # NEW: Check if this is a child task and update parent task status
        if execution_log_id:
            # Find the parent task (sfdc_authenticate) for this execution
            try:
                from shared.db_service_client import get_db_client
                db_service = get_db_client()
                if db_service:
                    parent_task = db_service.get_task_by_execution_log_and_type(execution_log_id, "sfdc_authenticate")
                    if parent_task:
                        parent_task_id = parent_task.get('TaskId')
                        update_parent_task_status(execution_log_id, parent_task_id)
            except Exception as parent_check_error:
                logger.warning(f"Could not check parent task status: {str(parent_check_error)}")

        # Now enqueue all dependent tasks AFTER metadata extraction is 100% complete
        logger.info(f"[MetadataTask {task_id}] Metadata extraction completed. Now enqueueing all dependent tasks.")

        try:
            # Prepare common parameters for all tasks
            common_task_params = {
                "org_name": org_name,
                "blob_prefix": returned_blob_path,
                "integration_id": integration_id,
                "access_token": access_token,
                "instance_url": instance_url,
                "environment": environment
            }

            # Enqueue all dependent tasks that require metadata extraction to complete first
            
            # 1. Overview data collection (medium priority)
            logger.info(f"[MetadataTask {task_id}] Enqueueing overview task")
            processor.enqueue_task(
                task_type=TASK_TYPE_OVERVIEW,
                org_id=org_id,
                user_id=user_id,
                params=common_task_params.copy(),
                priority=TASK_PRIORITY_MEDIUM,
                execution_log_id=execution_log_id
            )

            # 2. Profiles and Permissions retrieval (medium priority)
            logger.info(f"[MetadataTask {task_id}] Enqueueing profiles task")
            processor.enqueue_task(
                task_type=TASK_TYPE_PROFILES,
                org_id=org_id,
                user_id=user_id,
                params=common_task_params.copy(),
                priority=TASK_PRIORITY_MEDIUM,
                execution_log_id=execution_log_id
            )

            # 3. All security policy tasks (medium priority)
            security_tasks = [
                "profiles_permission_sets",
                "mfa_enforcement",
                "login_ip_ranges",
                "device_activation",
                "login_hours", 
                "session_timeout",
                "api_whitelisting",
                "password_policy",
                "pmd_apex_security"
            ]

            for task_type in security_tasks:
                logger.info(f"[MetadataTask {task_id}] Enqueueing {task_type} task")
                processor.enqueue_task(
                    task_type=task_type,
                    org_id=org_id,
                    user_id=user_id,
                    params=common_task_params.copy(),
                    priority=TASK_PRIORITY_MEDIUM,
                    execution_log_id=execution_log_id
                )

            logger.info(f"[MetadataTask {task_id}] Successfully enqueued all dependent tasks")

        except Exception as e:
            logger.error(f"[MetadataTask {task_id}] Error enqueueing policy-based tasks: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # Don't fail the entire task if policy task enqueueing fails, just log the error
            # The metadata extraction task is already marked as completed, so this is just a warning

    except Exception as e:
        error_message = f"Error processing metadata extraction task: {str(e)}"
        logger.error(error_message)
        import traceback
        logger.error(traceback.format_exc())

        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=100,
            message=error_message
        )

def process_notification_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a notification task

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=20,
            message="Preparing notification"
        )

        # Extract notification parameters
        notification_type = params.get("notification_type", "task_completed")
        recipients = params.get("recipients", [])
        subject = params.get("subject", "AtomSec Notification")
        message = params.get("message", "")
        related_task_id = params.get("related_task_id", "")

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=40,
            message=f"Sending {notification_type} notification to {len(recipients)} recipients"
        )

        # In a real implementation, you would send the notification via email, SMS, or in-app
        # For now, we'll just simulate the process

        # Update task status
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=70,
            message="Recording notification delivery"
        )

        # In a real implementation, you would record the notification delivery in a database
        # For now, we'll just simulate the process

        # Update task status with the result
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message="Notification sent successfully",
            result=json.dumps({
                "notification_type": notification_type,
                "recipients": recipients,
                "subject": subject,
                "sent_at": datetime.now().isoformat(),
                "related_task_id": related_task_id
            })
        )
    except Exception as e:
        logger.error(f"Error processing notification task: {str(e)}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=0,
            message=f"Error processing notification task: {str(e)}"
        )

def get_execution_log_id_from_params(params, parent_execution_log_id=None):
    """
    Extract execution_log_id from params or use provided value.
    
    This function ensures consistent handling of execution_log_id across all task processors.
    It prioritizes:
    1. The execution_log_id from params (if present)
    2. The explicitly provided parent_execution_log_id (if present)
    3. Returns None (caller should handle this case)
    
    Args:
        params: Task parameters dictionary
        parent_execution_log_id: Optional explicit execution_log_id
        
    Returns:
        str: The execution_log_id to use, or None if not found
    """
    execution_log_id = None
    
    # First try to get from params
    if params and isinstance(params, dict):
        execution_log_id = params.get('execution_log_id')
        if execution_log_id:
            logger.debug(f"Using execution_log_id from params: {execution_log_id}")
    
    # Then use provided parent_execution_log_id
    if not execution_log_id and parent_execution_log_id:
        execution_log_id = parent_execution_log_id
        logger.debug(f"Using provided parent_execution_log_id: {execution_log_id}")
    
    # Log if no execution_log_id was found
    if not execution_log_id:
        logger.warning(f"No execution_log_id found in params or provided explicitly.")
        
    return execution_log_id

def process_sfdc_authenticate_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    execution_log_id = get_execution_log_id_from_params(params, execution_log_id)
    if not execution_log_id:
        logger.error(f"[AuthTask {task_id}] execution_log_id is missing. Refusing to enqueue downstream tasks.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "execution_log_id is missing.")
        return
    logger.info(f"[AuthTask {task_id}] Using execution_log_id: {execution_log_id}")
    """
    Processes the Salesforce authentication task.
    Retrieves credentials, authenticates with Salesforce, and if successful,
    enqueues subsequent data collection tasks.
    """
    logger.info(f"Processing Salesforce authentication task {task_id} for integration {org_id}")
    logger.info(f"[AuthTask {task_id}] Received user_id: '{user_id}' (type: {type(user_id)})")

    # Ensure user_id is not None
    if user_id is None:
        user_id = 'system'
        logger.info(f"[AuthTask {task_id}] Fixed user_id to: '{user_id}'")

    processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, f"Starting Salesforce authentication for {org_id}")

    integration_id = params.get("integration_id") # This should be the same as org_id for this task
    if not integration_id:
        logger.error(f"[AuthTask {task_id}] Missing integration_id in task parameters.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "Missing integration_id in task parameters.")
        return

    db_client = get_db_client()
    integration_entity = db_client.get_integration_by_id(integration_id)

    if not integration_entity:
        logger.error(f"[AuthTask {task_id}] Integration {integration_id} not found.")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Integration {integration_id} not found.")
        return

    tenant_url = integration_entity.get("tenant_url")
    environment = integration_entity.get("environment", "production")
    is_sandbox = environment.lower() == "sandbox"
    service_name = f"salesforce-{integration_id}"

    logger.info(f"[AuthTask {task_id}] Attempting to retrieve credentials for {service_name}")

    client_id = None
    client_secret = None
    jwt_username = None
    private_key = None
    auth_flow_type = None
    auth_success = False
    try:
        if is_local_dev():
            logger.info(f"[AuthTask {task_id}] Local dev: Retrieving creds from Credentials table via DB service for {service_name}")
            # Use DB service for credentials retrieval
            credentials = db_client.get_integration_credentials(integration_id)
            if credentials:
                # Handle different credential formats from DB service
                if "client-id" in credentials:
                    # Table storage format with individual credential entries
                    client_id_entry = credentials.get("client-id", {})
                    client_id = client_id_entry.get("value")
                    auth_flow_type = client_id_entry.get("auth_flow")

                    if auth_flow_type == "jwt":
                        username_entry = credentials.get("username", {})
                        jwt_username = username_entry.get("value")
                        pkey_entry = credentials.get("private-key", {})
                        private_key = pkey_entry.get("value")
                    else:
                        secret_entry = credentials.get("client-secret", {})
                        client_secret = secret_entry.get("value")
                        if client_id and client_secret: auth_flow_type = "client_credentials"
                else:
                    # SQL format with direct fields
                    client_id = credentials.get("client_id")
                    client_secret = credentials.get("client_secret")
                    auth_flow_type = "client_credentials"  # Default for SQL format

                if client_id and not auth_flow_type:
                    # if auth flow not specified, try to infer based on available creds
                    if jwt_username and private_key: auth_flow_type = "jwt"
                    elif client_secret: auth_flow_type = "client_credentials"
            else:
                raise Exception("Credentials not available via DB service in local dev.")
        else:
            logger.info(f"[AuthTask {task_id}] Production: Retrieving creds from Key Vault for {service_name}")
            client_id = get_secret(f"{service_name}-client-id")
            # Check for AuthFlow hint if stored with client-id or as a separate secret
            auth_flow_hint = get_secret(f"{service_name}-auth-flow")
            if auth_flow_hint and auth_flow_hint.lower() in ["jwt", "client_credentials"]:
                auth_flow_type = auth_flow_hint.lower()

            if auth_flow_type == "jwt":
                jwt_username = get_secret(f"{service_name}-username")
                private_key = get_secret(f"{service_name}-private-key")
            else: # Default to client_credentials or if AuthFlow is not jwt
                client_secret = get_secret(f"{service_name}-client-secret")
                if client_id and client_secret : auth_flow_type = "client_credentials"
            if client_id and not auth_flow_type:
                # Try to infer if not specified
                jwt_username_check = get_secret(f"{service_name}-username")
                if jwt_username_check: # If username exists, assume JWT
                    jwt_username = jwt_username_check
                    private_key = get_secret(f"{service_name}-private-key")
                    auth_flow_type = "jwt"
                else: # else assume client_credentials
                    client_secret = get_secret(f"{service_name}-client-secret")
                    auth_flow_type = "client_credentials"

        if not client_id:
            raise Exception("Client ID could not be retrieved.")
        if auth_flow_type == "jwt" and (not jwt_username or not private_key):
            raise Exception("JWT username or private key could not be retrieved for JWT flow.")
        if auth_flow_type == "client_credentials" and not client_secret:
            raise Exception("Client Secret could not be retrieved for Client Credentials flow.")
        if not auth_flow_type:
            raise Exception("Could not determine authentication flow type (JWT or Client Credentials).")

        logger.info(f"[AuthTask {task_id}] Credentials retrieved. Determined auth flow: {auth_flow_type}")
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 30, f"Credentials retrieved. Attempting {auth_flow_type} authentication.")

        logger.info(f"[AuthTask {task_id}] Calling central authentication service. Flow type: {auth_flow_type}")
        auth_step_log = [f"Central Auth Init. Flow: {auth_flow_type}, User: {jwt_username if auth_flow_type == 'jwt' else 'N/A (CC)'}, ClientID: {client_id[:5]}..."]

        auth_success, auth_error_message, sf_instance, connection_details = authenticate_salesforce_integration(
            auth_flow_type=auth_flow_type,
            tenant_url=tenant_url, # This is the my-domain or login URL base
            environment=environment,
            client_id=client_id,
            client_secret=client_secret,
            jwt_username=jwt_username,
            private_key=private_key
        )

        # Log all steps from the central auth service if it returns them, or rely on its internal logging.
        # For simplicity, we assume central service logs sufficiently.

        if auth_success and sf_instance and connection_details:
            access_token = connection_details.get("access_token")
            instance_url_fqdn = connection_details.get("instance_url") # This is the full FQDN like https://mydomain.my.salesforce.com

            if not access_token or not instance_url_fqdn:
                logger.error(f"[AuthTask {task_id}] Central auth succeeded but access_token or instance_url missing in returned details.")
                processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "Auth token/instance URL missing post central auth.")
                return

            logger.info(f"[AuthTask {task_id}] Salesforce central authentication successful for {org_id}. Instance URL: {instance_url_fqdn}")
            processor.update_task_status(task_id, TASK_STATUS_RUNNING, 70, "Authentication successful. Enqueuing data collection tasks.")

            common_task_params = {
                "access_token": access_token,
                "instance_url": instance_url_fqdn, # Use the FQDN from auth service
                "integration_id": integration_id,
                "tenant_url": tenant_url,
                "environment": environment,
                "execution_log_id": execution_log_id
            }

            # NOTE: Policy creation removed - policies should only be created when a new integration is added
            # not on every scan. Policy creation is now handled in the integration creation flow.

            processor.update_task_status(task_id, TASK_STATUS_RUNNING, 80, "Enqueuing core tasks.")

            # FIXED: Only enqueue core tasks that don't depend on metadata extraction
            # Let metadata extraction task handle enqueueing dependent tasks after it completes

            # 1. Health Check security analysis (high priority, no dependencies)
            latest_health_check_task = processor.get_latest_task_for_org(org_id, TASK_TYPE_HEALTH_CHECK)
            if latest_health_check_task and latest_health_check_task["status"] in (TASK_STATUS_PENDING, TASK_STATUS_RUNNING):
                logger.info(f"[AuthTask {task_id}] Skipping health check enqueue for {org_id}: existing task {latest_health_check_task['task_id']} is {latest_health_check_task['status']}")
            else:
                logger.info(f"[AuthTask {task_id}] About to enqueue health check task with user_id: '{user_id}' (type: {type(user_id)}), execution_log_id: {execution_log_id}")
                processor.enqueue_task(TASK_TYPE_HEALTH_CHECK, org_id, user_id, params=common_task_params.copy(), priority=TASK_PRIORITY_HIGH, execution_log_id=execution_log_id)

            # 2. Metadata extraction (high priority, will enqueue dependent tasks when complete)
            latest_metadata_task = processor.get_latest_task_for_org(org_id, TASK_TYPE_METADATA_EXTRACTION)
            if latest_metadata_task and latest_metadata_task["status"] in (TASK_STATUS_PENDING, TASK_STATUS_RUNNING):
                logger.info(f"[AuthTask {task_id}] Skipping metadata extraction enqueue for {org_id}: existing task {latest_metadata_task['task_id']} is {latest_metadata_task['status']}")
            else:
                logger.info(f"[AuthTask {task_id}] About to enqueue metadata extraction task with user_id: '{user_id}' (type: {type(user_id)}), execution_log_id: {execution_log_id}")
                processor.enqueue_task(TASK_TYPE_METADATA_EXTRACTION, org_id, user_id, params=common_task_params.copy(), priority=TASK_PRIORITY_HIGH, execution_log_id=execution_log_id)
                logger.info(f"[AuthTask {task_id}] Enqueued metadata extraction task for {org_id}. It will handle enqueueing dependent tasks when complete.")

            logger.info(f"[AuthTask {task_id}] Successfully enqueued core tasks for {org_id}.")
            
            # Update LastScan timestamp on the integration record upon successful auth and task enqueuing via DB service
            now_iso = datetime.now().isoformat()
            update_data = {"last_scan": now_iso}
            success = db_client.update_integration(integration_id, update_data)
            if success:
                logger.info(f"[AuthTask {task_id}] Updated integration {integration_id} with last scan timestamp via DB service.")
            else:
                logger.error(f"[AuthTask {task_id}] Failed to update integration {integration_id} via DB service.")
            
            # CRITICAL FIX: Update task status to COMPLETED AFTER enqueuing dependent tasks
            # This prevents the race condition where dependent tasks check for completed auth task
            processor.update_task_status(task_id, TASK_STATUS_COMPLETED, 100, f"Authentication successful. Data collection tasks for {org_id} enqueued.")
            logger.info(f"[AuthTask {task_id}] Status set to COMPLETED after enqueueing dependent tasks.")
            


        else:
            logger.error(f"[AuthTask {task_id}] Salesforce central authentication failed for {org_id}: {auth_error_message}")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Salesforce authentication failed: {auth_error_message}")
            # Update Integration IsActive to False via DB service
            update_data = {"is_active": False}
            success = db_client.update_integration(integration_id, update_data)
            if success:
                logger.info(f"[AuthTask {task_id}] Marked integration {integration_id} as inactive due to auth failure via DB service.")
            else:
                logger.error(f"[AuthTask {task_id}] Failed to mark integration {integration_id} as inactive via DB service.")

        # If authentication and enqueuing succeeded, set auth_success = True
        auth_success = True
    except Exception as e:
        logger.error(f"[AuthTask {task_id}] Error during Salesforce authentication task for {org_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Error during authentication: {str(e)}")
        if integration_id:
            try:
                update_data = {"is_active": False}
                success = db_client.update_integration(integration_id, update_data)
                if success:
                    logger.info(f"[AuthTask {task_id}] Marked integration {integration_id} as inactive due to an exception in the auth task via DB service.")
                else:
                    logger.error(f"[AuthTask {task_id}] Failed to mark integration {integration_id} as inactive via DB service.")
            except Exception as update_err:
                logger.error(f"[AuthTask {task_id}] Failed to mark integration {integration_id} as inactive after exception: {str(update_err)}")
        return
    finally:
        # Note: Task status is now updated to COMPLETED before enqueuing dependent tasks
        # to prevent race conditions. No need to update again here.
        pass

def get_profile_assignment_counts_bulk(access_token: str, instance_url: str, environment: str = 'production') -> Dict[str, Dict[str, Any]]:
    """
    Fetch assignment counts for all profiles in a single optimized SOQL query.
    This is the primary method used in production - EXACT OLD ARCHITECTURE.
    
    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL
        environment: Environment (production/development)
    
    Returns:
        Dictionary mapping profile_id to profile data with counts
    """
    try:
        # Single query to get all profile counts at once - EXACT OLD ARCHITECTURE
        user_count_query = (
            "SELECT ProfileId, Profile.Name, COUNT(Id) cnt "
            "FROM User "
            "WHERE IsActive = TRUE "
            "AND Id NOT IN (SELECT UserId FROM UserLogin WHERE IsFrozen = true) "
            "GROUP BY ProfileId, Profile.Name"
        )
        
        # Execute the query in the event loop
        import asyncio
        import concurrent.futures

        def run_query():
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                return new_loop.run_until_complete(
                    execute_salesforce_query(user_count_query, access_token, instance_url)
                )
            finally:
                new_loop.close()

        try:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_query)
                user_count_result = future.result()
        except Exception as e:
            logger.error(f"Error executing profile assignment query: {e}")
            user_count_result = None
        
        profile_active_user_counts = {}
        if user_count_result and 'records' in user_count_result:
            logger.info(f"DEBUG: SOQL query returned {len(user_count_result['records'])} records")
            logger.info(f"DEBUG: Raw SOQL result: {user_count_result['records']}")
            
            for rec in user_count_result['records']:
                profile_id = rec.get('ProfileId')
                profile_name = rec.get('Name')  # From Profile.Name relationship
                count = rec.get('cnt')
                
                logger.info(f"DEBUG: Processing record - ProfileId: {profile_id}, ProfileName: {profile_name}, Count: {count}")
                
                if profile_id and profile_name and count is not None:
                    profile_active_user_counts[profile_id] = {
                        'ProfileId': profile_id,
                        'ProfileName': profile_name,
                        'AssignmentCount': int(count)
                    }
                    logger.info(f"DEBUG: Added profile {profile_name} with {count} users")
                else:
                    logger.warning(f"DEBUG: Skipping record due to missing data - ProfileId: {profile_id}, ProfileName: {profile_name}, Count: {count}")
        else:
            logger.warning(f"DEBUG: No records found in SOQL result: {user_count_result}")
        
        logger.info(f"Retrieved assignment counts for {len(profile_active_user_counts)} profiles in single batch query")
        return profile_active_user_counts

    except Exception as e:
        logger.error(f"Error getting profile assignment counts: {str(e)}")
        return {}

def profile_active_user_counts(access_token: str, instance_url: str) -> Dict[str, int]:
    """
    Legacy function for backward compatibility - returns profile name to count mapping.
    """
    bulk_counts = get_profile_assignment_counts_bulk(access_token, instance_url)
    profile_counts = {}
    for profile_id, profile_data in bulk_counts.items():
        profile_counts[profile_data['ProfileName']] = profile_data['AssignmentCount']
    return profile_counts

def get_permission_set_assignments_bulk(access_token: str, instance_url: str, environment: str = 'production') -> Dict[tuple, int]:
    """
    Get all permission set assignments across all profiles in a single query.
    This is the optimized method used in production - EXACT OLD ARCHITECTURE.
    
    Args:
        access_token: Salesforce access token
        instance_url: Salesforce instance URL
        environment: Environment (production/development)
    
    Returns:
        Dictionary mapping (ProfileId, ProfileName, PermissionSetName) to count
    """
    try:
        # Single query to get all permission set assignments with profile information - EXACT OLD ARCHITECTURE
        soql = (
            "SELECT Assignee.Profile.Name, Assignee.ProfileId, PermissionSet.Name "
            "FROM PermissionSetAssignment"
        )
        
        # Execute the query in the event loop
        import asyncio
        import concurrent.futures

        def run_query():
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                return new_loop.run_until_complete(
                    execute_salesforce_query(soql, access_token, instance_url)
                )
            finally:
                new_loop.close()

        try:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_query)
                resp = future.result()
        except Exception as e:
            logger.error(f"Error executing permission set assignment query: {e}")
            resp = None
        
        assignment_counts = {}
        if resp and 'records' in resp:
            records = resp.get('records', [])
            
            # Aggregate counts per (ProfileId, ProfileName, PermissionSetName)
            for rec in records:
                assignee = rec.get('Assignee')
                permission_set = rec.get('PermissionSet')
                
                profile_id = assignee.get('ProfileId') if assignee else None
                profile = assignee.get('Profile') if assignee else None
                profile_name = profile.get('Name') if profile else None
                ps_name = permission_set.get('Name') if permission_set else None
                
                if profile_id and profile_name and ps_name:
                    key = (profile_id, profile_name, ps_name)
                    assignment_counts[key] = assignment_counts.get(key, 0) + 1
        
        logger.info(f"Retrieved permission set assignment counts for {len(assignment_counts)} combinations")
        return assignment_counts

    except Exception as e:
        logger.error(f"Error getting permission set assignment counts: {str(e)}")
        return {}

def process_profiles_permission_sets_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process profiles and permission sets task with best practices comparison

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    logger.info(f"Processing profiles permission sets task {task_id} for organization {org_id}")
    processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Starting profiles permission sets analysis")

    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    integration_id_from_params = params.get("integration_id")
    environment = params.get("environment", "production")

    if not all([access_token, instance_url, integration_id_from_params]):
        err_msg = "Missing access_token, instance_url, or integration_id in task parameters for profiles permission sets task."
        logger.error(f"[ProfilesPermissionSetsTask {task_id}] {err_msg}")
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
        return

    if org_id != integration_id_from_params:
        logger.warning(f"[ProfilesPermissionSetsTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

    try:
        # Load best practices XML
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 20, "Loading best practices configuration")

        # Try to load best practices from the best_practices directory
        best_practices_path = os.path.join(os.path.dirname(__file__), "..", "best_practices", "Profiles_PermissionSetRisks-BestPractice.xml")
        if not os.path.exists(best_practices_path):
            # Fallback to a default path or create minimal best practices
            logger.warning(f"Best practices XML not found at {best_practices_path}, using minimal default practices")
            best_practices = []
        else:
            best_practices = load_best_practices_xml(best_practices_path)
            logger.info(f"Loaded {len(best_practices)} best practices from XML")

        # Get blob repository for metadata
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 30, "Initializing blob storage")
        blob_repo = BlobStorageRepository(container_name="salesforce-metadata")

        # Get blob prefix from task parameters (set by metadata extraction task)
        blob_prefix = params.get("blob_prefix")
        if not blob_prefix:
            # Fallback: construct blob prefix based on org_id and task_id
            blob_prefix = f"{org_id}/{task_id}"
            logger.warning(f"[ProfilesPermissionSetsTask {task_id}] blob_prefix not found in params, using fallback: {blob_prefix}")

        # Call the core logic and get results
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 50, "Processing profiles in blob storage")
        inserted_count, inserted_profiles = process_profiles_in_blob(blob_repo, blob_prefix, best_practices, org_id, execution_log_id, access_token=access_token, instance_url=instance_url, environment=environment)

        # --- Fetch Profile Name -> Id mapping from Salesforce ---
        normalized_profile_id_map = {}
        if access_token and instance_url:
            processor.update_task_status(task_id, TASK_STATUS_RUNNING, 60, "Fetching profile mappings from Salesforce")
            profile_query = "SELECT Id, Name FROM Profile"

            # Execute the query in the event loop
            import asyncio
            import concurrent.futures

            def run_query():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(
                        execute_salesforce_query(profile_query, access_token, instance_url)
                    )
                finally:
                    new_loop.close()

            try:
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_query)
                    profile_query_result = future.result()
            except Exception as e:
                logger.error(f"Error executing profile query: {e}")
                profile_query_result = None

            if profile_query_result and "records" in profile_query_result:
                for rec in profile_query_result["records"]:
                    norm_name = normalize_profile_name(rec["Name"])
                    normalized_profile_id_map[norm_name] = rec["Id"]

        unmatched_profiles = []
        # inserted_count and inserted_profiles are now returned from process_profiles_in_blob

        # Get all profile assignment counts in a single batch query (EXACT OLD ARCHITECTURE)
        profile_assignment_counts_bulk = {}
        if access_token and instance_url:
            processor.update_task_status(task_id, TASK_STATUS_RUNNING, 65, "Fetching profile assignment counts")
            profile_assignment_counts_bulk = get_profile_assignment_counts_bulk(access_token, instance_url, environment)
            logger.info(f"Retrieved assignment counts for {len(profile_assignment_counts_bulk)} profiles")
            
            # DEBUG: Log the actual data being retrieved
            logger.info(f"DEBUG: Profile assignment counts data: {profile_assignment_counts_bulk}")
            for profile_id, profile_data in profile_assignment_counts_bulk.items():
                logger.info(f"DEBUG: Profile {profile_data['ProfileName']} (ID: {profile_id}) has {profile_data['AssignmentCount']} users")

        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 70, "Storing analysis results")

        # Get DB service client for database operations
        db_client = get_db_client()
        if not db_client:
            logger.error(f"[ProfilesPermissionSetsTask {task_id}] DB service client not available")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, "DB service client not available")
            return

        # Store profile assignment counts using EXACT OLD ARCHITECTURE approach
        for profile_id, profile_data in profile_assignment_counts_bulk.items():
            try:
                assignment_data = {
                    'org_id': str(org_id),
                    'profile_name': profile_data['ProfileName'],
                    'profile_id': profile_data['ProfileId'],
                    'assignment_count': profile_data['AssignmentCount'],
                    'execution_log_id': execution_log_id,
                    'created_at': datetime.now().isoformat(),
                }
                
                logger.info(f"DEBUG: Sending assignment data to DB: {assignment_data}")
                
                try:
                    # Store profile assignment count using DB service
                    if not db_client.store_profile_assignment_count_data(str(org_id), execution_log_id, [assignment_data]):
                        logger.error(f"Failed to store ProfileAssignmentCount for profile {profile_data['ProfileName']}")
                    else:
                        logger.info(f"DEBUG: Successfully sent assignment data for profile {profile_data['ProfileName']}")
                except Exception as e:
                    logger.error(f"Failed to store ProfileAssignmentCount for profile {profile_data['ProfileName']}: {e}")
            except Exception as e:
                logger.error(f"Error inserting assignment count for profile {profile_data.get('ProfileName', 'unknown')}: {e}")

        logger.info(f"Inserted {inserted_count} PoliciesResult entities for org {org_id}")

        # After processing profiles, process permission sets with the same execution_log_id
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 80, "Processing permission sets")
        try:
            # Load best practices for permission sets
            best_practices_path = os.path.join(os.path.dirname(__file__), "..", "best_practices", "Profiles_PermissionSetRisks-BestPractice.xml")
            if os.path.exists(best_practices_path):
                best_practices = load_best_practices_xml(best_practices_path)

                # Process permission sets in blob storage
                process_permissionsets_in_blob(
                    blob_repo,
                    blob_prefix,
                    best_practices,
                    org_id,
                    execution_log_id,
                    access_token=access_token,
                    instance_url=instance_url,
                    environment=params.get('environment', 'production')
                )
                logger.info(f"Completed permission set processing for org_id={org_id}")
            else:
                logger.warning(f"Best practices XML not found at {best_practices_path}, skipping permission set processing")
        except Exception as e:
            logger.error(f"Error processing permission sets: {e}")
            # Don't fail the entire task for permission set errors

        # Get and store permission set assignment counts (EXACT OLD ARCHITECTURE)
        if access_token and instance_url:
            processor.update_task_status(task_id, TASK_STATUS_RUNNING, 85, "Fetching permission set assignment counts")
            try:
                ps_assignments = get_permission_set_assignments_bulk(access_token, instance_url, environment)
                
                # Store permission set assignment counts
                for (profile_id, profile_name, ps_name), count in ps_assignments.items():
                    if count > 0:  # Only store if there are assignments
                        try:
                            ps_assignment_data = {
                                'org_id': str(org_id),
                                'profile_name': profile_name,
                                'profile_id': profile_id,
                                'permission_set_name': ps_name,
                                'assignment_count': count,
                                'execution_log_id': execution_log_id,
                                'created_at': datetime.now().isoformat(),
                            }
                            
                            # Store permission set assignment count using DB service
                            if not db_client.store_profile_assignment_count_data(str(org_id), execution_log_id, [ps_assignment_data]):
                                logger.error(f"Failed to store PermissionSetAssignmentCount for {ps_name} in profile {profile_name}")
                        except Exception as e:
                            logger.error(f"Failed to store PermissionSetAssignmentCount for {ps_name} in profile {profile_name}: {e}")
                
                logger.info(f"Stored permission set assignment counts for {len(ps_assignments)} combinations")
            except Exception as e:
                logger.error(f"Error processing permission set assignment counts: {e}")
                # Don't fail the entire task for permission set assignment count errors

        # Mark task as completed
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_COMPLETED,
            progress=100,
            message=f"Profiles and Permission Sets scan completed and results stored.",
            result=json.dumps({
                "profiles_processed": len(inserted_profiles),
                "policies_results_inserted": inserted_count,
                "unmatched_profiles": len(unmatched_profiles)
            })
        )
    except Exception as e:
        logger.error(f"[ProfilesPermissionSetsTask {task_id}] Error processing profiles permission sets task: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, f"Error processing profiles permission sets task: {str(e)}")

# --- Helper functions for profiles permission sets processing ---

def load_best_practices_xml(xml_path):
    """Load best practices from XML file"""
    try:
        tree = ET.parse(xml_path)
        root = tree.getroot()
        best_practices = []
        for usertype_elem in root.findall('UserType'):
            usertype = usertype_elem.attrib.get('name', '')
            for practice in usertype_elem.findall('Practice'):
                bp = {child.tag: child.text for child in practice}
                bp['UserType'] = usertype  # Add UserType from attribute
                best_practices.append(bp)
        return best_practices
    except Exception as e:
        logger.error(f"Error loading best practices XML: {e}")
        return []

def extract_user_license_from_xml(xml_bytes):
    """Extract user license from profile XML"""
    try:
        if isinstance(xml_bytes, bytes):
            xml_str = xml_bytes.decode('utf-8', errors='ignore')
        else:
            xml_str = xml_bytes
        match = re.search(r'<userLicense>(.*?)</userLicense>', xml_str, re.DOTALL)
        if match:
            return match.group(1).strip()
        else:
            logger.debug("No <userLicense> tag found in XML.")
    except Exception as e:
        logger.error(f"Error extracting userLicense with regex: {e}")
        logger.info(f"[DEBUG] Error extracting userLicense. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return ''

def normalize_permission_name(name):
    """Normalize permission name for comparison"""
    if not name:
        return ''
    return name.strip().lower().replace(' ', '').replace('_', '')

def normalize_user_type(val):
    """Normalize user type for comparison"""
    if not val:
        return 'blank'
    return val.strip().lower()

def get_relevant_best_practices_for_profile(user_license, best_practices):
    """
    Get best practices relevant to a profile's user license type.
    Implements fallback logic when exact user type match is not found.
    """
    profile_user_type = normalize_user_type(user_license)
    
    # Step 1: Try to find exact user type match
    relevant_bps = [bp for bp in best_practices 
                   if normalize_user_type(bp.get('UserType')) == profile_user_type]
    
    # Step 2: If no exact match found, fallback to 'blank' user type
    if not relevant_bps:
        relevant_bps = [bp for bp in best_practices 
                       if normalize_user_type(bp.get('UserType')) == 'blank']
        
    # Step 3: If still no match, use all best practices (universal)
    if not relevant_bps:
        relevant_bps = best_practices
        
    return relevant_bps

def normalize_value(val):
    """Normalize value for comparison"""
    if val is None:
        return 'false'
    val_str = str(val).strip().lower()
    # Handle various representations of true/false
    if val_str in ['❌ false', 'x false', 'false', 'no', 'disabled', '0']:
        return 'false'
    elif val_str in ['✅ true', 'tickmark/true', 'true', 'yes', 'enabled', '1']:
        return 'true'
    return val_str

def normalize_profile_name(name):
    """Normalize profile name for comparison"""
    if not name:
        return ''
    # Decode URL-encoded characters, strip whitespace, and lowercase
    return urllib.parse.unquote(name).strip().lower()

def parse_profile_permissions(xml_bytes):
    """Parse user permissions from profile XML"""
    perms = []
    try:
        root = ET.fromstring(xml_bytes)
        # Find all userPermissions, regardless of namespace
        for perm in root.iter():
            if perm.tag.endswith('userPermissions'):
                name_elem = None
                enabled_elem = None
                for child in perm:
                    if child.tag.endswith('name'):
                        name_elem = child
                    elif child.tag.endswith('enabled'):
                        enabled_elem = child
                if name_elem is not None and enabled_elem is not None:
                    perms.append({
                        'name': name_elem.text.strip() if name_elem.text else '',
                        'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                    })
    except ET.ParseError as e:
        logger.error(f"[ERROR] XML parsing error in profile: {e}")
        logger.info(f"[DEBUG] XML parsing error. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    except Exception as e:
        logger.error(f"[ERROR] Error parsing userPermissions: {e}")
        logger.info(f"[DEBUG] Error parsing userPermissions. XML preview: {xml_bytes[:200] if isinstance(xml_bytes, bytes) else str(xml_bytes)[:200]}")
    return perms

def process_profiles_in_blob(blob_repo, blob_prefix, best_practices, org_id, execution_log_id, summary_output_path=None, access_token=None, instance_url=None, environment='production'):
    """Process profiles in blob storage and compare with best practices - EXACT OLD ARCHITECTURE LOGIC"""
    import xml.etree.ElementTree as ET
    import os
    profile_folder = f"{blob_prefix}/profiles/"
    logger.info(f"[DEBUG] Looking for profiles in folder: {profile_folder}")
    blobs = blob_repo.list_blobs(name_starts_with=profile_folder)
    logger.info(f"[DEBUG] Blobs found: {blobs}")
    
    inserted_count = 0
    inserted_profiles = set()
    
    for blob_name in blobs:
        if not blob_name.endswith('.profile'):
            continue
            
        profile_name = os.path.basename(blob_name).replace('.profile', '')
        
        try:
            # Step 1: Extract user license from profile XML
            xml_bytes = blob_repo.get_blob_bytes(blob_name)
            if xml_bytes is None:
                logger.error(f"Failed to download blob bytes for {blob_name}")
                continue
                
            user_license = extract_user_license_from_xml(xml_bytes)
            
            # Step 2: Extract user permissions from profile XML
            user_permissions = parse_profile_permissions(xml_bytes)
            
            if not user_permissions:
                logger.warning(f"No user permissions found in profile {profile_name}")
                continue
            
            # Step 3: Get relevant best practices based on user type (EXACT OLD ARCHITECTURE)
            relevant_bps = get_relevant_best_practices_for_profile(user_license, best_practices)
            
            # Step 4: Compare each relevant best practice with profile permissions (EXACT OLD ARCHITECTURE)
            results = []
            profile_user_type = normalize_user_type(user_license)
            
            for bp in relevant_bps:
                bp_setting = (bp.get('SalesforceSetting') or '').strip()
                bp_standard_value = (bp.get('StandardValue') or '').strip()
                normalized_bp_setting = normalize_permission_name(bp_setting)
                
                # Find matching permission in profile
                profile_perm = next((p for p in user_permissions 
                                   if normalize_permission_name(p['name']) == normalized_bp_setting), None)
                
                if profile_perm is not None:
                    # Permission found - compare values (EXACT OLD ARCHITECTURE)
                    profile_value = profile_perm['enabled']
                    match = normalize_value(profile_value) == normalize_value(bp_standard_value)
                    
                    results.append({
                        'SalesforceSetting': bp_setting,
                        'StandardValue': bp_standard_value,
                        'ProfileValue': profile_value,
                        'Match': match,
                        'MissingInProfile': False,
                        'Description': bp.get('Description'),
                        'OWASP': bp.get('OWASP'),
                        'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                        'UserType': bp.get('UserType'),
                        'MatchedUserType': profile_user_type
                    })
                else:
                    # Permission not found - mark as missing (EXACT OLD ARCHITECTURE)
                    results.append({
                        'SalesforceSetting': bp_setting,
                        'StandardValue': bp_standard_value,
                        'ProfileValue': None,
                        'Match': False,
                        'MissingInProfile': True,
                        'Description': bp.get('Description'),
                        'OWASP': bp.get('OWASP'),
                        'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                        'UserType': bp.get('UserType'),
                        'MatchedUserType': profile_user_type
                    })
            
            # Store results using DB service client (match old architecture format)
            if results and len(results) > 0:
                # Send individual results to DB function (it will group them by profile)
                policies_data = []
                for result in results:
                    # Skip records where ProfileValue is null (EXACT OLD ARCHITECTURE)
                    if result.get('ProfileValue') is None:
                        continue
                        
                    policy_data = {
                        'Setting': result.get('SalesforceSetting', ''),
                        'StandardValue': result.get('StandardValue', ''),
                        'OrgValue': result.get('ProfileValue', ''),  # The actual permission value
                        'Match': result.get('Match', False),
                        'IssueDescription': result.get('Description', ''),
                        'OWASPCategory': result.get('OWASP', ''),
                        'Severity': result.get('RiskTypeBasedOnSeverity', ''),
                        'IntegrationId': str(org_id),
                        'TaskStatusId': execution_log_id,
                        'CreatedAt': datetime.now().isoformat(),
                        'Type': 'ProfilePermissions',
                        'ProfileName': profile_name,
                        'PermissionSetName': ''
                    }
                    policies_data.append(policy_data)
                
                try:
                    db_client = get_db_client()
                    if db_client.store_policies_result_data(str(org_id), execution_log_id, policies_data):
                        inserted_count += 1
                        inserted_profiles.add(profile_name)  # Track successful insertion
                    else:
                        logger.error(f"Failed to store PoliciesResult for profile {profile_name}")
                except Exception as e:
                    logger.error(f"Failed to store PoliciesResult for profile {profile_name}: {e}")
                    
        except Exception as e:
            logger.error(f"[DEBUG] Profile '{profile_name}': Error processing: {e}")
    
    logger.info(f"Processed {len(blobs)} profile blobs, inserted {inserted_count} profiles")
    return inserted_count, inserted_profiles

def process_permissionsets_in_blob(blob_repo, blob_prefix, best_practices, org_id, execution_log_id, access_token=None, instance_url=None, environment='production'):
    import xml.etree.ElementTree as ET
    import urllib.parse
    logger = logging.getLogger(__name__)
    permissionset_folder = f"{blob_prefix}/permissionsets/"
    permission_set_blobs = blob_repo.list_blobs(name_starts_with=permissionset_folder)
    for blob_name in permission_set_blobs:
        if not blob_name.endswith('.permissionset'):
            continue
        permission_set_name = blob_name.split('/')[-1].replace('.permissionset', '')
        logger.debug(f"[DEBUG] Processing Permission Set: {permission_set_name}")
        try:
            xml_bytes = blob_repo.get_blob_bytes(blob_name)
            # Extract <License> (case-insensitive)
            try:
                xml_str = xml_bytes.decode('utf-8', errors='ignore')
            except Exception as e:
                logger.error(f"Failed to decode XML for {permission_set_name}: {e}")
                continue
            match = re.search(r'<license>(.*?)</license>', xml_str, re.DOTALL | re.IGNORECASE)
            license_value = match.group(1).strip() if match else ''
            normalized_license = (license_value or '').strip().lower().replace(' ', '') or 'blank'
            logger.debug(f"[DEBUG] Permission Set '{permission_set_name}': License='{license_value}' (normalized: '{normalized_license}')")
            # Parse userPermissions
            try:
                root = ET.fromstring(xml_str)
                user_permissions = []
                for perm in root.iter():
                    if perm.tag.endswith('userPermissions'):
                        name_elem = None
                        enabled_elem = None
                        for child in perm:
                            if child.tag.endswith('name'):
                                name_elem = child
                            elif child.tag.endswith('enabled'):
                                enabled_elem = child
                        if name_elem is not None and enabled_elem is not None:
                            user_permissions.append({
                                'name': name_elem.text.strip() if name_elem.text else '',
                                'enabled': enabled_elem.text.strip() if enabled_elem.text else ''
                            })
            except Exception as e:
                logger.error(f"Failed to parse userPermissions for {permission_set_name}: {e}")
                continue
            norm_names = [normalize_permission_name(p['name']) for p in user_permissions]
            # Best-practices matching (EXACT OLD ARCHITECTURE)
            relevant_bps = get_relevant_best_practices_for_profile(license_value, best_practices)
            matched_user_type = normalize_user_type(license_value)
            results_arr = []
            for bp in relevant_bps:
                bp_setting = (bp.get('SalesforceSetting') or '').strip()
                bp_standard_value = (bp.get('StandardValue') or '').strip()
                normalized_bp_setting = normalize_permission_name(bp_setting)
                ps_perm = next((p for p in user_permissions if normalize_permission_name(p['name']) == normalized_bp_setting), None)
                if ps_perm is not None:
                    ps_value = ps_perm['enabled']
                    match = normalize_value(ps_value) == normalize_value(bp_standard_value)
                    # Add ONLY mismatches for permission sets (like old architecture)
                    if not match:
                        results_arr.append({
                            'SalesforceSetting': bp_setting,
                            'StandardValue': bp_standard_value,
                            'PermissionSetValue-UserPermissions': ps_value,
                            'Match': False,  # Always False for permission sets
                            'UserType': matched_user_type,
                            'Description': bp.get('Description'),
                            'OWASP': bp.get('OWASP'),
                            'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity')
                        })
            # Store results if found (match old architecture format)
            if results_arr:
                # Send individual results to DB function (it will group them by permission set)
                policies_data = []
                for result in results_arr:
                    policy_data = {
                        'Setting': result.get('SalesforceSetting', ''),
                        'StandardValue': result.get('StandardValue', ''),
                        'OrgValue': result.get('PermissionSetValue-UserPermissions', ''),  # The actual permission value
                        'Match': result.get('Match', False),
                        'UserType': result.get('UserType', ''),
                        'IssueDescription': result.get('Description', ''),
                        'OWASPCategory': result.get('OWASP', ''),
                        'Severity': result.get('RiskTypeBasedOnSeverity', ''),
                        'IntegrationId': str(org_id),
                        'TaskStatusId': execution_log_id,
                        'CreatedAt': datetime.now().isoformat(),
                        'Type': 'PermissionSetPermissions',
                        'ProfileName': '',
                        'PermissionSetName': permission_set_name
                    }
                    policies_data.append(policy_data)

                # Store via DB service
                try:
                    db_client = get_db_client()
                    success = db_client.store_policies_result_data(
                        org_id=str(org_id),
                        execution_log_id=execution_log_id,
                        policies_data=policies_data
                    )
                    if success:
                        logger.info(f"Successfully stored policies results for permission set {permission_set_name}")
                    else:
                        logger.error(f"Failed to store policies results for permission set {permission_set_name}")
                except Exception as e:
                    logger.error(f"Error storing policies results via DB service: {e}")
        except Exception as e:
            logger.error(f"[DEBUG] Permission Set '{permission_set_name}': Error processing: {e}")

def load_best_practices_xml(xml_path):
    """Load best practices from XML file"""
    import xml.etree.ElementTree as ET
    try:
        tree = ET.parse(xml_path)
        root = tree.getroot()
        best_practices = []
        for user_type in root.findall('UserType'):
            user_type_name = user_type.get('name', '')
            for practice in user_type.findall('Practice'):
                bp = {'UserType': user_type_name}
                for child in practice:
                    bp[child.tag] = child.text.strip() if child.text else ''
                best_practices.append(bp)
        logger.info(f"Loaded {len(best_practices)} best practices from {xml_path}")
        return best_practices
    except Exception as e:
        logger.error(f"Error loading best practices from {xml_path}: {e}")
        return []



def process_permission_sets_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: str) -> None:
    """
    Process a permission sets task - extracts permission set data from Salesforce

    Args:
        processor: Background processor
        task_id: Task ID
        org_id: Organization ID
        user_id: User ID
        params: Task parameters
        execution_log_id: Execution log ID
    """
    try:
        logger.info(f"Processing permission sets task {task_id} for organization {org_id}")
        processor.update_task_status(task_id, TASK_STATUS_RUNNING, 10, "Starting permission sets data collection")

        access_token = params.get("access_token")
        instance_url = params.get("instance_url")
        integration_id_from_params = params.get("integration_id")

        if not all([access_token, instance_url, integration_id_from_params]):
            err_msg = "Missing access_token, instance_url, or integration_id in task parameters for permission sets."
            logger.error(f"[PermissionSetsTask {task_id}] {err_msg}")
            processor.update_task_status(task_id, TASK_STATUS_FAILED, 100, err_msg)
            return

        if org_id != integration_id_from_params:
            logger.warning(f"[PermissionSetsTask {task_id}] org_id ({org_id}) and integration_id_from_params ({integration_id_from_params}) mismatch. Using org_id from task.")

        try:
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=30,
                message="Fetching permission sets from Salesforce"
            )

            # Query permission sets (excluding profile-owned ones)
            permission_sets_query = """
                SELECT Id, Name, Label, Description, License, IsOwnedByProfile, Type
                FROM PermissionSet
                WHERE IsOwnedByProfile = false
                ORDER BY Name
            """

            # Execute the query in the event loop
            import asyncio
            import concurrent.futures

            def run_query():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(
                        execute_salesforce_query(permission_sets_query, access_token, instance_url)
                    )
                finally:
                    new_loop.close()

            try:
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_query)
                    permission_sets_result = future.result()
            except Exception as e:
                logger.error(f"Error executing permission sets query: {e}")
                permission_sets_result = None

            if not permission_sets_result or "records" not in permission_sets_result:
                logger.warning(f"[PermissionSetsTask {task_id}] No permission sets found")
                processor.update_task_status(task_id, TASK_STATUS_COMPLETED, 100, "No permission sets found")
                return

            permission_sets = permission_sets_result["records"]
            logger.info(f"[PermissionSetsTask {task_id}] Found {len(permission_sets)} permission sets")

            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_RUNNING,
                progress=60,
                message=f"Processing {len(permission_sets)} permission sets"
            )

            # Store permission sets data via DB service
            db_client = get_db_client()

            # Transform permission sets data for storage
            permission_sets_data = []
            for ps in permission_sets:
                permission_sets_data.append({
                    'permission_set_id': ps.get('Id', ''),
                    'name': ps.get('Name', ''),
                    'label': ps.get('Label', ''),
                    'description': ps.get('Description', ''),
                    'license': ps.get('License', ''),
                    'type': ps.get('Type', ''),
                    'is_owned_by_profile': ps.get('IsOwnedByProfile', False)
                })

            # Store permission sets data via DB service
            success = db_client.store_permission_sets_data(
                org_id=org_id,
                execution_log_id=execution_log_id,
                permission_sets_data=permission_sets_data
            )

            if success:
                logger.info(f"[PermissionSetsTask {task_id}] Successfully stored {len(permission_sets_data)} permission sets for {org_id}")
                processor.update_task_status(
                    task_id=task_id,
                    status=TASK_STATUS_COMPLETED,
                    progress=100,
                    message=f"Successfully processed {len(permission_sets_data)} permission sets"
                )
            else:
                logger.error(f"[PermissionSetsTask {task_id}] Failed to store permission sets data for {org_id}")
                processor.update_task_status(
                    task_id=task_id,
                    status=TASK_STATUS_FAILED,
                    progress=100,
                    message="Failed to store permission sets data"
                )

        except Exception as e:
            logger.error(f"[PermissionSetsTask {task_id}] Error processing permission sets task: {str(e)}")
            logger.error(f"[PermissionSetsTask {task_id}] Traceback: {traceback.format_exc()}")
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=100,
                message=f"Error processing permission sets task: {str(e)}"
            )

    except Exception as e:
        logger.error(f"[PermissionSetsTask {task_id}] Error processing permission sets task: {str(e)}")
        logger.error(f"[PermissionSetsTask {task_id}] Traceback: {traceback.format_exc()}")
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_FAILED,
            progress=100,
            message=f"Error processing permission sets task: {str(e)}"
        )

def check_parent_task_completion(execution_log_id: str) -> bool:
    """
    Check if all child tasks for a parent task have completed successfully
    
    Args:
        execution_log_id: Execution log ID to check
        
    Returns:
        bool: True if all child tasks completed successfully, False otherwise
    """
    try:
        from shared.db_service_client import get_db_client
        db_client = get_db_client()
        
        if not db_client:
            logger.error("DB service client not available for parent task completion check")
            return False
        
        # Get all tasks for this execution log ID
        tasks = db_client.get_tasks_by_execution_log(execution_log_id)
        
        if not tasks:
            logger.warning(f"No tasks found for execution log ID: {execution_log_id}")
            return False
        
        # Check if all tasks are completed
        all_completed = True
        failed_tasks = []
        
        for task in tasks:
            task_id = task.get('TaskId')
            task_type = task.get('TaskType')
            status = task.get('Status')
            
            if status not in ['Completed', 'Success']:
                all_completed = False
                if status == 'Failed':
                    failed_tasks.append(f"{task_type} ({task_id})")
        
        if all_completed:
            logger.info(f"All child tasks for execution log {execution_log_id} completed successfully")
            return True
        else:
            if failed_tasks:
                logger.warning(f"Some child tasks failed for execution log {execution_log_id}: {', '.join(failed_tasks)}")
            else:
                logger.info(f"Some child tasks still in progress for execution log {execution_log_id}")
            return False
            
    except Exception as e:
        logger.error(f"Error checking parent task completion for execution log {execution_log_id}: {str(e)}")
        return False

def update_parent_task_status(execution_log_id: str, parent_task_id: str) -> None:
    """
    Update parent task status based on child task completion
    
    Args:
        execution_log_id: Execution log ID
        parent_task_id: Parent task ID to update
    """
    try:
        from shared.background_processor import BackgroundProcessor, TASK_STATUS_COMPLETED, TASK_STATUS_FAILED
        
        # Check if all child tasks completed
        all_completed = check_parent_task_completion(execution_log_id)
        
        processor = BackgroundProcessor()
        
        if all_completed:
            # Update parent task to completed
            processor.update_task_status(
                task_id=parent_task_id,
                status=TASK_STATUS_COMPLETED,
                progress=100,
                message="All child tasks completed successfully"
            )
            logger.info(f"Parent task {parent_task_id} marked as completed - all child tasks finished")
        else:
            # Check if any child tasks failed
            from shared.db_service_client import get_db_client
            db_client = get_db_client()
            
            if db_client:
                tasks = db_client.get_tasks_by_execution_log(execution_log_id)
                failed_tasks = [t for t in tasks if t.get('Status') == 'Failed']
                
                if failed_tasks:
                    # Update parent task to failed
                    failed_task_names = [t.get('TaskType') for t in failed_tasks]
                    error_message = f"Child tasks failed: {', '.join(failed_task_names)}"
                    
                    processor.update_task_status(
                        task_id=parent_task_id,
                        status=TASK_STATUS_FAILED,
                        progress=100,
                        message=error_message
                    )
                    logger.warning(f"Parent task {parent_task_id} marked as failed - child tasks failed: {error_message}")
                else:
                    # Some tasks still in progress - leave parent as in progress
                    logger.info(f"Parent task {parent_task_id} still in progress - waiting for child tasks")
        
    except Exception as e:
        logger.error(f"Error updating parent task status for {parent_task_id}: {str(e)}")