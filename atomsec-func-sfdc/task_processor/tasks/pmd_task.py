import logging
from typing import Dict, Any, Optional
import os
import tempfile
from shared.background_processor import BackgroundProcessor
from shared.db_service_client import get_db_client
from pmd_components.pmd_scanner import PMDScanner
from pmd_components.pmd_blob_handler import PMDBlobHandler
from pmd_components.pmd_results_processor import PMDResultsProcessor
from datetime import datetime

os.environ["JAVA_TOOL_OPTIONS"] = "--enable-native-access=ALL-UNNAMED"

logger = logging.getLogger(__name__)

def _truncate_error_message(message: str, max_length: int = 30000) -> str:
    """
    Truncate error message to fit within Azure Table Storage limits
    
    Args:
        message: Error message to truncate
        max_length: Maximum length (default 30KB to be safe)
    
    Returns:
        Truncated error message
    """
    if len(message) <= max_length:
        return message
    
    # Truncate and add indicator
    truncated = message[:max_length-100]  # Leave room for truncation indicator
    return f"{truncated}... [MESSAGE TRUNCATED - SEE LOGS FOR FULL ERROR]"

def _get_enabled_pmd_categories_and_rules(org_id: str) -> tuple[list[str], list[str]]:
    """
    Get enabled PMD categories and individual rules for the given integration

    Args:
        org_id: Organization/Integration ID

    Returns:
        Tuple of (enabled_categories, enabled_individual_rules)
    """
    try:
        db_client = get_db_client()
        subtasks = db_client.get_pmd_subtasks(org_id)

        if not subtasks:
            logger.warning(f"No enabled PMD subtasks found for integration {org_id}, using all available PMD categories")
            # Use all available PMD categories when no subtasks are configured
            return ["security", "design", "performance", "bestpractices", "codestyle"], []

        enabled_categories = []
        enabled_individual_rules = []

        for subtask in subtasks:
            subtask_name = subtask.get("SubtaskName", "").lower()
            subtask_id = subtask.get("SubtaskId")
            subtask_enabled = subtask.get("Enabled", True)  # Default to True if not specified

            # Only process enabled subtasks
            if subtask_name and subtask_enabled:
                enabled_categories.append(subtask_name)

                # Get individual rules for this subtask
                individual_rules = db_client.get_pmd_rules(subtask_id)
                for rule in individual_rules:
                    rule_name = rule.get("RuleName", "")
                    rule_enabled = rule.get("Enabled", True)  # Default to True if not specified
                    
                    # Only add enabled individual rules
                    if rule_name and rule_enabled and rule_name not in enabled_individual_rules:
                        enabled_individual_rules.append(rule_name)

        # Remove duplicates from categories as well
        enabled_categories = list(set(enabled_categories))

        logger.info(f"Found {len(enabled_categories)} enabled PMD categories and {len(enabled_individual_rules)} individual rules for integration {org_id}")
        logger.info(f"Enabled categories: {enabled_categories}")
        logger.info(f"Enabled individual rules: {enabled_individual_rules}")

        return enabled_categories, enabled_individual_rules

    except Exception as e:
        logger.error(f"Error getting enabled PMD categories and rules for integration {org_id}: {e}")
        # Fallback to default categories
        return ["security", "performance"], []

def process_pmd_task(processor: BackgroundProcessor, task_id: str, org_id: str, user_id: str, params: Dict[str, Any], execution_log_id: Optional[str] = None) -> None:
    """
    Process a PMD task by scanning Apex classes from blob storage.
    
    This updated version uses modular components and removes hardcoding.
    Now supports granular subtask execution based on enabled categories.
    """
    logger.info(f"Starting PMD task for org_id: {org_id}")
    processor.update_task_status(task_id, "running", progress=10, message="Initializing PMD scan")

    try:
        # 1. Get blob prefix from params (no hardcoding)
        blob_prefix = params.get("blob_prefix")
        if not blob_prefix:
            error_message = "blob_prefix not found in task parameters. PMD scan cannot proceed without Apex classes."
            logger.error(f"[PMDTask {task_id}] {error_message}")
            processor.update_task_status(task_id, "failed", message=_truncate_error_message(error_message))
            return

        # 2. Initialize modular components
        processor.update_task_status(task_id, "running", progress=20, message="Initializing PMD components")
        
        # Initialize scanner with optional custom rules directory
        custom_rules_dir = params.get("custom_rules_dir")
        scanner = PMDScanner(custom_rules_dir=custom_rules_dir)
        
        # Initialize blob handler
        blob_handler = PMDBlobHandler(container_name="salesforce-metadata")
        
        # Initialize results processor
        results_processor = PMDResultsProcessor()
        
        # 3. Get enabled scan categories from database (new granular approach)
        scan_categories = params.get("scan_categories")  # Allow override from params
        enabled_individual_rules = params.get("enabled_individual_rules")  # Allow override from params
        
        if not scan_categories or not enabled_individual_rules:
            scan_categories, enabled_individual_rules = _get_enabled_pmd_categories_and_rules(org_id)
        
        logger.info(f"PMD scan will use categories: {scan_categories}")
        logger.info(f"PMD scan will use individual rules: {enabled_individual_rules}")
        processor.update_task_status(task_id, "running", progress=25, message=f"Configured PMD scan for {len(scan_categories)} categories and {len(enabled_individual_rules)} individual rules")
        
        # 4. Create temporary directory for scan
        temp_dir = f"/tmp/pmd_scan_{org_id}_{task_id}"
        os.makedirs(temp_dir, exist_ok=True)
        results_file = os.path.join(temp_dir, "pmd_results.csv")
        
        # 5. Create custom ruleset if individual rules are specified
        custom_ruleset_file = None
        if enabled_individual_rules:
            custom_ruleset_file = os.path.join(temp_dir, "custom_ruleset.xml")
            scanner.rules_config.create_custom_ruleset_with_enabled_rules(enabled_individual_rules, custom_ruleset_file)
            logger.info(f"Created custom ruleset with {len(enabled_individual_rules)} rules: {custom_ruleset_file}")
        
        # 6. Get blob information
        processor.update_task_status(task_id, "running", progress=30, message="Analyzing blob storage")
        blob_info = blob_handler.get_blob_info(blob_prefix)
        
        if blob_info["classes_count"] == 0:
            logger.warning(f"No Apex classes found in blob storage at {blob_prefix}/classes/")
            processor.update_task_status(task_id, "completed", progress=100, message="No Apex classes found to scan.")
            return
        
        logger.info(f"Found {blob_info['classes_count']} Apex classes ({blob_info['total_size_mb']} MB)")

        # 7. Run PMD scan using blob handler with enabled categories and individual rules
        processor.update_task_status(task_id, "running", progress=50, message=f"Running PMD scan on {blob_info['classes_count']} classes with {len(scan_categories)} categories and {len(enabled_individual_rules)} individual rules")
        
        scan_result = blob_handler.scan_classes_in_blob(
            blob_prefix=blob_prefix,
            scanner=scanner,
            output_file=results_file,
            categories=scan_categories,
            temp_dir=temp_dir,
            custom_ruleset_file=custom_ruleset_file
        )
        
        if not scan_result.get("success", False):
            error_message = scan_result.get("error", "Unknown error during PMD scan")
            logger.error(f"PMD scan failed: {error_message}")
            processor.update_task_status(task_id, "failed", message=_truncate_error_message(error_message))
            return

        # 8. Process and store findings
        processor.update_task_status(task_id, "running", progress=80, message="Processing and storing findings")
        
        findings = scan_result.get("findings", [])
        processing_result = results_processor.process_findings(
            findings=findings,
            org_id=org_id,
            task_id=task_id,
            execution_log_id=execution_log_id,
            blob_prefix=blob_prefix
        )
        
        # 9. Upload results to blob storage
        processor.update_task_status(task_id, "running", progress=90, message="Uploading results to blob storage")
        
        results_url = blob_handler.upload_scan_results(
            results_file=results_file,
            blob_prefix=blob_prefix,
            results_type="pmd_results"
        )
        
        # 10. Update task result with comprehensive information
        task_result = {
            "pmd_results_csv": results_url,
            "environment": "local" if scanner.is_local else "production",
            "scan_timestamp": datetime.now().isoformat(),
            "findings_count": len(findings),
            "files_scanned": scan_result.get("files_scanned", 0),
            "total_classes_found": blob_info["classes_count"],
            "processed_findings": processing_result["processed_count"],
            "error_count": processing_result["error_count"],
            "scan_categories": scan_categories,
            "enabled_categories_count": len(scan_categories),
            "enabled_individual_rules": enabled_individual_rules,
            "enabled_individual_rules_count": len(enabled_individual_rules),
            "blob_prefix": blob_prefix,
            "scanner_info": scanner.get_scanner_info()
        }
        
        processor.update_task_result(task_id, task_result)
        
        # 11. Log summary
        logger.info("=" * 60)
        logger.info("PMD SCAN SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Organization ID: {org_id}")
        logger.info(f"Blob Prefix: {blob_prefix}")
        logger.info(f"Classes Found: {blob_info['classes_count']}")
        logger.info(f"Files Scanned: {scan_result.get('files_scanned', 0)}")
        logger.info(f"Findings Found: {len(findings)}")
        logger.info(f"Findings Processed: {processing_result['processed_count']}")
        logger.info(f"Enabled Categories: {', '.join(scan_categories)}")
        logger.info(f"Enabled Individual Rules: {', '.join(enabled_individual_rules)}")
        logger.info(f"Results URL: {results_url}")
        logger.info("=" * 60)
        
        processor.update_task_status(task_id, "completed", progress=100, message=f"PMD scan completed successfully with {len(scan_categories)} categories and {len(enabled_individual_rules)} individual rules")

    except Exception as e:
        logger.error(f"Error during PMD task: {e}", exc_info=True)
        processor.update_task_status(task_id, "failed", message=_truncate_error_message(str(e))) 