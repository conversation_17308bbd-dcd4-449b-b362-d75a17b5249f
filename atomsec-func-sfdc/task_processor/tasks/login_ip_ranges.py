import os
import asyncio
import json
import logging
from datetime import datetime
import ipaddress
from shared.data_access import BlobStorageRepository
from shared.db_service_client import get_db_client
from shared.salesforce_utils import execute_salesforce_query
from .utils import (
    normalize_profile_name,
    get_active_profiles_and_permissionsets
)
import xml.etree.ElementTree as ET

# The evaluate_ip_range function below is designed to handle the following user scenarios:
#
# - Private range (should flag as private IPs)
# - Full range (should flag as wide range and possibly unsafe)
# - Public cloud range (should flag as possibly unsafe/public cloud)
# - Single public IP (should not flag any issues)
# - Shared address space (should not flag as private, but is reserved)
# - TEST-NET-3 (should not flag as private, but is reserved for documentation)
# - Invalid start IP (should flag as invalid IP)
#
# See test_login_ip_ranges_standalone.py for concrete test cases.

def evaluate_ip_range(start_ip, end_ip):
    issues = []
    try:
        start = ipaddress.IPv4Address(start_ip)
        end = ipaddress.IPv4Address(end_ip)
    except Exception as e:
        issues.append(f"Invalid IP address: {start_ip} - {end_ip} ({e})")
        return issues
    # Check if range is too wide
    ip_range_size = int(end) - int(start)
    if ip_range_size > 65535:
        issues.append(f"Wide range: {start_ip} - {end_ip} ({ip_range_size} IPs)")
    # Check for private IP usage
    for ip in [start, end]:
        if ip.is_private:
            issues.append(f"Private IP used: {ip}")
    # Check for known wide public cloud ranges
    if any(str(ip).startswith(prefix) for prefix in ["0.", "1.", "2.", "3.", "52."]):
        issues.append(f"Possibly unsafe or public cloud IP range: {start_ip} - {end_ip}")
    return issues

def process_login_ip_ranges_task(processor, task_id, org_id, user_id, params, execution_log_id):
    logger = logging.getLogger(__name__)
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    org_name = params.get("org_name")
    environment = params.get("environment", "production")
    blob_prefix = params.get("blob_prefix")
    if not (access_token and instance_url and blob_prefix):
        logger.error(f"[LoginIPRangesTask {task_id}] Missing required params (access_token, instance_url, blob_prefix)")
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message="Missing required params for Login IP Ranges task."
        )
        return
    blob_repo = BlobStorageRepository(container_name="salesforce-metadata")
    repo = get_db_client()
    try:
        # 1. Query Salesforce for active profiles (with active user assignments)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        profile_query = "SELECT Id, Name FROM Profile"
        profile_query_result = loop.run_until_complete(
            execute_salesforce_query(
                profile_query,
                access_token=access_token,
                instance_url=instance_url,
                environment=environment
            )
        )
        # Get active profiles using shared utility
        active_profile_names, _ = get_active_profiles_and_permissionsets(access_token, instance_url, environment)
        # 2. Traverse profile XML files in blob storage
        profile_folder = f"{blob_prefix}/profiles/"
        blobs = blob_repo.list_blobs(name_starts_with=profile_folder)
        inserted_profiles = set()
        for blob_name in blobs:
            if not blob_name.endswith('.profile'):
                continue
            profile_name = os.path.basename(blob_name).replace('.profile', '')
            norm_blob_name = normalize_profile_name(profile_name)
            if norm_blob_name not in active_profile_names:
                continue  # Only process active profiles
            if norm_blob_name in inserted_profiles:
                continue  # Skip duplicate
            is_admin = norm_blob_name == 'admin'
            is_standard = norm_blob_name == 'standard'
            # Only process if this profile is active
            if not (
                norm_blob_name in active_profile_names or
                (is_admin and 'system administrator' in active_profile_names) or
                (is_standard and 'standard platform user' in active_profile_names)
            ):
                continue
            try:
                xml_bytes = blob_repo.get_blob_bytes(blob_name)
                if xml_bytes is None:
                    logger.error(f"[LoginIPRangesTask {task_id}] Failed to download blob bytes for {blob_name}")
                    continue
            except Exception as e:
                logger.error(f"[LoginIPRangesTask {task_id}] Error downloading blob {blob_name}: {e}")
                continue
            # Parse XML for <loginIpRanges>
            try:
                xml_str = xml_bytes.decode('utf-8', errors='ignore')
                logger.debug(f"[LoginIPRangesTask {task_id}] XML for {profile_name} (first 1000 chars):\n{xml_str[:1000]}")
                root = ET.fromstring(xml_bytes)
                issues_found = []
                found_any_ip_range = False
                for ip_range in root.iter():
                    if ip_range.tag.endswith('loginIpRanges'):
                        start_elem = None
                        end_elem = None
                        for child in ip_range:
                            if child.tag.endswith('startAddress'):
                                start_elem = child
                            elif child.tag.endswith('endAddress'):
                                end_elem = child
                        if start_elem is not None and end_elem is not None:
                            found_any_ip_range = True
                            start_ip = start_elem.text.strip()
                            end_ip = end_elem.text.strip()
                            logger.debug(f"[LoginIPRangesTask {task_id}] Found IP range in {profile_name}: {start_ip} - {end_ip}")
                            issues = evaluate_ip_range(start_ip, end_ip)
                            for issue in issues:
                                issues_found.append({
                                    'StartIP': start_ip,
                                    'EndIP': end_ip,
                                    'Issue': issue
                                })
                if not found_any_ip_range:
                    logger.debug(f"[LoginIPRangesTask {task_id}] No <loginIpRanges> found in {profile_name}")
                # Store results if issues found - OLD ARCHITECTURE FORMAT
                if issues_found:
                    # Remove duplicates
                    unique_results = []
                    seen = set()
                    for entry in issues_found:
                        entry_json = json.dumps(entry, sort_keys=True)
                        if entry_json not in seen:
                            unique_results.append(entry)
                            seen.add(entry_json)
                    
                    # Store results using DB service client - OLD ARCHITECTURE FORMAT
                    policy_data = {
                        'PartitionKey': str(org_id),
                        'RowKey': f"{norm_blob_name}-{execution_log_id}",
                        'OrgValue': json.dumps(unique_results),  # Store all results as JSON array
                        'IntegrationId': str(org_id),
                        'TaskStatusId': execution_log_id,
                        'CreatedAt': datetime.now().isoformat(),
                        'ProfileName': norm_blob_name,
                        'Type': 'LoginIPRangesProfilePermissions',
                    }

                    try:
                        repo.store_policies_result_data(str(org_id), execution_log_id, [policy_data])
                        inserted_profiles.add(norm_blob_name)
                        logger.info(f"[LoginIPRangesTask {task_id}] Stored results for profile {norm_blob_name}")
                    except Exception as e:
                        logger.error(f"[LoginIPRangesTask {task_id}] Failed to store login IP ranges results for profile {norm_blob_name}: {e}")
            except Exception as e:
                logger.error(f"[LoginIPRangesTask {task_id}] Error parsing XML for {profile_name}: {e}")
        loop.close()
        processor.update_task_status(
            task_id=task_id,
            status="completed",
            progress=100,
            message="Login IP Ranges check completed and results stored."
        )
    except Exception as e:
        logger.error(f"[LoginIPRangesTask {task_id}] Error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message=f"Error processing Login IP Ranges task: {e}"
        ) 