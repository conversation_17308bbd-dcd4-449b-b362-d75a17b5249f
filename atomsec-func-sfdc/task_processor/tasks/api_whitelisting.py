import os
import asyncio
import json
import logging
from datetime import datetime
import xml.etree.ElementTree as ET
from shared.data_access import BlobStorageRepository
from shared.db_service_client import get_db_client
from shared.salesforce_utils import execute_salesforce_query
from .utils import (
    normalize_profile_name,
    get_active_profiles_and_permissionsets
)

def process_api_whitelisting_task(processor, task_id, org_id, user_id, params, execution_log_id):
    """
    Handler for API Whitelisting task. Checks API access settings in profiles against best practices.
    Only inserts mismatches or missing settings into PoliciesResult.
    """
    logger = logging.getLogger(__name__)
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    org_name = params.get("org_name")
    environment = params.get("environment", "production")
    blob_prefix = params.get("blob_prefix")
    
    if not (access_token and instance_url and blob_prefix):
        logger.error(f"[APIWhitelistingTask {task_id}] Missing required params (access_token, instance_url, blob_prefix)")
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message="Missing required params for API Whitelisting task."
        )
        return

    blob_repo = BlobStorageRepository(container_name="salesforce-metadata")
    repo = get_db_client()

    try:
        # Get active profiles using shared utility
        active_profile_names, _ = get_active_profiles_and_permissionsets(access_token, instance_url, environment)
        
        # Traverse profile XML files in blob storage
        profile_folder = f"{blob_prefix}/profiles/"
        blobs = blob_repo.list_blobs(name_starts_with=profile_folder)
        inserted_profiles = set()
        
        for blob_name in blobs:
            if not blob_name.endswith('.profile'):
                continue
                
            profile_name = os.path.basename(blob_name).replace('.profile', '')
            norm_blob_name = normalize_profile_name(profile_name)
            
            if norm_blob_name not in active_profile_names:
                continue  # Only process active profiles
                
            if norm_blob_name in inserted_profiles:
                continue  # Skip duplicate
                
            is_admin = norm_blob_name == 'admin'
            is_standard = norm_blob_name == 'standard'
            
            # Only process if this profile is active
            if not (
                norm_blob_name in active_profile_names or
                (is_admin and 'system administrator' in active_profile_names) or
                (is_standard and 'standard platform user' in active_profile_names)
            ):
                continue

            try:
                xml_bytes = blob_repo.get_blob_bytes(blob_name)
                if xml_bytes is None:
                    logger.error(f"[APIWhitelistingTask {task_id}] Failed to download blob bytes for {blob_name}")
                    continue
            except Exception as e:
                logger.error(f"[APIWhitelistingTask {task_id}] Error downloading blob {blob_name}: {e}")
                continue

            # Parse XML for API-related permissions
            try:
                xml_str = xml_bytes.decode('utf-8', errors='ignore')
                root = ET.fromstring(xml_bytes)
                issues_found = []
                
                # Check for API access permissions
                api_permissions = [
                    'ApiEnabled',
                    'ApiUserOnly',
                    'RestrictApiAccessToIPRanges',
                    'RestrictApiAccessToLoginHours'
                ]
                
                for permission in api_permissions:
                    found_permission = False
                    for elem in root.iter():
                        if elem.tag.endswith('userPermissions'):
                            name_elem = None
                            enabled_elem = None
                            for child in elem:
                                if child.tag.endswith('name'):
                                    name_elem = child
                                elif child.tag.endswith('enabled'):
                                    enabled_elem = child
                            
                            if name_elem and name_elem.text and name_elem.text.strip() == permission:
                                found_permission = True
                                enabled = enabled_elem.text.strip() if enabled_elem and enabled_elem.text else 'false'
                                
                                # Check if API access is enabled but not properly restricted
                                if permission == 'ApiEnabled' and enabled.lower() == 'true':
                                    # Check if IP restrictions are not enabled
                                    ip_restriction_found = False
                                    for ip_elem in root.iter():
                                        if ip_elem.tag.endswith('userPermissions'):
                                            ip_name_elem = None
                                            ip_enabled_elem = None
                                            for ip_child in ip_elem:
                                                if ip_child.tag.endswith('name'):
                                                    ip_name_elem = ip_child
                                                elif ip_child.tag.endswith('enabled'):
                                                    ip_enabled_elem = ip_child
                                            
                                            if (ip_name_elem and ip_name_elem.text and 
                                                ip_name_elem.text.strip() == 'RestrictApiAccessToIPRanges' and
                                                ip_enabled_elem and ip_enabled_elem.text and 
                                                ip_enabled_elem.text.strip().lower() == 'true'):
                                                ip_restriction_found = True
                                                break
                                    
                                    if not ip_restriction_found:
                                        issues_found.append({
                                            'Setting': 'ApiEnabled',
                                            'Issue': f'API access enabled but IP restrictions not configured for {permission}',
                                            'Recommendation': 'Enable IP restrictions for API access to prevent unauthorized access',
                                            'RiskLevel': 'High'
                                        })
                                break
                    
                    # If API permission not found, it might be a security concern
                    if not found_permission and permission in ['RestrictApiAccessToIPRanges', 'RestrictApiAccessToLoginHours']:
                        issues_found.append({
                            'Setting': permission,
                            'Issue': f'API restriction setting {permission} not configured',
                            'Recommendation': f'Configure {permission} to restrict API access',
                            'RiskLevel': 'Medium'
                        })
                
                # Insert only if issues found
                if issues_found:
                    # Transform the data structure to match DB service expectations
                    policies_data = []
                    for issue in issues_found:
                        policy_data = {
                            'Setting': issue.get('Setting', 'APIWhitelisting'),
                            'OrgValue': issue.get('Issue', ''),
                            'StandardValue': 'API access properly restricted',
                            'OWASPCategory': 'A01:2021',  # Access Control
                            'IssueDescription': issue.get('Issue', ''),
                            'Recommendations': issue.get('Recommendation', ''),
                            'Severity': issue.get('RiskLevel', 'High'),
                            'Weakness': 'API_ACCESS_NOT_RESTRICTED',
                            'IntegrationId': str(org_id),
                            'TaskStatusId': execution_log_id,
                            'CreatedAt': datetime.now().isoformat(),
                            'Type': 'APIWhitelistingProfilePermissions',
                            'ProfileName': profile_name
                        }
                        policies_data.append(policy_data)

                    try:
                        repo.store_policies_result_data(str(org_id), execution_log_id, policies_data)
                        inserted_profiles.add(norm_blob_name)
                    except Exception as e:
                        logger.error(f"[APIWhitelistingTask {task_id}] Failed to store API whitelisting results for profile {profile_name}: {e}")
                        
            except Exception as e:
                logger.error(f"[APIWhitelistingTask {task_id}] Error parsing XML for {profile_name}: {e}")

        processor.update_task_status(
            task_id=task_id,
            status="completed",
            progress=100,
            message="API Whitelisting check completed and results stored."
        )
        
    except Exception as e:
        logger.error(f"[APIWhitelistingTask {task_id}] Error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message=f"Error processing API Whitelisting task: {e}"
        ) 