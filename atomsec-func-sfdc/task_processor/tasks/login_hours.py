import os
import asyncio
import json
import logging
from datetime import datetime
import xml.etree.ElementTree as ET
from shared.data_access import BlobStorageRepository
from shared.db_service_client import get_db_client
from shared.salesforce_utils import execute_salesforce_query
from .utils import (
    normalize_profile_name,
    get_active_profiles_and_permissionsets
)

def process_login_hours_task(processor, task_id, org_id, user_id, params, execution_log_id):
    """
    Handler for Login Hours task. Checks login hours settings in profiles against best practices.
    Only inserts mismatches or missing settings into PoliciesResult.
    """
    logger = logging.getLogger(__name__)
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    org_name = params.get("org_name")
    environment = params.get("environment", "production")
    blob_prefix = params.get("blob_prefix")
    
    if not (access_token and instance_url and blob_prefix):
        logger.error(f"[LoginHoursTask {task_id}] Missing required params (access_token, instance_url, blob_prefix)")
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message="Missing required params for Login Hours task."
        )
        return

    blob_repo = BlobStorageRepository(container_name="salesforce-metadata")
    repo = get_db_client()

    try:
        # Get active profiles using shared utility
        active_profile_names, _ = get_active_profiles_and_permissionsets(access_token, instance_url, environment)
        
        # Traverse profile XML files in blob storage
        profile_folder = f"{blob_prefix}/profiles/"
        blobs = blob_repo.list_blobs(name_starts_with=profile_folder)
        inserted_profiles = set()
        
        for blob_name in blobs:
            if not blob_name.endswith('.profile'):
                continue
                
            profile_name = os.path.basename(blob_name).replace('.profile', '')
            norm_blob_name = normalize_profile_name(profile_name)
            
            if norm_blob_name not in active_profile_names:
                continue  # Only process active profiles
                
            if norm_blob_name in inserted_profiles:
                continue  # Skip duplicate
                
            is_admin = norm_blob_name == 'admin'
            is_standard = norm_blob_name == 'standard'
            
            # Only process if this profile is active
            if not (
                norm_blob_name in active_profile_names or
                (is_admin and 'system administrator' in active_profile_names) or
                (is_standard and 'standard platform user' in active_profile_names)
            ):
                continue

            try:
                xml_bytes = blob_repo.get_blob_bytes(blob_name)
                if xml_bytes is None:
                    logger.error(f"[LoginHoursTask {task_id}] Failed to download blob bytes for {blob_name}")
                    continue
            except Exception as e:
                logger.error(f"[LoginHoursTask {task_id}] Error downloading blob {blob_name}: {e}")
                continue

            # Parse XML for <loginHours>
            try:
                xml_str = xml_bytes.decode('utf-8', errors='ignore')
                root = ET.fromstring(xml_bytes)
                issues_found = []
                found_any_login_hours = False
                
                for login_hours in root.iter():
                    if login_hours.tag.endswith('loginHours'):
                        found_any_login_hours = True
                        # Check if login hours are configured
                        has_any_hours = False
                        for child in login_hours:
                            if child.tag.endswith('mondayStart') or child.tag.endswith('tuesdayStart') or \
                               child.tag.endswith('wednesdayStart') or child.tag.endswith('thursdayStart') or \
                               child.tag.endswith('fridayStart') or child.tag.endswith('saturdayStart') or \
                               child.tag.endswith('sundayStart'):
                                if child.text and child.text.strip():
                                    has_any_hours = True
                                    break
                        
                        if not has_any_hours:
                            issues_found.append({
                                'Setting': 'LoginHours',
                                'Issue': 'No login hours configured - users can login 24/7',
                                'Recommendation': 'Configure login hours to restrict access to business hours only',
                                'RiskLevel': 'Medium'
                            })
                        else:
                            # Check for overly permissive hours (e.g., 24/7 access)
                            # This is a basic check - in a real implementation you'd want more sophisticated analysis
                            issues_found.append({
                                'Setting': 'LoginHours',
                                'Issue': 'Login hours are configured but should be reviewed for security',
                                'Recommendation': 'Ensure login hours are restricted to business hours only',
                                'RiskLevel': 'Low'
                            })
                
                if not found_any_login_hours:
                    logger.debug(f"[LoginHoursTask {task_id}] No <loginHours> found in {profile_name}")
                    issues_found.append({
                        'Setting': 'LoginHours',
                        'Issue': 'Login hours not configured - users can login 24/7',
                        'Recommendation': 'Configure login hours to restrict access to business hours only',
                        'RiskLevel': 'Medium'
                    })
                
                # Insert only if issues found
                if issues_found:
                    # Transform the data structure to match DB service expectations
                    policies_data = []
                    for issue in issues_found:
                        policy_data = {
                            'Setting': issue.get('Setting', 'LoginHours'),
                            'OrgValue': issue.get('Issue', ''),
                            'StandardValue': 'Configured login hours',
                            'OWASPCategory': 'A01:2021',  # Access Control
                            'IssueDescription': issue.get('Issue', ''),
                            'Recommendations': issue.get('Recommendation', ''),
                            'Severity': issue.get('RiskLevel', 'Medium'),
                            'Weakness': 'LOGIN_HOURS_NOT_CONFIGURED',
                            'IntegrationId': str(org_id),
                            'TaskStatusId': execution_log_id,
                            'CreatedAt': datetime.now().isoformat(),
                            'Type': 'LoginHoursProfilePermissions',
                            'ProfileName': profile_name
                        }
                        policies_data.append(policy_data)

                    try:
                        repo.store_policies_result_data(str(org_id), execution_log_id, policies_data)
                        inserted_profiles.add(norm_blob_name)
                    except Exception as e:
                        logger.error(f"[LoginHoursTask {task_id}] Failed to store login hours results for profile {profile_name}: {e}")
                        
            except Exception as e:
                logger.error(f"[LoginHoursTask {task_id}] Error parsing XML for {profile_name}: {e}")

        processor.update_task_status(
            task_id=task_id,
            status="completed",
            progress=100,
            message="Login Hours check completed and results stored."
        )
        
    except Exception as e:
        logger.error(f"[LoginHoursTask {task_id}] Error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message=f"Error processing Login Hours task: {e}"
        ) 