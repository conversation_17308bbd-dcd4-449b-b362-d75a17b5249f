import os
import asyncio
import json
import logging
from datetime import datetime
import xml.etree.ElementTree as ET
from shared.data_access import BlobStorageRepository
from shared.db_service_client import get_db_client
from shared.salesforce_utils import execute_salesforce_query
from .utils import (
    load_best_practices_xml,
    normalize_profile_name,
    normalize_permission_name,
    normalize_value,
    get_active_profiles_and_permissionsets
)

def is_profile_match(norm_profile_name, session_norm):
    admin_names = {'admin', 'system administrator'}
    standard_names = {'standard', 'standard platform user'}
    if norm_profile_name == session_norm:
        return True
    if norm_profile_name in admin_names and session_norm in admin_names:
        return True
    if norm_profile_name in standard_names and session_norm in standard_names:
        return True
    return False

def parse_best_practices_with_picklists(bp_path):
    tree = ET.parse(bp_path)
    root = tree.getroot()
    practices = []
    for usertype in root.findall('.//UserType'):
        for practice in usertype.findall('Practice'):
            bp = {child.tag: child.text for child in practice if child.tag != 'PicklistValues'}
            picklist_elem = practice.find('PicklistValues')
            if picklist_elem is not None:
                picklist_map = {}
                for val in picklist_elem.findall('Value'):
                    code = val.attrib.get('code')
                    label = val.text
                    if code and label:
                        picklist_map[code] = label
                bp['PicklistValues'] = picklist_map
            practices.append(bp)
    return practices

def process_session_timeout_task(processor, task_id, org_id, user_id, params, execution_log_id):
    logger = logging.getLogger(__name__)
    access_token = params.get("access_token")
    instance_url = params.get("instance_url")
    org_name = params.get("org_name")
    environment = params.get("environment", "production")
    blob_prefix = params.get("blob_prefix")
    if not (access_token and instance_url and blob_prefix):
        logger.error(f"[SessionTimeoutTask {task_id}] Missing required params (access_token, instance_url, blob_prefix)")
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message="Missing required params for Session Timeout task."
        )
        return
    # Load Session Timeout best practices XML
    bp_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..', 'best_practices', 'SessionTimeout-BestPractice.xml'))
    best_practices = parse_best_practices_with_picklists(bp_path)
    blob_repo = BlobStorageRepository(container_name="salesforce-metadata")
    repo = get_db_client()
    try:
        # Get active profiles using shared utility
        active_profile_names, _ = get_active_profiles_and_permissionsets(access_token, instance_url, environment)
        # 2. Traverse session setting files in blob storage
        session_folder = f"{blob_prefix}/profileSessionSettings/"
        session_blobs = blob_repo.list_blobs(name_starts_with=session_folder)
        # Build a map of normalized profile name (from <profile> tag) to session setting blob name
        session_blob_map = {}
        for blob_name in session_blobs:
            if not blob_name.endswith('.profileSessionSetting'):
                continue
            try:
                xml_bytes = blob_repo.get_blob_bytes(blob_name)
                root = ET.fromstring(xml_bytes)
                profile_tag = None
                for elem in root.iter():
                    if elem.tag.endswith('profile'):
                        profile_tag = elem
                        break
                if profile_tag is not None and profile_tag.text:
                    norm_profile = profile_tag.text.strip().lower()
                    session_blob_map[norm_profile] = blob_name
            except Exception as e:
                logger.error(f"[SessionTimeoutTask {task_id}] Error reading <profile> tag in {blob_name}: {e}")
        inserted_profiles = set()
        for norm_profile_name in set(active_profile_names):  # Deduplicate upfront
            if norm_profile_name in inserted_profiles:
                continue  # Skip duplicate
            matched_blob = None
            for session_norm, blob_name in session_blob_map.items():
                if is_profile_match(norm_profile_name, session_norm):
                    matched_blob = blob_name
                    break
            results_arr = []
            if not matched_blob:
                # No session setting file found for this profile
                for bp in best_practices:
                    results_arr.append({
                        'SalesforceSetting': bp.get('SalesforceSetting'),
                        'StandardValue': bp.get('StandardValue'),
                        'OrgValue': None,
                        'Description': bp.get('Description'),
                        'OWASP': bp.get('OWASP'),
                        'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                        'Issue': 'Session setting file missing for profile'
                    })
            else:
                # Parse the session setting XML and compare each setting
                try:
                    xml_bytes = blob_repo.get_blob_bytes(matched_blob)
                    xml_str = xml_bytes.decode('utf-8', errors='ignore')
                    root = ET.fromstring(xml_bytes)
                    for bp in best_practices:
                        bp_setting = (bp.get('SalesforceSetting') or '').strip()
                        bp_standard_value = (bp.get('StandardValue') or '').strip()
                        picklist_map = bp.get('PicklistValues', {})
                        found_elem = None
                        for elem in root.iter():
                            if elem.tag.endswith(bp_setting):
                                found_elem = elem
                                break
                        if found_elem is not None:
                            org_value = found_elem.text.strip() if found_elem.text else None
                            match = normalize_value(org_value) == normalize_value(bp_standard_value)
                            # If picklist, display only label for value
                            display_org_value = org_value
                            display_standard_value = bp_standard_value
                            if picklist_map:
                                display_org_value = picklist_map.get(org_value, org_value)
                                display_standard_value = picklist_map.get(bp_standard_value, bp_standard_value)
                            if not match:
                                results_arr.append({
                                    'SalesforceSetting': bp_setting,
                                    'StandardValue': display_standard_value,
                                    'OrgValue': display_org_value,
                                    'Description': bp.get('Description'),
                                    'OWASP': bp.get('OWASP'),
                                    'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                                    'Issue': 'Session Timeout best practice violation'
                                })
                        else:
                            # Setting missing in session file
                            display_standard_value = bp_standard_value
                            picklist_map = bp.get('PicklistValues', {})
                            if picklist_map and bp_standard_value in picklist_map:
                                display_standard_value = picklist_map[bp_standard_value]
                            results_arr.append({
                                'SalesforceSetting': bp_setting,
                                'StandardValue': display_standard_value,
                                'OrgValue': None,
                                'Description': bp.get('Description'),
                                'OWASP': bp.get('OWASP'),
                                'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                                'Issue': 'Session Timeout best practices are not configured for this profile'
                            })
                except Exception as e:
                    logger.error(f"[SessionTimeoutTask {task_id}] Error parsing session setting XML for {norm_profile_name}: {e}")
                    for bp in best_practices:
                        results_arr.append({
                            'SalesforceSetting': bp.get('SalesforceSetting'),
                            'StandardValue': bp.get('StandardValue'),
                            'OrgValue': None,
                            'Description': bp.get('Description'),
                            'OWASP': bp.get('OWASP'),
                            'RiskTypeBasedOnSeverity': bp.get('RiskTypeBasedOnSeverity'),
                            'Issue': 'Session setting file unreadable for profile'
                        })
            # Store results if any issues found - OLD ARCHITECTURE FORMAT
            if results_arr:
                # Remove duplicates
                unique_results = []
                seen = set()
                for entry in results_arr:
                    entry_json = json.dumps(entry, sort_keys=True)
                    if entry_json not in seen:
                        unique_results.append(entry)
                        seen.add(entry_json)
                
                # Store results using DB service client - OLD ARCHITECTURE FORMAT
                policy_data = {
                    'PartitionKey': str(org_id),
                    'RowKey': f"{norm_profile_name}-{execution_log_id}",
                    'OrgValue': json.dumps(unique_results),  # Store all results as JSON array
                    'IntegrationId': str(org_id),
                    'TaskStatusId': execution_log_id,
                    'CreatedAt': datetime.now().isoformat(),
                    'ProfileName': norm_profile_name,
                    'Type': 'SessionTimeoutProfilePermissions',
                }

                try:
                    repo.store_policies_result_data(str(org_id), execution_log_id, [policy_data])
                    inserted_profiles.add(norm_profile_name)
                    logger.info(f"[SessionTimeoutTask {task_id}] Stored results for profile {norm_profile_name}")
                except Exception as e:
                    logger.error(f"[SessionTimeoutTask {task_id}] Failed to store session timeout results for profile {norm_profile_name}: {e}")
        processor.update_task_status(
            task_id=task_id,
            status="completed",
            progress=100,
            message="Session Timeout check completed and results stored."
        )
    except Exception as e:
        logger.error(f"[SessionTimeoutTask {task_id}] Error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        processor.update_task_status(
            task_id=task_id,
            status="failed",
            progress=0,
            message=f"Error processing Session Timeout task: {e}"
        ) 