# Troubleshooting Guide

This guide provides solutions to common issues you might encounter when working with the AtomSec Azure Functions application.

## Local Development Issues

### Setup Script Issues

#### Setup Script Fails

**Symptoms:**
- `setup_local_dev.py` script exits with an error
- Error messages about missing prerequisites

**Solutions:**
1. Install the required prerequisites:
   - Node.js and npm: https://nodejs.org/
   - Azure Functions Core Tools: https://learn.microsoft.com/en-us/azure/azure-functions/functions-run-local

2. Run the script with administrator privileges:
   - Windows: Right-click on PowerShell/Command Prompt and select "Run as administrator"
   - Linux/macOS: Use `sudo python setup_local_dev.py`

3. Check for detailed error messages in the script output and follow the instructions

### Azurite Issues

#### Azurite Won't Start

**Symptoms:**
- Error message when running `azurite` command
- `setup_local_dev.py` script fails to start Azurite

**Solutions:**
1. Check if ports are in use:
   ```bash
   # Windows
   netstat -ano | findstr 10100
   netstat -ano | findstr 10101
   netstat -ano | findstr 10102

   # Linux/macOS
   lsof -i :10100
   lsof -i :10101
   lsof -i :10102
   ```

2. Kill any processes using those ports:
   ```bash
   # Windows (replace PID with the process ID from netstat)
   taskkill /F /PID <PID>

   # Linux/macOS (replace PID with the process ID from lsof)
   kill -9 <PID>
   ```

3. Try using different ports:
   ```bash
   azurite --location .azurite --silent --blobPort 10200 --queuePort 10201 --tablePort 10202
   ```

   Then update `local.settings.json` with the new connection string:
   ```json
   "AzureWebJobsStorage": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10200/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10201/devstoreaccount1;TableEndpoint=http://127.0.0.1:10202/devstoreaccount1;"
   ```

4. If you're getting permission errors when installing Azurite:
   - Windows: Run PowerShell as Administrator
   - Linux/macOS: Use `sudo npm install -g azurite`

#### Storage Connection Errors

**Symptoms:**
- Functions fail to start with storage connection errors
- Error messages about "The specified account name is invalid" or "No connection could be made"
- Error messages like "Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it"

**Solutions:**
1. Verify Azurite is running:
   ```bash
   # Windows
   tasklist | findstr azurite

   # Linux/macOS
   ps aux | grep azurite
   ```

2. Use the provided script to start Azurite:
   ```bash
   # PowerShell
   .\start-azurite.ps1
   ```

3. Check if the ports are available:
   ```bash
   # Windows
   Test-NetConnection -ComputerName localhost -Port 10100
   Test-NetConnection -ComputerName localhost -Port 10101
   Test-NetConnection -ComputerName localhost -Port 10102
   ```

4. Check if the `.azurite` directory exists and has proper permissions

5. Try clearing the Azurite data:
   ```bash
   rm -rf .azurite
   mkdir .azurite
   ```

6. Use the combined script to start both Azurite and the Functions host:
   ```bash
   # PowerShell
   .\start-local-dev.ps1
   ```

7. If you're still having issues, the code has been updated to handle the case when Azurite is not running. You can still run the Functions host and access endpoints that don't require storage.

#### Azure Data Tables SDK Version Issues

**Symptoms:**
- Error message: `TableClient.query_entities() missing 1 required positional argument: 'query_filter'`
- Functions that use Table Storage fail with TypeError

**Solutions:**
1. The code has been updated to handle different versions of the Azure Data Tables SDK. If you encounter this error, make sure you're using the latest version of the code.

2. If you need to fix this manually, update the `query_entities` method in `shared/data_access.py` to handle the required `query_filter` parameter:
   ```python
   def query_entities(self, filter_query: Optional[str] = None):
       try:
           # Handle different versions of the Azure Data Tables SDK
           try:
               if filter_query:
                   entities = self.table_client.query_entities(filter_query)
               else:
                   # Try with empty string as query_filter (newer SDK versions)
                   entities = self.table_client.query_entities(query_filter="")
           except TypeError:
               # For older SDK versions that don't require query_filter
               entities = self.table_client.query_entities()

           return list(entities)
       except Exception as e:
           logger.error(f"Error querying entities: {str(e)}")
           return []
   ```

3. You can also downgrade the Azure Data Tables SDK to a version that doesn't require the `query_filter` parameter:
   ```bash
   pip install azure-data-tables==12.4.0
   ```

### Function Runtime Issues

#### Functions Won't Start

**Symptoms:**
- Error when running `func start`
- Functions host crashes immediately
- Error message about "An item with the same key has already been added"

**Solutions:**
1. Check Python version (should be 3.9+):
   ```bash
   python --version
   ```

2. Verify all dependencies are installed:
   ```bash
   pip install -r requirements.txt
   ```

3. Check for syntax errors in your Python code

4. Try running with verbose logging:
   ```bash
   func start --verbose
   ```

5. If you see an error about duplicate keys (e.g., "An item with the same key has already been added. Key: AZURE_FUNCTIONS_ENVIRONMENT"):

   This is a known issue with Azure Functions Core Tools where it conflicts with environment variables defined in local.settings.json.

   **Solution 1**: Remove the environment variable from local.settings.json
   - Edit `local.settings.json` and remove the `AZURE_FUNCTIONS_ENVIRONMENT` entry
   - Run the setup script again: `python setup_local_dev.py`

   **Solution 2**: Use the provided scripts
   - Use the `start-func.bat` (Windows CMD) or `start-func.ps1` (PowerShell) scripts
   - These scripts clear the environment variable before starting the function host

   **Solution 3**: Use the alternative VS Code task
   - In VS Code, press Ctrl+Shift+P and select "Tasks: Run Task"
   - Choose "Start Function App (Alternative)"

   **Solution 4**: Clear the environment variable manually
   ```bash
   # PowerShell
   $env:AZURE_FUNCTIONS_ENVIRONMENT = ""
   func host start --no-build

   # CMD
   set "AZURE_FUNCTIONS_ENVIRONMENT="
   func host start --no-build
   ```

#### Import Errors

**Symptoms:**
- Error messages like "No module named 'xyz'"
- Functions fail to load

**Solutions:**
1. Ensure you're running from the project root directory

2. Check that your virtual environment is activated:
   ```bash
   # Windows
   echo %VIRTUAL_ENV%

   # Linux/macOS
   echo $VIRTUAL_ENV
   ```

3. Reinstall dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Check for circular imports in your code

### Authentication Issues

#### Azure Authentication Errors

**Symptoms:**
- Error messages about "Authentication failed"
- Functions that access Azure services fail

**Solutions:**
1. Log in with Azure CLI:
   ```bash
   az login
   ```

2. Verify your account has access to the required Azure resources:
   ```bash
   az account show
   ```

3. Set the correct subscription:
   ```bash
   az account set --subscription <subscription-id>
   ```

#### Salesforce Authentication Errors

**Symptoms:**
- Functions that access Salesforce fail
- Error messages about "Invalid client credentials" or "Authentication failed"

**Solutions:**
1. Check your `config.py` file has the correct credentials

2. Verify your Salesforce user has API access enabled

3. Try generating new client credentials in Salesforce

4. Check if your Salesforce password needs to be reset

5. For sandbox environments, use `https://test.salesforce.com/services/oauth2/token` as the auth URL

## Deployment Issues

### Pipeline Failures

#### Build Failures

**Symptoms:**
- Azure DevOps pipeline fails during the build stage
- Error messages about missing dependencies

**Solutions:**
1. Check the pipeline logs for specific error messages

2. Verify all dependencies are listed in `requirements.txt`

3. Check for Python version compatibility issues

4. Ensure the pipeline has access to all required resources

#### Deployment Failures

**Symptoms:**
- Pipeline fails during the deployment stage
- Function App doesn't update after successful pipeline run

**Solutions:**
1. Check if the Function App exists and is accessible

2. Verify the pipeline has proper permissions to deploy

3. Check for conflicts with existing resources

4. Try a manual deployment to isolate the issue:
   ```bash
   func azure functionapp publish <app-name>
   ```

### Runtime Errors in Azure

#### Functions Crash in Azure

**Symptoms:**
- Functions work locally but crash in Azure
- Error messages in Application Insights logs

**Solutions:**
1. Check Application Insights logs for detailed error messages

2. Verify all environment variables are set correctly in the Azure portal

3. Check for differences between local and Azure environments

4. Try deploying a simplified version to isolate the issue

#### Performance Issues

**Symptoms:**
- Functions are slow in Azure
- Timeouts or cold start issues

**Solutions:**
1. Check the Function App plan (Consumption vs Premium)

2. Consider using the Premium plan for better performance

3. Optimize your code for faster execution

4. Use async/await for I/O-bound operations

5. Configure proper timeouts in `host.json`

## Data Access Issues

### Blob Storage Issues

**Symptoms:**
- Can't read or write blobs
- Error messages about containers not found

**Solutions:**
1. Check if the container exists:
   ```python
   from shared.data_access import BlobStorageRepository

   repo = BlobStorageRepository(container_name="your-container")
   print(repo.container_client.exists())
   ```

2. Verify permissions on the storage account

3. Check for naming convention issues (container names must be lowercase)

### Table Storage Issues

**Symptoms:**
- Can't query or insert entities
- Error messages about tables not found

**Solutions:**
1. Check if the table exists:
   ```python
   from shared.data_access import TableStorageRepository

   repo = TableStorageRepository(table_name="YourTable")
   # The table should be created automatically if it doesn't exist
   ```

2. Verify entity structure (must have PartitionKey and RowKey)

3. Check for invalid characters in property names

## Logging and Monitoring

### Viewing Logs

#### Local Logs

Logs are displayed in the terminal where you started the Functions host. You can also find them in:

- VS Code: Output panel (select "Azure Functions" from the dropdown)
- Command line: Terminal where you ran `func start`

#### Azure Logs

1. **Azure Portal**: Go to your Function App > Functions > [Function Name] > Monitor
2. **Application Insights**: Go to your Application Insights resource > Logs
3. **Log Analytics**: Query logs using Kusto Query Language (KQL)

### Common Log Queries

#### Find Errors in Application Insights

```kusto
exceptions
| where timestamp > ago(1h)
| order by timestamp desc
```

#### Check Function Execution Times

```kusto
requests
| where name startswith "Function:"
| summarize avg(duration), max(duration) by name
| order by avg_duration desc
```

## Advanced Troubleshooting

### Debugging with VS Code

1. Set breakpoints in your code
2. Press F5 to start debugging
3. Use the Debug Console to evaluate expressions
4. Step through code execution with F10 (step over) and F11 (step into)

### Profiling Performance Issues

1. Use the `cProfile` module to profile your code:
   ```python
   import cProfile

   def my_function():
       # Your code here

   cProfile.run('my_function()')
   ```

2. Use Application Insights Performance testing

### Memory Issues

1. Monitor memory usage in Azure Portal
2. Check for memory leaks (objects not being garbage collected)
3. Use the `memory_profiler` package to identify memory-intensive code

## Getting Help

If you've tried the solutions in this guide and still have issues:

1. Check the [Azure Functions documentation](https://learn.microsoft.com/en-us/azure/azure-functions/)
2. Search for similar issues on Stack Overflow
3. Contact the development team for assistance
4. Open an issue in the repository with detailed information about the problem
