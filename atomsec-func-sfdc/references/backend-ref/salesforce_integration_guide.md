# Salesforce Integration Guide

This guide explains how to integrate with Salesforce using JWT authentication and securely store credentials in Azure Key Vault.

## Overview

The application provides endpoints for testing Salesforce connections and storing credentials securely in Azure Key Vault. This ensures that sensitive credentials are never stored in the database and can only be accessed by authenticated users.

## Prerequisites

1. Salesforce Connected App with OAuth enabled
2. Client ID (Consumer Key) and Client Secret (Consumer Secret) from the Connected App
3. Azure Key Vault for storing credentials
4. Valid authentication token for the application

## Integration Process

### 1. Create a Connected App in Salesforce

1. Log in to Salesforce
2. Go to Setup > App Manager > New Connected App
3. Fill in the required fields:
   - Connected App Name
   - API Name
   - Contact Email
4. Enable OAuth Settings:
   - Enable OAuth Settings: Checked
   - Callback URL: https://your-app-url/callback (can be a placeholder)
   - Selected OAuth Scopes:
     - Access and manage your data (api)
     - Perform requests on your behalf at any time (refresh_token, offline_access)
5. Save and wait for the app to be created
6. Note the Consumer Key (Client ID) and Consumer Secret (Client Secret)

### 2. Test the Connection

Use the test connection endpoint to verify that the credentials work:

```http
POST /api/integration/test-connection
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "tenantUrl": "https://your-instance.my.salesforce.com",
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "type": "Salesforce",
  "environment": "production"
}
```

### 3. Connect the Integration

If the test is successful, use the connect endpoint to store the credentials and create the integration:

```http
POST /api/integration/connect
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "orgName": "Your Organization Name",
  "tenantUrl": "https://your-instance.my.salesforce.com",
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "description": "Optional description",
  "type": "Salesforce",
  "environment": "production"
}
```

## Authentication Flow

The application uses the OAuth 2.0 Client Credentials flow to authenticate with Salesforce:

1. The application sends a request to Salesforce with the Client ID and Client Secret
2. Salesforce validates the credentials and returns an access token
3. The application uses the access token to make API calls to Salesforce

## Security Considerations

1. **Credential Storage**: Client ID and Client Secret are stored in Azure Key Vault, not in the database
2. **Authentication**: All endpoints require authentication with a valid JWT token
3. **Access Control**: Only authenticated users can test connections and store credentials
4. **Audit Logging**: All operations are logged for security auditing

## Using the API in Code

### Testing a Connection

```javascript
import { testIntegrationConnection } from './api';

async function testConnection() {
  try {
    const result = await testIntegrationConnection(
      'https://your-instance.my.salesforce.com',
      'your-client-id',
      'your-client-secret',
      'Salesforce',
      'production'
    );
    
    if (result.data.success) {
      console.log('Connection successful!');
      console.log('Instance URL:', result.data.details.instance_url);
    } else {
      console.error('Connection failed:', result.data.error);
    }
  } catch (error) {
    console.error('Error testing connection:', error);
  }
}
```

### Connecting an Integration

```javascript
import { connectIntegration } from './api';

async function connectSalesforce() {
  try {
    const result = await connectIntegration(
      'Your Organization Name',
      'https://your-instance.my.salesforce.com',
      'your-client-id',
      'your-client-secret',
      'Optional description',
      'Salesforce',
      'production'
    );
    
    if (result.data.success) {
      console.log('Integration connected successfully!');
      console.log('Integration ID:', result.data.id);
    } else {
      console.error('Failed to connect integration:', result.data.error);
    }
  } catch (error) {
    console.error('Error connecting integration:', error);
  }
}
```

## Troubleshooting

### Connection Test Fails

1. Verify that the Client ID and Client Secret are correct
2. Ensure that the Connected App in Salesforce is properly configured
3. Check that the tenant URL is correct and accessible
4. Verify that the user has the necessary permissions in Salesforce

### Credential Storage Fails

1. Ensure that Azure Key Vault is properly configured
2. Verify that the application has the necessary permissions to access Key Vault
3. Check the application logs for specific error messages

## References

- [Salesforce OAuth 2.0 Documentation](https://help.salesforce.com/s/articleView?id=sf.remoteaccess_oauth_flows.htm&type=5)
- [Salesforce Connected App Documentation](https://help.salesforce.com/s/articleView?id=sf.connected_app_overview.htm&type=5)
- [Azure Key Vault Documentation](https://docs.microsoft.com/en-us/azure/key-vault/)
