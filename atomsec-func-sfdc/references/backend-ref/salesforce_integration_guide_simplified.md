# Salesforce Integration Guide (Simplified)

This guide explains how to integrate with Salesforce using the simplified authentication approach.

## Overview

The application provides endpoints for testing Salesforce connections and storing credentials securely in Azure Key Vault. This ensures that sensitive credentials are never stored in the database and can only be accessed by authenticated users.

## Prerequisites

1. Salesforce Connected App with OAuth enabled
2. Client ID (Consumer Key) and Client Secret (Consumer Secret) from the Connected App
3. Azure Key Vault for storing credentials
4. Valid authentication token for the application

## Integration Process

### 1. Create a Connected App in Salesforce

1. Log in to Salesforce as an administrator
2. Go to Setup > App Manager > New Connected App
3. Fill in the required fields:
   - Connected App Name: AtomSec Integration
   - API Name: AtomSec_Integration
   - Contact Email: your email address
4. Enable OAuth Settings:
   - Enable OAuth Settings: Checked
   - Callback URL: https://your-app-url/callback (can be a placeholder)
   - Selected OAuth Scopes:
     - Access and manage your data (api)
     - Perform requests on your behalf at any time (refresh_token, offline_access)
5. Save the Connected App
6. Note the Consumer Key (Client ID) and Consumer Secret (Client Secret)

### 2. Test the Connection

Use the test connection endpoint to verify that the credentials work:

```http
POST /api/integration/test-connection
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "tenantUrl": "https://your-instance.my.salesforce.com",
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "type": "Salesforce",
  "environment": "production"
}
```

### 3. Connect the Integration

If the test is successful, use the connect endpoint to store the credentials and create the integration:

```http
POST /api/integration/connect
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "orgName": "Your Organization Name",
  "tenantUrl": "https://your-instance.my.salesforce.com",
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "description": "Optional description",
  "type": "Salesforce",
  "environment": "production"
}
```

## Authentication Flow

The application uses a simplified approach to test Salesforce connections:

1. The application validates the Client ID and Client Secret by attempting to get a token
2. If the credentials are valid, the application stores them in Azure Key Vault
3. The application creates a record in the database with the integration details (but not the credentials)
4. When the application needs to access Salesforce, it retrieves the credentials from Azure Key Vault

## Security Considerations

1. **Credential Storage**: Client ID and Client Secret are stored in Azure Key Vault, not in the database
2. **Authentication**: All endpoints require authentication with a valid JWT token
3. **Access Control**: Only authenticated users can test connections and store credentials
4. **Audit Logging**: All operations are logged for security auditing

## Using the API in Code

### Testing a Connection

```javascript
import { testIntegrationConnection } from './api';

async function testConnection() {
  try {
    const response = await testIntegrationConnection(
      'https://your-instance.my.salesforce.com',
      'your-client-id',
      'your-client-secret',
      'Salesforce',
      'production'
    );
    
    if (response.data.success) {
      console.log('Connection successful!');
    } else {
      console.error('Connection failed:', response.data.error);
    }
  } catch (error) {
    console.error('Error testing connection:', error);
  }
}
```

### Connecting an Integration

```javascript
import { connectIntegration } from './api';

async function connectSalesforce() {
  try {
    const result = await connectIntegration(
      'Your Organization Name',
      'https://your-instance.my.salesforce.com',
      'your-client-id',
      'your-client-secret',
      'Optional description',
      'Salesforce',
      'production'
    );
    
    if (result.data.success) {
      console.log('Integration connected successfully!');
      console.log('Integration ID:', result.data.id);
    } else {
      console.error('Failed to connect integration:', result.data.error);
    }
  } catch (error) {
    console.error('Error connecting integration:', error);
  }
}
```

## Troubleshooting

### Common Errors

1. **Invalid Client ID or Client Secret**: Make sure the Client ID and Client Secret are correct
2. **Invalid Tenant URL**: Make sure the Tenant URL is in the correct format (e.g., https://mycompany.my.salesforce.com)
3. **Connected App Not Configured Correctly**: Make sure the Connected App has the necessary OAuth scopes

### Debugging Tips

1. Check the application logs for detailed error messages
2. Verify that the Connected App is properly configured in Salesforce
3. Make sure the Client ID and Client Secret are correct
4. Check that the Tenant URL is correct and accessible

## References

- [Salesforce OAuth 2.0 Documentation](https://help.salesforce.com/s/articleView?id=sf.remoteaccess_oauth_flows.htm&type=5)
- [Salesforce Connected App Documentation](https://help.salesforce.com/s/articleView?id=sf.connected_app_overview.htm&type=5)
- [Azure Key Vault Documentation](https://docs.microsoft.com/en-us/azure/key-vault/)
