# Salesforce JWT Authentication Guide

This guide explains how to set up JWT authentication for server-to-server connections with Salesforce.

## Overview

JWT (JSON Web Token) Bearer flow is the recommended authentication method for server-to-server integrations with Salesforce. It provides a secure way to authenticate without storing user credentials or requiring user interaction.

## Prerequisites

1. Salesforce account with administrative access
2. Connected App in Salesforce
3. Digital certificate and private key
4. Salesforce username

## Step 1: Generate a Certificate and Private Key

You can use the provided script to generate a self-signed certificate and private key:

```bash
python scripts/generate_certificate.py
```

This will create the following files in the `certs` directory:
- `server.crt`: The certificate file to upload to Salesforce
- `server.key`: The private key file to use for JWT signing
- `server.pfx`: A PKCS12 file for import into browsers or keystores

## Step 2: Configure a Connected App in Salesforce

1. Log in to Salesforce as an administrator
2. Go to Setup > App Manager > New Connected App
3. Fill in the required fields:
   - Connected App Name: AtomSec Integration
   - API Name: AtomSec_Integration
   - Contact Email: your email address
4. Enable OAuth Settings:
   - Enable OAuth Settings: Checked
   - Callback URL: https://your-app-url/callback (can be a placeholder)
   - Selected OAuth Scopes:
     - Access and manage your data (api)
     - Perform requests on your behalf at any time (refresh_token, offline_access)
5. Enable for Device Flow: Checked
6. Configure digital signatures:
   - Use digital signatures: Checked
   - Upload the `server.crt` file
7. Save the Connected App
8. Note the Consumer Key (Client ID) and Consumer Secret (Client Secret)

## Step 3: Store the Private Key in Azure Key Vault

You can use the provided script to store the private key in Azure Key Vault:

```bash
python scripts/store_jwt_key.py certs/server.key <EMAIL>
```

This will store the private key and username in Azure Key Vault for use with the JWT authentication flow.

## Step 4: Test the Connection

Use the test connection endpoint to verify that the JWT authentication works:

```http
POST /api/integration/test-connection
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "tenantUrl": "https://your-instance.my.salesforce.com",
  "clientId": "your-client-id",
  "useJwt": true,
  "username": "<EMAIL>",
  "privateKey": "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
}
```

## Step 5: Connect the Integration

If the test is successful, use the connect endpoint to store the credentials and create the integration:

```http
POST /api/integration/connect
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "orgName": "Your Organization Name",
  "tenantUrl": "https://your-instance.my.salesforce.com",
  "clientId": "your-client-id",
  "useJwt": true,
  "username": "<EMAIL>",
  "privateKey": "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----",
  "description": "Optional description",
  "type": "Salesforce",
  "environment": "production"
}
```

## JWT Authentication Flow

The JWT Bearer flow involves the following steps:

1. Create a JWT payload with the following claims:
   - `iss`: The Connected App Consumer Key (Client ID)
   - `sub`: The Salesforce username
   - `aud`: The Salesforce authentication URL (https://login.salesforce.com or https://test.salesforce.com)
   - `exp`: The expiration time (usually a few minutes in the future)

2. Sign the JWT with the private key using the RS256 algorithm

3. Exchange the JWT for an access token by sending a POST request to the Salesforce token endpoint:
   ```
   POST /services/oauth2/token
   Content-Type: application/x-www-form-urlencoded
   
   grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer&assertion=your-signed-jwt
   ```

4. Use the access token to make API calls to Salesforce

## Security Considerations

1. **Private Key Security**: Store the private key securely in Azure Key Vault
2. **Certificate Rotation**: Regularly rotate the certificate and private key
3. **Access Control**: Limit access to the Connected App in Salesforce
4. **IP Restrictions**: Consider restricting the Connected App to specific IP addresses
5. **Audit Logging**: Enable audit logging for the Connected App

## Troubleshooting

### Common Errors

1. **Invalid JWT**: Check that the JWT is properly signed and formatted
2. **Invalid Audience**: Make sure the `aud` claim matches the Salesforce authentication URL
3. **Invalid Issuer**: Make sure the `iss` claim matches the Connected App Consumer Key
4. **Invalid Subject**: Make sure the `sub` claim matches a valid Salesforce username
5. **Expired JWT**: Make sure the `exp` claim is in the future

### Debugging Tips

1. Check the Salesforce login history for failed login attempts
2. Enable debug logging in the application
3. Verify that the certificate is properly uploaded to the Connected App
4. Verify that the private key matches the certificate
5. Check that the Connected App has the necessary OAuth scopes

## References

- [Salesforce JWT Bearer Flow Documentation](https://help.salesforce.com/s/articleView?id=sf.remoteaccess_oauth_jwt_flow.htm&type=5)
- [Salesforce Connected App Documentation](https://help.salesforce.com/s/articleView?id=sf.connected_app_overview.htm&type=5)
- [JWT.io](https://jwt.io/) - Useful for debugging JWT tokens
