# Azure Key Vault Integration Guide

This guide explains how to use the Azure Key Vault integration to securely store and retrieve client credentials and other secrets.

## Overview

The application provides a comprehensive set of tools for managing Azure Key Vault resources and secrets. This includes creating Key Vaults, managing access policies, and storing/retrieving secrets using the `azure-mgmt-keyvault` and `azure-keyvault-secrets` packages.

## Prerequisites

1. Azure subscription with access to Azure Key Vault
2. Proper permissions to access the Key Vault (Contributor or Key Vault Administrator role)
3. Azure CLI installed and configured (`az login`)
4. Valid authentication token (user must be logged in)

## Available Endpoints

### Web Interfaces

- `/key-vault-management`: A comprehensive web interface for managing Azure Key Vault resources and secrets
- `/key-vault-form`: A simpler form focused on storing client credentials in Azure Key Vault

### Key Vault Management API Endpoints

- `POST /api/key-vault/create`: Create a new Key Vault
- `POST /api/key-vault/access-policy`: Add an access policy to a Key Vault

### Secret Management API Endpoints

- `POST /api/key-vault/secrets`: Store a generic secret in Azure Key Vault
- `POST /api/key-vault/client-credentials`: Store client ID and client secret in Azure Key Vault

## Using the Web Interfaces

### Key Vault Management Interface

1. Navigate to `/key-vault-management` in your browser
2. The interface provides four main tabs:
   - **Create Key Vault**: Create a new Azure Key Vault
   - **Manage Access Policies**: Add access policies to an existing Key Vault
   - **Store Client Credentials**: Store client ID and client secret in Azure Key Vault
   - **Store Generic Secret**: Store any generic secret in Azure Key Vault

### Simple Form

1. Navigate to `/key-vault-form` in your browser
2. Fill in the form with the following information:
   - Service Name (optional): A prefix for the secret names (e.g., `azure-ad`, `salesforce`)
   - Client ID: The client ID to store
   - Client Secret: The client secret to store
3. Click "Store Credentials" to save the credentials in Azure Key Vault

## Using the API Endpoints

### Create a Key Vault

```http
POST /api/key-vault/create
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "vault_name": "my-key-vault",
  "resource_group": "my-resource-group",
  "location": "eastus",
  "add_current_user": true
}
```

### Add an Access Policy

```http
POST /api/key-vault/access-policy
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "vault_name": "my-key-vault",
  "resource_group": "my-resource-group",
  "object_id": "00000000-0000-0000-0000-000000000000",
  "tenant_id": "00000000-0000-0000-0000-000000000000" (optional)
}
```

### Store a Generic Secret

```http
POST /api/key-vault/secrets
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "secret_name": "my-secret-name",
  "secret_value": "my-secret-value",
  "content_type": "text/plain", (optional)
  "vault_url": "https://my-key-vault.vault.azure.net/" (optional)
}
```

### Store Client Credentials

```http
POST /api/key-vault/client-credentials
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "service_name": "azure-ad", (optional, defaults to "default")
  "client_id": "your-client-id",
  "client_secret": "your-client-secret",
  "vault_url": "https://my-key-vault.vault.azure.net/" (optional)
}
```

The `service_name` parameter is used as a prefix for the secret names in Azure Key Vault.

## Secret Naming Convention

When storing client credentials, the following naming convention is used:

- Client ID: `{service_name}-client-id`
- Client Secret: `{service_name}-client-secret`

For example, if you specify `service_name` as `azure-ad`, the secrets will be stored as:

- `azure-ad-client-id`
- `azure-ad-client-secret`

## Using Azure Key Vault in Code

The `shared.azure_services` module provides functions for working with Azure Key Vault:

### Key Vault Management

```python
from shared.azure_services import create_key_vault_if_not_exists, add_access_policy, get_current_object_id

# Create a Key Vault if it doesn't exist
vault_url = create_key_vault_if_not_exists(
    vault_name="my-key-vault",
    resource_group="my-resource-group",
    location="eastus"
)

# Get current user's object ID
object_id = get_current_object_id()

# Add an access policy
success = add_access_policy(
    vault_name="my-key-vault",
    resource_group="my-resource-group",
    object_id=object_id
)
```

### Retrieving Secrets

To retrieve secrets in your code, use the `get_secret` function:

```python
from shared.azure_services import get_secret

# Get client ID
client_id = get_secret("azure-ad-client-id")

# Get client secret
client_secret = get_secret("azure-ad-client-secret")
```

### Setting Secrets

To set secrets in your code, use the `set_secret` function with enhanced options:

```python
from shared.azure_services import set_secret

# Set a secret with content type and tags
success = set_secret(
    secret_name="my-secret-name",
    secret_value="my-secret-value",
    vault_url=None,  # Optional
    content_type="text/plain",  # Optional
    tags={  # Optional
        "purpose": "testing",
        "environment": "development"
    }
)
```

### Storing Client Credentials

To store client credentials in your code, use the `store_client_credentials` function:

```python
from shared.azure_services import store_client_credentials

# Store client credentials
result = store_client_credentials(
    client_id="your-client-id",
    client_secret="your-client-secret",
    service_name="azure-ad",  # Optional, defaults to "default"
    vault_url=None  # Optional
)

if result.get('success', False):
    print("Client credentials stored successfully")
    print(f"Client ID secret name: {result.get('client_id_secret_name')}")
    print(f"Client Secret secret name: {result.get('client_secret_secret_name')}")
else:
    print(f"Failed to store client credentials: {result.get('message')}")
```

## Testing the Functionality

You can test the Azure Key Vault functionality using the provided test scripts:

### Basic Key Vault Operations

```bash
python test_key_vault.py
```

This script demonstrates how to set and get secrets using the Azure Key Vault integration.

### Key Vault Management Operations

```bash
python test_keyvault_mgmt.py
```

This script demonstrates how to create Key Vaults, manage access policies, and work with secrets using the enhanced functionality.

## Authentication

All Key Vault operations require authentication. The endpoints are protected and will only work for authenticated users with a valid JWT token.

### Web Form Authentication

The web form automatically checks if you are logged in. If not, it will display a message with a link to log in.

### API Authentication

When making API calls, you must include a valid JWT token in the Authorization header:

```http
POST /api/key-vault/client-credentials
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "service_name": "azure-ad",
  "client_id": "your-client-id",
  "client_secret": "your-client-secret"
}
```

You can obtain a JWT token by logging in through the application's authentication endpoints.

## Troubleshooting

### Authentication Issues

If you encounter authentication issues, make sure you are logged in to Azure CLI:

```bash
az login
```

For API authentication issues, ensure that:

1. You are logged in to the application
2. Your JWT token is valid and not expired
3. You are including the token in the Authorization header

### Permission Issues

If you encounter permission issues, make sure you have the necessary permissions to access the Key Vault:

1. Go to the Azure Portal
2. Navigate to your Key Vault
3. Go to "Access policies"
4. Add your user account with the necessary permissions (Get, List, Set, Delete)

### Local Development

For local development, make sure the following environment variables are set in your `local.settings.json` file:

```json
{
  "Values": {
    "USE_LOCAL_STORAGE": "true",
    "KEY_VAULT_URL": "https://akv-atomsec-dev.vault.azure.net/",
    "AZURE_SUBSCRIPTION_ID": "your-subscription-id",
    "AZURE_TENANT_ID": "your-tenant-id",
    "AZURE_RESOURCE_GROUP": "your-resource-group"
  }
}
```

## Security Considerations

1. **Access Control**: Always use the principle of least privilege when granting access to Key Vault.
2. **Secrets Rotation**: Regularly rotate client secrets and other credentials.
3. **Audit Logging**: Enable audit logging for Key Vault operations.
4. **Network Security**: Consider using Private Link or service endpoints to restrict network access to Key Vault.
5. **Purge Protection**: Enable purge protection to prevent accidental deletion of secrets.
