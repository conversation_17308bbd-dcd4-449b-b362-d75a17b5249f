# Azure Function Authentication with MSAL.js

This document provides instructions for setting up authentication between your React frontend (using MSAL.js) and your Azure Function backend.

## Overview

The authentication flow works as follows:

1. User logs in to the React app using MSAL.js (Azure AD authentication)
2. MSAL.js acquires an access token for the user
3. The React app includes this token in the Authorization header when calling the Azure Function API
4. The Azure Function validates the token and processes the request

## Frontend Setup (Already Implemented)

The frontend is already set up to:

1. Authenticate users with Azure AD using MSAL.js
2. Acquire tokens with the necessary scopes
3. Include the token in API requests to the Azure Function

## Azure Function Backend Setup

To validate tokens in your Azure Function backend, follow these steps:

### 1. Install Required Packages

Add the following packages to your Azure Function project:

```bash
npm install jsonwebtoken jwks-rsa
```

### 2. Create a Token Validation Middleware

Create a file named `validateToken.js` in your Azure Function project:

```javascript
const jwt = require('jsonwebtoken');
const jwksClient = require('jwks-rsa');

// Configure the JWKS client
const client = jwksClient({
  jwksUri: `https://login.microsoftonline.com/common/discovery/v2.0/keys`
  // If you're using a specific tenant, replace 'common' with your tenant ID
  // jwksUri: `https://login.microsoftonline.com/{your-tenant-id}/discovery/v2.0/keys`
});

// Function to get the signing key
function getSigningKey(kid) {
  return new Promise((resolve, reject) => {
    client.getSigningKey(kid, (err, key) => {
      if (err) {
        reject(err);
        return;
      }
      
      const signingKey = key.getPublicKey() || key.rsaPublicKey;
      resolve(signingKey);
    });
  });
}

// Middleware to validate the token
async function validateToken(req, context) {
  // Get the authorization header
  const authHeader = req.headers.authorization;
  
  if (!authHeader) {
    return { isValid: false, error: 'No authorization header' };
  }
  
  // Extract the token
  const token = authHeader.replace('Bearer ', '');
  
  try {
    // Decode the token to get the header (without verification)
    const decodedToken = jwt.decode(token, { complete: true });
    
    if (!decodedToken) {
      return { isValid: false, error: 'Invalid token format' };
    }
    
    // Get the signing key
    const kid = decodedToken.header.kid;
    const signingKey = await getSigningKey(kid);
    
    // Verify the token
    const verifiedToken = jwt.verify(token, signingKey, {
      algorithms: ['RS256'],
      audience: process.env.AZURE_AD_CLIENT_ID, // Your Azure AD application client ID
      // issuer: `https://login.microsoftonline.com/{your-tenant-id}/v2.0` // Optional: Verify the issuer
    });
    
    // Token is valid, return the decoded token
    return { isValid: true, user: verifiedToken };
  } catch (error) {
    context.log.error('Token validation error:', error);
    return { isValid: false, error: error.message };
  }
}

module.exports = validateToken;
```

### 3. Use the Middleware in Your Azure Function

Here's an example of how to use the token validation middleware in an Azure Function:

```javascript
const validateToken = require('../validateToken');

module.exports = async function (context, req) {
  // Validate the token
  const authResult = await validateToken(req, context);
  
  if (!authResult.isValid) {
    context.res = {
      status: 401,
      body: { error: 'Unauthorized', message: authResult.error }
    };
    return;
  }
  
  // Token is valid, process the request
  const user = authResult.user;
  
  // Your function logic here
  context.res = {
    status: 200,
    body: {
      message: 'Hello from Azure Function!',
      user: {
        name: user.name,
        email: user.preferred_username || user.email,
        // Don't include sensitive information
      }
    }
  };
};
```

### 4. Configure CORS in Your Azure Function

Make sure CORS is properly configured in your Azure Function to allow requests from your frontend:

In your `host.json` file:

```json
{
  "version": "2.0",
  "extensions": {
    "http": {
      "routePrefix": "api",
      "cors": {
        "allowedOrigins": [
          "http://localhost:3000",
          "https://your-production-domain.com"
        ],
        "allowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allowedHeaders": ["Content-Type", "Authorization", "X-Requested-With"],
        "allowCredentials": true
      }
    }
  }
}
```

### 5. Set Environment Variables

Set the following environment variables in your Azure Function app settings:

- `AZURE_AD_CLIENT_ID`: Your Azure AD application client ID

## Testing the Authentication

1. Start your React frontend (`npm start`)
2. Start your Azure Function backend
3. Log in to the React app
4. Use the AzureFunctionDemo component to test calling your protected API

## Troubleshooting

### Token Validation Issues

If you're having issues with token validation:

1. Check that the client ID in your Azure Function matches the client ID in your React app
2. Verify that the token has the correct audience (should match your client ID)
3. Check the token expiration (tokens typically expire after 1 hour)
4. Ensure your Azure Function has the correct CORS configuration

### CORS Issues

If you're seeing CORS errors:

1. Verify that your Azure Function's CORS configuration includes your frontend's origin
2. Check that the allowed headers include 'Authorization'
3. Ensure allowCredentials is set to true if you're using cookies

## Resources

- [Microsoft Identity Platform Documentation](https://docs.microsoft.com/en-us/azure/active-directory/develop/)
- [Secure an Azure Function with Azure AD](https://docs.microsoft.com/en-us/azure/app-service/configure-authentication-provider-aad)
- [MSAL.js Documentation](https://github.com/AzureAD/microsoft-authentication-library-for-js/tree/dev/lib/msal-browser)
