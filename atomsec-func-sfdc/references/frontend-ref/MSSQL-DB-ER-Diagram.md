erDiagram
      
"dbo.SecurityHealthChecks" {
    int ID "PK"
          varchar(255) RiskType ""
          varchar(255) Setting ""
          varchar(255) SettingGroup ""
          varchar(255) OrgValue ""
          varchar(255) StandardValue ""
          int ExecutionLogId "FK"
          
}
"dbo.ProjectDetails" {
    int ProjectID "PK"
          nvarchar(255) ProjectName ""
          nvarchar(255) KeyVaultID ""
          
}
"dbo.ProfilePermissions" {
    int PermissionID "PK"
          nvarchar(255) SalesforceID ""
          nvarchar(255) ProfileName ""
          nvarchar(255) ObjectName ""
          tinyint Read ""
          tinyint Create ""
          tinyint Edit ""
          tinyint Delete ""
          tinyint ViewAllRecords ""
          tinyint ModifyAllRecords ""
          nvarchar(255) SecurityRisk ""
          datetime LastUpdated ""
          
}
"dbo.SecuritySettings" {
    nvarchar(255) SettingName ""
          nvarchar(255) OrgValue ""
          nvarchar(255) StandardValue ""
          nvarchar(1000) SecurityRisk ""
          
}
"dbo.ProfileMetadataDeprecate" {
    nvarchar(255) ProfileName ""
          nvarchar(255) ObjectName ""
          bit PermissionsRead ""
          bit PermissionsCreate ""
          bit PermissionsEdit ""
          bit PermissionsDelete ""
          bit ViewAllRecords ""
          bit ModifyAllRecords ""
          
}
"dbo.ProfileMetadata" {
    varchar(255) ProfileName ""
          varchar(255) ObjectName ""
          bit PermissionsRead ""
          bit PermissionsCreate ""
          bit PermissionsEdit ""
          bit PermissionsDelete ""
          bit ViewAllRecords ""
          bit ModifyAllRecords ""
          varchar(255) ParentName ""
          
}
"dbo.App_Account" {
    int ID "PK"
          nvarchar(100) Name ""
          
}
"dbo.App_User" {
    int UserId "PK"
          nvarchar(100) Name ""
          nvarchar(100) Email ""
          nvarchar(20) Phone ""
          int AccountId "FK"
          
}
"dbo.App_Organization" {
    int Id "PK"
          nvarchar(50) CloudProvider ""
          nvarchar(50) Type ""
          nvarchar(100) Name ""
          nvarchar(255) Description ""
          int AccountId "FK"
          
}
"dbo.App_OrgAccess" {
    int Id "PK"
          int OrgId "FK"
          int UserId "FK"
          nvarchar(50) Role ""
          
}
"dbo.App_PolicyCategory" {
    int Id "PK"
          nvarchar(100) Name ""
          nvarchar(-1) Query ""
          
}
"dbo.App_Policies" {
    int Id "PK"
          nvarchar(100) Name ""
          nvarchar(100) RiskType ""
          nvarchar(255) Setting ""
          nvarchar(255) StandardValue ""
          int Category "FK"
          nvarchar(100) OWASPCategory ""
          
}
"dbo.Role" {
    int RoleId "PK"
          varchar(50) Rolename ""
          varchar(-1) Description ""
          datetime CreatedDate ""
          
}
"dbo.App_ExecutionLog" {
    int Id "PK"
          nvarchar(100) Timestamp ""
          int OrgId "FK"
          
}
"dbo.App_OrgPolicies" {
    int Id "PK"
          int OrgId "FK"
          int PolicyId "FK"
          int ExecutionLogId "FK"
          nvarchar(255) OrgValue ""
          
}
"dbo.User_Account" {
    int UserId "PK"
          varchar(50) FirstName ""
          varchar(50) MiddleName ""
          varchar(50) LastName ""
          date DoB ""
          varchar(50) Email ""
          numeric Contact ""
          varchar(50) State ""
          varchar(50) Country ""
          
}
"dbo.App_PoliciesResult" {
    int Id "PK"
          int OrgPolicyId "FK"
          int PolicyId "FK"
          int ExecutionLogId "FK"
          nvarchar(255) OrgValue ""
          
}
"dbo.App_User_Roles" {
    int UserId "FK"
          int RoleId "FK"
          datetime CreatedDate ""
          
}
"dbo.Permission" {
    int PermissionId "PK"
          varchar(50) Permission-Label ""
          varchar(-1) Action ""
          varchar(-1) Description ""
          date CreatedDate ""
          
}
"dbo.App_Credentials" {
    int Id "PK"
          int OrgId "FK"
          int KeyVaultId ""
          
}
"dbo.Role_Permissions" {
    int RoleId "FK"
          int PermissionId "FK"
          date CreatedDate ""
          
}
"dbo.App_User_Login" {
    int UserId "FK, PK"
          varchar(50) Username ""
          nvarchar(50) PasswordHash ""
          nvarchar(50) PasswordSalt ""
          int HashAlgorithmId "FK"
          
}
"dbo.HashingAlgorithm" {
    int HashAlgortihmId "PK"
          varchar(50) AlgorithmName ""
          
}
      "dbo.SecurityHealthChecks" |o--|{ "dbo.App_ExecutionLog": "Id"
"dbo.App_User" |o--|{ "dbo.App_Account": "ID"
"dbo.App_Organization" |o--|{ "dbo.App_Account": "ID"
"dbo.App_OrgAccess" |o--|{ "dbo.App_Organization": "Id"
"dbo.App_OrgAccess" |o--|{ "dbo.App_User": "UserId"
"dbo.App_Policies" |o--|{ "dbo.App_PolicyCategory": "Id"
"dbo.App_ExecutionLog" |o--|{ "dbo.App_Organization": "Id"
"dbo.App_OrgPolicies" |o--|{ "dbo.App_Organization": "Id"
"dbo.App_OrgPolicies" |o--|{ "dbo.App_Policies": "Id"
"dbo.App_OrgPolicies" |o--|{ "dbo.App_ExecutionLog": "Id"
"dbo.App_PoliciesResult" |o--|{ "dbo.App_OrgPolicies": "Id"
"dbo.App_PoliciesResult" |o--|{ "dbo.App_Policies": "Id"
"dbo.App_PoliciesResult" |o--|{ "dbo.App_ExecutionLog": "Id"
"dbo.App_User_Roles" ||--|{ "dbo.User_Account": "UserId"
"dbo.App_User_Roles" ||--|{ "dbo.Role": "RoleId"
"dbo.App_Credentials" |o--|{ "dbo.App_Organization": "Id"
"dbo.Role_Permissions" ||--|{ "dbo.Role": "RoleId"
"dbo.Role_Permissions" ||--|{ "dbo.Permission": "PermissionId"
"dbo.App_User_Login" ||--|{ "dbo.User_Account": "UserId"
"dbo.App_User_Login" ||--|{ "dbo.HashingAlgorithm": "HashAlgortihmId"
