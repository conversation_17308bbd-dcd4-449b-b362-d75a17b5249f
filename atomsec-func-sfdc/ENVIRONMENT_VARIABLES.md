# Environment Variables for AtomSec SFDC Function App

This document lists all environment variables required for the AtomSec SFDC Function App, including both local development and Azure production environments.

## Core Configuration

### Azure AD Configuration
- `AZURE_AD_CLIENT_ID` - Azure AD application client ID
- `<PERSON><PERSON><PERSON>E_AD_CLIENT_SECRET` - Azure AD application client secret
- `AZURE_AD_TENANT_ID` - Azure AD tenant ID
- `AZURE_AD_REDIRECT_URI` - Azure AD redirect URI (default: http://localhost:3000 for local dev)
- `AZURE_AD_AUTHORITY` - Azure AD authority URL (optional, auto-generated from tenant ID)

### Key Vault Configuration
- `KEY_VAULT_URL` - Azure Key Vault URL (default: https://akv-atomsec-dev.vault.azure.net/)

### JWT Configuration
- `JWT_SECRET` - JWT signing secret (for local development only)
- In production, JWT secret is retrieved from Key Vault

### Storage Configuration
- `AzureStorageConnectionString` - Azure Storage connection string (for local development)
- `AZURE_STORAGE_CONNECTION_STRING` - Azure Storage connection string (for production)
- In production, storage connection string is retrieved from Key Vault if not set

### SQL Database Configuration
- `SQL_CONNECTION_STRING` - SQL Database connection string
- In production, SQL connection string is retrieved from Key Vault if not set

### Frontend URL Configuration
- `FRONTEND_URL` - Frontend application URL (highest priority)
- In production, frontend URL is retrieved from Key Vault if not set

### Backend URL Configuration
- `base_url` - API base URL for tests and integrations (used by test_integration_tabs.py)

## Database Service Configuration

The SFDC function app now uses a centralized database service for all database operations. The following environment variables configure the connection to the DB service:

### DB Service URL Configuration
- `DB_SERVICE_URL` - Base URL for the DB service (highest priority)
- `DB_SERVICE_BASE_URL` - Alternative name for DB_SERVICE_URL
- In local development, defaults to: `http://localhost:7072/api/db`
- In production, defaults to: `https://atomsec-func-db-r.azurewebsites.net/api/db`
- In production, can be retrieved from Key Vault secret: `db-service-url`

### DB Service Connection Settings
- `DB_SERVICE_TIMEOUT` - Request timeout in seconds (default: 30)
- `DB_SERVICE_RETRY_ATTEMPTS` - Number of retry attempts (default: 3)
- `DB_SERVICE_RETRY_DELAY` - Delay between retries in seconds (default: 1)

### DB Service Authentication
- `DB_SERVICE_API_KEY` - Optional API key for DB service authentication
- In production, API key can be retrieved from Key Vault secret: `db-service-api-key`

### DB Service Customization
- `DB_SERVICE_USER_AGENT` - Custom user agent string (default: atomsec-func-sfdc/1.0.0)

## PMD Configuration

The SFDC function app includes PMD (Programming Mistake Detector) for static code analysis. The following environment variables configure PMD behavior:

### PMD General Settings
- `PMD_ENABLED` - Whether PMD scanning is enabled (default: true)
  - Valid values: "true", "1", "yes", "on" (case-insensitive)
  - Any other value disables PMD scanning

### PMD Scan Configuration
- `PMD_DEFAULT_CATEGORIES` - Comma-separated list of default PMD categories
  - Default: "security,performance"
  - Example: "security,performance,codestyle,design"

### PMD Performance Settings
- `PMD_MAX_FINDINGS` - Maximum number of findings to process (default: 10000)
- `PMD_SCAN_TIMEOUT` - PMD scan timeout in seconds (default: 300)
- `PMD_TEMP_DIR` - Temporary directory for PMD scans (default: /tmp)

## Environment-Specific Configuration

### Local Development Environment

For local development, the following variables are typically set in a `.env` file or local environment:

```bash
# Azure AD (for local development)
AZURE_AD_CLIENT_ID=your_client_id
AZURE_AD_CLIENT_SECRET=your_client_secret
AZURE_AD_TENANT_ID=your_tenant_id
AZURE_AD_REDIRECT_URI=http://localhost:3000

# Storage (Azurite for local development)
AzureStorageConnectionString=DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;

# JWT (for local development only)
JWT_SECRET=dev_secret_key_do_not_use_in_production

# DB Service (local development)
DB_SERVICE_URL=http://localhost:7072/api/db
DB_SERVICE_TIMEOUT=30
DB_SERVICE_RETRY_ATTEMPTS=3
DB_SERVICE_RETRY_DELAY=1

# PMD (local development)
PMD_ENABLED=true
PMD_DEFAULT_CATEGORIES=security,performance
PMD_MAX_FINDINGS=10000
PMD_SCAN_TIMEOUT=300
PMD_TEMP_DIR=/tmp
```

### Azure Production Environment

For Azure production, environment variables are typically set in the Azure Function App Configuration:

```bash
# Azure AD (production)
AZURE_AD_CLIENT_ID=your_production_client_id
AZURE_AD_CLIENT_SECRET=your_production_client_secret
AZURE_AD_TENANT_ID=your_production_tenant_id
AZURE_AD_REDIRECT_URI=https://your-frontend-app.azurewebsites.net

# Key Vault
KEY_VAULT_URL=https://your-key-vault.vault.azure.net/

# Storage (production)
AZURE_STORAGE_CONNECTION_STRING=your_production_storage_connection_string

# SQL Database (production)
SQL_CONNECTION_STRING=your_production_sql_connection_string

# Frontend URL (production)
FRONTEND_URL=https://your-frontend-app.azurewebsites.net

# DB Service (production)
DB_SERVICE_URL=https://atomsec-func-db-r.azurewebsites.net/api/db
DB_SERVICE_TIMEOUT=30
DB_SERVICE_RETRY_ATTEMPTS=3
DB_SERVICE_RETRY_DELAY=1
DB_SERVICE_API_KEY=your_db_service_api_key

# PMD (production)
PMD_ENABLED=true
PMD_DEFAULT_CATEGORIES=security,performance,codestyle
PMD_MAX_FINDINGS=10000
PMD_SCAN_TIMEOUT=300
PMD_TEMP_DIR=/tmp
```

## Key Vault Secrets (Production Only)

In production, the following secrets are typically stored in Azure Key Vault and retrieved automatically:

- `jwt-secret` - JWT signing secret
- `storage-connection-string` - Azure Storage connection string
- `sql-connection-string` - SQL Database connection string
- `frontend-url` - Frontend application URL
- `db-service-url` - DB service URL
- `db-service-api-key` - DB service API key

## Validation and Troubleshooting

### Environment Variable Validation

The application logs the configuration it uses at startup. Check the logs for:

1. **DB Service Configuration**: Look for "DB service configuration:" log messages
2. **PMD Configuration**: Look for "PMD configuration:" log messages
3. **Storage Configuration**: Look for storage connection string validation messages
4. **Azure AD Configuration**: Look for Azure AD configuration validation messages

### Common Issues

1. **DB Service Connection Failures**:
   - Verify `DB_SERVICE_URL` is correct
   - Check if the DB service is running and accessible
   - Verify network connectivity between services

2. **PMD Scan Failures**:
   - Check if `PMD_ENABLED` is set to "true"
   - Verify `PMD_TEMP_DIR` is writable
   - Check `PMD_SCAN_TIMEOUT` is sufficient for large codebases

3. **Authentication Failures**:
   - Verify Azure AD configuration
   - Check Key Vault access permissions
   - Ensure secrets are properly stored in Key Vault

### Testing Configuration

You can test the configuration by:

1. **Health Check**: Call the health check endpoint to verify all services are accessible
2. **DB Service Test**: Make a simple API call to the DB service
3. **PMD Test**: Run a small PMD scan to verify configuration

## Migration Notes

### From Direct Database Access to DB Service

The SFDC function app has been migrated from direct database access to using a centralized DB service. This change:

1. **Improves Scalability**: Database operations are centralized
2. **Enhances Security**: Database credentials are managed in one place
3. **Simplifies Maintenance**: Database schema changes only need to be made in one service

### Required Changes for Deployment

When deploying to Azure:

1. **Set DB Service URL**: Configure `DB_SERVICE_URL` to point to your DB service
2. **Configure Authentication**: Set up API key authentication if required
3. **Update Network Rules**: Ensure the SFDC function app can reach the DB service
4. **Test Integration**: Verify all database operations work through the DB service

### Backward Compatibility

The application maintains backward compatibility where possible:

- Old environment variables are still supported
- Fallback mechanisms are in place for missing configuration
- Graceful degradation when services are unavailable 