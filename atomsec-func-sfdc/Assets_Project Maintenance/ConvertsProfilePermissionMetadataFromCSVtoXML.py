#!/usr/bin/env python3
"""
Converts Profile Permission Metadata from CSV to XML format
This script helps convert CSV-based permission data to XML format for best practices.
"""

import csv
import xml.etree.ElementTree as ET
from xml.dom import minidom
import argparse
import sys
import os

def csv_to_xml(csv_file_path, xml_file_path):
    """
    Convert CSV file to XML format for best practices
    
    Args:
        csv_file_path (str): Path to input CSV file
        xml_file_path (str): Path to output XML file
    """
    try:
        # Create root element
        root = ET.Element("BestPractices")
        
        # Read CSV file
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            current_user_type = None
            user_type_element = None
            
            for row in reader:
                user_type = row.get('UserType', '').strip()
                
                # Create new UserType element if needed
                if user_type != current_user_type:
                    current_user_type = user_type
                    user_type_element = ET.SubElement(root, "UserType")
                    user_type_element.set("name", user_type)
                
                # Create Practice element
                practice = ET.SubElement(user_type_element, "Practice")
                
                # Add child elements
                for field_name, field_value in row.items():
                    if field_name != 'UserType' and field_value.strip():
                        field_element = ET.SubElement(practice, field_name)
                        field_element.text = field_value.strip()
        
        # Pretty print XML
        rough_string = ET.tostring(root, 'utf-8')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="    ")
        
        # Remove empty lines
        lines = [line for line in pretty_xml.split('\n') if line.strip()]
        pretty_xml = '\n'.join(lines)
        
        # Write to file
        with open(xml_file_path, 'w', encoding='utf-8') as xmlfile:
            xmlfile.write(pretty_xml)
        
        print(f"Successfully converted {csv_file_path} to {xml_file_path}")
        
    except Exception as e:
        print(f"Error converting CSV to XML: {str(e)}")
        sys.exit(1)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Convert CSV to XML for best practices')
    parser.add_argument('csv_file', nargs='?', help='Input CSV file path')
    parser.add_argument('xml_file', nargs='?', help='Output XML file path')

    args = parser.parse_args()

    # If no arguments provided, use default files in current directory
    if not args.csv_file:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        args.csv_file = os.path.join(current_dir, 'ProfileAndPermissionMetadataSample.csv')
        args.xml_file = os.path.join(current_dir, 'Profiles_PermissionSetRisks-BestPractice_from_csv.xml')
        print(f"Using default files:")
        print(f"  Input: {args.csv_file}")
        print(f"  Output: {args.xml_file}")

    # Check if input file exists
    if not os.path.exists(args.csv_file):
        print(f"Error: Input file {args.csv_file} does not exist")
        sys.exit(1)

    # Convert CSV to XML
    csv_to_xml(args.csv_file, args.xml_file)

if __name__ == "__main__":
    main()
