"""
Tests for deployment verification

This module contains tests to verify that functions are available after deployment.
"""

import unittest
import requests
import os

class TestDeploymentVerification(unittest.TestCase):
    """Test cases for deployment verification"""

    def setUp(self):
        """Set up the test environment"""
        # Skip these tests if not running in a deployment environment
        if not os.environ.get('RUN_DEPLOYMENT_TESTS', '').lower() == 'true':
            self.skipTest("Skipping deployment tests. Set RUN_DEPLOYMENT_TESTS=true to run.")
            
        # Get the base URL from environment variable or use the default production URL
        self.base_url = os.environ.get('FUNCTION_APP_URL', 'https://func-atomsec-sfdc-dev.azurewebsites.net')
        
        # Ensure base_url doesn't end with a slash
        if self.base_url.endswith('/'):
            self.base_url = self.base_url[:-1]
            
        print(f"Testing deployment at: {self.base_url}")

    def test_deployed_functions_availability(self):
        """Test that functions are available after deployment by making HTTP requests to key endpoints"""
        # List of critical endpoints to test
        endpoints = [
            # Home endpoint (Swagger-like documentation)
            '/api/home',
            # Health check endpoint
            '/api/health',
            # Authentication endpoints
            '/api/auth/azure/login'
        ]
        
        # Test each endpoint
        for endpoint in endpoints:
            url = f"{self.base_url}{endpoint}"
            try:
                # Make a GET request to the endpoint
                response = requests.get(url, timeout=30)
                
                # Check if the response is successful (status code 200-299) or a redirect (status code 300-399)
                self.assertTrue(
                    200 <= response.status_code < 400,
                    f"Endpoint {endpoint} returned status code {response.status_code}"
                )
                
                # Log the successful response
                print(f"✅ Endpoint {endpoint} is available (Status: {response.status_code})")
                
            except requests.RequestException as e:
                # If the request fails, fail the test
                self.fail(f"Failed to connect to {endpoint}: {str(e)}")
                
    def test_proxy_functions_availability(self):
        """Test that proxy functions are available after deployment"""
        # List of proxy functions to test
        proxy_endpoints = [
            # Home endpoint
            '/api/home',
            # Auth endpoints
            '/api/auth/azure/login',
            # Integration endpoints (these will require authentication, so we're just checking if they exist)
            '/api/api/integration/test-connection'
        ]
        
        # Test each proxy endpoint
        for endpoint in proxy_endpoints:
            url = f"{self.base_url}{endpoint}"
            try:
                # Make a HEAD request to check if the endpoint exists
                # Using HEAD to avoid processing large responses
                response = requests.head(url, timeout=30)
                
                # For auth endpoints, a redirect is expected and is considered successful
                if 'auth' in endpoint and 300 <= response.status_code < 400:
                    print(f"✅ Auth endpoint {endpoint} is available (Status: {response.status_code} - Redirect)")
                    continue
                    
                # Check if the response is successful or a redirect
                self.assertTrue(
                    response.status_code < 500,  # Accept any non-server error
                    f"Proxy endpoint {endpoint} returned status code {response.status_code}"
                )
                
                # Log the successful response
                print(f"✅ Proxy endpoint {endpoint} is available (Status: {response.status_code})")
                
            except requests.RequestException as e:
                # If the request fails, fail the test
                self.fail(f"Failed to connect to proxy endpoint {endpoint}: {str(e)}")
                
    def test_blueprint_functions_availability(self):
        """Test that blueprint functions are available after deployment"""
        # Dictionary mapping blueprints to their key endpoints
        blueprint_endpoints = {
            'profile_metadata': ['/api/api/profile'],
            'security_health_check': ['/api/api/security_health_check'],
            'general': ['/api/health', '/api/info'],
            'security_data': ['/api/api/security_data'],
            'auth': ['/api/auth/signup', '/api/auth/login'],
            'azure_ad_auth': ['/api/auth/azure/login'],
            'organization': ['/api/api/orgs'],
            'security_analysis': ['/api/api/health-score', '/api/api/health-risks'],
            'scan': ['/api/api/scan'],
            'key_vault': ['/api/api/key-vault/secrets'],
            'integration': ['/api/api/integrations']
        }
        
        # Test each blueprint's endpoints
        for blueprint_name, endpoints in blueprint_endpoints.items():
            for endpoint in endpoints:
                url = f"{self.base_url}{endpoint}"
                try:
                    # Make a HEAD request to check if the endpoint exists
                    # Using HEAD to avoid processing large responses
                    response = requests.head(url, timeout=30)
                    
                    # For auth endpoints, a redirect is expected and is considered successful
                    if 'auth' in endpoint and 300 <= response.status_code < 400:
                        print(f"✅ Blueprint {blueprint_name} endpoint {endpoint} is available (Status: {response.status_code} - Redirect)")
                        continue
                        
                    # For endpoints that require authentication, a 401 or 403 is expected and is considered successful
                    # (it means the endpoint exists but requires authentication)
                    if response.status_code in [401, 403]:
                        print(f"✅ Blueprint {blueprint_name} endpoint {endpoint} is available but requires authentication (Status: {response.status_code})")
                        continue
                        
                    # Check if the response is successful, a redirect, or an authentication error
                    self.assertTrue(
                        response.status_code < 500,  # Accept any non-server error
                        f"Blueprint {blueprint_name} endpoint {endpoint} returned status code {response.status_code}"
                    )
                    
                    # Log the successful response
                    print(f"✅ Blueprint {blueprint_name} endpoint {endpoint} is available (Status: {response.status_code})")
                    
                except requests.RequestException as e:
                    # If the request fails, fail the test
                    self.fail(f"Failed to connect to blueprint {blueprint_name} endpoint {endpoint}: {str(e)}")


if __name__ == '__main__':
    unittest.main()
