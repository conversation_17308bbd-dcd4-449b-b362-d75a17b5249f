"""
Pytest configuration file

This file is automatically loaded by pytest and contains configuration and fixtures
for all tests.
"""

import os
import sys
import pytest
import asyncio
import logging
from unittest.mock import patch

# Determine the project root directory
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Add project root and key directories to Python path
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'blueprints'))
sys.path.insert(0, os.path.join(project_root, 'shared'))
sys.path.insert(0, os.path.join(project_root, 'tests'))  # Add tests directory to path

# Log Python path for debugging
logger = logging.getLogger("s.conftest")
logger.info(f"Python path: {sys.path}")
logger.info(f"Current directory: {os.getcwd()}")

# Set environment variables for testing
os.environ["PYTEST_RUNNING"] = "true"
os.environ["ATOMSEC_TEST_ENV"] = "true"
os.environ["IS_LOCAL_DEV"] = "true"

# Configure logging for tests
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Import our mock implementations
from tests.mock_table_storage import MockTableStorageRepository, reset_tables

# Define a custom event loop policy for async tests
@pytest.fixture(scope="session")
def event_loop_policy():
    """Return the event loop policy to use."""
    return asyncio.DefaultEventLoopPolicy()

# Optional: Add a fixture to mock configuration for testing
@pytest.fixture(scope="session")
def mock_config():
    """Provide a mock configuration for testing."""
    return {
        "is_local_dev": True,
        "test_environment": True,
        "base_url": "https://localhost:7071"
    }

# Fixture to patch the get_table_storage_repository function
@pytest.fixture(autouse=True)
def mock_table_storage():
    """
    Patch the get_table_storage_repository function to use our mock implementation.
    This fixture is automatically used for all tests.
    """
    # Reset tables before each test
    reset_tables()

    # Define a patched version of get_table_storage_repository
    def patched_get_table_storage_repository(table_name):
        logger.info(f"Creating mock table storage repository for {table_name}")
        return MockTableStorageRepository(table_name=table_name)

    # Try to import and verify the module exists
    data_access_available = False
    try:
        import shared.data_access as data_access_module
        logger.info("Successfully imported shared.data_access module")

        # Verify the function exists
        if hasattr(data_access_module, 'get_table_storage_repository'):
            logger.info("Found get_table_storage_repository function in shared.data_access")
            data_access_available = True
        else:
            logger.warning("get_table_storage_repository function not found in shared.data_access")

    except ImportError as e:
        logger.warning(f"Could not import shared.data_access (this is OK for deployment tests): {e}")
        # This is expected in some environments where optional dependencies aren't installed
    except Exception as e:
        logger.warning(f"Unexpected error importing shared.data_access: {e}")

    # Apply the patch only if the module is available
    if data_access_available:
        try:
            with patch('shared.data_access.get_table_storage_repository', side_effect=patched_get_table_storage_repository):
                logger.info("Successfully applied patch for shared.data_access.get_table_storage_repository")
                yield
        except Exception as e:
            logger.warning(f"Failed to apply patch (continuing without it): {e}")
            yield
    else:
        logger.info("Skipping shared.data_access patch - module not available (this is OK for deployment tests)")
        yield

# NOTE: DB service mock removed - user repository tests moved to atomsec-func-db-r service
