"""
Tests for the user repository module

This module contains tests for the user repository functions.
"""

import unittest
import os
import sys
from datetime import date
from unittest.mock import patch, MagicMock

# Add parent directory to path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the module to test
from shared.user_repository import (
    create_user, get_user_account_by_email, authenticate_user,
    hash_password, verify_password, generate_salt
)


class TestUserRepository(unittest.TestCase):
    """Test case for user repository functions"""

    @patch('shared.user_repository.get_user_account_table_repo')
    @patch('shared.user_repository.get_user_login_table_repo')
    @patch('shared.user_repository.is_local_dev')
    def test_create_user_local(self, mock_is_local_dev, mock_get_user_login_table_repo, mock_get_user_account_table_repo):
        """Test creating a user in local development environment"""
        # Setup mocks
        mock_is_local_dev.return_value = True

        mock_user_account_repo = MagicMock()
        mock_user_account_repo.query_entities.return_value = []
        mock_user_account_repo.insert_entity.return_value = True
        mock_get_user_account_table_repo.return_value = mock_user_account_repo

        mock_user_login_repo = MagicMock()
        mock_user_login_repo.query_entities.return_value = []
        mock_user_login_repo.insert_entity.return_value = True
        mock_get_user_login_table_repo.return_value = mock_user_login_repo

        # Call the function
        user_id = create_user(
            email="<EMAIL>",
            password="password123",
            first_name="Test",
            last_name="User",
            dob="1990-01-01",
            contact="**********",
            state="CA",
            country="USA"
        )

        # Assertions
        self.assertIsNotNone(user_id)
        mock_user_account_repo.insert_entity.assert_called_once()
        mock_user_login_repo.insert_entity.assert_called_once()

    @patch('shared.user_repository.get_user_account_table_repo')
    @patch('shared.user_repository.is_local_dev')
    def test_get_user_account_by_email_local(self, mock_is_local_dev, mock_get_user_account_table_repo):
        """Test getting a user account by email in local development environment"""
        # Setup mocks
        mock_is_local_dev.return_value = True

        # Mock repository to return user when queried
        mock_user_account_repo = MagicMock()
        # Always return the user regardless of the query
        mock_user_account_repo.query_entities.return_value = [{
            "UserId": 1234,
            "FirstName": "Test",
            "MiddleName": "",
            "LastName": "User",
            "DoB": "1990-01-01",
            "Email": "<EMAIL>",
            "RowKey": "<EMAIL>",
            "Contact": "**********",
            "State": "CA",
            "Country": "USA"
        }]
        mock_get_user_account_table_repo.return_value = mock_user_account_repo

        # Call the function
        user_account = get_user_account_by_email("<EMAIL>")

        # Assertions
        self.assertIsNotNone(user_account)
        self.assertEqual(user_account.UserId, 1234)
        self.assertEqual(user_account.Email, "<EMAIL>")
        self.assertEqual(user_account.FirstName, "Test")
        self.assertEqual(user_account.LastName, "User")
        # Now we expect the query_entities to be called at least once
        self.assertTrue(mock_user_account_repo.query_entities.called)

    def test_hash_password(self):
        """Test hashing a password"""
        # Generate a salt
        salt = generate_salt()

        # Hash a password
        hashed_password = hash_password("password123", salt, 1)

        # Assertions
        self.assertIsNotNone(hashed_password)
        self.assertTrue(len(hashed_password) > 0)

        # Verify the password
        self.assertTrue(verify_password("password123", hashed_password, salt, 1))
        self.assertFalse(verify_password("wrongpassword", hashed_password, salt, 1))

    @patch('shared.user_repository.get_user_login_table_repo')
    @patch('shared.user_repository.is_local_dev')
    def test_authenticate_user_local_success(self, mock_is_local_dev, mock_get_user_login_table_repo):
        """Test authenticating a user successfully in local development environment"""
        # Setup mocks
        mock_is_local_dev.return_value = True

        # Create a salt and hash a password
        salt = generate_salt()
        hashed_password = hash_password("password123", salt, 1)

        # Mock repository to return user when queried
        mock_user_login_repo = MagicMock()
        # Always return the user regardless of the query
        mock_user_login_repo.query_entities.return_value = [{
            "UserId": 1234,
            "Username": "<EMAIL>",
            "RowKey": "<EMAIL>",
            "PasswordHash": hashed_password,
            "PasswordSalt": salt,
            "HashAlgorithmId": 1
        }]
        mock_get_user_login_table_repo.return_value = mock_user_login_repo

        # Call the function
        user_id = authenticate_user("<EMAIL>", "password123")

        # Assertions
        self.assertIsNotNone(user_id)
        self.assertEqual(user_id, 1234)
        # Now we expect the query_entities to be called at least once
        self.assertTrue(mock_user_login_repo.query_entities.called)

    @patch('shared.user_repository.get_user_login_table_repo')
    @patch('shared.user_repository.is_local_dev')
    def test_authenticate_user_local_failure(self, mock_is_local_dev, mock_get_user_login_table_repo):
        """Test authenticating a user with wrong password in local development environment"""
        # Setup mocks
        mock_is_local_dev.return_value = True

        # Create a salt and hash a password
        salt = generate_salt()
        hashed_password = hash_password("password123", salt, 1)

        # Mock repository to return user when queried
        mock_user_login_repo = MagicMock()
        # Always return the user regardless of the query
        mock_user_login_repo.query_entities.return_value = [{
            "UserId": 1234,
            "Username": "<EMAIL>",
            "RowKey": "<EMAIL>",
            "PasswordHash": hashed_password,
            "PasswordSalt": salt,
            "HashAlgorithmId": 1
        }]
        mock_get_user_login_table_repo.return_value = mock_user_login_repo

        # Call the function with wrong password
        user_id = authenticate_user("<EMAIL>", "wrongpassword")

        # Assertions
        self.assertIsNone(user_id)
        # Now we expect the query_entities to be called at least once
        self.assertTrue(mock_user_login_repo.query_entities.called)


if __name__ == '__main__':
    unittest.main()
