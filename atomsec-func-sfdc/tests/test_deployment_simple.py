"""
Simple deployment tests that don't require heavy dependencies.
These tests can run in the Azure DevOps pipeline without pyodbc or database connections.
"""

import os
import sys
import pytest
import importlib.util
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestDeploymentBasics:
    """Basic deployment verification tests"""
    
    def test_project_structure(self):
        """Test that essential project files exist"""
        project_root = Path(__file__).parent.parent
        
        # Essential files that should exist
        essential_files = [
            'function_app.py',
            'host.json',
            'requirements.txt',
            'shared/__init__.py',
            'blueprints/__init__.py'
        ]
        
        for file_path in essential_files:
            full_path = project_root / file_path
            assert full_path.exists(), f"Essential file missing: {file_path}"
    
    def test_requirements_file(self):
        """Test that requirements.txt is valid"""
        project_root = Path(__file__).parent.parent
        requirements_file = project_root / 'requirements.txt'
        
        assert requirements_file.exists(), "requirements.txt file missing"
        
        # Read and validate requirements
        with open(requirements_file, 'r') as f:
            content = f.read()
        
        assert len(content.strip()) > 0, "requirements.txt is empty"
        
        # Check for essential packages
        essential_packages = ['azure-functions', 'fastapi']
        for package in essential_packages:
            assert package in content, f"Essential package missing: {package}"
    
    def test_host_json_valid(self):
        """Test that host.json is valid JSON"""
        project_root = Path(__file__).parent.parent
        host_json = project_root / 'host.json'
        
        assert host_json.exists(), "host.json file missing"
        
        import json
        with open(host_json, 'r') as f:
            config = json.load(f)
        
        assert isinstance(config, dict), "host.json should contain a JSON object"
        assert 'version' in config, "host.json should have a version field"
    
    def test_function_app_imports(self):
        """Test that function_app.py can be imported without database dependencies"""
        project_root = Path(__file__).parent.parent
        function_app_path = project_root / 'function_app.py'
        
        assert function_app_path.exists(), "function_app.py file missing"
        
        # Try to load the module spec without importing (to avoid dependency issues)
        spec = importlib.util.spec_from_file_location("function_app", function_app_path)
        assert spec is not None, "Could not load function_app.py module spec"
        assert spec.loader is not None, "function_app.py module spec has no loader"
    
    def test_shared_modules_exist(self):
        """Test that essential shared modules exist"""
        project_root = Path(__file__).parent.parent
        shared_dir = project_root / 'shared'
        
        essential_modules = [
            'common.py',
            'config.py',
            'auth_utils.py',
            'db_service_client.py'
        ]
        
        for module in essential_modules:
            module_path = shared_dir / module
            assert module_path.exists(), f"Essential shared module missing: {module}"
    
    def test_blueprints_exist(self):
        """Test that essential blueprint modules exist"""
        project_root = Path(__file__).parent.parent
        blueprints_dir = project_root / 'blueprints'
        
        essential_blueprints = [
            'integration.py',
            'general.py'
        ]
        
        for blueprint in essential_blueprints:
            blueprint_path = blueprints_dir / blueprint
            assert blueprint_path.exists(), f"Essential blueprint missing: {blueprint}"
    
    def test_config_files_exist(self):
        """Test that configuration files exist"""
        project_root = Path(__file__).parent.parent

        config_files = [
            'local.settings.json'
        ]

        for config_file in config_files:
            config_path = project_root / config_file
            assert config_path.exists(), f"Configuration file missing: {config_file}"
    
    def test_scripts_exist(self):
        """Test that essential scripts exist"""
        project_root = Path(__file__).parent.parent
        scripts_dir = project_root / 'scripts'
        
        essential_scripts = [
            'deployment_health_check.py',
            'init_policy_and_rule.py'
        ]
        
        for script in essential_scripts:
            script_path = scripts_dir / script
            assert script_path.exists(), f"Essential script missing: {script}"

class TestEnvironmentConfiguration:
    """Test environment configuration"""
    
    def test_python_version(self):
        """Test that Python version is compatible"""
        import sys
        version = sys.version_info
        
        # Should be Python 3.8 or higher
        assert version.major == 3, "Should be using Python 3"
        assert version.minor >= 8, f"Python version should be 3.8+, got {version.major}.{version.minor}"
    
    def test_essential_imports(self):
        """Test that essential packages can be imported"""
        essential_imports = [
            'json',
            'os',
            'sys',
            'datetime',
            'uuid',
            'logging'
        ]
        
        for module_name in essential_imports:
            try:
                __import__(module_name)
            except ImportError:
                pytest.fail(f"Could not import essential module: {module_name}")

@pytest.mark.deployment
class TestDeploymentReadiness:
    """Tests specifically for deployment readiness"""
    
    def test_no_debug_flags(self):
        """Test that debug flags are not enabled in production files"""
        project_root = Path(__file__).parent.parent
        
        # Files that should not contain debug flags
        files_to_check = [
            'function_app.py',
            'shared/config.py'
        ]
        
        debug_patterns = ['DEBUG = True', 'debug=True', 'DEBUG=True']
        
        for file_path in files_to_check:
            full_path = project_root / file_path
            if full_path.exists():
                with open(full_path, 'r') as f:
                    content = f.read()
                
                for pattern in debug_patterns:
                    assert pattern not in content, f"Debug flag found in {file_path}: {pattern}"
    
    def test_no_hardcoded_secrets(self):
        """Test that no hardcoded secrets are present"""
        project_root = Path(__file__).parent.parent
        
        # Patterns that might indicate hardcoded secrets
        secret_patterns = [
            'password=',
            'secret=',
            'key=',
            'token='
        ]
        
        # Files to check (excluding test files and this file)
        files_to_check = []
        for py_file in project_root.rglob('*.py'):
            if 'test' not in str(py_file) and '__pycache__' not in str(py_file):
                files_to_check.append(py_file)
        
        for file_path in files_to_check[:10]:  # Limit to first 10 files to avoid timeout
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                
                for pattern in secret_patterns:
                    if pattern in content:
                        # Allow environment variable patterns
                        if 'os.environ' in content or 'getenv' in content:
                            continue
                        # This is just a warning, not a failure
                        print(f"Warning: Potential hardcoded secret pattern in {file_path}: {pattern}")
            except Exception:
                # Skip files that can't be read
                continue

if __name__ == "__main__":
    # Run tests when script is executed directly
    pytest.main([__file__, "-v"])
