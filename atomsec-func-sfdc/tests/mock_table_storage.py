"""
Mock Table Storage for Testing

This module provides a mock implementation of the TableStorageRepository class
that stores entities in memory for testing purposes.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

# Configure module-level logger
logger = logging.getLogger(__name__)

# In-memory storage for tables
_tables = {}

class MockTableStorageRepository:
    """Mock implementation of TableStorageRepository for testing"""

    def __init__(self, table_name: str):
        """
        Initialize the mock table storage repository

        Args:
            table_name: Name of the table
        """
        self.table_name = table_name
        
        # Initialize table if it doesn't exist
        if table_name not in _tables:
            _tables[table_name] = []
            logger.info(f"Initialized mock table: {table_name}")

    def insert_entity(self, entity: Dict[str, Any]) -> bool:
        """
        Insert an entity into the table

        Args:
            entity: Entity to insert (dictionary)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure partition key and row key
            if 'PartitionKey' not in entity:
                entity['PartitionKey'] = datetime.now().strftime("%Y%m%d")
                logger.info(f"Added default PartitionKey: {entity['PartitionKey']}")
            if 'RowKey' not in entity:
                entity['RowKey'] = datetime.now().strftime("%H%M%S%f")
                logger.info(f"Added default RowKey: {entity['RowKey']}")

            # Add entity to table
            _tables[self.table_name].append(entity.copy())
            logger.info(f"Inserted entity with RowKey {entity.get('RowKey')} into table {self.table_name}")
            
            return True
        except Exception as e:
            logger.error(f"Error inserting entity: {str(e)}")
            return False

    def query_entities(self, filter_query: str = None) -> List[Dict[str, Any]]:
        """
        Query entities from the table

        Args:
            filter_query: Filter query string (optional)

        Returns:
            List[Dict[str, Any]]: List of entities
        """
        try:
            # Get all entities from the table
            entities = _tables.get(self.table_name, [])
            
            # If no filter query, return all entities
            if not filter_query:
                logger.info(f"Returning {len(entities)} entities from table {self.table_name}")
                return entities
            
            # Parse filter query
            filtered_entities = []
            
            # Simple filter parsing for common patterns
            if "eq" in filter_query:
                # Handle equality filter
                parts = filter_query.split("eq")
                if len(parts) == 2:
                    field = parts[0].strip()
                    value = parts[1].strip()
                    
                    # Remove quotes if present
                    if value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    elif value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    
                    # Filter entities
                    filtered_entities = [e for e in entities if str(e.get(field, "")) == value]
                    logger.info(f"Filtered {len(filtered_entities)} entities from table {self.table_name} with filter: {filter_query}")
                    return filtered_entities
            
            # If filter parsing fails, return all entities
            logger.warning(f"Failed to parse filter query: {filter_query}, returning all entities")
            return entities
        except Exception as e:
            logger.error(f"Error querying entities: {str(e)}")
            return []

    def get_entity(self, partition_key: str, row_key: str) -> Optional[Dict[str, Any]]:
        """
        Get an entity from the table

        Args:
            partition_key: Partition key
            row_key: Row key

        Returns:
            Dict[str, Any]: Entity or None if not found
        """
        try:
            # Get all entities from the table
            entities = _tables.get(self.table_name, [])
            
            # Find entity with matching partition key and row key
            for entity in entities:
                if entity.get('PartitionKey') == partition_key and entity.get('RowKey') == row_key:
                    logger.info(f"Found entity with PartitionKey={partition_key}, RowKey={row_key} in table {self.table_name}")
                    return entity
            
            logger.warning(f"Entity not found with PartitionKey={partition_key}, RowKey={row_key} in table {self.table_name}")
            return None
        except Exception as e:
            logger.error(f"Error getting entity: {str(e)}")
            return None

    def update_entity(self, entity: Dict[str, Any]) -> bool:
        """
        Update an entity in the table

        Args:
            entity: Entity to update (dictionary)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure entity has partition key and row key
            if 'PartitionKey' not in entity or 'RowKey' not in entity:
                logger.error("Entity must have PartitionKey and RowKey")
                return False
            
            # Get all entities from the table
            entities = _tables.get(self.table_name, [])
            
            # Find entity with matching partition key and row key
            for i, existing_entity in enumerate(entities):
                if existing_entity.get('PartitionKey') == entity.get('PartitionKey') and existing_entity.get('RowKey') == entity.get('RowKey'):
                    # Update entity
                    _tables[self.table_name][i] = entity.copy()
                    logger.info(f"Updated entity with RowKey {entity.get('RowKey')} in table {self.table_name}")
                    return True
            
            # If entity not found, insert it
            _tables[self.table_name].append(entity.copy())
            logger.info(f"Inserted entity with RowKey {entity.get('RowKey')} into table {self.table_name} (update operation)")
            return True
        except Exception as e:
            logger.error(f"Error updating entity: {str(e)}")
            return False

    def delete_entity(self, partition_key: str, row_key: str) -> bool:
        """
        Delete an entity from the table

        Args:
            partition_key: Partition key
            row_key: Row key

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get all entities from the table
            entities = _tables.get(self.table_name, [])
            
            # Find entity with matching partition key and row key
            for i, entity in enumerate(entities):
                if entity.get('PartitionKey') == partition_key and entity.get('RowKey') == row_key:
                    # Delete entity
                    del _tables[self.table_name][i]
                    logger.info(f"Deleted entity with PartitionKey={partition_key}, RowKey={row_key} from table {self.table_name}")
                    return True
            
            logger.warning(f"Entity not found with PartitionKey={partition_key}, RowKey={row_key} in table {self.table_name}")
            return False
        except Exception as e:
            logger.error(f"Error deleting entity: {str(e)}")
            return False

    def delete_table(self) -> bool:
        """
        Delete the table

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Delete table
            if self.table_name in _tables:
                del _tables[self.table_name]
                logger.info(f"Deleted table {self.table_name}")
                return True
            
            logger.warning(f"Table {self.table_name} not found")
            return False
        except Exception as e:
            logger.error(f"Error deleting table: {str(e)}")
            return False

# Function to reset all tables (useful for test cleanup)
def reset_tables():
    """Reset all tables"""
    global _tables
    _tables = {}
    logger.info("Reset all mock tables")
