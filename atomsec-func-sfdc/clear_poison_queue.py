#!/usr/bin/env python3
"""
Clear Poison Queue Script

This script clears messages from the poison queue and optionally reprocesses them
by moving them back to the main queue with the correct encoding.
"""

import os
import json
from azure.storage.queue import QueueServiceClient

def get_connection_string():
    """Get the storage connection string"""
    # Use the connection string from local.settings.json for local development
    return "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;"

def clear_poison_queue(queue_name, reprocess=False):
    """
    Clear messages from a poison queue
    
    Args:
        queue_name: Name of the poison queue (e.g., 'task-queue-high-poison')
        reprocess: If True, move messages back to the main queue for reprocessing
    """
    connection_string = get_connection_string()
    queue_service = QueueServiceClient.from_connection_string(connection_string)
    
    # Get the poison queue client
    poison_queue_client = queue_service.get_queue_client(queue_name)
    
    # Get the main queue name (remove '-poison' suffix)
    main_queue_name = queue_name.replace('-poison', '')
    main_queue_client = queue_service.get_queue_client(main_queue_name) if reprocess else None
    
    print(f"Processing poison queue: {queue_name}")
    
    # Get queue properties to see message count
    properties = poison_queue_client.get_queue_properties()
    message_count = properties.approximate_message_count
    print(f"Found {message_count} messages in poison queue")
    
    if message_count == 0:
        print("No messages to process")
        return
    
    processed_count = 0
    reprocessed_count = 0
    
    # Process messages in batches
    while True:
        # Receive messages (up to 32 at a time)
        messages = poison_queue_client.receive_messages(max_messages=32, visibility_timeout=300)
        message_list = list(messages)
        
        if not message_list:
            break
            
        for message in message_list:
            try:
                # Parse the message content
                content = message.content
                print(f"Processing message ID: {message.id}")
                print(f"Message content preview: {content[:100]}...")
                
                # Validate that it's valid JSON
                task_data = json.loads(content)
                
                if reprocess and main_queue_client:
                    # Send the message to the main queue with correct encoding
                    main_queue_client.send_message(content)
                    reprocessed_count += 1
                    print(f"  ✓ Reprocessed message for task: {task_data.get('task_id', 'unknown')}")
                
                # Delete the message from poison queue
                poison_queue_client.delete_message(message)
                processed_count += 1
                print(f"  ✓ Deleted from poison queue")
                
            except json.JSONDecodeError as e:
                print(f"  ✗ Invalid JSON in message {message.id}: {e}")
                # Still delete invalid messages
                poison_queue_client.delete_message(message)
                processed_count += 1
            except Exception as e:
                print(f"  ✗ Error processing message {message.id}: {e}")
                # Delete problematic messages to prevent infinite loop
                poison_queue_client.delete_message(message)
                processed_count += 1
    
    print(f"\nSummary:")
    print(f"  Processed: {processed_count} messages")
    if reprocess:
        print(f"  Reprocessed: {reprocessed_count} messages")
    print(f"  Poison queue cleared: {queue_name}")

def main():
    """Main function"""
    print("🧹 Poison Queue Cleaner")
    print("=" * 50)
    
    # List of poison queues to check
    poison_queues = [
        'task-queue-high-poison',
        'task-queue-medium-poison', 
        'task-queue-low-poison',
        'task-queue-poison'
    ]
    
    # Ask user what to do
    print("\nOptions:")
    print("1. Clear poison queues only (delete messages)")
    print("2. Clear and reprocess messages (move back to main queues)")
    
    choice = input("\nEnter your choice (1 or 2): ").strip()
    
    reprocess = choice == '2'
    
    if reprocess:
        print("\n⚠️  WARNING: This will reprocess all poison messages.")
        print("Make sure the SFDC service is running and the encoding fix is applied.")
        confirm = input("Continue? (y/N): ").strip().lower()
        if confirm != 'y':
            print("Cancelled.")
            return
    
    print(f"\n{'Clearing and reprocessing' if reprocess else 'Clearing'} poison queues...")
    
    total_processed = 0
    for queue_name in poison_queues:
        try:
            clear_poison_queue(queue_name, reprocess)
            total_processed += 1
        except Exception as e:
            print(f"Error processing {queue_name}: {e}")
    
    print(f"\n✅ Completed processing {total_processed} poison queues")
    
    if reprocess:
        print("\n📝 Next steps:")
        print("1. Check the SFDC service logs to see if messages are being processed")
        print("2. Monitor the main queues to ensure messages are being consumed")
        print("3. Check that no new messages appear in poison queues")

if __name__ == "__main__":
    main()
