{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "python", "FUNCTIONS_EXTENSION_VERSION": "~4", "AZURE_AD_CLIENT_ID": "your_azure_ad_client_id", "AZURE_AD_CLIENT_SECRET": "your_azure_ad_client_secret", "AZURE_AD_TENANT_ID": "your_azure_ad_tenant_id", "AZURE_AD_REDIRECT_URI": "http://localhost:3000", "KEY_VAULT_URL": "https://akv-atomsec-dev.vault.azure.net/", "JWT_SECRET": "dev_secret_key_do_not_use_in_production", "AzureStorageConnectionString": "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;TableEndpoint=http://127.0.0.1:10002/devstoreaccount1;", "SQL_CONNECTION_STRING": "your_sql_connection_string_for_local_dev", "FRONTEND_URL": "http://localhost:3000", "base_url": "http://localhost:7071", "DB_SERVICE_URL": "http://localhost:7072/api/db", "DB_SERVICE_TIMEOUT": "30", "DB_SERVICE_RETRY_ATTEMPTS": "3", "DB_SERVICE_RETRY_DELAY": "1", "DB_SERVICE_API_KEY": "your_local_db_service_api_key", "DB_SERVICE_USER_AGENT": "atomsec-func-sfdc/1.0.0-local", "PMD_ENABLED": "true", "PMD_DEFAULT_CATEGORIES": "security,performance", "PMD_MAX_FINDINGS": "10000", "PMD_SCAN_TIMEOUT": "300", "PMD_TEMP_DIR": "/tmp"}, "Host": {"LocalHttpPort": 7071, "CORS": "*", "CORSCredentials": false}}