# AtomSec SFDC API

This project contains the API for the AtomSec application, built with FastAPI and deployed as an Azure Function. It supports local development using Azurite for storage emulation.

## FastAPI with Azure Functions ASGI Integration

This project uses FastAPI with Azure Functions ASGI integration to combine the best of both worlds:

1. **FastAPI** for enhanced API features like automatic documentation, request validation, and dependency injection
2. **Azure Functions** for serverless scalability and integration with Azure services

FastAPI offers several advantages for API development:

1. **Automatic Documentation**: Interactive API documentation with Swagger UI and ReDoc
2. **Request Validation**: Automatic validation of request data using Pydantic models
3. **Dependency Injection**: Clean way to handle dependencies and share code
4. **Better Error Handling**: More structured error handling with HTTP exceptions
5. **Type Hints**: Better IDE support and code completion

For detailed information about the ASGI integration, see the [ASGI Integration Guide](ASGI_INTEGRATION.md).

## Project Setup

### Prerequisites
- Python 3.8-3.11
- pip
- virtualenv (recommended)
- Azure Functions Core Tools v4
- Azurite (for local storage emulation)

### Local Development Setup

1. Clone the repository
```bash
git clone <repository-url>
cd atomsec-func-sfdc
```

## Architecture

The project follows a modular, blueprint-based architecture that aligns with Azure best practices:

### Implementation Approach

This project uses a hybrid approach that combines:

1. **Azure Functions with Blueprints**: For most functionality, we use Azure Functions with blueprints for better organization.
2. **FastAPI with ASGI Integration**: For more complex API scenarios, we use FastAPI with Azure Functions ASGI integration.

#### When to Use Each Approach:

- **Azure Functions with Blueprints**: Use for simple endpoints with straightforward request/response patterns.
- **FastAPI with ASGI Integration**: Use for complex endpoints that require advanced features like:
  - Request validation with Pydantic models
  - Dependency injection
  - Automatic documentation
  - Complex routing

#### API Routing Conventions:

To ensure consistent API routing, we follow these conventions:

1. **Frontend Expectations**: The frontend expects endpoints with a single `/api/` prefix (e.g., `/api/accounts`).
2. **Backend Implementation**:
   - For Azure Functions, we define routes with a single `/api/` prefix in function_app.py.
   - For FastAPI, we define routes with a single `/api/` prefix for all endpoints.
3. **Proxy Functions**: We use proxy functions in function_app.py to route requests to the appropriate handlers.

### Directory Structure

```
atomsec-func-sfdc/
│
├── function_app.py              # Main entry point with function app definition
├── blueprints/                  # Organized function modules
│   ├── __init__.py              # Makes the directory a package
│   ├── profile_metadata.py      # Profile metadata functions
│   ├── security_health_check.py # Security health check functions
│   ├── auth.py                  # Authentication functions
│   └── general.py               # General utility functions
│
├── shared/                      # Shared code and utilities
│   ├── __init__.py              # Makes the directory a package
│   ├── azure_services.py        # Azure service connections
│   ├── data_access.py           # Data access repositories
│   ├── database_models.py       # Database model definitions
│   ├── user_repository.py       # User data access functions
│   └── utils.py                 # Utility functions
│
├── tests/                       # Test cases
│   ├── __init__.py
│   ├── test_profile_metadata.py
│   ├── test_security_health_check.py
│   └── test_user_repository.py
│
├── scripts/                     # Utility scripts
│   └── init_database.py         # Database initialization script
│
├── host.json                    # Host configuration
├── local.settings.json          # Local settings (not checked into source control)
├── requirements.txt             # Project dependencies
└── README.md                    # Project documentation
```

### Key Design Principles

1. **Blueprint-Based Organization**: Functions are organized into logical blueprints for better modularity and maintainability
2. **Centralized Configuration**: All Azure service connections are managed through the `shared/azure_services.py` module
3. **Repository Pattern**: Data access is abstracted through repository classes in `shared/data_access.py`
4. **Environment Awareness**: The code automatically detects if it's running locally or in Azure and configures itself accordingly
5. **Async Support**: Asynchronous functions for better performance and scalability
6. **Proper Error Handling**: Consistent error handling and logging throughout the application
7. **Database Models**: Structured database models for consistent data representation

## Available Endpoints

- `/api/home`: Home page with links to all available endpoints
- `/api/health-score`: View Salesforce security health score data
- `/api/health-risks`: View Salesforce security health risks data
- `/api/integration/{tenant_url}/profiles`: View Salesforce profiles for a specific integration
- `/api/integration/{tenant_url}/permission-sets`: View Salesforce permission sets for a specific integration
- `/api/auth/signup`: User registration endpoint
- `/api/auth/login`: User login endpoint
- `/api/auth/token/refresh`: Token refresh endpoint
- `/api/health`: Health check endpoint
- `/api/info`: System information endpoint

## Local Development Setup

For detailed instructions on setting up and running the application locally, see the [Local Development Guide](references/local_development_guide.md).

### Quick Start

1. Clone the repository and navigate to the project directory

2. Create and activate a Python virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows use `venv\Scripts\activate`
```

3. Install dependencies
```bash
pip install -e .[dev]
pip install -r requirements.txt
```

### Running Tests

To run tests locally:
```bash
python -m pytest tests/ -v
```

### Deployment Configuration

The project uses Azure DevOps for CI/CD with the following key configurations:
- Python 3.11 runtime
- Editable package installation
- Comprehensive test reporting
- Slot deployment strategy

### Troubleshooting Common Issues

#### Import Errors
- Ensure you're using the editable package installation (`pip install -e .`)
- Check that `PYTHONPATH` includes the project root directory


```bash
cp config.py.template config.py
# Edit config.py with your Salesforce credentials
```

5. Initialize the database:

```bash
python scripts/init_database.py
```

6. Run the Azure Functions app with FastAPI integration:

```bash
# In one terminal
azurite --location .azurite --silent --blobPort 10100 --queuePort 10101 --tablePort 10102

# In another terminal
func start
```

Alternatively, in VS Code, use the "Attach to Python Functions" launch configuration.

7. Test the application:
   - FastAPI Documentation: `http://localhost:7071/api/docs`
   - Health Check: `http://localhost:7071/api/health`
   - Debug Health Check: `http://localhost:7071/api/debug-health`

## Deployment

### Azure DevOps Pipeline

The project includes an Azure DevOps pipeline configuration in `pipeline-func-sfdc-dev.yml` that handles:

1. Building the application
2. Running tests
3. Deploying to a staging slot
4. Swapping to production after successful deployment

### Manual Deployment

You can also deploy manually using the Azure Functions Core Tools:

```bash
func azure functionapp publish <APP_NAME>
```

### Deployment Verification Tests

The project includes tests to verify that functions are available after deployment. These tests check:

1. General function availability by making HTTP requests to key endpoints
2. Proxy function availability
3. Blueprint function availability

To run these tests manually after deployment, use the provided verification script:

```bash
# Run with default URL (https://func-atomsec-sfdc-dev.azurewebsites.net)
python scripts/verify_deployment.py

# Or specify a custom URL
python scripts/verify_deployment.py --url https://your-function-app-url.azurewebsites.net
```

The verification script will generate a JUnit XML report file (`deployment-verification-results.xml`) that can be used for test result reporting in CI/CD systems.

Alternatively, you can run the tests directly:

```bash
# Set environment variables
export RUN_DEPLOYMENT_TESTS=true
export FUNCTION_APP_URL=https://func-atomsec-sfdc-dev.azurewebsites.net

# Run the tests
python -m pytest tests/test_deployment_verification.py -v
```

These tests are also automatically run as part of the Azure DevOps pipeline after deployment.

## Troubleshooting

For detailed troubleshooting information, see the [Troubleshooting Guide](references/troubleshooting_guide.md).

### Function Discovery and function.json Files

This project uses the blueprint pattern for organizing Azure Functions, which provides a cleaner and more maintainable code structure. However, the Azure Portal and some deployment tools rely on function.json files to discover functions.

To address this, we:

1. **Don't include function.json files in source control** (except for special cases like queue triggers)
2. **Generate them before deployment** using a script
3. **Remove them after deployment** to keep the repository clean

#### Managing function.json Files

To remove existing function.json files (except for special cases):

```bash
# PowerShell
.\scripts\remove_function_json.ps1
```

To generate function.json files when needed:

```bash
# Generate function.json files
python scripts/generate_function_json.py

# Deploy the application with the generated function.json files
func azure functionapp publish <APP_NAME>
```

This approach gives us the best of both worlds - clean code organization with the blueprint pattern, and Azure Portal compatibility when needed.

### Common Issues

1. **Azurite not starting**: Check if ports 10100, 10101, and 10102 are available
2. **Authentication errors**: Run `az login` to authenticate with Azure CLI
3. **Missing dependencies**: Run `pip install -r requirements.txt` to install all dependencies
4. **Import errors**: Make sure you're running from the project root directory
5. **Salesforce authentication**: Ensure your `config.py` file has valid credentials
6. **Database initialization**: Run `python scripts/init_database.py` to initialize the database
7. **API routing issues**:
   - Check that the frontend is using the correct endpoint URLs with the `/api/` prefix
   - Verify that the backend has proxy functions defined for all endpoints
   - Make sure the WrapperFunction/__init__.py file correctly routes requests to the ASGI app
   - Check the logs for detailed request/response information

### Logs

- Azurite logs are stored in the `.azurite` directory
- Function logs are displayed in the terminal when running locally
- In Azure, check the Application Insights logs or the Function App logs in the Azure Portal

## User Authentication

The application includes a complete user authentication system with the following features:

- User registration with secure password storage
- User login with JWT token-based authentication
- Token refresh for extended sessions
- Role-based access control
- Password hashing with salt for security
- Support for multiple hashing algorithms

### Database Schema

The user authentication system uses the following database tables:

- `User_Account`: Stores user profile information
- `App_User_Login`: Stores user login credentials
- `HashingAlgorithm`: Stores available password hashing algorithms
- `Role`: Stores user roles
- `App_User_Roles`: Maps users to roles
- `Permission`: Stores available permissions
- `Role_Permissions`: Maps roles to permissions
- `App_User_Token`: Stores refresh tokens

## Additional Documentation

- [Local Development Guide](references/local_development_guide.md): Detailed instructions for setting up and running the application locally
- [Troubleshooting Guide](references/troubleshooting_guide.md): Solutions to common issues
- [Legacy Code Cleanup](references/legacy_code_cleanup.md): Information about the code cleanup process
- [Test Structure](references/test_structure.md): Documentation about the test structure

#### Test Environment
- Environment variables are set in `tests/conftest.py`
- Local development flag: `IS_LOCAL_DEV=true`

### Azure Function Deployment

The pipeline (`pipeline-func-sfdc-dev.yml`) handles:
- Dependency installation
- Package archiving
- Azure Function deployment
- Slot swapping

## Profile Comparison Process

The AtomSec SFDC API includes a comprehensive profile comparison system that analyzes Salesforce profiles and permission sets against security best practices. This system helps identify potential security risks and compliance issues.

### Overview

The profile comparison process consists of several key components:

1. **Best Practices Configuration**: XML-based configuration defining security standards
2. **Metadata Extraction**: Retrieval of profile and permission set metadata from Salesforce
3. **Comparison Engine**: Analysis of actual vs. recommended settings
4. **Results Storage**: Persistent storage of analysis results
5. **API Endpoints**: RESTful endpoints for accessing comparison data

### Best Practices Configuration

The system uses an XML configuration file (`best_practices/Profiles_PermissionSetRisks-BestPractice.xml`) that defines security best practices for different user types:

```xml
<BestPractices>
    <UserType name="Salesforce">
        <Practice>
            <SalesforceSetting>AccessCMC</SalesforceSetting>
            <StandardValue>FALSE</StandardValue>
            <Description>Allows users to export data weekly.</Description>
            <OWASP>A1: Broken Access Control</OWASP>
            <RiskTypeBasedOnSeverity>High</RiskTypeBasedOnSeverity>
        </Practice>
    </UserType>
</BestPractices>
```

#### Supported User Types

- **Salesforce**: Standard Salesforce user license
- **Salesforce Integration**: Integration user license
- **Guest User License**: Guest user license
- **Blank**: Default/fallback configuration

#### Best Practice Elements

- `SalesforceSetting`: The specific permission or setting name
- `StandardValue`: The recommended value (TRUE/FALSE)
- `Description`: Human-readable description of the permission
- `OWASP`: Related OWASP security category
- `RiskTypeBasedOnSeverity`: Risk level (High, Medium, Low, Informational)

### Task Processing Workflow

The profile comparison process is implemented as background tasks:

#### 1. Authentication Task (`TASK_TYPE_SFDC_AUTHENTICATE`)
- Retrieves Salesforce credentials from Azure Key Vault or local storage
- Authenticates with Salesforce using JWT or Client Credentials flow
- Enqueues subsequent data collection tasks upon successful authentication

#### 2. Metadata Extraction Task (`TASK_TYPE_METADATA_EXTRACTION`)
- Downloads complete Salesforce metadata using the Metadata API
- Stores metadata as ZIP files in Azure Blob Storage
- Includes profiles, permission sets, and other security-relevant metadata

#### 3. Profiles Permission Sets Task (`TASK_TYPE_PROFILES_PERMISSION_SETS`)
- Processes profile metadata from blob storage
- Compares actual profile settings against best practices
- Stores analysis results in the database
- Fetches user assignment counts for each profile

#### 4. Permission Sets Task (`TASK_TYPE_PERMISSION_SETS`)
- Processes permission set metadata from blob storage
- Compares permission set settings against best practices
- Identifies deviations from security standards

### Data Storage

The system stores analysis results in several database tables:

#### PoliciesResult Table
Stores individual policy comparison results:
- `OrgPolicyId`: Unique identifier for the policy result
- `PolicyId`: The specific setting being analyzed
- `ExecutionLogId`: Links to the specific scan execution
- `OrgValue`: The actual value found in the organization
- `StandardValue`: The recommended value from best practices
- `IssueDescription`: Description of the security implication
- `Severity`: Risk level (High, Medium, Low)
- `Type`: Whether it's a Profile or Permission Set result
- `ProfileName`/`PermissionSetName`: The specific profile or permission set

#### ProfileAssignmentCount Table
Stores user assignment information:
- `ProfileName`: Name of the profile
- `AssignmentCount`: Number of active users assigned to the profile
- `Type`: Distinguishes between profile and permission set assignments

### API Endpoints

#### Get Profiles Permissions Data
```
GET /api/profiles-permissions/{org_id}?execution_log_id={execution_log_id}
```

Returns comprehensive profile and permission set analysis data including:
- Policy comparison results
- Profile assignment counts
- Permission set assignments
- Dynamic list of all analyzed settings

Example response:
```json
{
    "status": "completed",
    "settings": ["AccessCMC", "ApiEnabled", "ModifyAllData"],
    "policies": [...],
    "profileAssignments": [...],
    "permissionSetAssignments": [...]
}
```

### Security Analysis Features

#### Risk Assessment
The system categorizes findings by severity:
- **High Risk**: Critical security vulnerabilities (e.g., "Modify All Data" enabled)
- **Medium Risk**: Significant security concerns (e.g., "View All Data" enabled)
- **Low Risk**: Minor security considerations
- **Informational**: Configuration items for awareness

#### OWASP Mapping
Each best practice is mapped to relevant OWASP categories:
- A01:2021-Broken Access Control
- A03:2021-Injection
- A05:2021-Security Misconfiguration
- A06:2021-Vulnerable and Outdated Components

#### License-Based Analysis
The system adapts its analysis based on user license types:
- Different security standards for different license types
- Fallback to default standards when license type is unknown
- Special handling for integration and guest user licenses

### Integration with Frontend

The profile comparison data is consumed by the frontend React application:

#### ProfilesAndPermissionsTab Component
- Displays comprehensive profile and permission set analysis
- Interactive data tables with filtering and sorting
- Visual charts showing risk distribution
- Summary cards with key metrics

#### Data Visualization
- Pie charts showing profile vs. permission set distribution
- Bar charts displaying permission usage across the organization
- Risk severity breakdowns
- Assignment count visualizations

### Performance Considerations

#### Asynchronous Processing
- All analysis tasks run asynchronously in the background
- Progress tracking through task status updates
- Non-blocking API responses during processing

#### Caching Strategy
- Results are cached in the database by execution log ID
- Incremental updates only when new scans are performed
- Efficient querying using indexed fields

#### Scalability
- Blob storage for large metadata files
- Database partitioning by organization ID
- Queue-based task processing for high throughput

### Error Handling and Monitoring

#### Comprehensive Logging
- Detailed logging at each processing stage
- Error tracking with stack traces
- Performance metrics and timing information

#### Graceful Degradation
- Fallback mechanisms when best practices XML is unavailable
- Partial results when some profiles cannot be processed
- Clear error messages for troubleshooting

#### Monitoring Integration
- Task status tracking for operational visibility
- Integration health monitoring
- Performance metrics collection

### Configuration Management

#### Environment-Specific Settings
- Different configurations for development, staging, and production
- Secure credential management through Azure Key Vault
- Environment-aware blob storage and database connections

#### Best Practices Updates
- Version-controlled best practices configuration
- Hot-swappable configuration without service restart
- Audit trail for configuration changes

This comprehensive profile comparison system provides organizations with deep insights into their Salesforce security posture, helping identify and remediate potential security risks before they can be exploited.

## Contributing

1. Create a new branch from `dev`
2. Make your changes
3. Run tests locally
4. Submit a pull request

## License

[Add your license information here]