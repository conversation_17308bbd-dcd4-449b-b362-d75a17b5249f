"""
Production Function App with Service Bus Triggers

This file contains the production version of the function app with actual
Service Bus triggers. Use this for deployment to Azure.
"""

import azure.functions as func
import logging
import json
from shared.background_processor import BackgroundProcessor
from shared.constants import (
    TASK_STATUS_RUNNING,
    TASK_STATUS_COMPLETED,
    TASK_STATUS_FAILED,
    TASK_TYPE_HEALTH_CHECK,
    TASK_TYPE_METADATA_EXTRACTION,
    TASK_TYPE_NOTIFICATION,
    TASK_TYPE_SFDC_AUTHENTICATE,
    TASK_TYPE_PROFILES_PERMISSION_SETS
)
from task_processor import (
    process_health_check_task,
    process_metadata_extraction_task,
    process_notification_task,
    process_sfdc_authenticate_task,
    process_profiles_permission_sets_task
)

# Create the function app
app = func.FunctionApp()

# Service Bus trigger functions for production
@app.service_bus_topic_trigger(
    arg_name="msg", 
    topic_name="atomsec-tasks", 
    subscription_name="sfdc-service-high",
    connection="AzureServiceBusConnectionString"
)
def task_processor_high(msg: func.ServiceBusMessage) -> None:
    """
    Process tasks from high priority Service Bus subscription

    Args:
        msg: Service Bus message
    """
    process_service_bus_task_message(msg, "high", "sfdc-service-high")

@app.service_bus_topic_trigger(
    arg_name="msg", 
    topic_name="atomsec-tasks", 
    subscription_name="sfdc-service-medium",
    connection="AzureServiceBusConnectionString"
)
def task_processor_medium(msg: func.ServiceBusMessage) -> None:
    """
    Process tasks from medium priority Service Bus subscription

    Args:
        msg: Service Bus message
    """
    process_service_bus_task_message(msg, "medium", "sfdc-service-medium")

@app.service_bus_topic_trigger(
    arg_name="msg", 
    topic_name="atomsec-tasks", 
    subscription_name="sfdc-service-low",
    connection="AzureServiceBusConnectionString"
)
def task_processor_low(msg: func.ServiceBusMessage) -> None:
    """
    Process tasks from low priority Service Bus subscription

    Args:
        msg: Service Bus message
    """
    process_service_bus_task_message(msg, "low", "sfdc-service-low")

def process_service_bus_task_message(msg: func.ServiceBusMessage, priority: str, subscription_name: str) -> None:
    """
    Process task message from Service Bus subscription

    Args:
        msg: Service Bus message
        priority: Priority level (high, medium, low)
        subscription_name: Name of the subscription that triggered the function
    """
    logging.info(f"Service Bus task processor triggered for {priority} priority task from subscription: {subscription_name}")

    try:
        # Parse message body
        message_body = msg.get_body().decode('utf-8')
        task_data = json.loads(message_body)

        # Extract task information
        task_id = task_data.get("task_id")
        task_type = task_data.get("task_type")
        org_id = task_data.get("org_id")
        user_id = task_data.get("user_id")
        params = task_data.get("params", {})
        execution_log_id = task_data.get("execution_log_id")

        logging.info(f"Processing {priority}-priority task {task_id} of type {task_type} for organization {org_id}")

        # Initialize background processor
        processor = BackgroundProcessor()

        # Update task status to running
        processor.update_task_status(
            task_id=task_id,
            status=TASK_STATUS_RUNNING,
            progress=10,
            message="Task started"
        )

        # Process task using the main task processor which handles all task types
        from task_processor import main as process_task
        process_task(task_id, org_id, task_type, user_id, params, execution_log_id)

        logging.info(f"Task {task_id} processed successfully")
    except Exception as e:
        logging.error(f"Error processing Service Bus task: {str(e)}")

        # Update task status to failed
        try:
            processor = BackgroundProcessor()
            processor.update_task_status(
                task_id=task_id,
                status=TASK_STATUS_FAILED,
                progress=100,
                message=f"Error processing task: {str(e)}"
            )
        except Exception as inner_e:
            logging.error(f"Error updating task status: {str(inner_e)}")

# Include other endpoints from the main function app
# Import and register other blueprints as needed
