from azure.data.tables import TableServiceClient
from shared.config import get_storage_connection_string

def check_credentials():
    try:
        # Get connection string
        connection_string = get_storage_connection_string()

        # Create table service client
        table_service = TableServiceClient.from_connection_string(connection_string)

        # Get credentials table
        table_client = table_service.get_table_client('Credentials')

        # Check if the table exists
        try:
            # List tables to check if Credentials exists
            tables = list(table_service.list_tables())
            table_names = [table.name for table in tables]
            if 'Credentials' in table_names:
                print(f"Credentials table exists")
            else:
                print(f"Credentials table does not exist. Available tables: {table_names}")
                return
        except Exception as e:
            print(f"Error listing tables: {str(e)}")
            return

        # Query all entities in the table
        entities = list(table_client.query_entities(""))
        print(f"Found {len(entities)} entities in the Credentials table")

        # Print all entities
        for entity in entities:
            print(f"PartitionKey: {entity.get('PartitionKey')}, RowKey: {entity.get('RowKey')}, Value: {entity.get('Value')}, AuthFlow: {entity.get('AuthFlow')}")

        # Check specific integration
        integration_id = 'e6c19379-8262-4050-872f-2767eebddfa1'
        service_name = f'salesforce-{integration_id}'
        print(f"\nChecking credentials for integration {integration_id}")

        # Check client ID
        try:
            client_id_entity = table_client.get_entity(service_name, 'client-id')
            print(f"Client ID: {client_id_entity.get('Value')}")
            print(f"Auth Flow: {client_id_entity.get('AuthFlow')}")
        except Exception as e:
            print(f"Client ID not found: {str(e)}")

        # Check client secret
        try:
            client_secret_entity = table_client.get_entity(service_name, 'client-secret')
            print(f"Client Secret: {client_secret_entity.get('Value')}")
        except Exception as e:
            print(f"Client Secret not found: {str(e)}")

    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    check_credentials()
