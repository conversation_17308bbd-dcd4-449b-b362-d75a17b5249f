[tool:pytest]
# Pytest configuration for deployment testing
# This configuration excludes tests that require heavy dependencies

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Exclude problematic tests during deployment
addopts = 
    --tb=short
    --strict-markers
    --disable-warnings
    --ignore=tests/test_fastapi.py
    --ignore=tests/test_fastapi_integration.py
    --ignore=tests/test_auth_fixed.py
    -v

# Markers for test categorization
markers =
    unit: Unit tests (fast, no external dependencies)
    integration: Integration tests (may require external services)
    deployment: Deployment verification tests
    slow: Slow running tests
    
# Minimum version
minversion = 6.0

# Log configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Timeout for tests
timeout = 300

# Coverage settings (if coverage is installed)
# addopts = --cov=shared --cov-report=term-missing
