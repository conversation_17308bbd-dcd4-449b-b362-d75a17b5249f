[pytest]
log_cli = true
log_cli_level = INFO
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = --import-mode=importlib --no-header --junitxml=test-output.xml
testpaths = tests
pythonpath = .

# Markers for test categorization
markers =
    unit: Unit tests (fast, no external dependencies)
    integration: Integration tests (may require external services)
    deployment: Deployment verification tests
    slow: Slow running tests


