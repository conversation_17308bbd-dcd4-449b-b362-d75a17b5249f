# Python virtual environment
.venv

# Conflicting modules
/asyncio/

# Python cache files
.python_packages
__pycache__
*.pyc
.pytest_cache

# Azurite local DB files
.azurite/
.azurite/__azurite_db_*.json
__blobstorage__/
__queuestorage__/

# Local settings and credentials
# local.settings.json
# config.py

# VS Code settings
.vscode/*
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

certs
static
# Documentation (keep in source control)
# references

# Generated function.json files
*/function.json
!task_processor/function.json
!WrapperFunction/function.json

logs/
function.log
best_practices.xml
SFDC-local-testing.logs
.DS_Store
func.log
temp.txt
FUNCAPP.log
.claude/*