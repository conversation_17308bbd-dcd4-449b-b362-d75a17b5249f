# Data Models Export

## 📊 Core Data Models

### Task Data Model
```typescript
interface Task {
  // Core identifiers
  task_id: string;                    // UUID
  task_type: string;                  // Task type constant
  org_id: string;                     // Organization ID
  user_id: string;                    // User ID
  execution_log_id: string;           // Workflow execution ID
  
  // Status and progress
  status: TaskStatus;                 // pending | running | completed | failed | retry | cancelled
  priority: TaskPriority;             // high | medium | low
  progress: number;                   // 0-100
  retry_count: number;                // Current retry attempt
  
  // Metadata
  created_at: string;                 // ISO timestamp
  updated_at: string;                 // ISO timestamp
  completed_at?: string;              // ISO timestamp
  scheduled_time?: string;            // ISO timestamp for scheduled tasks
  
  // Content
  params: Record<string, any>;        // Task-specific parameters
  message?: string;                   // Status message
  result?: string;                    // JSON-encoded result
  error?: string;                     // Error message if failed
}

enum TaskStatus {
  PENDING = "pending",
  RUNNING = "running",
  COMPLETED = "completed",
  FAILED = "failed",
  RETRY = "retry",
  CANCELLED = "cancelled"
}

enum TaskPriority {
  HIGH = "high",
  MEDIUM = "medium",
  LOW = "low"
}
```

### Policy Data Model
```typescript
interface Policy {
  policy_id: string;                  // UUID
  integration_id: string;             // Organization ID
  user_id: string;                    // User ID
  name: string;                       // Policy name
  description?: string;               // Policy description
  enabled: boolean;                   // Policy enabled flag
  created_at: string;                 // ISO timestamp
  updated_at: string;                 // ISO timestamp
}

interface Rule {
  rule_id: string;                    // UUID
  policy_id: string;                  // Reference to Policy
  task_type: string;                  // Task type this rule applies to
  enabled: boolean;                   // Rule enabled flag
  conditions?: Record<string, any>;   // Rule conditions
  created_at: string;                 // ISO timestamp
  updated_at: string;                 // ISO timestamp
}
```

### Execution Log Data Model
```typescript
interface ExecutionLog {
  execution_log_id: string;           // UUID
  org_id: string;                     // Organization ID
  user_id: string;                    // User ID
  execution_type: string;             // Type of execution
  status: string;                     // Execution status
  start_time: string;                 // ISO timestamp
  end_time?: string;                  // ISO timestamp
  total_tasks: number;                // Total tasks in execution
  completed_tasks: number;            // Completed tasks count
  failed_tasks: number;               // Failed tasks count
  priority: string;                   // Execution priority
}
```

### Integration Data Model
```typescript
interface Integration {
  integration_id: string;             // UUID
  name: string;                       // Integration name
  type: string;                       // Integration type (e.g., "Salesforce")
  tenant_url: string;                 // Tenant URL
  environment: string;                // Environment (production, sandbox)
  is_active: boolean;                 // Active status
  health_score?: number;              // Health score (0-100)
  last_scan?: string;                 // Last scan timestamp
  last_health_check_scan?: string;    // Last health check timestamp
  last_metadata_scan_time?: string;   // Last metadata scan timestamp
  created_at: string;                 // ISO timestamp
  updated_at: string;                 // ISO timestamp
}
```

## 🗄️ Azure Table Storage Schema

### TaskStatus Table
```json
{
  "PartitionKey": "task_{org_id}",
  "RowKey": "{task_id}",
  "TaskId": "{task_id}",
  "TaskType": "metadata_extraction",
  "OrgId": "{org_id}",
  "UserId": "{user_id}",
  "Status": "completed",
  "Priority": "high",
  "Progress": 100,
  "Message": "Task completed successfully",
  "CreatedAt": "2025-07-07T19:16:17.275408",
  "UpdatedAt": "2025-07-07T19:16:17.275411",
  "RetryCount": 0,
  "ExecutionLogId": "{execution_log_id}",
  "Params": "{json_encoded_params}"
}
```

### Policy Table
```json
{
  "PartitionKey": "policy_{integration_id}",
  "RowKey": "{policy_id}",
  "PolicyId": "{policy_id}",
  "IntegrationId": "{integration_id}",
  "UserId": "{user_id}",
  "Name": "Security Policy",
  "Description": "Default security policy",
  "Enabled": true,
  "CreatedAt": "2025-07-07T19:16:17.275408",
  "UpdatedAt": "2025-07-07T19:16:17.275411"
}
```

### Rule Table
```json
{
  "PartitionKey": "rule_{policy_id}",
  "RowKey": "{rule_id}",
  "RuleId": "{rule_id}",
  "PolicyId": "{policy_id}",
  "TaskType": "metadata_extraction",
  "Enabled": true,
  "Conditions": "{json_encoded_conditions}",
  "CreatedAt": "2025-07-07T19:16:17.275408",
  "UpdatedAt": "2025-07-07T19:16:17.275411"
}
```

### Integration Table
```json
{
  "PartitionKey": "integration",
  "RowKey": "{integration_id}",
  "IntegrationId": "{integration_id}",
  "Name": "Salesforce Org",
  "Type": "Salesforce",
  "TenantUrl": "mtx2-dev-ed.my.salesforce.com",
  "Environment": "production",
  "IsActive": true,
  "HealthScore": 85,
  "LastScan": "2025-07-07T19:16:17.275408",
  "LastHealthCheckScan": "2025-07-07T19:16:17.275408",
  "LastMetadataScanTime": "2025-07-07T19:16:17.275408",
  "CreatedAt": "2025-07-07T19:16:17.275408",
  "UpdatedAt": "2025-07-07T19:16:17.275411"
}
```

## 📋 Database Schema (SQL)

### Tasks Table
```sql
CREATE TABLE Tasks (
    TaskId NVARCHAR(36) PRIMARY KEY,
    TaskType NVARCHAR(50) NOT NULL,
    OrgId NVARCHAR(36) NOT NULL,
    UserId NVARCHAR(36) NOT NULL,
    ExecutionLogId NVARCHAR(36) NOT NULL,
    Status NVARCHAR(20) NOT NULL DEFAULT 'pending',
    Priority NVARCHAR(10) NOT NULL DEFAULT 'medium',
    Progress INT NOT NULL DEFAULT 0,
    RetryCount INT NOT NULL DEFAULT 0,
    Params NVARCHAR(MAX),
    Message NVARCHAR(500),
    Result NVARCHAR(MAX),
    Error NVARCHAR(500),
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CompletedAt DATETIME2,
    ScheduledTime DATETIME2,
    INDEX IX_Tasks_OrgId (OrgId),
    INDEX IX_Tasks_Status (Status),
    INDEX IX_Tasks_ExecutionLogId (ExecutionLogId),
    INDEX IX_Tasks_TaskType (TaskType)
);
```

### Policies Table
```sql
CREATE TABLE Policies (
    PolicyId NVARCHAR(36) PRIMARY KEY,
    IntegrationId NVARCHAR(36) NOT NULL,
    UserId NVARCHAR(36) NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500),
    Enabled BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    INDEX IX_Policies_IntegrationId (IntegrationId),
    INDEX IX_Policies_UserId (UserId)
);
```

### Rules Table
```sql
CREATE TABLE Rules (
    RuleId NVARCHAR(36) PRIMARY KEY,
    PolicyId NVARCHAR(36) NOT NULL,
    TaskType NVARCHAR(50) NOT NULL,
    Enabled BIT NOT NULL DEFAULT 1,
    Conditions NVARCHAR(MAX),
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY (PolicyId) REFERENCES Policies(PolicyId),
    INDEX IX_Rules_PolicyId (PolicyId),
    INDEX IX_Rules_TaskType (TaskType)
);
```

### Integrations Table
```sql
CREATE TABLE Integrations (
    IntegrationId NVARCHAR(36) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Type NVARCHAR(50) NOT NULL,
    TenantUrl NVARCHAR(200) NOT NULL,
    Environment NVARCHAR(20) NOT NULL DEFAULT 'production',
    IsActive BIT NOT NULL DEFAULT 1,
    HealthScore INT,
    LastScan DATETIME2,
    LastHealthCheckScan DATETIME2,
    LastMetadataScanTime DATETIME2,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);
```

### ExecutionLogs Table
```sql
CREATE TABLE ExecutionLogs (
    ExecutionLogId NVARCHAR(36) PRIMARY KEY,
    OrgId NVARCHAR(36) NOT NULL,
    UserId NVARCHAR(36) NOT NULL,
    ExecutionType NVARCHAR(50) NOT NULL,
    Status NVARCHAR(20) NOT NULL DEFAULT 'pending',
    StartTime DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    EndTime DATETIME2,
    TotalTasks INT NOT NULL DEFAULT 0,
    CompletedTasks INT NOT NULL DEFAULT 0,
    FailedTasks INT NOT NULL DEFAULT 0,
    Priority NVARCHAR(10) NOT NULL DEFAULT 'medium',
    INDEX IX_ExecutionLogs_OrgId (OrgId),
    INDEX IX_ExecutionLogs_Status (Status)
);
```

## 🔄 Data Flow Models

### Task Creation Flow
```mermaid
graph TD
    A[Client Request] --> B[Validate Input]
    B --> C[Check Idempotency]
    C --> D[Check Policy Rules]
    D --> E[Generate Task ID]
    E --> F[Create in Database]
    F --> G[Send to Queue]
    G --> H[Return Task ID]
```

### Task Processing Flow
```mermaid
graph TD
    A[Task Poller] --> B[Get Pending Tasks]
    B --> C[Check Dependencies]
    C --> D{Dependencies Met?}
    D -->|Yes| E[Process Task]
    D -->|No| F[Skip Task]
    E --> G[Update Status]
    G --> H[Store Results]
    F --> I[Retry Later]
```

### Data Storage Flow
```mermaid
graph TD
    A[Task Processor] --> B[Process Data]
    B --> C[Transform Data]
    C --> D[Validate Data]
    D --> E[Store in Database]
    E --> F[Update Task Status]
    F --> G[Log Results]
```

## 🧪 Data Validation

### Task Validation
```python
def validate_task_data(task_data: dict) -> tuple[bool, str]:
    """Validate task data before creation"""
    required_fields = ['task_type', 'org_id', 'user_id', 'execution_log_id']
    
    for field in required_fields:
        if field not in task_data or not task_data[field]:
            return False, f"Missing required field: {field}"
    
    # Validate task type
    valid_task_types = [
        'sfdc_authenticate', 'metadata_extraction', 'health_check',
        'profiles', 'profiles_permission_sets', 'permission_sets',
        'mfa_enforcement', 'device_activation', 'login_ip_ranges',
        'login_hours', 'session_timeout', 'api_whitelisting',
        'password_policy', 'pmd_apex_security', 'overview',
        'data_export', 'report_generation', 'scheduled_scan', 'notification'
    ]
    
    if task_data['task_type'] not in valid_task_types:
        return False, f"Invalid task type: {task_data['task_type']}"
    
    # Validate UUIDs
    uuid_fields = ['org_id', 'user_id', 'execution_log_id']
    for field in uuid_fields:
        if not is_valid_uuid(task_data[field]):
            return False, f"Invalid UUID format for {field}"
    
    return True, "Validation successful"
```

### Policy Validation
```python
def validate_policy_data(policy_data: dict) -> tuple[bool, str]:
    """Validate policy data before creation"""
    required_fields = ['integration_id', 'user_id', 'name']
    
    for field in required_fields:
        if field not in policy_data or not policy_data[field]:
            return False, f"Missing required field: {field}"
    
    # Validate name length
    if len(policy_data['name']) > 100:
        return False, "Policy name too long (max 100 characters)"
    
    # Validate description length
    if 'description' in policy_data and len(policy_data['description']) > 500:
        return False, "Policy description too long (max 500 characters)"
    
    return True, "Validation successful"
```

## 📊 Data Migration

### Azure Table to SQL Migration
```python
def migrate_table_to_sql(table_name: str, connection_string: str):
    """Migrate data from Azure Table Storage to SQL Database"""
    
    # Get table repository
    table_repo = TableStorageRepository(table_name)
    
    # Get SQL connection
    sql_conn = get_sql_connection(connection_string)
    
    # Get all entities from table
    entities = list(table_repo.query_entities())
    
    for entity in entities:
        # Transform entity to SQL format
        sql_data = transform_entity_to_sql(entity, table_name)
        
        # Insert into SQL database
        insert_sql_record(sql_conn, table_name, sql_data)
    
    print(f"Migrated {len(entities)} records from {table_name}")
```

### Data Transformation
```python
def transform_entity_to_sql(entity: dict, table_name: str) -> dict:
    """Transform Azure Table entity to SQL format"""
    
    if table_name == "TaskStatus":
        return {
            'TaskId': entity.get('RowKey'),
            'TaskType': entity.get('TaskType'),
            'OrgId': entity.get('OrgId'),
            'UserId': entity.get('UserId'),
            'ExecutionLogId': entity.get('ExecutionLogId'),
            'Status': entity.get('Status'),
            'Priority': entity.get('Priority'),
            'Progress': entity.get('Progress', 0),
            'RetryCount': entity.get('RetryCount', 0),
            'Params': entity.get('Params'),
            'Message': entity.get('Message'),
            'Result': entity.get('Result'),
            'Error': entity.get('Error'),
            'CreatedAt': parse_datetime(entity.get('CreatedAt')),
            'UpdatedAt': parse_datetime(entity.get('UpdatedAt')),
            'CompletedAt': parse_datetime(entity.get('CompletedAt')),
            'ScheduledTime': parse_datetime(entity.get('ScheduledTime'))
        }
    
    # Add other table transformations as needed
    return entity
```

## 🔍 Data Querying

### Common Queries
```python
# Get pending tasks for an org
def get_pending_tasks(org_id: str) -> List[dict]:
    query = f"PartitionKey eq 'task_{org_id}' and Status eq 'pending'"
    return table_repo.query_entities(query)

# Get completed tasks by type
def get_completed_tasks_by_type(org_id: str, task_type: str) -> List[dict]:
    query = f"PartitionKey eq 'task_{org_id}' and TaskType eq '{task_type}' and Status eq 'completed'"
    return table_repo.query_entities(query)

# Get latest task by type
def get_latest_task_by_type(org_id: str, task_type: str, status: str = None) -> Optional[dict]:
    query = f"PartitionKey eq 'task_{org_id}' and TaskType eq '{task_type}'"
    if status:
        query += f" and Status eq '{status}'"
    
    entities = list(table_repo.query_entities(query))
    if entities:
        # Sort by CreatedAt descending and return first
        entities.sort(key=lambda x: x.get('CreatedAt', ''), reverse=True)
        return entities[0]
    return None

# Get tasks by execution log
def get_tasks_by_execution_log(execution_log_id: str) -> List[dict]:
    # Note: This requires scanning all entities since ExecutionLogId is not a partition key
    all_entities = list(table_repo.query_entities())
    return [e for e in all_entities if e.get('ExecutionLogId') == execution_log_id]
```

## 🎯 Key Design Principles

### 1. **Partitioning Strategy**
- Use `task_{org_id}` as PartitionKey for task tables
- Use `policy_{integration_id}` as PartitionKey for policy tables
- Use `rule_{policy_id}` as PartitionKey for rule tables
- Use `integration` as PartitionKey for integration table

### 2. **Indexing Strategy**
- Index on frequently queried fields
- Composite indexes for complex queries
- Covering indexes for performance

### 3. **Data Consistency**
- Use transactions where possible
- Implement idempotency checks
- Validate data before storage

### 4. **Scalability**
- Partition data by organization
- Use appropriate data types
- Optimize query patterns

### 5. **Security**
- Encrypt sensitive data
- Implement access controls
- Audit data access

---

This data models export provides a complete foundation for implementing data storage and management in different architectural models.
