# Requirements Document

## Introduction

This feature improves the task processing architecture by transitioning from a database-polling model to a queue-based system. The current system relies on the TaskStatus table for both task coordination and status tracking, which creates tight coupling and potential performance bottlenecks. The new architecture will use Azure Storage Queues for task coordination while maintaining the TaskStatus table purely for reference and UI display purposes.

## Requirements

### Requirement 1

**User Story:** As a system administrator, I want task processing to use queue-based coordination instead of database polling, so that the system can handle higher throughput and reduce database load.

#### Acceptance Criteria

1. WHEN the DB service initiates a scan task THEN it SHALL enqueue the task to an Azure Storage Queue with executionLogId
2. WHEN a task is enqueued THEN the system SHALL include all necessary metadata (executionLogId, task type, parameters) in the queue message
3. WHEN the queue-based system is active THEN database polling for task pickup SHALL be eliminated
4. WHEN tasks are processed via queues THEN the system SHALL maintain the same throughput or better compared to the current database-polling approach

### Requirement 2

**User Story:** As a developer, I want the SFDC service to pick up scan tasks from the queue and maintain execution context, so that all child tasks can be properly tracked and correlated.

#### Acceptance Criteria

1. WHEN the SFDC service processes a queue message THEN it SHALL extract the executionLogId from the message
2. WHEN the SFDC service creates child tasks THEN it SHALL propagate the executionLogId to all child tasks
3. WHEN child tasks are created THEN they SHALL maintain the parent-child relationship through the executionLogId
4. WHEN the SFDC service encounters a malformed queue message THEN it SHALL handle the error gracefully and log appropriate details

### Requirement 3

**User Story:** As a system operator, I want parent tasks to be marked as successfully completed only when all child tasks complete successfully, so that task status accurately reflects the overall operation state.

#### Acceptance Criteria

1. WHEN all child tasks associated with an executionLogId complete successfully THEN the parent task SHALL be marked as "Successfully Completed"
2. WHEN any child task fails THEN the parent task SHALL be marked as "Failed" with appropriate error details
3. WHEN child tasks are still in progress THEN the parent task SHALL remain in "In Progress" status
4. WHEN task completion status is updated THEN the system SHALL update the TaskStatus table for reference purposes

### Requirement 4

**User Story:** As an application user, I want to view task progress and status through the UI, so that I can monitor the execution of my scan operations.

#### Acceptance Criteria

1. WHEN a user views the task status interface THEN it SHALL display current task progress from the TaskStatus table
2. WHEN task status is updated THEN the UI SHALL reflect the changes without requiring manual refresh
3. WHEN viewing task details THEN users SHALL see parent-child task relationships and their individual statuses
4. WHEN a task fails THEN the UI SHALL display meaningful error messages and failure reasons

### Requirement 5

**User Story:** As a system architect, I want the TaskStatus table to serve only as a reference for UI display, so that the system maintains separation of concerns between task coordination and status reporting.

#### Acceptance Criteria

1. WHEN the new queue-based system is implemented THEN the TaskStatus table SHALL NOT be used for task pickup coordination
2. WHEN tasks are processed THEN the TaskStatus table SHALL be updated for reference and display purposes only
3. WHEN querying task status for UI display THEN the system SHALL use the TaskStatus table as the primary source
4. WHEN the TaskStatus table is unavailable THEN task processing SHALL continue uninterrupted using the queue system

### Requirement 6

**User Story:** As a system administrator, I want proper error handling and retry mechanisms for queue processing, so that temporary failures don't result in lost tasks.

#### Acceptance Criteria

1. WHEN a queue message processing fails THEN the system SHALL implement exponential backoff retry logic
2. WHEN a message exceeds maximum retry attempts THEN it SHALL be moved to a poison message queue
3. WHEN poison messages are detected THEN the system SHALL log detailed error information for troubleshooting
4. WHEN queue services are temporarily unavailable THEN the system SHALL handle the outage gracefully and resume processing when available

### Requirement 7

**User Story:** As a developer, I want proper logging and monitoring for the queue-based task processing, so that I can troubleshoot issues and monitor system performance.

#### Acceptance Criteria

1. WHEN tasks are enqueued THEN the system SHALL log the executionLogId and task metadata
2. WHEN tasks are dequeued and processed THEN the system SHALL log processing start and completion events
3. WHEN errors occur during queue processing THEN the system SHALL log detailed error information including executionLogId
4. WHEN monitoring the system THEN administrators SHALL have visibility into queue depth, processing rates, and error rates