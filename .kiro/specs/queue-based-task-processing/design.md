# Design Document

## Overview

This design document outlines the architecture for transitioning from a database-polling task processing model to a queue-based system. The new architecture will use Azure Storage Queues for task coordination while maintaining the TaskStatus table purely for reference and UI display purposes.

## Architecture

### Current Architecture Issues
- Database polling creates unnecessary load on the database
- TaskStatus table serves dual purpose (coordination + reference)
- Tight coupling between task coordination and status tracking
- Limited scalability due to polling overhead

### New Queue-Based Architecture

```mermaid
graph TB
    subgraph "DB Service"
        A[Scan Initiation] --> B[Create ExecutionLog]
        B --> C[Create TaskStatus Entry]
        C --> D[Enqueue Task to Azure Storage Queue]
    end
    
    subgraph "Azure Storage Queues"
        E[task-queue-high]
        F[task-queue-medium]
        G[task-queue-low]
    end
    
    subgraph "SFDC Service"
        H[Queue Listener] --> I[Process Task Message]
        I --> J[Extract ExecutionLogId]
        J --> K[Create Child Tasks]
        K --> L[Update TaskStatus Table]
    end
    
    subgraph "UI/Frontend"
        M[Task Status Display] --> N[Query TaskStatus Table]
    end
    
    D --> E
    D --> F
    D --> G
    
    E --> H
    F --> H
    G --> H
    
    L --> N
```

## Components and Interfaces

### 1. Enhanced Queue Manager (DB Service)

**Location**: `atomsec-func-db-r/shared/queue_manager.py`

**Responsibilities**:
- Manage Azure Storage Queue connections
- Handle priority-based task routing
- Implement retry mechanisms with exponential backoff
- Manage poison message handling

**Key Methods**:
```python
class QueueManager:
    def enqueue_scan_task(self, execution_log_id: str, task_data: Dict[str, Any], priority: str) -> bool
    def handle_poison_messages(self, queue_name: str) -> None
    def get_queue_metrics(self) -> Dict[str, Any]
```

### 2. Task Coordination Service (DB Service)

**Location**: `atomsec-func-db-r/shared/task_coordination_service.py` (new)

**Responsibilities**:
- Orchestrate task creation and enqueueing
- Maintain execution context through executionLogId
- Handle task lifecycle management

**Key Methods**:
```python
class TaskCoordinationService:
    def initiate_scan_task(self, org_id: str, user_id: str, task_type: str, params: Dict[str, Any]) -> str
    def create_execution_context(self, org_id: str, user_id: str, task_type: str) -> str
    def enqueue_with_context(self, execution_log_id: str, task_data: Dict[str, Any]) -> bool
```

### 3. Queue Message Processor (SFDC Service)

**Location**: `atomsec-func-sfdc/shared/queue_message_processor.py` (new)

**Responsibilities**:
- Listen to Azure Storage Queues
- Process incoming task messages
- Extract and propagate executionLogId
- Handle message acknowledgment and error scenarios

**Key Methods**:
```python
class QueueMessageProcessor:
    def process_queue_message(self, message: Dict[str, Any]) -> bool
    def extract_execution_context(self, message: Dict[str, Any]) -> str
    def propagate_execution_context(self, execution_log_id: str, child_tasks: List[Dict[str, Any]]) -> None
    def handle_message_error(self, message: Dict[str, Any], error: Exception) -> None
```

### 4. Enhanced Task Status Service

**Location**: `atomsec-func-db-r/shared/task_status_service.py` (existing, enhanced)

**Responsibilities**:
- Maintain TaskStatus table for UI reference
- Track parent-child task relationships
- Implement completion status aggregation logic

**Enhanced Methods**:
```python
class TaskStatusService:
    def create_parent_task(self, execution_log_id: str, task_data: Dict[str, Any]) -> str
    def create_child_task(self, parent_execution_log_id: str, task_data: Dict[str, Any]) -> str
    def update_parent_completion_status(self, execution_log_id: str) -> bool
    def get_task_hierarchy(self, execution_log_id: str) -> Dict[str, Any]
```

### 5. Execution Context Manager

**Location**: `atomsec-func-db-r/shared/execution_context_manager.py` (new)

**Responsibilities**:
- Manage execution context lifecycle
- Track parent-child relationships
- Determine overall execution status

**Key Methods**:
```python
class ExecutionContextManager:
    def create_execution_context(self, org_id: str, task_type: str, user_id: str) -> str
    def add_child_task(self, execution_log_id: str, child_task_id: str) -> bool
    def check_completion_status(self, execution_log_id: str) -> str
    def get_execution_summary(self, execution_log_id: str) -> Dict[str, Any]
```

## Data Models

### Queue Message Format

```json
{
  "task_id": "uuid",
  "execution_log_id": "uuid",
  "task_type": "scan_type",
  "org_id": "organization_id",
  "user_id": "user_id",
  "priority": "high|medium|low",
  "params": {
    "access_token": "token",
    "instance_url": "url",
    "environment": "production|sandbox"
  },
  "created_at": "2025-01-17T10:00:00Z",
  "retry_count": 0,
  "parent_execution_log_id": "uuid_or_null"
}
```

### Enhanced TaskStatus Table Schema

```json
{
  "TaskId": "uuid",
  "ExecutionLogId": "uuid",
  "ParentExecutionLogId": "uuid_or_null",
  "TaskType": "string",
  "OrgId": "string",
  "UserId": "string",
  "Status": "pending|running|completed|failed|cancelled",
  "Priority": "high|medium|low",
  "Progress": "0-100",
  "Message": "string",
  "Result": "json_string",
  "CreatedAt": "datetime",
  "UpdatedAt": "datetime",
  "CompletedAt": "datetime_or_null",
  "RetryCount": "integer",
  "Params": "json_string",
  "IsParentTask": "boolean",
  "ChildTaskCount": "integer",
  "CompletedChildCount": "integer"
}
```

### Execution Context Table Schema

```json
{
  "ExecutionLogId": "uuid",
  "OrgId": "string",
  "TaskType": "string",
  "UserId": "string",
  "Status": "pending|running|completed|failed",
  "ParentTaskId": "uuid",
  "ChildTaskIds": "json_array",
  "CreatedAt": "datetime",
  "UpdatedAt": "datetime",
  "CompletedAt": "datetime_or_null",
  "TotalChildTasks": "integer",
  "CompletedChildTasks": "integer",
  "FailedChildTasks": "integer"
}
```

## Error Handling

### Retry Strategy

1. **Exponential Backoff**: Implement exponential backoff for transient failures
2. **Maximum Retry Attempts**: Limit retries to prevent infinite loops
3. **Poison Message Handling**: Move messages to poison queue after max retries
4. **Dead Letter Queue**: Implement dead letter queue for manual intervention

### Error Scenarios

1. **Queue Service Unavailable**: Graceful degradation with local queuing
2. **Malformed Messages**: Log and move to poison queue
3. **Task Processing Failures**: Update status and implement retry logic
4. **Database Unavailable**: Continue queue processing, update status when available

## Testing Strategy

### Unit Tests

1. **Queue Manager Tests**
   - Message serialization/deserialization
   - Priority routing logic
   - Error handling scenarios

2. **Task Coordination Tests**
   - Execution context creation
   - Task enqueueing logic
   - Parent-child relationship management

3. **Message Processor Tests**
   - Message processing logic
   - Context propagation
   - Error handling

### Integration Tests

1. **End-to-End Flow Tests**
   - Complete scan task lifecycle
   - Parent-child task coordination
   - Status aggregation logic

2. **Queue Integration Tests**
   - Azure Storage Queue connectivity
   - Message delivery guarantees
   - Poison message handling

3. **Database Integration Tests**
   - TaskStatus table operations
   - Execution context management
   - Status update propagation

### Performance Tests

1. **Queue Throughput Tests**
   - Message processing rate
   - Queue depth under load
   - Priority queue performance

2. **Database Load Tests**
   - TaskStatus table query performance
   - Concurrent update handling
   - Index optimization validation

## Monitoring and Observability

### Metrics

1. **Queue Metrics**
   - Message count per priority queue
   - Processing rate and latency
   - Error rate and retry count

2. **Task Metrics**
   - Task completion rate
   - Average task duration
   - Parent-child task correlation

3. **System Metrics**
   - Database query performance
   - Queue service availability
   - Memory and CPU utilization

### Logging

1. **Structured Logging**
   - Include executionLogId in all log entries
   - Consistent log format across services
   - Correlation IDs for tracing

2. **Log Levels**
   - DEBUG: Detailed processing information
   - INFO: Task lifecycle events
   - WARN: Retry attempts and recoverable errors
   - ERROR: Unrecoverable errors and failures

### Alerting

1. **Queue Depth Alerts**
   - High queue depth indicating processing delays
   - Poison message queue growth

2. **Task Failure Alerts**
   - High task failure rate
   - Parent task completion timeouts

3. **System Health Alerts**
   - Queue service connectivity issues
   - Database performance degradation

## Migration Strategy

### Phase 1: Infrastructure Setup
- Deploy enhanced queue manager
- Create new queue message processor
- Update TaskStatus table schema

### Phase 2: Parallel Processing
- Run both polling and queue-based systems
- Gradually migrate task types to queue-based processing
- Monitor performance and reliability

### Phase 3: Complete Migration
- Disable database polling
- Remove legacy polling code
- Optimize queue-based processing

### Phase 4: Cleanup
- Remove unused database polling infrastructure
- Optimize TaskStatus table for reference-only usage
- Implement final monitoring and alerting

## Security Considerations

1. **Queue Access Control**
   - Implement proper Azure Storage Queue permissions
   - Use managed identities for authentication
   - Encrypt sensitive data in queue messages

2. **Data Protection**
   - Sanitize sensitive information in logs
   - Implement message encryption for sensitive parameters
   - Secure storage of access tokens and credentials

3. **Audit Trail**
   - Log all task creation and processing events
   - Maintain execution context audit trail
   - Implement compliance reporting capabilities