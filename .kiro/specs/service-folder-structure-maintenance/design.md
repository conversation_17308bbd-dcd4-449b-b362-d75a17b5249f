# Design Document

## Overview

This design focuses on maintaining and improving the folder structure for both the SFDC service (atomsec-func-sfdc) and DB service (atomsec-func-db-r). After analyzing the current state, we found that the core source code structure is already well-organized. The main issues are scattered test files, temporary/deprecated files, and inconsistent placement of utility scripts and documentation.

## Current State Assessment

### What's Already Good
- **API Structure**: Both services have well-organized `api/` directories
- **Shared Components**: Both services have comprehensive `shared/` directories
- **Documentation**: SFDC service has excellent `docs/` structure
- **Infrastructure**: SFDC service has well-organized `infrastructure/` directory
- **Configuration**: SFDC service has good `config/` organization
- **Scripts**: Both services have organized `scripts/` directories

### What Needs Improvement
- **Test Organization**: Test files scattered in root directories
- **Temporary Files**: Cache, log, and result files cluttering root
- **Deprecated Files**: Old disabled files need cleanup
- **Utility Scripts**: Some utility scripts at root level
- **Documentation**: DB service documentation scattered at root

## Design Principles

### 1. Preserve Good Structure
- Keep existing well-organized directories intact
- Avoid unnecessary restructuring that could break imports
- Focus on cleanup and organization rather than major changes

### 2. Consistent Organization
- Both services should follow similar patterns
- Test structure should mirror source structure
- Clear separation between production and development files

### 3. Clean Root Directory
- Minimize files at root level
- Move temporary/cache files to appropriate directories
- Remove deprecated and unused files

### 4. Maintainable Structure
- Clear naming conventions
- Logical grouping of related files
- Easy navigation for developers

## Target Folder Structure

### SFDC Service (atomsec-func-sfdc)
```
atomsec-func-sfdc/
├── api/                          # ✅ Already well-organized
├── shared/                       # ✅ Already well-organized
├── blueprints/                   # ✅ Already well-organized
├── task_processor/               # ✅ Already exists
├── pmd_components/               # ✅ Already well-organized
├── repositories/                 # ✅ Already exists
├── routers/                      # ✅ Already exists
├── tests/                        # ✅ Structure exists, needs population
│   ├── unit/
│   │   ├── api/                  # Move test_api.py, test_endpoints.py, etc.
│   │   ├── shared/               # Move test_enhanced_*.py files
│   │   ├── blueprints/
│   │   └── task_processor/
│   ├── integration/              # Move test_integration.py
│   ├── performance/              # ✅ Already exists
│   ├── security/                 # ✅ Already exists
│   └── architecture/             # ✅ Already exists
├── docs/                         # ✅ Already comprehensive
├── config/                       # ✅ Already well-organized
├── infrastructure/               # ✅ Already well-organized
├── scripts/                      # ✅ Exists, add utility scripts
├── examples/                     # ✅ Already exists
├── cache/                        # NEW: For azurite and temp files
├── logs/                         # NEW: For log files
├── reports/                      # NEW: For result/report files
├── function_app.py               # ✅ Main entry point
├── host.json                     # ✅ Function configuration
├── requirements.txt              # ✅ Dependencies
└── README.md                     # ✅ Documentation
```

### DB Service (atomsec-func-db-r)
```
atomsec-func-db-r/
├── api/                          # ✅ Already well-organized
├── shared/                       # ✅ Already well-organized
├── repositories/                 # ✅ Already exists
├── tests/                        # ✅ Structure exists, needs population
│   ├── unit/
│   │   ├── api/
│   │   ├── shared/               # Move test_enhanced_*.py files
│   │   └── repositories/
│   └── integration/              # Move run_integration_tests.py
├── scripts/                      # ✅ Already exists
├── config/                       # ✅ Minimal config needs
├── docs/                         # NEW: Move scattered .md files
├── examples/                     # NEW: Move example_*.py files
├── cache/                        # NEW: For azurite files
├── logs/                         # NEW: For log files
├── function_app.py               # ✅ Main entry point
├── host.json                     # ✅ Function configuration
├── requirements.txt              # ✅ Dependencies
└── README.md                     # ✅ Documentation
```

## File Movement Strategy

### Phase 1: Cleanup Operations
1. **Remove Deprecated Files**
   - Delete `*.disabled.old` files
   - Evaluate and remove `*.disabled` files
   - Remove empty function directories

2. **Organize Temporary Files**
   - Move cache directories to `cache/`
   - Move log files to `logs/`
   - Move result files to `reports/`

3. **Fix Naming Issues**
   - Rename problematic directory names (spaces, special chars)

### Phase 2: Test Organization
1. **SFDC Service Tests**
   - Move root test files to appropriate `tests/unit/` subdirectories
   - Ensure test structure mirrors source structure
   - Update import statements

2. **DB Service Tests**
   - Move root test files to `tests/unit/shared/`
   - Move integration test runner to `tests/integration/`
   - Create consistent directory structure

### Phase 3: Utility and Documentation Organization
1. **Utility Scripts**
   - Move utility scripts from root to `scripts/`
   - Update any references to moved scripts

2. **Documentation**
   - Move scattered documentation files to `docs/`
   - Organize by category (API, deployment, etc.)

3. **Examples**
   - Move example files to `examples/` directory
   - Separate from production code

## Import Strategy

### Minimal Import Changes
Since the core structure is already good, import changes will be minimal:

1. **Test Files**: Update imports in moved test files
2. **Utility Scripts**: Update imports in moved utility scripts
3. **Example Files**: Update imports in moved example files
4. **No Core Changes**: Main application imports remain unchanged

### Import Patterns
```python
# Test files will use relative imports
from shared.auth_utils import AuthService
from api.account_endpoints import AccountAPI

# Moved scripts will maintain same import patterns
from shared.config import get_config
```

## Risk Mitigation

### Low-Risk Approach
- **Preserve Core Structure**: No changes to main application structure
- **Incremental Changes**: Move files in small batches
- **Test After Each Phase**: Validate functionality after each major change

### Rollback Strategy
- **Git Branches**: Use feature branches for each phase
- **Backup Points**: Create backup branches before major changes
- **Incremental Commits**: Small commits for easy rollback

### Testing Strategy
- **Unit Tests**: Run after each file movement
- **Integration Tests**: Validate after each phase
- **Function App**: Test startup after import changes

## Success Metrics

### Immediate Goals
- Clean root directories with minimal files
- All tests properly organized and discoverable
- No broken imports or functionality
- Consistent structure between services

### Long-term Goals
- Easier developer onboarding
- Consistent file placement patterns
- Reduced maintenance overhead
- Clear separation of concerns

## Implementation Considerations

### Azure Functions Compatibility
- Maintain `function_app.py` at root level
- Keep `host.json` configuration unchanged
- Preserve blueprint registration patterns

### CI/CD Compatibility
- Ensure test discovery still works
- Validate deployment scripts function correctly
- Check for hardcoded paths in pipelines

### Development Workflow
- Maintain local development setup
- Preserve debugging capabilities
- Keep IDE navigation patterns working

## Validation Approach

### Automated Validation
- Run full test suite after each phase
- Validate function app startup
- Check import resolution

### Manual Validation
- Test API endpoints
- Verify local development setup
- Validate deployment process

### Documentation Updates
- Update README files
- Document new structure
- Create developer guidelines