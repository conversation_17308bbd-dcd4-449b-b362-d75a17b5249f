# Implementation Plan

## Architecture Context (from .cursor/rules)
The SFDC service follows a **microservices architecture** with specific patterns:
- **Blueprint-based modular organization** for API endpoints
- **Proxy architecture** where DB service acts as single entry point for frontend
- **Internal service communication** between DB service and SFDC backend
- **Separation of concerns** with SFDC backend handling Salesforce-specific logic
- **Centralized authentication and CORS** handled by DB service

## Current State Analysis

### Files to Be Moved/Cleaned

**SFDC Service (atomsec-func-sfdc):**
- Test files at root: `test_enhanced_error_handling.py`, `test_account_management.py`, `test_api.py`, `test_endpoints.py`, `test_integration.py`, `test_local_startup.py`, `test_queue_message_processor.py`, `test_queue_processing.py`
- Disabled/deprecated files: `app.py.disabled.old`, `function_app_backup_full.py.disabled.old`, `function_app_complex.py.disabled.old`, `WrapperFunction_backup.py.disabled.old`, `api/general_endpoints.py.disabled`
- Cache/temp files: `c:azurite/`, `__azurite_db_*.json`, `c:azuritedebug.log`, `__blobstorage__/`, `__pycache__/`
- Function directories: `AccountsApiFunction/`, `DirectAccountsFunction/`, `DirectRolesFunction/`, `IntegrationFunction/`, `RolesApiFunction/`, `RolesManagementFunction/`, `WrapperFunction/`, `home/` (mostly empty, need evaluation)
- Configuration scattered: `local.settings.json`, various `*.json` result files, `AzuriteConfig`
- Temporary/result files: `architecture_compliance_results.json`, `integration_configuration_results.json`, `integration_validation_report.json`, `monitoring_alerting_validation_report.json`, `service_integration_validation_results.json`, `deployment_pipeline_validation_report.json`
- Problematic directories: `Assets|Project Maintenance/` (contains spaces), `references/`
- Utility scripts at root: `check_credentials.py`, `check_policies_result.py`, `create_policies_result.py`, `create_queues.py`, `setup_local_dev.py`, `simple_test.py`, `task_management.py`, `service_bus_processor.py`

**DB Service (atomsec-func-db-r):**
- Test files at root: `test_enhanced_database_security.py`, `test_enhanced_queue_manager.py`, `test_enhanced_task_coordination_service.py`, `test_enhanced_task_status_service.py`, `test_execution_context_manager.py`, `test_task_coordination_service.py`
- Example files at root: `example_enhanced_database_security_usage.py`, `example_enhanced_execution_context_usage.py`, `example_enhanced_queue_manager_usage.py`, `example_enhanced_task_coordination_usage.py`, `example_enhanced_task_status_usage.py`, `example_execution_context_manager_usage.py`, `example_task_coordination_usage.py`
- Log files: `backendfunction.log`, `DB-local-testing.log`, `dbfunction.log`, `func.log`
- Cache directory: `c:azurite/` needs proper handling
- Documentation files at root: `API_CONSOLIDATION_SUMMARY.md`, `CONSOLIDATED_API_ENDPOINTS.md`, `ENHANCED_DATABASE_SECURITY_IMPLEMENTATION.md`, `MIGRATION_GUIDE.md`, `QUEUE_BASED_PROCESSING_DEPLOYMENT_GUIDE.md`, `SFDC_PROXY_ARCHITECTURE.md`

### Already Implemented Structure Elements
- ✅ `api/` directory exists in both services (well-organized)
- ✅ `shared/` directory exists in both services (well-organized)
- ✅ `repositories/` directory exists in both services (minimal but present)
- ✅ `blueprints/` directory exists in SFDC service (well-organized)
- ✅ `task_processor/` directory exists in SFDC service (minimal structure)
- ✅ `pmd_components/` directory exists in SFDC service (well-organized)
- ✅ `docs/` directory exists in SFDC service (comprehensive structure)
- ✅ `infrastructure/` directory exists in SFDC service (well-organized)
- ✅ `scripts/` directory exists in both services (well-organized)
- ✅ `tests/` directory exists in both services (good structure but incomplete coverage)
- ✅ `config/` directory exists in SFDC service (well-organized)
- ✅ `examples/` directory exists in SFDC service (well-organized)

## Task Breakdown

### Phase 1: Analysis and Preparation

#### Task 1.1: File Audit and Categorization
**Priority:** High  
**Effort:** 2 hours  
**Dependencies:** None

**Acceptance Criteria:**
- [x] Complete inventory of all files in both services
- [x] Categorization of files by type (source, test, config, temp, deprecated)
- [x] List of files to be deleted, moved, or reorganized
- [x] Documentation of current import dependencies

**Implementation Steps:**
1. Run automated scan for file types in both services
2. Identify all `test_*.py` files at wrong locations
3. Catalog all `*.disabled*`, `*.old`, `*.log`, cache files
4. Map current import statements and dependencies
5. Create detailed file movement plan

#### Task 1.2: Create Standard Directory Structure
**Priority:** High  
**Effort:** 1 hour  
**Dependencies:** Task 1.1

**Acceptance Criteria:**
- [x] Standard directory structure created in both services
- [x] README files created for each major directory
- [x] `.gitignore` updated to exclude temp/cache/log directories
- [x] Directory structure documented

**Implementation Steps:**
1. Create missing directories in both services:
   ```
   mkdir -p src/{api,shared,repositories}
   mkdir -p tests/{unit,integration,mocks}
   mkdir -p config/{monitoring,deployment}
   mkdir -p {temp,logs,cache}
   ```
2. Create README.md files for each directory explaining purpose
3. Update `.gitignore` to exclude temp/logs/cache directories
4. Document the folder structure in main README

#### Task 1.3: Backup and Branch Creation
**Priority:** High  
**Effort:** 0.5 hours  
**Dependencies:** None

**Acceptance Criteria:**
- [x] Backup branch created before any changes
- [x] All file movements documented for potential rollback
- [x] Recovery plan documented

**Implementation Steps:**
1. Create backup branch: `git checkout -b backup/before-folder-restructure`
2. Document current structure state
3. Create rollback procedure documentation

### Phase 2: Cleanup Operations

#### Task 2.1: Remove Deprecated and Temporary Files
**Priority:** High  
**Effort:** 1.5 hours  
**Dependencies:** Task 1.3

**Acceptance Criteria:**
- [x] All `*.disabled.old` files removed from SFDC service
- [x] All `*.disabled` files evaluated and removed/archived
- [x] All cache directories (`c:azurite/`, `__blobstorage__/`, `__pycache__/`) cleaned up
- [x] All scattered log files moved to `logs/` or removed
- [x] Azurite database files moved to `cache/`
- [x] Temporary result files organized or removed
- [x] Problematic directory names fixed

**Implementation Steps:**
1. Remove deprecated files from SFDC service:
   ```bash
   rm atomsec-func-sfdc/app.py.disabled.old
   rm atomsec-func-sfdc/function_app_backup_full.py.disabled.old
   rm atomsec-func-sfdc/function_app_complex.py.disabled.old
   rm atomsec-func-sfdc/WrapperFunction_backup.py.disabled.old
   rm atomsec-func-sfdc/api/general_endpoints.py.disabled
   ```
2. Move cache directories and files:
   ```bash
   mkdir -p atomsec-func-sfdc/cache/
   mv atomsec-func-sfdc/c:azurite/ atomsec-func-sfdc/cache/azurite/
   mv atomsec-func-sfdc/__azurite_db_*.json atomsec-func-sfdc/cache/
   mv atomsec-func-sfdc/__blobstorage__/ atomsec-func-sfdc/cache/blobstorage/
   ```
3. Move log files:
   ```bash
   mkdir -p atomsec-func-sfdc/logs/ atomsec-func-db-r/logs/
   mv atomsec-func-sfdc/c:azuritedebug.log atomsec-func-sfdc/logs/
   mv atomsec-func-sfdc/SFDC-local-testing.log atomsec-func-sfdc/logs/
   mv atomsec-func-db-r/*.log atomsec-func-db-r/logs/
   ```
4. Organize result files:
   ```bash
   mkdir -p atomsec-func-sfdc/reports/
   mv atomsec-func-sfdc/*_results.json atomsec-func-sfdc/reports/
   mv atomsec-func-sfdc/*_report.json atomsec-func-sfdc/reports/
   ```
5. Fix problematic directory name:
   ```bash
   mv "atomsec-func-sfdc/Assets|Project Maintenance" atomsec-func-sfdc/assets_project_maintenance
   ```
6. Update `.gitignore` to prevent future cache/log commits

#### Task 2.2: Consolidate Function Directories and Utility Scripts (SFDC)
**Priority:** Medium  
**Effort:** 2.5 hours  
**Dependencies:** Task 2.1

**Acceptance Criteria:**
- [ ] Empty function directories removed (`AccountsApiFunction/`, `DirectAccountsFunction/`, etc.)
- [ ] Any useful code consolidated into main structure
- [ ] Utility scripts moved to appropriate locations
- [ ] Function app registration updated if needed
- [ ] References directory reorganized

**Implementation Steps:**
1. Evaluate function directories (most appear to contain only `__init__.py`):
   ```bash
   # Check contents first, then remove if empty
   ls -la atomsec-func-sfdc/AccountsApiFunction/
   ls -la atomsec-func-sfdc/DirectAccountsFunction/
   ls -la atomsec-func-sfdc/DirectRolesFunction/
   ls -la atomsec-func-sfdc/IntegrationFunction/
   ls -la atomsec-func-sfdc/RolesApiFunction/
   ls -la atomsec-func-sfdc/RolesManagementFunction/
   ls -la atomsec-func-sfdc/WrapperFunction/
   ls -la atomsec-func-sfdc/home/
   ```
2. Remove empty function directories after verification
3. Move utility scripts to scripts directory:
   ```bash
   mv atomsec-func-sfdc/check_credentials.py atomsec-func-sfdc/scripts/
   mv atomsec-func-sfdc/check_policies_result.py atomsec-func-sfdc/scripts/
   mv atomsec-func-sfdc/create_policies_result.py atomsec-func-sfdc/scripts/
   mv atomsec-func-sfdc/create_queues.py atomsec-func-sfdc/scripts/
   mv atomsec-func-sfdc/setup_local_dev.py atomsec-func-sfdc/scripts/
   mv atomsec-func-sfdc/simple_test.py atomsec-func-sfdc/scripts/
   mv atomsec-func-sfdc/task_management.py atomsec-func-sfdc/scripts/
   mv atomsec-func-sfdc/service_bus_processor.py atomsec-func-sfdc/scripts/
   ```
4. Reorganize references directory:
   ```bash
   mv atomsec-func-sfdc/references/ atomsec-func-sfdc/docs/references/
   ```
5. Update any import statements that reference moved scripts

### Phase 3: Test File Organization

#### Task 3.1: Reorganize SFDC Service Tests
**Priority:** High  
**Effort:** 2 hours  
**Dependencies:** Task 1.2

**Acceptance Criteria:**
- [ ] All `test_*.py` files moved from root to appropriate test directories
- [ ] Test structure mirrors source code structure
- [ ] All tests can be discovered and run successfully
- [ ] Test import statements updated

**Implementation Steps:**
1. Move scattered test files from root:
   ```bash
   # Move to appropriate test directories
   mv atomsec-func-sfdc/test_enhanced_error_handling.py atomsec-func-sfdc/tests/unit/shared/
   mv atomsec-func-sfdc/test_account_management.py atomsec-func-sfdc/tests/unit/api/
   mv atomsec-func-sfdc/test_api.py atomsec-func-sfdc/tests/unit/api/
   mv atomsec-func-sfdc/test_endpoints.py atomsec-func-sfdc/tests/unit/api/
   mv atomsec-func-sfdc/test_local_startup.py atomsec-func-sfdc/tests/unit/
   mv atomsec-func-sfdc/test_queue_message_processor.py atomsec-func-sfdc/tests/unit/shared/
   mv atomsec-func-sfdc/test_queue_processing.py atomsec-func-sfdc/tests/unit/shared/
   mv atomsec-func-sfdc/test_integration.py atomsec-func-sfdc/tests/integration/
   ```
2. Ensure unit test subdirectories exist (most already exist):
   ```bash
   mkdir -p atomsec-func-sfdc/tests/unit/api/
   mkdir -p atomsec-func-sfdc/tests/unit/shared/
   mkdir -p atomsec-func-sfdc/tests/unit/blueprints/
   mkdir -p atomsec-func-sfdc/tests/unit/task_processor/
   mkdir -p atomsec-func-sfdc/tests/unit/pmd_components/
   ```
3. Update import statements in all moved test files
4. Update test discovery configuration in CI/CD
5. Verify all tests still pass after movement

#### Task 3.2: Reorganize DB Service Tests and Examples
**Priority:** High  
**Effort:** 2.5 hours  
**Dependencies:** Task 1.2

**Acceptance Criteria:**
- [ ] All `test_*.py` files moved from root to appropriate directories
- [ ] All `example_*.py` files moved to examples directory
- [ ] `run_integration_tests.py` moved to proper location
- [ ] Documentation files organized
- [ ] Test structure consistent with SFDC service
- [ ] All tests executable after reorganization

**Implementation Steps:**
1. Move test files from root:
   ```bash
   mv atomsec-func-db-r/test_enhanced_database_security.py atomsec-func-db-r/tests/unit/shared/
   mv atomsec-func-db-r/test_enhanced_queue_manager.py atomsec-func-db-r/tests/unit/shared/
   mv atomsec-func-db-r/test_enhanced_task_coordination_service.py atomsec-func-db-r/tests/unit/shared/
   mv atomsec-func-db-r/test_enhanced_task_status_service.py atomsec-func-db-r/tests/unit/shared/
   mv atomsec-func-db-r/test_execution_context_manager.py atomsec-func-db-r/tests/unit/shared/
   mv atomsec-func-db-r/test_task_coordination_service.py atomsec-func-db-r/tests/unit/shared/
   mv atomsec-func-db-r/run_integration_tests.py atomsec-func-db-r/tests/integration/
   ```
2. Move example files to examples directory:
   ```bash
   mkdir -p atomsec-func-db-r/examples/
   mv atomsec-func-db-r/example_*.py atomsec-func-db-r/examples/
   ```
3. Move documentation files to docs directory:
   ```bash
   mkdir -p atomsec-func-db-r/docs/
   mv atomsec-func-db-r/API_CONSOLIDATION_SUMMARY.md atomsec-func-db-r/docs/
   mv atomsec-func-db-r/CONSOLIDATED_API_ENDPOINTS.md atomsec-func-db-r/docs/
   mv atomsec-func-db-r/ENHANCED_DATABASE_SECURITY_IMPLEMENTATION.md atomsec-func-db-r/docs/
   mv atomsec-func-db-r/MIGRATION_GUIDE.md atomsec-func-db-r/docs/
   mv atomsec-func-db-r/QUEUE_BASED_PROCESSING_DEPLOYMENT_GUIDE.md atomsec-func-db-r/docs/
   mv atomsec-func-db-r/SFDC_PROXY_ARCHITECTURE.md atomsec-func-db-r/docs/
   ```
4. Create consistent test directory structure:
   ```bash
   mkdir -p atomsec-func-db-r/tests/unit/shared/
   mkdir -p atomsec-func-db-r/tests/unit/api/
   mkdir -p atomsec-func-db-r/tests/unit/repositories/
   ```
5. Update import paths in test files
6. Verify test execution works correctly

### Phase 4: Source Code Organization

#### Task 4.1: Organize SFDC Service Source Code (Optional - Current Structure is Good)
**Priority:** Low  
**Effort:** 1 hour  
**Dependencies:** Task 3.1

**Note:** The current SFDC service structure is already well-organized. This task is optional and focuses on minor improvements only.

**Acceptance Criteria:**
- [ ] Current structure validated and documented
- [ ] Minor organizational improvements made if needed
- [ ] All imports working correctly

**Implementation Steps:**
1. **SKIP** creating `src/` directory - current structure is good:
   - `api/` - already well organized
   - `shared/` - already well organized  
   - `blueprints/` - already well organized
   - `task_processor/` - already exists
   - `pmd_components/` - already well organized
   - `repositories/` - already exists
2. Validate current import statements work correctly
3. Document the current structure as the standard
4. Test function app startup and API endpoints
5. Focus on maintaining current good structure rather than reorganizing

#### Task 4.2: Organize DB Service Source Code (Optional - Current Structure is Good)
**Priority:** Low  
**Effort:** 1 hour  
**Dependencies:** Task 3.2

**Note:** The current DB service structure is already well-organized. This task is optional and focuses on validation only.

**Acceptance Criteria:**
- [ ] Current structure validated and documented
- [ ] All imports working correctly
- [ ] Structure consistency with SFDC service maintained

**Implementation Steps:**
1. **SKIP** creating `src/` directory - current structure is good:
   - `api/` - already well organized
   - `shared/` - already well organized
   - `repositories/` - already exists
2. Validate current import statements work correctly
3. Document the current structure as the standard
4. Test function app functionality
5. Ensure consistency with SFDC service structure patterns

#### Task 4.3: Validate Configuration and Documentation Organization
**Priority:** Medium  
**Effort:** 1 hour  
**Dependencies:** Task 4.1, Task 4.2

**Note:** Most configuration and documentation is already well-organized. This task focuses on validation and minor cleanup.

**Acceptance Criteria:**
- [ ] Configuration files validated in `config/` directory (SFDC - already good)
- [ ] Documentation structure validated in `docs/` hierarchy (SFDC - already comprehensive)
- [ ] Infrastructure code validated in `infrastructure/` directory (SFDC - already good)
- [ ] Examples directory validated (SFDC - already exists)
- [ ] DB service documentation organized (moved in Task 3.2)

**Implementation Steps:**
1. **VALIDATE** configuration organization:
   - SFDC `config/` directory already well-organized
   - DB service has minimal config needs
2. **VALIDATE** documentation structure:
   - SFDC `docs/` already comprehensive and well-structured
   - DB service docs moved to `docs/` in Task 3.2
3. **VALIDATE** examples organization:
   - SFDC `examples/` already exists and organized
   - DB service examples moved in Task 3.2
4. **VALIDATE** infrastructure organization:
   - SFDC `infrastructure/` already well-organized
   - DB service doesn't have infrastructure code
5. Update any hardcoded paths if found during validation

### Phase 5: Import Resolution and Testing

#### Task 5.1: Update Import Statements for Moved Files
**Priority:** High  
**Effort:** 2 hours  
**Dependencies:** Task 4.2

**Acceptance Criteria:**
- [ ] All import statements updated for moved test files
- [ ] All import statements updated for moved utility scripts
- [ ] No broken imports in either service
- [ ] Module loading working correctly

**Implementation Steps:**
1. Update imports in moved test files to reference correct paths
2. Update imports in moved utility scripts (now in scripts/)
3. Update any references to moved example files
4. Test import resolution with Python interpreter:
   ```python
   # Test imports in moved files work correctly
   python -c "from tests.unit.shared.test_enhanced_error_handling import *"
   ```
5. Update any dynamic imports or `importlib` usage if present
6. **NOTE:** No major src/ restructuring needed since current structure is good

#### Task 5.2: Validate Azure Function Configuration
**Priority:** High  
**Effort:** 1 hour  
**Dependencies:** Task 5.1

**Acceptance Criteria:**
- [ ] `host.json` configuration validated (no changes needed)
- [ ] Function app entry points working
- [ ] Blueprint registrations functioning
- [ ] Local development working

**Implementation Steps:**
1. Test local function app startup for both services
2. Validate `host.json` configuration (should not need changes)
3. Verify blueprint registration in `function_app.py` still works
4. Test API endpoint accessibility
5. Validate function trigger bindings
6. **NOTE:** Minimal changes expected since no major restructuring occurred

#### Task 5.3: Validate CI/CD Pipelines
**Priority:** High  
**Effort:** 1 hour  
**Dependencies:** Task 5.2

**Acceptance Criteria:**
- [ ] Pipeline scripts validated for current structure
- [ ] Test discovery working in CI/CD
- [ ] Deployment scripts functioning
- [ ] Build processes working correctly

**Implementation Steps:**
1. Validate pipeline test commands work with reorganized tests:
   ```yaml
   # Ensure test discovery still works
   python -m pytest tests/
   ```
2. Validate deployment scripts work with current structure
3. Check for any hardcoded paths that reference moved files
4. Test pipeline execution in development branch
5. **NOTE:** Minimal changes expected since major structure remains the same

### Phase 6: Validation and Documentation

#### Task 6.1: Comprehensive Testing
**Priority:** High  
**Effort:** 3 hours  
**Dependencies:** Task 5.3

**Acceptance Criteria:**
- [ ] All unit tests passing
- [ ] All integration tests passing
- [ ] Function app deployment successful
- [ ] API endpoints responding correctly
- [ ] No functionality regression

**Implementation Steps:**
1. Run full test suite for both services
2. Test local development environment setup
3. Deploy to development environment
4. Validate all API endpoints
5. Run integration tests against deployed services
6. Performance test to ensure no degradation

#### Task 6.2: Create Structure Documentation
**Priority:** Medium  
**Effort:** 2 hours  
**Dependencies:** Task 6.1

**Acceptance Criteria:**
- [ ] Folder structure documented
- [ ] File naming conventions documented
- [ ] Developer guidelines created
- [ ] Migration guide documented

**Implementation Steps:**
1. Create `docs/development/folder-structure.md`
2. Document naming conventions and standards
3. Create developer onboarding guide
4. Document the migration process for future reference
5. Update main README files with structure information

#### Task 6.3: Maintenance Automation
**Priority:** Low  
**Effort:** 2 hours  
**Dependencies:** Task 6.2

**Acceptance Criteria:**
- [ ] Automated checks for structure compliance
- [ ] Cleanup scripts for temp files
- [ ] Code review checklist updated
- [ ] IDE configurations provided

**Implementation Steps:**
1. Create script to validate folder structure compliance
2. Create cleanup script for temp/cache files
3. Update code review checklist template
4. Provide VS Code/IDE configuration for consistent structure
5. Add structure validation to CI/CD pipeline

## Risk Mitigation

### High-Risk Areas
1. **Import Resolution:** Complex dependency chains may break
   - **Mitigation:** Thorough testing at each step, automated import validation
2. **Function App Registration:** Blueprint imports may fail
   - **Mitigation:** Test function app startup after each major change
3. **CI/CD Pipeline:** Test discovery and deployment may fail
   - **Mitigation:** Update pipelines incrementally, test in isolation

### Rollback Triggers
- More than 50% of tests failing after import updates
- Function app failing to start locally
- Critical API endpoints returning errors
- CI/CD pipeline completely broken

## Success Metrics

### Immediate Success Criteria
- [ ] Zero test failures after reorganization
- [ ] Function apps start successfully locally
- [ ] All API endpoints respond correctly
- [ ] CI/CD pipelines execute successfully

### Long-term Success Criteria
- [ ] Developer onboarding time reduced by 25%
- [ ] Consistent folder structure maintained over 3 months
- [ ] No temporary files accumulating in root directories
- [ ] New code consistently placed in correct locations

## Timeline Estimate

**Total Effort:** 20 hours (reduced from 32 due to current good structure)  
**Duration:** 1-2 weeks (with testing and validation)

**Week 1:** Tasks 1.1-3.2 (Analysis, cleanup, test organization) - 12 hours  
**Week 2:** Tasks 4.1-6.3 (Validation, import resolution, documentation) - 8 hours

**Note:** Timeline reduced significantly because:
- Current source code structure is already well-organized
- No major src/ restructuring needed
- Focus is on cleanup and test organization rather than major reorganization 